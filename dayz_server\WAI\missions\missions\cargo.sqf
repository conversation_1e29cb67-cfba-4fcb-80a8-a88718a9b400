local _mission = count WAI_MissionData -1;
local _aiType = _this select 0; // "Bandit" or "Hero"
local _position = [30] call WAI_FindPos;
local _name = "Cargo";
local _startTime = diag_tickTime;
local _difficulty = "Medium";
local _localized = ["STR_CL_MISSION_BANDIT", "STR_CL_MISSION_HERO"] select (_aiType == "Hero");
local _localName = "STR_CUST_WAI_CARGO_TITLE";

diag_log format["[WAI]: %1 %2 started at %3.",_aiType,_name,_position];

local _messages = if (_aiType == "Hero") then {
	["STR_CUST_WAI_HERO_CARGO_START","STR_CUST_WAI_HERO_CARGO_WIN","STR_CUST_WAI_HERO_CARGO_FAIL"];
} else {
	["STR_CUST_WAI_BANDIT_CARGO_START","STR_CUST_WAI_BANDIT_CARGO_WIN","STR_CUST_WAI_BANDIT_CARGO_FAIL"];
};

////////////////////// Do not edit this section ///////////////////////////
local _markers = [1,1,1,1];
//[position,createMarker,setMarkerColor,setMarkerType,setMarkerShape,setMarkerBrush,setMarkerSize,setMarkerText,setMarkerAlpha]
_markers set [0, [_position, "WAI" + str(_mission), "ColorYellow", "", "ELLIPSE", "SolidBorder", [300,300], [], 0]];
_markers set [1, [_position, "WAI" + str(_mission) + "dot", "ColorBlue", "mil_dot", "", "", [], [_localName], 0]];
if (WAI_AutoClaim) then {_markers set [2, [_position, "WAI" + str(_mission) + "auto", "ColorBlue", "", "ELLIPSE", "Border", [WAI_AcAlertDistance,WAI_AcAlertDistance], [], 0]];};
DZE_ServerMarkerArray set [count DZE_ServerMarkerArray, _markers]; // Markers added to global array for JIP player requests.
_markerIndex = count DZE_ServerMarkerArray - 1;
PVDZ_ServerMarkerSend = ["start",_markers];
publicVariable "PVDZ_ServerMarkerSend";

WAI_MarkerReady = true;

// Add the mission's position to the global array so that other missions do not spawn near it.
DZE_MissionPositions set [count DZE_MissionPositions, _position];
local _posIndex = count DZE_MissionPositions - 1;

// Send announcement
[_difficulty,(_messages select 0)] call WAI_Message;

// Wait until a player is within range or timeout is reached.
local _timeout = false;
local _claimPlayer = objNull;

while {WAI_WaitForPlayer && !_timeout && {isNull _claimPlayer}} do {
	_claimPlayer = [_position, WAI_TimeoutDist] call isClosestPlayer;
	
	if (diag_tickTime - _startTime >= (WAI_Timeout * 60)) then {
		_timeout = true;
	};
	uiSleep 1;
};

if (_timeout) exitWith {
	[_mission, _aiType, _markerIndex, _posIndex] call WAI_AbortMission;
	[_difficulty,(_messages select 2)] call WAI_Message;
	diag_log format["WAI: %1 %2 aborted.",_aiType,_name,_position];
};
//////////////////////////////// End //////////////////////////////////////

// Spawn Crates
local _loot = if (_aiType == "Hero") then {Loot_Junkyard select 0;} else {Loot_Junkyard select 1;};
[[
	[_loot,WAI_CrateSm,[0.01,0]]
],_position,_mission] call WAI_SpawnCrate;

// Spawn Objects
[[
	//Buildings 
["MAP_Misc_Cargo1Bo",[14,-10,0],0],
["MAP_Misc_Cargo2A",[18,-10,0],-89],
["MAP_Misc_Cargo2B",[20,-3,0],2],
["MAP_Misc_Cargo2C", [14,1,0],0],
["MAP_Misc_Cargo2C", [21,-17,0],119],
["MAP_Misc_Cargo1G", [9,-1,0],39],
["MAP_Misc_Cargo1E",[11,7,0],86],
["MAP_Misc_Cargo1C",[2,-15,0],0],
["MAP_Misc_Cargo1B",[6,-8,0],111],
["MAP_Misc_Cargo2E", [3.5,6,0],-40],
["MAP_Misc_Cargo2D", [-2,-10,0],0],
["MAP_Misc_Cargo1F", [-11,10,0],0],
["MAP_Misc_Cargo1E",[-9,-4,0],0],
["MAP_Misc_Cargo1D",[21,6,0],-48],
["MAP_Misc_Cargo1Bo",[-6,6,0],-139],
["MAP_Misc_Cargo1B",[-19,-7,0],-41],
["MAP_Misc_Cargo1A",[-15,-1,0],14],
["MAP_Misc_Cargo1G", [-4,16,0],109],
["MAP_Misc_Cargo2C", [7,11,0],-114],
["MAP_Misc_Cargo2B", [4,-24,0],-74],
["MAP_Misc_Cargo2C",[4,20,0],-44],
["MAP_Misc_Cargo2A",[-12,-13,0],-22],
["MAP_A_CraneCon",[-21,10,0],0]
	
],_position,_mission] call WAI_SpawnObjects;

// AI Group Spawn Examples
// Parameters:	0: Position
//				1: Unit Count
//				2: Unit Skill ("easy","medium","hard","extreme" or "random")
//				3: Primary gun - "Random", "Unarmed", "Classname", Array
//				4: Launcher - "AT", "AA", or "" for no launcher
//				5: Backpack - "Random", "Classname", "none" or Array
//				6: Skin (_aiType, "Random","classname", array)
//				7: Gearset - 0,1,2, or "Random"
//				8: AI Type (_aiType, or ["type", #] format to overwrite default gain amount) ***Used to determine humanity gain or loss***
//				9: Mission variable from line 5 (_mission)

// Troops
local _num = round (random 3) + 4; // You can use this to get a random number of AI between 4 and 7. 
[[(_position select 0) + 3, (_position select 1) + 25, 0],4,_difficulty, "Random","AT","Random","Random","Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) - 27, (_position select 1) - 4, 0],4,_difficulty, "Random","","Random","Random","Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) + 33, (_position select 1) - 2, 0],4,_difficulty, "Random","AA","Random","Random","Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) + 10, (_position select 1) - 37, 0],_num,_difficulty,"Random","","Random","Random","Random",_aiType,_mission] call WAI_SpawnGroup;

// Vehicle Patrol
[[(_position select 0) + 60, _position select 1, 0],[(_position select 0) + 60, _position select 1, 0],50,2,"HMMWV_Armored",_difficulty,_aiType,_aiType,_mission] call WAI_VehPatrol;

// Static Guns
[[
	[(_position select 0) + 80, (_position select 1) - 28, 0],
	[(_position select 0) + 27, (_position select 1) - 40, 0]
],"KORD_high_TK_EP1",_difficulty,_aiType,_aiType,"Random","Random","Random",_mission] call WAI_SpawnStatic;

[
	_mission, // Mission number
	_position, // Position of mission
	_difficulty, // Difficulty
	_name, // Name of Mission
	_localName, // localized marker text
	_aiType, // "Bandit" or "Hero"
	_markerIndex,
	_posIndex,
	_claimPlayer,
	true, // show mission marker?
	true, // make minefields available for this mission
	["crate"], // Completion type: ["crate"], ["kill"], or ["assassinate", _unitGroup],
	_messages
] spawn WAI_MissionMonitor;
