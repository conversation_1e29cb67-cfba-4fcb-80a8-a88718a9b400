// This file contains loot array definitions per mission
// Array format [long guns, tools, items, pistols, backpacks] - Either a number or a custom array.
// First array is for Hero missions, second is for bandit missions. Change the values to preferences.
// [[Hero Loot Array],
//	[Bandit Loot Array]]

/***** Easy Missions *****/
Loot_UralAttack = [
	[4,8,35,3,1], // Hero
	[4,8,35,3,1] // Bandit
];
Loot_Farmer = [
	[6,5,15,3,3], // Hero
	[6,5,15,3,3] // Bandit
];
Loot_MediCamp = [
	[4,4,[70,WAI_Medical],3,3], // Hero
	[4,4,[70,WAI_Medical],3,3] // Bandit
];
Loot_Outpost = [
	[10,4,35,2,3], // Hero
	[10,4,35,2,3] // Bandit
];
Loot_ScoutPatrol = [
	[[5,WA<PERSON>_Sniper],8,36,2,3], // Hero
	[[5,WA<PERSON>_Sniper],8,36,2,3] // Bandit
];
Loot_SlaughterHouse = [
	[15,5,[30,WAI_FD],2,3], // Hero
	[15,5,[30,WAI_FD],2,3] // Bandit
];

/***** Medium Missions *****/
Loot_AbandonedTrader = [
	[[5,WAI_ValueWeapon],5,45,3,3], // Hero
	[[5,WAI_ValueWeapon],5,45,3,3] // Bandit
];
Loot_ArmedVehicle = [
	[3,2,[35,WAI_VehAmmo],1,3], // Hero
	[3,2,[35,WAI_VehAmmo],1,3] // Bandit
];
Loot_BHC = [ // Black Hawk Crash
	[10,5,20,3,1], // Hero
	[10,5,20,3,1] // Bandit
];
Loot_DrugBust = [
	[8,5,[10,WAI_Hemp],3,3], // Hero
	[8,5,[10,WAI_Hemp],3,3] // Bandit
];
Loot_Junkyard = [
	[15,5,16,3,1], // Hero
	[15,5,16,3,1] // Bandit
];
Loot_Patrol = [
	[3,0,[3,["ItemBriefcase100oz"]],0,1], // Hero
	[3,0,[3,["ItemBriefcase100oz"]],0,1] // Bandit
];
Loot_VehicleDrop = [
	[3,0,[3,["ItemBriefcase100oz"]],0,1], // Hero
	[3,0,[3,["ItemBriefcase100oz"]],0,1] // Bandit
];
Loot_WeaponCache = [
	[[10,WAI_ValueWeapon],4,10,3,4], // Hero
	[[10,WAI_ValueWeapon],4,10,3,4] // Bandit
];

/***** Hard Missions *****/
Loot_ArmyBase = [
	[25,5,15,3,5], // Hero
	[25,5,15,3,5] // Bandit
];
Loot_Base = [
	[20,8,20,3,[4,WAI_PacksLg]], // Hero
	[20,8,20,3,[4,WAI_PacksLg]] // Bandit
];
Loot_CannibalCave = [
	[10,8,[5,WAI_Lottery],3,[2,WAI_PacksLg]], // Hero
	[10,8,[5,WAI_Lottery],3,[2,WAI_PacksLg]] // Bandit
];
Loot_CapturedMV22 = [
	[5,5,[80,WAI_Medical],3,3], // Hero
	[5,5,[80,WAI_Medical],3,3] // Bandit
];
Loot_CropRaider = [
	[15,5,[30,WAI_Hemp],3,3], // Hero
	[15,5,[30,WAI_Hemp],3,3] // Bandit
];
Loot_DronePilot = [
	[14,8,15,3,[2,WAI_PacksLg]], // Hero
	[14,8,15,3,[2,WAI_PacksLg]] // Bandit
];
Loot_GemTower = [
	[8,5,[8,wai_gems],3,2], // Hero
	[8,5,[8,wai_gems],3,2] // Bandit
];
Loot_IkeaConvoy = [
	[[1,WAI_Chainsaws],[12,WAI_ToolsBuildable],[30,WAI_Ikea],3,4], // Hero
	[[1,WAI_Chainsaws],[12,WAI_ToolsBuildable],[30,WAI_Ikea],3,4] // Bandit
];
Loot_LumberJack = [
	[8,[8,WAI_ToolsBuildable],[20,WAI_Wood],3,[4,WAI_PacksLg]], // Hero
	[8,[8,WAI_ToolsBuildable],[20,WAI_Wood],3,[4,WAI_PacksLg]] // Bandit
];
Loot_MacDonald = [
	[19,5,[30,WAI_Hemp],3,2], // Hero
	[19,5,[30,WAI_Hemp],3,2] // Bandit
];
Loot_Radioshack = [
	[10,5,15,3,2], // Hero
	[10,5,15,3,2] // Bandit
];
Loot_Extraction = [
	[[18,WAI_ValueWeapon],4,[10,WAI_HeliAmmo],3,2], // Hero
	[[18,WAI_ValueWeapon],4,[10,WAI_HeliAmmo],3,2] // Bandit
];
Loot_TankColumn = [
	[[2,WAI_LaunchersALL],5,35,3,4], // Hero
	[[2,WAI_LaunchersALL],5,35,3,4] // Bandit
];
Loot_OilReserve = [
	[13,3,[7,WAI_OilReserve],3,1], // Hero
	[13,3,[7,WAI_OilReserve],3,1]  // Bandit
];	

/***** Extreme Missions *****/
Loot_Firestation1 = [ // Fire Station Crate 1
	[12,2,[8,WAI_HighValue],0,2], // Hero
	[12,2,[8,WAI_HighValue],0,2] // Bandit
];
Loot_Firestation2 = [ // Fire Station Crate 2
	[[3,WAI_LaunchersALL],[4,WAI_BlackTools],[6,WAI_Lottery],3,5], // Hero
	[[3,WAI_LaunchersALL],[4,WAI_BlackTools],[6,WAI_Lottery],3,5] // Bandit
];
Loot_Mayors = [
	[[6,WAI_ValueWeapon],[7,WAI_BlackTools],[8,WAI_Lottery],3,[2,WAI_PacksLg]], // Hero
	[[6,WAI_ValueWeapon],[7,WAI_BlackTools],[8,WAI_Lottery],3,[2,WAI_PacksLg]] // Bandit
];
Loot_Presidents = [
	[20,[3,WAI_BlackTools],[25,WAI_Presidents],2,4], // Hero
	[20,[3,WAI_BlackTools],[25,WAI_Presidents],2,4] // Bandit
];
Loot_Wuhan = [
	[50,[5,WAI_BlackTools],40,2,3], // Hero
	[50,[5,WAI_BlackTools],40,2,3] // Bandit
];
Loot_GraySkull1 = [ // Castle Grayskull Crate 1
	[2,0,[10,WAI_Lottery],0,1], // Hero
	[2,0,[10,WAI_Lottery],0,1] // Bandit
];
Loot_GraySkull2 = [ // Castle Grayskull Crate 2
	[[15,WAI_ValueWeapon],[8,WAI_BlackTools],20,3,2], // Hero
	[[15,WAI_ValueWeapon],[8,WAI_BlackTools],20,3,2] // Bandit
];
Loot_GraySkull3 = [ // Castle Grayskull Crate 3
	[20,5,35,3,2], // Hero
	[20,5,35,3,2] // Bandit
];
Loot_GraySkull4 = [ // He-Man smokes weed
	[4,2,[20,WAI_Hemp],2,3], // Hero
	[4,2,[20,WAI_Hemp],2,3] // Bandit
];
