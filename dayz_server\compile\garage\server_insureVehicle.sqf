private ["_array","_backPack","_backPackCount","_charID","_class","_clientID","_colour","_colour2","_damage","_displayName","_fuel","_hit","_hitpoints","_index","_inventory","_inventoryCount","_key","_magazine","_magazineCount","_message","_name","_objectID","_objectUID","_player","_playerUID","_selection","_vehicle","_weapons","_weaponsCount","_woGear"];

_player = _this select 0;
_obj = _this select 1;
_vehclass = _this select 2;
_vehName = getText (configFile >> "CfgVehicles" >> _vehclass >> "displayName");
_clientID = owner _player;
_playerUID = getPlayerUID _player;
_name = if (alive _player) then {[(name _player),1] call fnc_sanitizeInput;} else {"unknown player";};

_objID = _obj getVariable ["ObjectID","0"];

// was the vehicle already insured?
_insQuery = format ["SELECT Insurance_Price FROM object_data WHERE ObjectID = '%1'",_objID];
_insResult = [_insQuery, 2, false] call fn_asyncCall;
_insured = _insResult select 0;

if (parseNumber _insured == 0) then {
	_insQuery = format ["SELECT price FROM insurance_prices WHERE vehclass = '%1'",_vehclass];
	_insResult = [_insQuery, 2, false] call fn_asyncCall;
	_insPrice = _insResult select 0;

	_coins = _player getVariable [(["cashMoney","globalMoney"] select Z_persistentMoney),0];

	if (_coins >= _insPrice) then {
		_player setVariable [(["cashMoney","globalMoney"] select Z_persistentMoney),_coins - _insPrice,true];
		PVDZE_insureVehicleResult = [1,_insPrice,_vehName];
		_query = format ["UPDATE object_data SET Insurance_UID = '%1', Insurance_Price = (SELECT price FROM insurance_prices WHERE vehclass = '%2') WHERE ObjectID = '%3'",_playerUID,_vehclass,_objID];
		_result = [_query, 1, true] call fn_asyncCall;
		_obj setVariable ["Insurance_UID",_playerUID,true];
		_obj setVariable ["Insurance_Price",str _insPrice,true];
	} else {
		PVDZE_insureVehicleResult = [2,_insPrice,_vehName];
	};
} else {
	PVDZE_insureVehicleResult = [3,0,_vehName];
};
if (!isNull _player) then {_clientID publicVariableClient "PVDZE_insureVehicleResult";};
PVDZE_insureVehicleResult = nil;
_message = format["GARAGE: %1 (%2) tried to insure %3 (%6) @%4 %5",_name,_playerUID,_vehclass,mapGridPosition _player,getPosATL _player,_objID];
diag_log _message;
