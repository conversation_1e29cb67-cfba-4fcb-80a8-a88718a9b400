///////////////////////////////////////////////////////////////////////////////////////////
// NONPVP Pack Chernarus - By FoRcE
// Save as novylugmedcheck.sqf
// Add A New Folder Named newbases in your dayz_mission folder
// Call the file in your init.sqf in your dayz_missions.pbo
// Add [] ExecVM "newbases\novylugmedcheck.sqf";		    // Novy Lug Medical Checkpoint
// NOTE: If you have custom bases already change the link to yours
// Thanks And Enjoy
// Copy The Code Below The ///
///////////////////////////////////////////////////////////////////////////////////////////
 
if (isServer) then {
 
_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Shed_01_EP1", [9449.9893, 11429.881, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir -31.709793;
  _this setPos [9449.9893, 11429.881, -4.5776367e-005];
};
 
_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Shed_02_EP1", [9412.7656, 11432.69, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir 57.89901;
  _this setPos [9412.7656, 11432.69, 6.1035156e-005];
};
 
_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Shed_02_EP1", [9446.9873, 11454.038, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir 57.565479;
  _this setPos [9446.9873, 11454.038, -1.5258789e-005];
};
 
_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Guardhouse_EP1", [9444.0703, 11417.086, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir 144.44936;
  _this setPos [9444.0703, 11417.086, 0.00012207031];
};
 
_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [9380.6104, 11459.77, 0.080637328], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setDir 55.216797;
  _this setPos [9380.6104, 11459.77, 0.080637328];
};
 
_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [9383.9082, 11454.743], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setDir 57.107883;
  _this setPos [9383.9082, 11454.743];
};
 
_vehicle_30 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [9377.1641, 11464.924, 0.069448456], [], 0, "CAN_COLLIDE"];
  _vehicle_30 = _this;
  _this setDir 55.99445;
  _this setPos [9377.1641, 11464.924, 0.069448456];
};
 
_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["PowGen_Big", [9415.1357, 11429.581, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir -29.406164;
  _this setPos [9415.1357, 11429.581, -1.5258789e-005];
};
 
_vehicle_32 = objNull;
if (true) then
{
  _this = createVehicle ["PowGen_Big_EP1", [9418.834, 11431.429, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_32 = _this;
  _this setDir -28.615559;
  _this setPos [9418.834, 11431.429, 4.5776367e-005];
};
 
_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["Land_radar_EP1", [9442.1689, 11420.023, 3.3767953], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setPos [9442.1689, 11420.023, 3.3767953];
};
 
_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9402.8623, 11490.484, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir -36.20826;
  _this setPos [9402.8623, 11490.484, 6.1035156e-005];
};
 
_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9389.9961, 11481.166, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir -36.144047;
  _this setPos [9389.9961, 11481.166, -4.5776367e-005];
};
 
_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9388.2852, 11479.999, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir -35.866047;
  _this setPos [9388.2852, 11479.999, 7.6293945e-005];
};
 
_vehicle_37 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9381.0117, 11474.775, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_37 = _this;
  _this setDir -35.504562;
  _this setPos [9381.0117, 11474.775, -1.5258789e-005];
};
 
_vehicle_38 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9373.6885, 11469.555, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_38 = _this;
  _this setDir -35.756905;
  _this setPos [9373.6885, 11469.555, 7.6293945e-005];
};
 
_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9398.0449, 11498.558, -0.055664718], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setDir -32.187851;
  _this setPos [9398.0449, 11498.558, -0.055664718];
};
 
_vehicle_40 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9396.9521, 11492.439, 0.011710498], [], 0, "CAN_COLLIDE"];
  _vehicle_40 = _this;
  _this setDir 237.61931;
  _this setPos [9396.9521, 11492.439, 0.011710498];
};
 
_vehicle_41 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9417.3213, 11500.915, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_41 = _this;
  _this setDir -36.274773;
  _this setPos [9417.3213, 11500.915, -4.5776367e-005];
};
 
_vehicle_42 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9421.8486, 11504.162, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_42 = _this;
  _this setDir -36.400185;
  _this setPos [9421.8486, 11504.162, -7.6293945e-005];
};
 
_vehicle_43 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9422.5684, 11510.976, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_43 = _this;
  _this setDir -129.5988;
  _this setPos [9422.5684, 11510.976, -1.5258789e-005];
};
 
_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9372.5098, 11462.814, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setDir -124.35184;
  _this setPos [9372.5098, 11462.814, 6.1035156e-005];
};
 
_vehicle_45 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9376.918, 11456.056, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_45 = _this;
  _this setDir -123.60258;
  _this setPos [9376.918, 11456.056, -1.5258789e-005];
};
 
_vehicle_46 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9381.1689, 11449.552, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_46 = _this;
  _this setDir -124.16364;
  _this setPos [9381.1689, 11449.552, 6.1035156e-005];
};
 
_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9385.8789, 11442.635, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setDir -124.74117;
  _this setPos [9385.8789, 11442.635, 0.00012207031];
};
 
_vehicle_48 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9391.0469, 11435.015, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_48 = _this;
  _this setDir -124.51499;
  _this setPos [9391.0469, 11435.015, 7.6293945e-005];
};
 
_vehicle_49 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9408.0967, 11429.848], [], 0, "CAN_COLLIDE"];
  _vehicle_49 = _this;
  _this setDir 147.42488;
  _this setPos [9408.0967, 11429.848];
};
 
_vehicle_50 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9407.5967, 11429.215, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_50 = _this;
  _this setDir -32.011196;
  _this setPos [9407.5967, 11429.215, 0.00012207031];
};
 
_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9402.6055, 11426.609, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setDir 148.69965;
  _this setPos [9402.6055, 11426.609, -1.5258789e-005];
};
 
_vehicle_52 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [9395.9346, 11427.999, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_52 = _this;
  _this setDir -125.68646;
  _this setPos [9395.9346, 11427.999, -1.5258789e-005];
};
 
_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [9387.9395, 11448.769, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setDir 54.650894;
  _this setPos [9387.9395, 11448.769, 3.0517578e-005];
};
 
_vehicle_54 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [9391.7061, 11443.427, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_54 = _this;
  _this setDir 55.377113;
  _this setPos [9391.7061, 11443.427, 3.0517578e-005];
};
 
_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [9395.3896, 11437.971, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setDir 55.239365;
  _this setPos [9395.3896, 11437.971, -4.5776367e-005];
};
 
_vehicle_56 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [9399.4258, 11432.031, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_56 = _this;
  _this setDir 55.850979;
  _this setPos [9399.4258, 11432.031, -3.0517578e-005];
};
 
_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [9405.8789, 11487.452, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setDir 148.45465;
  _this setPos [9405.8789, 11487.452, 1.5258789e-005];
};
 
_vehicle_58 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [9417.3398, 11495.615, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_58 = _this;
  _this setDir 146.89153;
  _this setPos [9417.3398, 11495.615, 3.0517578e-005];
};
 
_vehicle_62 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9432.7344, 11445.235, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_62 = _this;
  _this setDir -30.18552;
  _this setPos [9432.7344, 11445.235, 3.0517578e-005];
};
 
_vehicle_63 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9434.2539, 11446.201, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_63 = _this;
  _this setDir -31.056648;
  _this setPos [9434.2539, 11446.201, -9.1552734e-005];
};
 
_vehicle_64 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9444.21, 11452.187, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_64 = _this;
  _this setDir -31.447172;
  _this setPos [9444.21, 11452.187, -4.5776367e-005];
};
 
_vehicle_65 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9442.7412, 11451.303, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_65 = _this;
  _this setDir -29.095554;
  _this setPos [9442.7412, 11451.303, 1.5258789e-005];
};
 
_vehicle_66 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [9428.6025, 11445.2], [], 0, "CAN_COLLIDE"];
  _vehicle_66 = _this;
  _this setDir -33.746716;
  _this setPos [9428.6025, 11445.2];
};
 
_vehicle_68 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9416.7148, 11517.916, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_68 = _this;
  _this setDir -130.92363;
  _this setPos [9416.7148, 11517.916, -7.6293945e-005];
};
 
_vehicle_69 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9411.1133, 11524.429, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_69 = _this;
  _this setDir -131.00679;
  _this setPos [9411.1133, 11524.429, -4.5776367e-005];
};
 
_vehicle_70 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9405.334, 11531.185, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_70 = _this;
  _this setDir -130.27441;
  _this setPos [9405.334, 11531.185, -1.5258789e-005];
};
 
_vehicle_71 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9400.0781, 11537.487, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_71 = _this;
  _this setDir -129.49686;
  _this setPos [9400.0781, 11537.487, -1.5258789e-005];
};
 
_vehicle_72 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9399.0244, 11545.518, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_72 = _this;
  _this setDir -65.62458;
  _this setPos [9399.0244, 11545.518, -3.0517578e-005];
};
 
_vehicle_73 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9405.5566, 11550.088, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_73 = _this;
  _this setDir -5.045917;
  _this setPos [9405.5566, 11550.088, -1.5258789e-005];
};
 
_vehicle_74 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9415.0117, 11550.834, 0.10929018], [], 0, "CAN_COLLIDE"];
  _vehicle_74 = _this;
  _this setDir -3.1196818;
  _this setPos [9415.0117, 11550.834, 0.10929018];
};
 
_vehicle_75 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9424.4189, 11551.477, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_75 = _this;
  _this setDir -5.235404;
  _this setPos [9424.4189, 11551.477, -3.0517578e-005];
};
 
_vehicle_76 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9433.4658, 11552.202, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_76 = _this;
  _this setDir -4.6880651;
  _this setPos [9433.4658, 11552.202, -1.5258789e-005];
};
 
_vehicle_81 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9483.4316, 11553.191, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_81 = _this;
  _this setDir 0.69816279;
  _this setPos [9483.4316, 11553.191, -3.0517578e-005];
};
 
_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9473.6504, 11553.159, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setDir 1.5366215;
  _this setPos [9473.6504, 11553.159, -1.5258789e-005];
};
 
_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9491.4541, 11550.041, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setDir 47.187714;
  _this setPos [9491.4541, 11550.041, -1.5258789e-005];
};
 
_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9497.6572, 11543.546, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setDir 46.768059;
  _this setPos [9497.6572, 11543.546, -4.5776367e-005];
};
 
_vehicle_85 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9503.5752, 11537.232, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_85 = _this;
  _this setDir 46.277466;
  _this setPos [9503.5752, 11537.232, 4.5776367e-005];
};
 
_vehicle_86 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9509.6582, 11530.853, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_86 = _this;
  _this setDir 46.101002;
  _this setPos [9509.6582, 11530.853, -1.5258789e-005];
};
 
_vehicle_87 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9516.4873, 11523.87, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_87 = _this;
  _this setDir 45.445118;
  _this setPos [9516.4873, 11523.87, 1.5258789e-005];
};
 
_vehicle_88 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9522.624, 11517.492, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_88 = _this;
  _this setDir 45.878555;
  _this setPos [9522.624, 11517.492, -6.1035156e-005];
};
 
_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9528.6553, 11511.166, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir 46.583065;
  _this setPos [9528.6553, 11511.166, -3.0517578e-005];
};
 
_vehicle_90 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9535.0762, 11504.357, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_90 = _this;
  _this setDir 46.40041;
  _this setPos [9535.0762, 11504.357, -6.1035156e-005];
};
 
_vehicle_91 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9540.3682, 11498.722, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_91 = _this;
  _this setDir 46.750656;
  _this setPos [9540.3682, 11498.722, -1.5258789e-005];
};
 
_vehicle_92 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9533.4463, 11491.77, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_92 = _this;
  _this setDir 161.38724;
  _this setPos [9533.4463, 11491.77, -4.5776367e-005];
};
 
_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9528.7324, 11490.182, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setDir 164.80792;
  _this setPos [9528.7324, 11490.182, -7.6293945e-005];
};
 
_vehicle_94 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9519.6113, 11488.088], [], 0, "CAN_COLLIDE"];
  _vehicle_94 = _this;
  _this setDir -191.54825;
  _this setPos [9519.6113, 11488.088];
};
 
_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9513.8965, 11486.979, -0.082213402], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setDir 166.42648;
  _this setPos [9513.8965, 11486.979, -0.082213402];
};
 
_vehicle_96 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9501.2412, 11483.727, 0.6255672], [], 0, "CAN_COLLIDE"];
  _vehicle_96 = _this;
  _this setDir 166.89401;
  _this setPos [9501.2412, 11483.727, 0.6255672];
};
 
_vehicle_97 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9493.0527, 11482.813, 0.44092131], [], 0, "CAN_COLLIDE"];
  _vehicle_97 = _this;
  _this setDir 174.498;
  _this setPos [9493.0527, 11482.813, 0.44092131];
};
 
_vehicle_98 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9483.7188, 11483.601, 0.29943562], [], 0, "CAN_COLLIDE"];
  _vehicle_98 = _this;
  _this setDir 193.20189;
  _this setPos [9483.7188, 11483.601, 0.29943562];
};
 
_vehicle_100 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9539.2588, 11493.76, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_100 = _this;
  _this setDir 158.27858;
  _this setPos [9539.2588, 11493.76, 3.0517578e-005];
};
 
_vehicle_101 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9479.2959, 11481.173], [], 0, "CAN_COLLIDE"];
  _vehicle_101 = _this;
  _this setDir -90.89682;
  _this setPos [9479.2959, 11481.173];
};
 
_vehicle_102 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9479.2656, 11473.345, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_102 = _this;
  _this setDir -89.486755;
  _this setPos [9479.2656, 11473.345, 6.1035156e-005];
};
 
_vehicle_103 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9474.6279, 11470.066, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_103 = _this;
  _this setDir 9.766099;
  _this setPos [9474.6279, 11470.066, -6.1035156e-005];
};
 
_vehicle_104 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9466.7725, 11471.573, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_104 = _this;
  _this setDir 10.976624;
  _this setPos [9466.7725, 11471.573, -9.1552734e-005];
};
 
_vehicle_105 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9442.9199, 11472.165, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_105 = _this;
  _this setDir -3.9838612;
  _this setPos [9442.9199, 11472.165, -0.00015258789];
};
 
_vehicle_106 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9437.499, 11467.191, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_106 = _this;
  _this setDir -80.30481;
  _this setPos [9437.499, 11467.191, -3.0517578e-005];
};
 
_vehicle_107 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9442.417, 11456.39, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_107 = _this;
  _this setDir -133.14665;
  _this setPos [9442.417, 11456.39, -9.1552734e-005];
};
 
_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9439.541, 11459.496, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setDir -133.64616;
  _this setPos [9439.541, 11459.496, -1.5258789e-005];
};
 
_vehicle_109 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [9507.3506, 11485.425, 0.10331704], [], 0, "CAN_COLLIDE"];
  _vehicle_109 = _this;
  _this setDir -192.35812;
  _this setPos [9507.3506, 11485.425, 0.10331704];
};
 
_vehicle_110 = objNull;
if (true) then
{
  _this = createVehicle ["Land_SS_hangar", [9453.5156, 11576.539, 0.56024444], [], 0, "CAN_COLLIDE"];
  _vehicle_110 = _this;
  _this setDir 362.11826;
  _this setPos [9453.5156, 11576.539, 0.56024444];
};
 
_vehicle_112 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [9436.252, 11550.657, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_112 = _this;
  _this setPos [9436.252, 11550.657, 6.1035156e-005];
};
 
_vehicle_113 = objNull;
if (true) then
{
  _this = createVehicle ["PowerGenerator_EP1", [9438.2041, 11549.85, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_113 = _this;
  _this setDir 88.417046;
  _this setPos [9438.2041, 11549.85, -3.0517578e-005];
};
 
_vehicle_114 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Antenna", [9432.9844, 11552.857, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_114 = _this;
  _this setPos [9432.9844, 11552.857, -3.0517578e-005];
};
 
_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["PARACHUTE_TARGET", [9420.5625, 11536.202, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setPos [9420.5625, 11536.202, -6.1035156e-005];
};
 
_vehicle_122 = objNull;
if (true) then
{
  _this = createVehicle ["PARACHUTE_TARGET", [9446.6592, 11530.869, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_122 = _this;
  _this setPos [9446.6592, 11530.869, -1.5258789e-005];
};
 
_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9422.8242, 11518.974, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setDir 7.5614042;
  _this setPos [9422.8242, 11518.974, -3.0517578e-005];
};
 
_vehicle_124 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9436.877, 11517.123, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_124 = _this;
  _this setDir 7.6180978;
  _this setPos [9436.877, 11517.123, -3.0517578e-005];
};
 
_vehicle_125 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9450.9043, 11515.253, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_125 = _this;
  _this setDir 7.6278725;
  _this setPos [9450.9043, 11515.253, -6.1035156e-005];
};
 
_vehicle_126 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9433.9209, 11525.224, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_126 = _this;
  _this setDir 92.103279;
  _this setPos [9433.9209, 11525.224, -7.6293945e-005];
};
 
_vehicle_127 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9458.5781, 11521.328, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_127 = _this;
  _this setDir -85.96962;
  _this setPos [9458.5781, 11521.328, -3.0517578e-005];
};
 
_vehicle_128 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9434.3916, 11536.219, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_128 = _this;
  _this setDir -87.385735;
  _this setPos [9434.3916, 11536.219, -3.0517578e-005];
};
 
_vehicle_129 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9459.3799, 11533.824, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_129 = _this;
  _this setDir 93.406944;
  _this setPos [9459.3799, 11533.824, -6.1035156e-005];
};
 
_vehicle_130 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9433.1709, 11507.145, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_130 = _this;
  _this setDir 5.4225655;
  _this setPos [9433.1709, 11507.145, -1.5258789e-005];
};
 
_vehicle_131 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9443.3281, 11506.149, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_131 = _this;
  _this setDir 5.9834876;
  _this setPos [9443.3281, 11506.149, -3.0517578e-005];
};
 
_vehicle_132 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9448.4248, 11505.606, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_132 = _this;
  _this setDir 5.8851471;
  _this setPos [9448.4248, 11505.606, 1.5258789e-005];
};
 
_vehicle_133 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9387.4258, 11467.455, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_133 = _this;
  _this setDir 55.10474;
  _this setPos [9387.4258, 11467.455, -1.5258789e-005];
};
 
_vehicle_134 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9407.5957, 11438.501, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_134 = _this;
  _this setDir 55.274487;
  _this setPos [9407.5957, 11438.501, 3.0517578e-005];
};
 
_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9398.6748, 11451.437, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir 55.526302;
  _this setPos [9398.6748, 11451.437, 4.5776367e-005];
};
 
_vehicle_138 = objNull;
if (true) then
{
  _this = createVehicle ["USMC_WarfareBFieldhHospital", [9391.6738, 11473.829, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_138 = _this;
  _this setDir -123.66594;
  _this setPos [9391.6738, 11473.829, -3.0517578e-005];
};
 
_vehicle_147 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [9436.2432, 11601.366], [], 0, "CAN_COLLIDE"];
  _vehicle_147 = _this;
  _this setDir 28.542408;
  _this setPos [9436.2432, 11601.366];
};
 
_vehicle_148 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [9422.585, 11609.042, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_148 = _this;
  _this setDir 209.54305;
  _this setPos [9422.585, 11609.042, -3.0517578e-005];
};
 
_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9430.2578, 11606.488, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setDir -53.151924;
  _this setPos [9430.2578, 11606.488, -3.0517578e-005];
};
 
_vehicle_151 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9431.7813, 11608.577, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_151 = _this;
  _this setDir -52.205257;
  _this setPos [9431.7813, 11608.577, -3.0517578e-005];
};
 
_vehicle_152 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9433.3125, 11610.602], [], 0, "CAN_COLLIDE"];
  _vehicle_152 = _this;
  _this setDir -52.027359;
  _this setPos [9433.3125, 11610.602];
};
 
_vehicle_153 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9423.541, 11610.39, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_153 = _this;
  _this setDir -47.029682;
  _this setPos [9423.541, 11610.39, 1.5258789e-005];
};
 
_vehicle_154 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9425.6973, 11612.521, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_154 = _this;
  _this setDir -45.493183;
  _this setPos [9425.6973, 11612.521, -1.5258789e-005];
};
 
_vehicle_155 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9427.4629, 11614.271], [], 0, "CAN_COLLIDE"];
  _vehicle_155 = _this;
  _this setDir -44.234924;
  _this setPos [9427.4629, 11614.271];
};
 
_vehicle_156 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9429.3154, 11615.98, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_156 = _this;
  _this setDir -40.989735;
  _this setPos [9429.3154, 11615.98, 3.0517578e-005];
};
 
_vehicle_157 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9437.1992, 11602.744], [], 0, "CAN_COLLIDE"];
  _vehicle_157 = _this;
  _this setDir -57.197498;
  _this setPos [9437.1992, 11602.744];
};
 
_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9438.5293, 11604.876], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setDir -58.358292;
  _this setPos [9438.5293, 11604.876];
};
 
_vehicle_159 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9439.9131, 11606.909, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_159 = _this;
  _this setDir -55.462772;
  _this setPos [9439.9131, 11606.909, -1.5258789e-005];
};
 
_vehicle_160 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9441.2324, 11609.163, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_160 = _this;
  _this setDir -64.49485;
  _this setPos [9441.2324, 11609.163, -1.5258789e-005];
};
 
_vehicle_161 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9442.2109, 11611.532, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_161 = _this;
  _this setDir -69.374092;
  _this setPos [9442.2109, 11611.532, -9.1552734e-005];
};
 
_vehicle_162 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9443.1084, 11613.963, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_162 = _this;
  _this setDir -69.618622;
  _this setPos [9443.1084, 11613.963, 3.0517578e-005];
};
 
_vehicle_163 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9443.9521, 11616.376, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_163 = _this;
  _this setDir -70.619293;
  _this setPos [9443.9521, 11616.376, -3.0517578e-005];
};
 
_vehicle_164 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9444.7568, 11618.797, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_164 = _this;
  _this setDir -72.514374;
  _this setPos [9444.7568, 11618.797, 1.5258789e-005];
};
 
_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9445.8232, 11621.076, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setDir -56.629791;
  _this setPos [9445.8232, 11621.076, -3.0517578e-005];
};
 
_vehicle_166 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9447.1641, 11623.252, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_166 = _this;
  _this setDir -57.47303;
  _this setPos [9447.1641, 11623.252, -3.0517578e-005];
};
 
_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9431.1719, 11617.481, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setDir -38.499813;
  _this setPos [9431.1719, 11617.481, 3.0517578e-005];
};
 
_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9433.0195, 11618.873, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setDir -35.377472;
  _this setPos [9433.0195, 11618.873, -3.0517578e-005];
};
 
_vehicle_169 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9435.1846, 11620.397, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_169 = _this;
  _this setDir -34.426605;
  _this setPos [9435.1846, 11620.397, -3.0517578e-005];
};
 
_vehicle_170 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9434.9561, 11612.588, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_170 = _this;
  _this setDir -48.22179;
  _this setPos [9434.9561, 11612.588, -1.5258789e-005];
};
 
_vehicle_171 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9436.6543, 11614.461, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_171 = _this;
  _this setDir -48.191078;
  _this setPos [9436.6543, 11614.461, -0.00010681152];
};
 
_vehicle_172 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9437.0908, 11621.664, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_172 = _this;
  _this setDir -32.961548;
  _this setPos [9437.0908, 11621.664, -4.5776367e-005];
};
 
_vehicle_173 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9438.7998, 11622.948, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_173 = _this;
  _this setDir -39.795792;
  _this setPos [9438.7998, 11622.948, -1.5258789e-005];
};
 
_vehicle_174 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9440.3496, 11624.58, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_174 = _this;
  _this setDir -53.198441;
  _this setPos [9440.3496, 11624.58, 1.5258789e-005];
};
 
_vehicle_175 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9440.2314, 11633.354, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_175 = _this;
  _this setDir -61.728123;
  _this setPos [9440.2314, 11633.354, -4.5776367e-005];
};
 
_vehicle_176 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9448.4951, 11625.448, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_176 = _this;
  _this setDir -58.40926;
  _this setPos [9448.4951, 11625.448, -4.5776367e-005];
};
 
_vehicle_177 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9441.4404, 11635.474], [], 0, "CAN_COLLIDE"];
  _vehicle_177 = _this;
  _this setDir -58.602139;
  _this setPos [9441.4404, 11635.474];
};
 
_vehicle_178 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9424.9258, 11611.795], [], 0, "CAN_COLLIDE"];
  _vehicle_178 = _this;
  _this setDir -44.472935;
  _this setPos [9424.9258, 11611.795];
};
 
_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9438.2666, 11616.287, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setDir -48.286041;
  _this setPos [9438.2666, 11616.287, -9.1552734e-005];
};
 
_vehicle_180 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Barrel_water", [9439.4844, 11617.489, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_180 = _this;
  _this setPos [9439.4844, 11617.489, 4.5776367e-005];
};
 
_vehicle_181 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Barrel_water", [9439.2275, 11617.783, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_181 = _this;
  _this setPos [9439.2275, 11617.783, 3.0517578e-005];
};
 
_vehicle_182 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Barrel_water", [9439.6953, 11617.944, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_182 = _this;
  _this setPos [9439.6953, 11617.944, 4.5776367e-005];
};
 
_vehicle_183 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierUSA", [9401.1299, 11550.643, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_183 = _this;
  _this setPos [9401.1299, 11550.643, -3.0517578e-005];
};
 
_vehicle_184 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [9446.749, 11636.413, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_184 = _this;
  _this setDir -152.16914;
  _this setPos [9446.749, 11636.413, -3.0517578e-005];
};
 
_vehicle_185 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9446.791, 11636.831, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_185 = _this;
  _this setDir 28.841988;
  _this setPos [9446.791, 11636.831, -1.5258789e-005];
};
 
_vehicle_186 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9444.5098, 11638.071, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_186 = _this;
  _this setDir 27.063734;
  _this setPos [9444.5098, 11638.071, -7.6293945e-005];
};
 
_vehicle_187 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9442.7754, 11637.586, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_187 = _this;
  _this setDir -56.612518;
  _this setPos [9442.7754, 11637.586, 9.1552734e-005];
};
 
_vehicle_188 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9453.6914, 11633.093, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_188 = _this;
  _this setDir 28.802855;
  _this setPos [9453.6914, 11633.093, -6.1035156e-005];
};
 
_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9455.8027, 11631.905], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setDir 30.141607;
  _this setPos [9455.8027, 11631.905];
};
 
_vehicle_190 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9437.7217, 11450.052, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_190 = _this;
  _this setDir -122.79015;
  _this setPos [9437.7217, 11450.052, 4.5776367e-005];
};
 
_vehicle_191 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [9444.9863, 11637.121, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_191 = _this;
  _this setPos [9444.9863, 11637.121, -3.0517578e-005];
};
 
_vehicle_192 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [9454.4609, 11631.753], [], 0, "CAN_COLLIDE"];
  _vehicle_192 = _this;
  _this setPos [9454.4609, 11631.753];
};
 
_vehicle_193 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [9436.5723, 11600.196, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_193 = _this;
  _this setPos [9436.5723, 11600.196, -1.5258789e-005];
};
 
_vehicle_194 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [9429.0986, 11604.8], [], 0, "CAN_COLLIDE"];
  _vehicle_194 = _this;
  _this setPos [9429.0986, 11604.8];
};
 
_vehicle_195 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [9422.1309, 11608.716], [], 0, "CAN_COLLIDE"];
  _vehicle_195 = _this;
  _this setPos [9422.1309, 11608.716];
};
 
_vehicle_197 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BarGate2", [9393.2178, 11459.549, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_197 = _this;
  _this setDir 54.127254;
  _this setPos [9393.2178, 11459.549, -1.5258789e-005];
};
 
_vehicle_198 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BarGate2", [9457.9561, 11511.661, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_198 = _this;
  _this setDir -254.47018;
  _this setPos [9457.9561, 11511.661, -3.0517578e-005];
};
 
_vehicle_199 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BarGate2", [9456.667, 11507.188, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_199 = _this;
  _this setDir 105.76154;
  _this setPos [9456.667, 11507.188, 3.0517578e-005];
};
 
_vehicle_200 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BarGate2", [9440.1904, 11449.773, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_200 = _this;
  _this setDir -32.806099;
  _this setPos [9440.1904, 11449.773, 1.5258789e-005];
};
 
_vehicle_201 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BarGate2", [9436.9814, 11447.824, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_201 = _this;
  _this setDir -30.086498;
  _this setPos [9436.9814, 11447.824, -1.5258789e-005];
};
 
 
_vehicle_202 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Com_tower_ep1", [9431.9805, 11555.951, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_202 = _this;
  _this setDir -2.3691399;
  _this setPos [9431.9805, 11555.951, -1.5258789e-005];
};
 
_vehicle_203 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Guardhouse_EP1", [9423.0029, 11557.292, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_203 = _this;
  _this setDir 90.7314;
  _this setPos [9423.0029, 11557.292, -3.0517578e-005];
};
 
_vehicle_204 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i_EP1", [9433.2637, 11570.236, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_204 = _this;
  _this setDir -88.290192;
  _this setPos [9433.2637, 11570.236, 6.1035156e-005];
};
 
_vehicle_205 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hlidac_Budka_EP1", [9426.6465, 11602.078, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_205 = _this;
  _this setDir 128.48003;
  _this setPos [9426.6465, 11602.078, -1.5258789e-005];
};
 
_vehicle_206 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hlidac_Budka_EP1", [9444.2451, 11633.141, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_206 = _this;
  _this setDir 119.34493;
  _this setPos [9444.2451, 11633.141, 1.5258789e-005];
};
 
_vehicle_207 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hlidac_Budka_EP1", [9404.2305, 11495.849], [], 0, "CAN_COLLIDE"];
  _vehicle_207 = _this;
  _this setDir 58.502087;
  _this setPos [9404.2305, 11495.849];
};
 
_vehicle_208 = objNull;
if (true) then
{
  _this = createVehicle ["GunrackUS_EP1", [9433.3262, 11579.444], [], 0, "CAN_COLLIDE"];
  _vehicle_208 = _this;
  _this setDir 1.8391871;
  _this setPos [9433.3262, 11579.444];
};
 
_vehicle_209 = objNull;
if (true) then
{
  _this = createVehicle ["GunrackUS_EP1", [9434.2344, 11579.346, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_209 = _this;
  _this setDir 1.9240649;
  _this setPos [9434.2344, 11579.346, -1.5258789e-005];
};
 
_vehicle_210 = objNull;
if (true) then
{
  _this = createVehicle ["GunrackUS_EP1", [9426.6348, 11560.347, 0.37672192], [], 0, "CAN_COLLIDE"];
  _vehicle_210 = _this;
  _this setPos [9426.6348, 11560.347, 0.37672192];
};
 
_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["GunrackUS_EP1", [9425.8447, 11560.319, 0.39798066], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setPos [9425.8447, 11560.319, 0.39798066];
};
 
_vehicle_212 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small_EP1", [9453.4873, 11630.155, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_212 = _this;
  _this setDir -150.51518;
  _this setPos [9453.4873, 11630.155, 3.0517578e-005];
};
 
_vehicle_213 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small_EP1", [9453.9316, 11625.598, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_213 = _this;
  _this setDir -61.574841;
  _this setPos [9453.9316, 11625.598, 1.5258789e-005];
};
 
_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small_EP1", [9449.7012, 11624.554], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setDir 30.829403;
  _this setPos [9449.7012, 11624.554];
};
 
_vehicle_216 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9439.0342, 11631.349], [], 0, "CAN_COLLIDE"];
  _vehicle_216 = _this;
  _this setDir -57.840302;
  _this setPos [9439.0342, 11631.349];
};
 
_vehicle_217 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9437.7061, 11629.285, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_217 = _this;
  _this setDir -56.933083;
  _this setPos [9437.7061, 11629.285, -1.5258789e-005];
};
 
_vehicle_218 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9437.9873, 11627.483], [], 0, "CAN_COLLIDE"];
  _vehicle_218 = _this;
  _this setDir 35.988766;
  _this setPos [9437.9873, 11627.483];
};
 
_vehicle_219 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9439.9922, 11626.132, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_219 = _this;
  _this setDir 32.251652;
  _this setPos [9439.9922, 11626.132, -3.0517578e-005];
};
 
_vehicle_220 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_concrete_High", [9420.082, 11569.174, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_220 = _this;
  _this setPos [9420.082, 11569.174, -4.5776367e-005];
};
 
_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [9417.7705, 11561.496, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setDir -176.26294;
  _this setPos [9417.7705, 11561.496, -3.0517578e-005];
};
 
_vehicle_222 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [9417.7578, 11560.388, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_222 = _this;
  _this setDir 181.9261;
  _this setPos [9417.7578, 11560.388, -9.1552734e-005];
};
 
_vehicle_223 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [9417.916, 11559.268, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_223 = _this;
  _this setDir 180.06595;
  _this setPos [9417.916, 11559.268, -4.5776367e-005];
};
 
_vehicle_224 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [9417.9307, 11558.149, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_224 = _this;
  _this setDir 179.29396;
  _this setPos [9417.9307, 11558.149, -6.1035156e-005];
};
 
_vehicle_227 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9421.9932, 11595.193, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_227 = _this;
  _this setDir -51.715431;
  _this setPos [9421.9932, 11595.193, 1.5258789e-005];
};
 
_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9432.2705, 11597.336, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setDir -49.23579;
  _this setPos [9432.2705, 11597.336, 1.5258789e-005];
};
 
_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9428.7637, 11593.236, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setDir -48.386848;
  _this setPos [9428.7637, 11593.236, 1.5258789e-005];
};
 
_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9425.3066, 11589.244, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setDir -48.406803;
  _this setPos [9425.3066, 11589.244, 1.5258789e-005];
};
 
_vehicle_231 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9421.875, 11585.119, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_231 = _this;
  _this setDir -48.697765;
  _this setPos [9421.875, 11585.119, -4.5776367e-005];
};
 
_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9418.4512, 11581.357, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setDir -48.886082;
  _this setPos [9418.4512, 11581.357, -4.5776367e-005];
};
 
_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9415.1934, 11577.388, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setDir -50.172989;
  _this setPos [9415.1934, 11577.388, 7.6293945e-005];
};
 
_vehicle_234 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9411.7568, 11573.028], [], 0, "CAN_COLLIDE"];
  _vehicle_234 = _this;
  _this setDir -50.094299;
  _this setPos [9411.7568, 11573.028];
};
 
_vehicle_235 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9408.4824, 11568.586], [], 0, "CAN_COLLIDE"];
  _vehicle_235 = _this;
  _this setDir -54.77636;
  _this setPos [9408.4824, 11568.586];
};
 
_vehicle_236 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9405.6846, 11563.935, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_236 = _this;
  _this setDir -59.135986;
  _this setPos [9405.6846, 11563.935, -9.1552734e-005];
};
 
_vehicle_237 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9403.5859, 11558.958, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_237 = _this;
  _this setDir -66.779861;
  _this setPos [9403.5859, 11558.958, 6.1035156e-005];
};
 
_vehicle_238 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9401.1816, 11550.679, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_238 = _this;
  _this setDir -72.48513;
  _this setPos [9401.1816, 11550.679, -4.5776367e-005];
};
 
_vehicle_239 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9418.6885, 11590.906, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_239 = _this;
  _this setDir -52.098034;
  _this setPos [9418.6885, 11590.906, -4.5776367e-005];
};
 
_vehicle_240 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9415.3066, 11586.556, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_240 = _this;
  _this setDir -52.268597;
  _this setPos [9415.3066, 11586.556, 1.5258789e-005];
};
 
_vehicle_241 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9411.8916, 11582.179, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_241 = _this;
  _this setDir -52.162594;
  _this setPos [9411.8916, 11582.179, 6.1035156e-005];
};
 
_vehicle_242 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9408.4463, 11577.849, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_242 = _this;
  _this setDir -52.348763;
  _this setPos [9408.4463, 11577.849, -6.1035156e-005];
};
 
_vehicle_243 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9405.1152, 11573.36, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_243 = _this;
  _this setDir -52.463943;
  _this setPos [9405.1152, 11573.36, 1.5258789e-005];
};
 
_vehicle_244 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9402.2861, 11569.044, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_244 = _this;
  _this setDir -55.707008;
  _this setPos [9402.2861, 11569.044, 3.0517578e-005];
};
 
_vehicle_245 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9399.6016, 11564.458, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_245 = _this;
  _this setDir -61.005203;
  _this setPos [9399.6016, 11564.458, -4.5776367e-005];
};
 
_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9397.1641, 11559.835], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setDir -62.91256;
  _this setPos [9397.1641, 11559.835];
};
 
_vehicle_247 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9394.7988, 11554.814, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_247 = _this;
  _this setDir -65.544487;
  _this setPos [9394.7988, 11554.814, -6.1035156e-005];
};
 
_vehicle_248 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9392.7109, 11549.65, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_248 = _this;
  _this setDir -69.552208;
  _this setPos [9392.7109, 11549.65, 1.5258789e-005];
};
 
_vehicle_249 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9391, 11544.479, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_249 = _this;
  _this setDir -73.33313;
  _this setPos [9391, 11544.479, 1.5258789e-005];
};
 
_vehicle_250 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9418.7266, 11604.586, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_250 = _this;
  _this setDir -51.72366;
  _this setPos [9418.7266, 11604.586, 3.0517578e-005];
};
 
_vehicle_251 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9415.6055, 11600.267, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_251 = _this;
  _this setDir -54.077389;
  _this setPos [9415.6055, 11600.267, 1.5258789e-005];
};
 
_vehicle_252 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9412.4189, 11595.89], [], 0, "CAN_COLLIDE"];
  _vehicle_252 = _this;
  _this setDir -53.895279;
  _this setPos [9412.4189, 11595.89];
};
 
_vehicle_253 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9409.3818, 11591.47], [], 0, "CAN_COLLIDE"];
  _vehicle_253 = _this;
  _this setDir -55.866875;
  _this setPos [9409.3818, 11591.47];
};
 
_vehicle_254 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9406.3008, 11586.865, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_254 = _this;
  _this setDir -55.818932;
  _this setPos [9406.3008, 11586.865, -1.5258789e-005];
};
 
_vehicle_255 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9403.1328, 11582.244, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_255 = _this;
  _this setDir -55.805168;
  _this setPos [9403.1328, 11582.244, -4.5776367e-005];
};
 
_vehicle_256 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9400.127, 11577.627, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_256 = _this;
  _this setDir -56.577877;
  _this setPos [9400.127, 11577.627, 1.5258789e-005];
};
 
_vehicle_257 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9383.6123, 11543.476, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_257 = _this;
  _this setDir -73.663414;
  _this setPos [9383.6123, 11543.476, -3.0517578e-005];
};
 
_vehicle_258 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9397.3252, 11572.822, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_258 = _this;
  _this setDir -59.152363;
  _this setPos [9397.3252, 11572.822, -4.5776367e-005];
};
 
_vehicle_259 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9394.9316, 11568.55, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_259 = _this;
  _this setDir -61.155643;
  _this setPos [9394.9316, 11568.55, -4.5776367e-005];
};
 
_vehicle_260 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9392.457, 11563.953, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_260 = _this;
  _this setDir -62.368065;
  _this setPos [9392.457, 11563.953, -1.5258789e-005];
};
 
_vehicle_261 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9389.8086, 11558.821, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_261 = _this;
  _this setDir -62.796314;
  _this setPos [9389.8086, 11558.821, 3.0517578e-005];
};
 
_vehicle_262 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9387.4629, 11553.862, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_262 = _this;
  _this setDir -64.18;
  _this setPos [9387.4629, 11553.862, -1.5258789e-005];
};
 
_vehicle_263 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9385.3076, 11548.68, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_263 = _this;
  _this setDir -67.296539;
  _this setPos [9385.3076, 11548.68, 1.5258789e-005];
};
 
_vehicle_264 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9397.2861, 11535.589], [], 0, "CAN_COLLIDE"];
  _vehicle_264 = _this;
  _this setDir -91.79644;
  _this setPos [9397.2861, 11535.589];
};
 
_vehicle_265 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9397.7725, 11529.908, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_265 = _this;
  _this setDir -93.887665;
  _this setPos [9397.7725, 11529.908, 1.5258789e-005];
};
 
_vehicle_266 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9398.8105, 11524.301, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_266 = _this;
  _this setDir -99.888985;
  _this setPos [9398.8105, 11524.301, 1.5258789e-005];
};
 
_vehicle_267 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9400.4668, 11518.895, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_267 = _this;
  _this setDir -109.93429;
  _this setPos [9400.4668, 11518.895, -1.5258789e-005];
};
 
_vehicle_268 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9400.9492, 11517.914, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_268 = _this;
  _this setDir 64.935097;
  _this setPos [9400.9492, 11517.914, -1.5258789e-005];
};
 
_vehicle_269 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9403.3682, 11512.833, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_269 = _this;
  _this setDir 63.7789;
  _this setPos [9403.3682, 11512.833, 1.5258789e-005];
};
 
_vehicle_270 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9409.7402, 11501.629, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_270 = _this;
  _this setDir 59.386272;
  _this setPos [9409.7402, 11501.629, 6.1035156e-005];
};
 
_vehicle_271 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9390.1494, 11539.094, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_271 = _this;
  _this setDir -81.157204;
  _this setPos [9390.1494, 11539.094, 1.5258789e-005];
};
 
_vehicle_272 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9389.7412, 11533.586, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_272 = _this;
  _this setDir -87.329033;
  _this setPos [9389.7412, 11533.586, -1.5258789e-005];
};
 
_vehicle_273 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9390.2734, 11528.217, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_273 = _this;
  _this setDir -94.859993;
  _this setPos [9390.2734, 11528.217, -1.5258789e-005];
};
 
_vehicle_274 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9391.0059, 11522.759, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_274 = _this;
  _this setDir -97.666908;
  _this setPos [9391.0059, 11522.759, -6.1035156e-005];
};
 
_vehicle_275 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9392.3994, 11517.438, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_275 = _this;
  _this setDir -104.53139;
  _this setPos [9392.3994, 11517.438, 1.5258789e-005];
};
 
_vehicle_276 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9394.4043, 11512.618, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_276 = _this;
  _this setDir -113.02122;
  _this setPos [9394.4043, 11512.618, 3.0517578e-005];
};
 
_vehicle_277 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9398.6289, 11504.941, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_277 = _this;
  _this setDir 60.328239;
  _this setPos [9398.6289, 11504.941, -6.1035156e-005];
};
 
_vehicle_278 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9394.7676, 11512.027, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_278 = _this;
  _this setDir 63.715534;
  _this setPos [9394.7676, 11512.027, -1.5258789e-005];
};
 
_vehicle_279 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9383.3887, 11542.69, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_279 = _this;
  _this setDir 101.89218;
  _this setPos [9383.3887, 11542.69, -4.5776367e-005];
};
 
_vehicle_280 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower", [9376.7471, 11536.552, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_280 = _this;
  _this setDir 81.819366;
  _this setPos [9376.7471, 11536.552, -4.5776367e-005];
};
 
_vehicle_281 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower", [9389.1289, 11486.503, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_281 = _this;
  _this setDir -35.07748;
  _this setPos [9389.1289, 11486.503, -1.5258789e-005];
};
 
_vehicle_282 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9365.8984, 11532.411, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_282 = _this;
  _this setDir -9.6825628;
  _this setPos [9365.8984, 11532.411, 4.5776367e-005];
};
 
_vehicle_283 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9350.7461, 11529.835, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_283 = _this;
  _this setDir -10.49176;
  _this setPos [9350.7461, 11529.835, -6.1035156e-005];
};
 
_vehicle_284 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9335.4834, 11526.831, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_284 = _this;
  _this setDir -11.049845;
  _this setPos [9335.4834, 11526.831, 3.0517578e-005];
};
 
_vehicle_285 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9321.5166, 11524.141], [], 0, "CAN_COLLIDE"];
  _vehicle_285 = _this;
  _this setDir -11.199886;
  _this setPos [9321.5166, 11524.141];
};
 
_vehicle_286 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9381.5264, 11489.209, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_286 = _this;
  _this setDir -16.232273;
  _this setPos [9381.5264, 11489.209, -3.0517578e-005];
};
 
_vehicle_287 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9367.001, 11484.789, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_287 = _this;
  _this setDir -18.221695;
  _this setPos [9367.001, 11484.789, 7.6293945e-005];
};
 
_vehicle_288 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9352.0713, 11479.971, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_288 = _this;
  _this setDir -17.690866;
  _this setPos [9352.0713, 11479.971, 6.1035156e-005];
};
 
_vehicle_289 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9337.7002, 11475.564, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_289 = _this;
  _this setDir -18.095726;
  _this setPos [9337.7002, 11475.564, -1.5258789e-005];
};
 
_vehicle_290 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9308.9092, 11522.848, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_290 = _this;
  _this setDir 168.76942;
  _this setPos [9308.9092, 11522.848, 4.5776367e-005];
};
 
_vehicle_291 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9316.2744, 11488.172, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_291 = _this;
  _this setDir 64.112572;
  _this setPos [9316.2744, 11488.172, -4.5776367e-005];
};
 
_vehicle_292 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9310.2666, 11500.896, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_292 = _this;
  _this setDir 65.880806;
  _this setPos [9310.2666, 11500.896, 1.5258789e-005];
};
 
_vehicle_293 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9304.6455, 11514.051, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_293 = _this;
  _this setDir 67.542473;
  _this setPos [9304.6455, 11514.051, 1.5258789e-005];
};
 
_vehicle_294 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9321.5029, 11477.901, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_294 = _this;
  _this setDir 63.008366;
  _this setPos [9321.5029, 11477.901, 4.5776367e-005];
};
 
_vehicle_295 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9331.5391, 11473.603], [], 0, "CAN_COLLIDE"];
  _vehicle_295 = _this;
  _this setDir -18.600103;
  _this setPos [9331.5391, 11473.603];
};
 
_vehicle_296 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier5x", [9435.96, 11596.213, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_296 = _this;
  _this setDir 48.551132;
  _this setPos [9435.96, 11596.213, -1.5258789e-005];
};
 
_vehicle_297 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [9396.4775, 11508.497, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_297 = _this;
  _this setDir 59.441471;
  _this setPos [9396.4775, 11508.497, -1.5258789e-005];
};
 
_vehicle_298 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9414.4492, 11478.208, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_298 = _this;
  _this setDir 57.976696;
  _this setPos [9414.4492, 11478.208, -9.1552734e-005];
};
 
_vehicle_299 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9422.667, 11465.003, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_299 = _this;
  _this setDir 57.663601;
  _this setPos [9422.667, 11465.003, -3.0517578e-005];
};
 
_vehicle_300 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10x", [9425.9365, 11459.829, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_300 = _this;
  _this setDir 58.065174;
  _this setPos [9425.9365, 11459.829, -6.1035156e-005];
};
 
_vehicle_301 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9435.8311, 11451.571], [], 0, "CAN_COLLIDE"];
  _vehicle_301 = _this;
  _this setDir 18.662239;
  _this setPos [9435.8311, 11451.571];
};
 
_vehicle_302 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9433.4629, 11452.339, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_302 = _this;
  _this setDir 16.980091;
  _this setPos [9433.4629, 11452.339, 3.0517578e-005];
};
 
_vehicle_303 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9431.0771, 11453.033], [], 0, "CAN_COLLIDE"];
  _vehicle_303 = _this;
  _this setDir 14.154952;
  _this setPos [9431.0771, 11453.033];
};
 
_vehicle_304 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9413.1729, 11435.344, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_304 = _this;
  _this setDir 152.6411;
  _this setPos [9413.1729, 11435.344, 3.0517578e-005];
};
 
_vehicle_305 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9414.5088, 11436.301, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_305 = _this;
  _this setDir 148.34383;
  _this setPos [9414.5088, 11436.301, 4.5776367e-005];
};
 
_vehicle_306 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9416.0957, 11437.06, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_306 = _this;
  _this setDir 150.20146;
  _this setPos [9416.0957, 11437.06, 1.5258789e-005];
};
 
_vehicle_307 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [9433.6289, 11593.74, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_307 = _this;
  _this setPos [9433.6289, 11593.74, -1.5258789e-005];
};
 
_vehicle_308 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [9397.9619, 11500.853, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_308 = _this;
  _this setDir -29.555843;
  _this setPos [9397.9619, 11500.853, 3.0517578e-005];
};
 
_vehicle_309 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [9326.793, 11475.533, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_309 = _this;
  _this setDir -18.113312;
  _this setPos [9326.793, 11475.533, 4.5776367e-005];
};
 
_vehicle_310 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [9307.3945, 11518.724, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_310 = _this;
  _this setDir -17.667664;
  _this setPos [9307.3945, 11518.724, -1.5258789e-005];
};
 
_vehicle_311 = objNull;
if (true) then
{
  _this = createVehicle ["PowerGenerator_EP1", [9306.3389, 11516.372, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_311 = _this;
  _this setDir -21.320955;
  _this setPos [9306.3389, 11516.372, 4.5776367e-005];
};
 
_vehicle_312 = objNull;
if (true) then
{
  _this = createVehicle ["PowerGenerator_EP1", [9324.5098, 11477.293, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_312 = _this;
  _this setDir -21.602596;
  _this setPos [9324.5098, 11477.293, 1.5258789e-005];
};
 
_vehicle_313 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [9388.084, 11493.274, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_313 = _this;
  _this setDir 147.92108;
  _this setPos [9388.084, 11493.274, 1.5258789e-005];
};
 
_vehicle_315 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9326.0146, 11482.285, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_315 = _this;
  _this setDir -114.25172;
  _this setPos [9326.0146, 11482.285, 7.6293945e-005];
};
 
_vehicle_317 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9322.4961, 11489.261, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_317 = _this;
  _this setDir -115.745;
  _this setPos [9322.4961, 11489.261, 4.5776367e-005];
};
 
_vehicle_318 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9319.0244, 11496.615, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_318 = _this;
  _this setDir -115.5284;
  _this setPos [9319.0244, 11496.615, 9.1552734e-005];
};
 
_vehicle_319 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9315.4727, 11504.607], [], 0, "CAN_COLLIDE"];
  _vehicle_319 = _this;
  _this setDir -113.20567;
  _this setPos [9315.4727, 11504.607];
};
 
_vehicle_320 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9312.0986, 11512.573, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_320 = _this;
  _this setDir -112.53227;
  _this setPos [9312.0986, 11512.573, 1.5258789e-005];
};
 
_vehicle_321 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9338.8496, 11481.986, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_321 = _this;
  _this setDir 162.60709;
  _this setPos [9338.8496, 11481.986, 3.0517578e-005];
};
 
_vehicle_322 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9347.1797, 11484.485, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_322 = _this;
  _this setDir 162.54802;
  _this setPos [9347.1797, 11484.485, -6.1035156e-005];
};
 
_vehicle_323 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9354.6836, 11486.849], [], 0, "CAN_COLLIDE"];
  _vehicle_323 = _this;
  _this setDir 163.93297;
  _this setPos [9354.6836, 11486.849];
};
 
_vehicle_324 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9362.3496, 11489.332, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_324 = _this;
  _this setDir 164.62816;
  _this setPos [9362.3496, 11489.332, 1.5258789e-005];
};
 
_vehicle_325 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9370.6318, 11492.153, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_325 = _this;
  _this setDir 164.11862;
  _this setPos [9370.6318, 11492.153, -4.5776367e-005];
};
 
_vehicle_326 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9321.6709, 11519.421, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_326 = _this;
  _this setDir -11.853987;
  _this setPos [9321.6709, 11519.421, -7.6293945e-005];
};
 
_vehicle_327 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9331.25, 11521.366, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_327 = _this;
  _this setDir -11.9978;
  _this setPos [9331.25, 11521.366, 3.0517578e-005];
};
 
_vehicle_328 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9340.084, 11523.148, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_328 = _this;
  _this setDir -11.408981;
  _this setPos [9340.084, 11523.148, 1.5258789e-005];
};
 
_vehicle_329 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9349.3213, 11524.803, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_329 = _this;
  _this setDir -10.783173;
  _this setPos [9349.3213, 11524.803, 3.0517578e-005];
};
 
_vehicle_330 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9358.3691, 11526.43], [], 0, "CAN_COLLIDE"];
  _vehicle_330 = _this;
  _this setDir -11.272575;
  _this setPos [9358.3691, 11526.43];
};
 
_vehicle_331 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9367.8486, 11528.157, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_331 = _this;
  _this setDir -12.039993;
  _this setPos [9367.8486, 11528.157, 3.0517578e-005];
};
 
_vehicle_332 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9379.6133, 11494.44, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_332 = _this;
  _this setDir 163.6965;
  _this setPos [9379.6133, 11494.44, -1.5258789e-005];
};
 
_vehicle_333 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNetVar_EAST", [9336.4121, 11496.645], [], 0, "CAN_COLLIDE"];
  _vehicle_333 = _this;
  _this setDir -17.348694;
  _this setPos [9336.4121, 11496.645];
};
 
_vehicle_334 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNetVar_EAST", [9334.2041, 11505.011, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_334 = _this;
  _this setDir -17.978868;
  _this setPos [9334.2041, 11505.011, 4.5776367e-005];
};
 
_vehicle_335 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNetVar_EAST", [9347.3301, 11500.15, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_335 = _this;
  _this setDir -16.63183;
  _this setPos [9347.3301, 11500.15, -7.6293945e-005];
};
 
_vehicle_336 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNetVar_EAST", [9345.1582, 11508.395, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_336 = _this;
  _this setDir -17.383831;
  _this setPos [9345.1582, 11508.395, 4.5776367e-005];
};
 
_vehicle_337 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNetVar_EAST", [9359.9053, 11503.739, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_337 = _this;
  _this setDir -15.60578;
  _this setPos [9359.9053, 11503.739, -6.1035156e-005];
};
 
_vehicle_338 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNetVar_EAST", [9357.5479, 11512.086], [], 0, "CAN_COLLIDE"];
  _vehicle_338 = _this;
  _this setDir -13.694502;
  _this setPos [9357.5479, 11512.086];
};
 
_vehicle_339 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNetVar_EAST", [9371.8145, 11507.538, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_339 = _this;
  _this setDir -16.271606;
  _this setPos [9371.8145, 11507.538, 3.0517578e-005];
};
 
_vehicle_340 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNetVar_EAST", [9369.6211, 11515.514, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_340 = _this;
  _this setDir -15.447039;
  _this setPos [9369.6211, 11515.514, 1.5258789e-005];
};
 
_vehicle_341 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net3", [9346.1641, 11498.989, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_341 = _this;
  _this setDir -12.742958;
  _this setPos [9346.1641, 11498.989, 4.5776367e-005];
};
 
_vehicle_342 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net3", [9343.8877, 11510.088, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_342 = _this;
  _this setDir -16.502785;
  _this setPos [9343.8877, 11510.088, -1.5258789e-005];
};
 
_vehicle_344 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9339.7676, 11512.057, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_344 = _this;
  _this setDir -198.39896;
  _this setPos [9339.7676, 11512.057, 3.0517578e-005];
};
 
_vehicle_345 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9343.4043, 11496.415, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_345 = _this;
  _this setDir 341.28738;
  _this setPos [9343.4043, 11496.415, 4.5776367e-005];
};
 
_vehicle_346 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1Bo_civil", [9333.1455, 11504.161, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_346 = _this;
  _this setPos [9333.1455, 11504.161, 1.5258789e-005];
};
 
_vehicle_347 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1Bo_civil", [9332.7314, 11496.57, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_347 = _this;
  _this setDir 19.384686;
  _this setPos [9332.7314, 11496.57, 1.5258789e-005];
};
 
_vehicle_348 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [9351.9385, 11500.009, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_348 = _this;
  _this setDir 76.149033;
  _this setPos [9351.9385, 11500.009, 3.0517578e-005];
};
 
_vehicle_349 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [9346.7979, 11514.283, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_349 = _this;
  _this setDir -101.62819;
  _this setPos [9346.7979, 11514.283, 1.5258789e-005];
};
 
_vehicle_350 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Checkpoint", [9463.248, 11650.129, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_350 = _this;
  _this setDir -149.98743;
  _this setPos [9463.248, 11650.129, -1.5258789e-005];
};
 
_vehicle_351 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_1L_Firstaid", [9449.2539, 11638.506, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_351 = _this;
  _this setDir -152.40059;
  _this setPos [9449.2539, 11638.506, -1.5258789e-005];
};
 
_vehicle_352 = objNull;
if (true) then
{
  _this = createVehicle ["SignM_FOB_Prostejov_EP1", [9446.2461, 11650.685, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_352 = _this;
  _this setDir -117.45872;
  _this setPos [9446.2461, 11650.685, -3.0517578e-005];
};
 
_vehicle_355 = objNull;
if (true) then
{
  _this = createVehicle ["Land_RedWhiteBarrier", [9449.2588, 11641.901, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_355 = _this;
  _this setDir 78.537186;
  _this setPos [9449.2588, 11641.901, -1.5258789e-005];
};
 
_vehicle_356 = objNull;
if (true) then
{
  _this = createVehicle ["Land_RedWhiteBarrier", [9452.21, 11646.544, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_356 = _this;
  _this setDir 74.984978;
  _this setPos [9452.21, 11646.544, 3.0517578e-005];
};
 
_vehicle_357 = objNull;
if (true) then
{
  _this = createVehicle ["Land_RedWhiteBarrier", [9454.8086, 11650.907, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_357 = _this;
  _this setDir 74.992416;
  _this setPos [9454.8086, 11650.907, -9.1552734e-005];
};
 
_vehicle_358 = objNull;
if (true) then
{
  _this = createVehicle ["Land_RedWhiteBarrier", [9462.8574, 11646.018, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_358 = _this;
  _this setDir -8.137373;
  _this setPos [9462.8574, 11646.018, -6.1035156e-005];
};
 
_vehicle_359 = objNull;
if (true) then
{
  _this = createVehicle ["Land_RedWhiteBarrier", [9460.3379, 11641.586, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_359 = _this;
  _this setDir -9.3977728;
  _this setPos [9460.3379, 11641.586, -1.5258789e-005];
};
 
_vehicle_360 = objNull;
if (true) then
{
  _this = createVehicle ["Land_RedWhiteBarrier", [9457.8584, 11637.301, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_360 = _this;
  _this setDir -4.7254944;
  _this setPos [9457.8584, 11637.301, 7.6293945e-005];
};
 
_vehicle_361 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_yellow_R", [9440.3438, 11618.913, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_361 = _this;
  _this setDir -148.23775;
  _this setPos [9440.3438, 11618.913, -4.5776367e-005];
};
 
_vehicle_362 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_tiny", [9373.8613, 11519.295, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_362 = _this;
  _this setDir -11.459538;
  _this setPos [9373.8613, 11519.295, 1.5258789e-005];
};
 
_vehicle_363 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_tiny", [9376.7637, 11510.363, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_363 = _this;
  _this setDir -15.362884;
  _this setPos [9376.7637, 11510.363, -1.5258789e-005];
};
 
_vehicle_364 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net1", [9374.2646, 11515.931, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_364 = _this;
  _this setPos [9374.2646, 11515.931, -3.0517578e-005];
};
 
_vehicle_365 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net1", [9374.6992, 11507.183, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_365 = _this;
  _this setPos [9374.6992, 11507.183, 1.5258789e-005];
};
 
_vehicle_368 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9396.8096, 11503.641, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_368 = _this;
  _this setDir 58.437733;
  _this setPos [9396.8096, 11503.641, -3.0517578e-005];
};
 
_vehicle_369 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9395.8096, 11505.352, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_369 = _this;
  _this setDir 61.765213;
  _this setPos [9395.8096, 11505.352, 1.5258789e-005];
};
 
_vehicle_370 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9468.9336, 11550.343, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_370 = _this;
  _this setPos [9468.9336, 11550.343, -3.0517578e-005];
};
 
_vehicle_371 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9470.4893, 11550.354, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_371 = _this;
  _this setPos [9470.4893, 11550.354, -4.5776367e-005];
};
 
_vehicle_372 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9435.543, 11592.467, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_372 = _this;
  _this setDir 23.81554;
  _this setPos [9435.543, 11592.467, -0.00015258789];
};
 
_vehicle_373 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9436.4023, 11591.181, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_373 = _this;
  _this setDir 61.419167;
  _this setPos [9436.4023, 11591.181, -4.5776367e-005];
};
 
_vehicle_374 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9422.9727, 11599.387, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_374 = _this;
  _this setDir 40.075443;
  _this setPos [9422.9727, 11599.387, 4.5776367e-005];
};
 
_vehicle_375 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9441.1895, 11630.226, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_375 = _this;
  _this setDir 28.744761;
  _this setPos [9441.1895, 11630.226, -6.1035156e-005];
};
 
_vehicle_376 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9442.3799, 11629.446, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_376 = _this;
  _this setDir 30.369581;
  _this setPos [9442.3799, 11629.446, -7.6293945e-005];
};
 
_vehicle_377 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_palletsfoiled_heap", [9366.8525, 11515.935, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_377 = _this;
  _this setPos [9366.8525, 11515.935, -7.6293945e-005];
};
 
_vehicle_378 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_palletsfoiled", [9373.3076, 11509.705, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_378 = _this;
  _this setPos [9373.3076, 11509.705, -3.0517578e-005];
};
 
_vehicle_379 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_concrete_High", [9353.9902, 11512.815, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_379 = _this;
  _this setPos [9353.9902, 11512.815, 0.00010681152];
};
 
_vehicle_381 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_BoardsPack1", [9351.7441, 11507.886], [], 0, "CAN_COLLIDE"];
  _vehicle_381 = _this;
  _this setPos [9351.7441, 11507.886];
};
 
_vehicle_382 = objNull;
if (true) then
{
  _this = createVehicle ["Barrels", [9360.3555, 11507.953, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_382 = _this;
  _this setPos [9360.3555, 11507.953, 3.0517578e-005];
};
 
_vehicle_383 = objNull;
if (true) then
{
  _this = createVehicle ["Barrels", [9359.3887, 11510.857, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_383 = _this;
  _this setPos [9359.3887, 11510.857, -1.5258789e-005];
};
 
_vehicle_384 = objNull;
if (true) then
{
  _this = createVehicle ["Barrels", [9365.7363, 11509.849, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_384 = _this;
  _this setPos [9365.7363, 11509.849, 1.5258789e-005];
};
 
_vehicle_385 = objNull;
if (true) then
{
  _this = createVehicle ["Barrels", [9370.7334, 11514.126, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_385 = _this;
  _this setPos [9370.7334, 11514.126, 3.0517578e-005];
};
 
_vehicle_387 = objNull;
if (true) then
{
  _this = createVehicle ["Bleacher_EP1", [9374.6523, 11526.438, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_387 = _this;
  _this setDir -13.744545;
  _this setPos [9374.6523, 11526.438, 3.0517578e-005];
};
 
_vehicle_388 = objNull;
if (true) then
{
  _this = createVehicle ["Bleacher_EP1", [9378.5977, 11527.47, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_388 = _this;
  _this setDir -9.7505655;
  _this setPos [9378.5977, 11527.47, -6.1035156e-005];
};
 
_vehicle_389 = objNull;
if (true) then
{
  _this = createVehicle ["Bleacher_EP1", [9392.7188, 11507.393, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_389 = _this;
  _this setDir 64.830353;
  _this setPos [9392.7188, 11507.393, 1.5258789e-005];
};
 
_vehicle_390 = objNull;
if (true) then
{
  _this = createVehicle ["Bleacher_EP1", [9391.1016, 11510.901, -0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_390 = _this;
  _this setDir 65.50853;
  _this setPos [9391.1016, 11510.901, -0.00022888184];
};
 
_vehicle_391 = objNull;
if (true) then
{
  _this = createVehicle ["Bleacher_EP1", [9329.5986, 11504.208, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_391 = _this;
  _this setDir 91.688347;
  _this setPos [9329.5986, 11504.208, -7.6293945e-005];
};
 
_vehicle_392 = objNull;
if (true) then
{
  _this = createVehicle ["Land_bags_stack_EP1", [9365.3975, 11507.435, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_392 = _this;
  _this setPos [9365.3975, 11507.435, -3.0517578e-005];
};
 
_vehicle_393 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Carpet_rack_EP1", [9356.6699, 11500.896, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_393 = _this;
  _this setPos [9356.6699, 11500.896, 3.0517578e-005];
};
 
_vehicle_395 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Crates_stack_EP1", [9339.9219, 11495.844, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_395 = _this;
  _this setPos [9339.9219, 11495.844, -7.6293945e-005];
};
 
_vehicle_396 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_kiosk_EP1", [9341.5859, 11497.531, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_396 = _this;
  _this setDir 69.14325;
  _this setPos [9341.5859, 11497.531, 1.5258789e-005];
};
 
_vehicle_397 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_meat_EP1", [9389.877, 11517.169, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_397 = _this;
  _this setDir 69.319275;
  _this setPos [9389.877, 11517.169, -1.5258789e-005];
};
 
_vehicle_398 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_meat_EP1", [9389.332, 11518.522, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_398 = _this;
  _this setDir 66.586609;
  _this setPos [9389.332, 11518.522, -3.0517578e-005];
};
 
_vehicle_399 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_meat_EP1", [9388.915, 11519.803, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_399 = _this;
  _this setDir 69.119583;
  _this setPos [9388.915, 11519.803, 1.5258789e-005];
};
 
_vehicle_400 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_meat_EP1", [9388.2949, 11521.132], [], 0, "CAN_COLLIDE"];
  _vehicle_400 = _this;
  _this setDir 70.918221;
  _this setPos [9388.2949, 11521.132];
};
 
_vehicle_401 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_waterl_EP1", [9375.6289, 11531.737, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_401 = _this;
  _this setDir -100.17766;
  _this setPos [9375.6289, 11531.737, -3.0517578e-005];
};
 
_vehicle_402 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_ConcBox_EP1", [9394.4395, 11499.43, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_402 = _this;
  _this setDir 149.99603;
  _this setPos [9394.4395, 11499.43, 1.5258789e-005];
};
 
_vehicle_404 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_TyreHeapEP1", [9378.1045, 11519.084, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_404 = _this;
  _this setPos [9378.1045, 11519.084, 1.5258789e-005];
};
 
_vehicle_405 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Coil_EP1", [9379.2334, 11515.057, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_405 = _this;
  _this setPos [9379.2334, 11515.057, -1.5258789e-005];
};
 
_vehicle_406 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_Crate_wood", [9379.8926, 11508.467, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_406 = _this;
  _this setDir -15.733382;
  _this setPos [9379.8926, 11508.467, -3.0517578e-005];
};
 
_vehicle_407 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Crates_stack_EP1", [9457.0996, 11509.516, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_407 = _this;
  _this setPos [9457.0996, 11509.516, 3.0517578e-005];
};
 
_vehicle_410 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_TankSmall2_EP1", [9433.209, 11508.976, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_410 = _this;
  _this setDir 6.0493193;
  _this setPos [9433.209, 11508.976, 4.5776367e-005];
};
 
_vehicle_411 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_TankSmall2_EP1", [9438.9785, 11508.367, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_411 = _this;
  _this setDir 6.4691639;
  _this setPos [9438.9785, 11508.367, 1.5258789e-005];
};
 
_vehicle_412 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_TankSmall2_EP1", [9444.5596, 11507.789, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_412 = _this;
  _this setDir 5.7885332;
  _this setPos [9444.5596, 11507.789, -3.0517578e-005];
};
 
_vehicle_413 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_TankSmall2_EP1", [9450.2021, 11507.209, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_413 = _this;
  _this setDir 6.919044;
  _this setPos [9450.2021, 11507.209, -6.1035156e-005];
};
 
_vehicle_414 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fuel_tank_big", [9436.7744, 11511.543, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_414 = _this;
  _this setDir 5.7230468;
  _this setPos [9436.7744, 11511.543, -1.5258789e-005];
};
 
_vehicle_415 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fuel_tank_big", [9445.5752, 11510.612], [], 0, "CAN_COLLIDE"];
  _vehicle_415 = _this;
  _this setDir 5.6237459;
  _this setPos [9445.5752, 11510.612];
};
 
_vehicle_416 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fuel_tank_big", [9454.5898, 11509.66, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_416 = _this;
  _this setDir 6.2323918;
  _this setPos [9454.5898, 11509.66, -1.5258789e-005];
};
 
_vehicle_417 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fuel_tank_small", [9429.2061, 11513.932, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_417 = _this;
  _this setDir -84.665924;
  _this setPos [9429.2061, 11513.932, 1.5258789e-005];
};
 
_vehicle_418 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fuel_tank_small", [9432.4658, 11513.597, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_418 = _this;
  _this setDir -83.483032;
  _this setPos [9432.4658, 11513.597, -1.5258789e-005];
};
 
_vehicle_419 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fuel_tank_small", [9435.7539, 11513.239, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_419 = _this;
  _this setDir -83.120262;
  _this setPos [9435.7539, 11513.239, -3.0517578e-005];
};
 
_vehicle_420 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fuel_tank_small", [9439.0527, 11512.867, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_420 = _this;
  _this setDir -83.711731;
  _this setPos [9439.0527, 11512.867, -1.5258789e-005];
};
 
_vehicle_421 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fuel_tank_small", [9441.8018, 11512.574, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_421 = _this;
  _this setDir -82.49054;
  _this setPos [9441.8018, 11512.574, -1.5258789e-005];
};
 
_vehicle_422 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fuel_tank_small", [9440.1289, 11550.762], [], 0, "CAN_COLLIDE"];
  _vehicle_422 = _this;
  _this setDir -180.38431;
  _this setPos [9440.1289, 11550.762];
};
 
_vehicle_423 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fuel_tank_small", [9329.8037, 11476.473, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_423 = _this;
  _this setDir -110.05101;
  _this setPos [9329.8037, 11476.473, 0.00010681152];
};
 
_vehicle_424 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fuel_tank_small", [9310.2949, 11519.653, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_424 = _this;
  _this setDir 164.68755;
  _this setPos [9310.2949, 11519.653, 1.5258789e-005];
};
 
_vehicle_425 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fuel_tank_small", [9430.5684, 11591.763, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_425 = _this;
  _this setDir 130.02042;
  _this setPos [9430.5684, 11591.763, 6.1035156e-005];
};
 
_vehicle_426 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fuel_tank_small", [9421.3057, 11434.167, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_426 = _this;
  _this setDir 60.191059;
  _this setPos [9421.3057, 11434.167, -6.1035156e-005];
};
 
_vehicle_427 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fuel_tank_small", [9422.5879, 11431.955, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_427 = _this;
  _this setDir 58.880154;
  _this setPos [9422.5879, 11431.955, 6.1035156e-005];
};
 
_vehicle_428 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_BoardsPack2", [9337.5723, 11505.81, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_428 = _this;
  _this setPos [9337.5723, 11505.81, -1.5258789e-005];
};
 
_vehicle_429 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2_noRoad", [9357.2598, 11521.917], [], 0, "CAN_COLLIDE"];
  _vehicle_429 = _this;
  _this setDir -191.0041;
  _this setPos [9357.2598, 11521.917];
};
 
_vehicle_430 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2_noRoad", [9366.7939, 11523.607, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_430 = _this;
  _this setDir 169.11646;
  _this setPos [9366.7939, 11523.607, -1.5258789e-005];
};
 
_vehicle_433 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo2D", [9465.0352, 11475.926, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_433 = _this;
  _this setDir 5.2818208;
  _this setPos [9465.0352, 11475.926, 3.0517578e-005];
};
 
_vehicle_434 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo2C", [9468.333, 11475.418, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_434 = _this;
  _this setDir 6.0281324;
  _this setPos [9468.333, 11475.418, 1.5258789e-005];
};
 
_vehicle_435 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo2B", [9472.4063, 11474.53], [], 0, "CAN_COLLIDE"];
  _vehicle_435 = _this;
  _this setDir 10.820588;
  _this setPos [9472.4063, 11474.53];
};
 
_vehicle_442 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [9536.6064, 11496.082, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_442 = _this;
  _this setDir 248.60985;
  _this setPos [9536.6064, 11496.082, -9.1552734e-005];
};
 
_vehicle_443 = objNull;
if (true) then
{
  _this = createVehicle ["Notice_board", [9421.1377, 11440.063], [], 0, "CAN_COLLIDE"];
  _vehicle_443 = _this;
  _this setDir 142.18831;
  _this setPos [9421.1377, 11440.063];
};
 
_vehicle_444 = objNull;
if (true) then
{
  _this = createVehicle ["PowerGenerator", [9426.8682, 11442.937, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_444 = _this;
  _this setDir 52.74881;
  _this setPos [9426.8682, 11442.937, 0.00015258789];
};
 
_vehicle_445 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [9408.4023, 11433.703, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_445 = _this;
  _this setDir -126.45805;
  _this setPos [9408.4023, 11433.703, 1.5258789e-005];
};
 
_vehicle_447 = objNull;
if (true) then
{
  _this = createVehicle ["Loudspeakers_EP1", [9425.2354, 11444.019, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_447 = _this;
  _this setPos [9425.2354, 11444.019, 6.1035156e-005];
};
 
_vehicle_449 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [9403.1445, 11443.378, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_449 = _this;
  _this setDir -34.880913;
  _this setPos [9403.1445, 11443.378, 3.0517578e-005];
};
 
_vehicle_450 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [9398.6699, 11449.98, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_450 = _this;
  _this setDir -33.732998;
  _this setPos [9398.6699, 11449.98, 6.1035156e-005];
};
 
_vehicle_451 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [9385.5293, 11468.674, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_451 = _this;
  _this setDir -36.909973;
  _this setPos [9385.5293, 11468.674, 4.5776367e-005];
};
 
_vehicle_452 = objNull;
if (true) then
{
  _this = createVehicle ["PowerGenerator", [9425.4883, 11514.147], [], 0, "CAN_COLLIDE"];
  _vehicle_452 = _this;
  _this setDir 6.5693979;
  _this setPos [9425.4883, 11514.147];
};
 
_vehicle_453 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Shed_wooden", [9423.2705, 11495.672, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_453 = _this;
  _this setDir -123.41735;
  _this setPos [9423.2705, 11495.672, -4.5776367e-005];
};
 
_vehicle_454 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Shed_wooden", [9426.5908, 11497.807], [], 0, "CAN_COLLIDE"];
  _vehicle_454 = _this;
  _this setDir -122.61421;
  _this setPos [9426.5908, 11497.807];
};
 
_vehicle_455 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Shed_wooden", [9429.7832, 11499.791, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_455 = _this;
  _this setDir -122.63516;
  _this setPos [9429.7832, 11499.791, 6.1035156e-005];
};
 
_vehicle_456 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Shed_wooden", [9433.2529, 11501.969, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_456 = _this;
  _this setDir -122.16252;
  _this setPos [9433.2529, 11501.969, -0.0001373291];
};
 
_vehicle_457 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [9422.4658, 11440.079, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_457 = _this;
  _this setPos [9422.4658, 11440.079, 0.00012207031];
};
 
_vehicle_461 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Danger", [9395.8086, 11458.435, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_461 = _this;
  _this setDir 58.147903;
  _this setPos [9395.8086, 11458.435, 1.5258789e-005];
};
 
_vehicle_462 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_1L_Firstaid", [9393.3936, 11462.005, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_462 = _this;
  _this setDir -127.56147;
  _this setPos [9393.3936, 11462.005, 3.0517578e-005];
};
 
_vehicle_463 = objNull;
if (true) then
{
  _this = createVehicle ["Info_Board_EP1", [9392.3965, 11462.296, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_463 = _this;
  _this setDir -119.96976;
  _this setPos [9392.3965, 11462.296, 3.0517578e-005];
};
 
_vehicle_464 = objNull;
if (true) then
{
  _this = createVehicle ["Info_Board_EP1", [9382.708, 11537.111, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_464 = _this;
  _this setDir -84.998962;
  _this setPos [9382.708, 11537.111, -1.5258789e-005];
};
 
_vehicle_466 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [9385.8047, 11529.299, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_466 = _this;
  _this setPos [9385.8047, 11529.299, 1.5258789e-005];
};
 
_vehicle_467 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [9386.7627, 11540.12, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_467 = _this;
  _this setDir 4.0413194;
  _this setPos [9386.7627, 11540.12, -1.5258789e-005];
};
 
_vehicle_468 = objNull;
if (true) then
{
  _this = createVehicle ["UAZWreck", [9388.625, 11547.902, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_468 = _this;
  _this setDir 13.62301;
  _this setPos [9388.625, 11547.902, -1.5258789e-005];
};
 
_vehicle_469 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [9391.1514, 11554.458], [], 0, "CAN_COLLIDE"];
  _vehicle_469 = _this;
  _this setDir 18.483486;
  _this setPos [9391.1514, 11554.458];
};
 
_vehicle_470 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [9395.8516, 11564.038], [], 0, "CAN_COLLIDE"];
  _vehicle_470 = _this;
  _this setDir 24.528206;
  _this setPos [9395.8516, 11564.038];
};
 
_vehicle_471 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [9400.5146, 11573.409, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_471 = _this;
  _this setDir 37.093819;
  _this setPos [9400.5146, 11573.409, -3.0517578e-005];
};
 
_vehicle_472 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [9405.8926, 11580.564], [], 0, "CAN_COLLIDE"];
  _vehicle_472 = _this;
  _this setPos [9405.8926, 11580.564];
};
 
_vehicle_473 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [9382.8076, 11520.553, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_473 = _this;
  _this setPos [9382.8076, 11520.553, 4.5776367e-005];
};
 
_vehicle_474 = objNull;
if (true) then
{
  _this = createVehicle ["BRDMWreck", [9411.1152, 11588.001, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_474 = _this;
  _this setDir 56.834667;
  _this setPos [9411.1152, 11588.001, 1.5258789e-005];
};
 
_vehicle_475 = objNull;
if (true) then
{
  _this = createVehicle ["BMP2Wreck", [9416.6367, 11594.567, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_475 = _this;
  _this setDir 46.382256;
  _this setPos [9416.6367, 11594.567, -1.5258789e-005];
};
 
_vehicle_476 = objNull;
if (true) then
{
  _this = createVehicle ["LADAWreck", [9420.5498, 11602.676], [], 0, "CAN_COLLIDE"];
  _vehicle_476 = _this;
  _this setPos [9420.5498, 11602.676];
};
 
_vehicle_477 = objNull;
if (true) then
{
  _this = createVehicle ["hiluxWreck", [9427.4258, 11609.112, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_477 = _this;
  _this setDir 80.403351;
  _this setPos [9427.4258, 11609.112, -1.5258789e-005];
};
 
_vehicle_478 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [9433.6855, 11615.674, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_478 = _this;
  _this setPos [9433.6855, 11615.674, 1.5258789e-005];
};
 
_vehicle_479 = objNull;
if (true) then
{
  _this = createVehicle ["LADAWreck", [9476.5586, 11679.473, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_479 = _this;
  _this setDir 29.969015;
  _this setPos [9476.5586, 11679.473, 3.0517578e-005];
};
 
_vehicle_480 = objNull;
if (true) then
{
  _this = createVehicle ["BRDMWreck", [9456.3828, 11657.947, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_480 = _this;
  _this setDir 57.884033;
  _this setPos [9456.3828, 11657.947, 3.0517578e-005];
};
 
_vehicle_481 = objNull;
if (true) then
{
  _this = createVehicle ["LADAWreck", [9463.1025, 11662.73, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_481 = _this;
  _this setDir 53.738903;
  _this setPos [9463.1025, 11662.73, -4.5776367e-005];
};
 
_vehicle_482 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [9468.0508, 11667.313, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_482 = _this;
  _this setDir 11.967374;
  _this setPos [9468.0508, 11667.313, 4.5776367e-005];
};
 
_vehicle_483 = objNull;
if (true) then
{
  _this = createVehicle ["hiluxWreck", [9472.4727, 11672.65, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_483 = _this;
  _this setDir 42.061348;
  _this setPos [9472.4727, 11672.65, -1.5258789e-005];
};
 
_vehicle_484 = objNull;
if (true) then
{
  _this = createVehicle ["datsun02Wreck", [9470.4814, 11663.16], [], 0, "CAN_COLLIDE"];
  _vehicle_484 = _this;
  _this setPos [9470.4814, 11663.16];
};
 
_vehicle_485 = objNull;
if (true) then
{
  _this = createVehicle ["SKODAWreck", [9471.9229, 11679.969, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_485 = _this;
  _this setDir 23.715944;
  _this setPos [9471.9229, 11679.969, -3.0517578e-005];
};
 
_vehicle_486 = objNull;
if (true) then
{
  _this = createVehicle ["UAZWreck", [9475.3965, 11683.325, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_486 = _this;
  _this setDir 34.99929;
  _this setPos [9475.3965, 11683.325, -7.6293945e-005];
};
 
_vehicle_487 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [9466.6768, 11672.145, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_487 = _this;
  _this setDir 30.411592;
  _this setPos [9466.6768, 11672.145, 1.5258789e-005];
};
 
_vehicle_488 = objNull;
if (true) then
{
  _this = createVehicle ["hiluxWreck", [9480.8535, 11690.604, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_488 = _this;
  _this setDir 37.622646;
  _this setPos [9480.8535, 11690.604, -7.6293945e-005];
};
 
_vehicle_489 = objNull;
if (true) then
{
  _this = createVehicle ["datsun01Wreck", [9480.6299, 11685.043, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_489 = _this;
  _this setDir 41.569031;
  _this setPos [9480.6299, 11685.043, -1.5258789e-005];
};
 
_vehicle_490 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BoatSmall_1", [9482.5215, 11687.528, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_490 = _this;
  _this setPos [9482.5215, 11687.528, -1.5258789e-005];
};
 
_vehicle_491 = objNull;
if (true) then
{
  _this = createVehicle ["T72Wreck", [9463.2324, 11624.229, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_491 = _this;
  _this setDir -119.38808;
  _this setPos [9463.2324, 11624.229, -4.5776367e-005];
};
 
_vehicle_492 = objNull;
if (true) then
{
  _this = createVehicle ["T72WreckTurret", [9463.8906, 11630.168, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_492 = _this;
  _this setDir 342.76041;
  _this setPos [9463.8906, 11630.168, 9.1552734e-005];
};
 
_vehicle_493 = objNull;
if (true) then
{
  _this = createVehicle ["Mi8Wreck", [9525.9932, 11651.596, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_493 = _this;
  _this setPos [9525.9932, 11651.596, 0];
};
 
_vehicle_494 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9523.0781, 11643.115, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_494 = _this;
  _this setPos [9523.0781, 11643.115, 1.5258789e-005];
};
 
_vehicle_495 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9520.3818, 11645.072, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_495 = _this;
  _this setPos [9520.3818, 11645.072, -1.5258789e-005];
};
 
_vehicle_496 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9529.2471, 11641.556, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_496 = _this;
  _this setPos [9529.2471, 11641.556, -1.5258789e-005];
};
 
_vehicle_497 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9482.2305, 11683.821, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_497 = _this;
  _this setPos [9482.2305, 11683.821, -9.1552734e-005];
};
 
_vehicle_498 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9476.7373, 11682.535, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_498 = _this;
  _this setPos [9476.7373, 11682.535, 7.6293945e-005];
};
 
_vehicle_499 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9476.0234, 11688.067, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_499 = _this;
  _this setPos [9476.0234, 11688.067, 1.5258789e-005];
};
 
_vehicle_500 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9471.71, 11670.835, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_500 = _this;
  _this setPos [9471.71, 11670.835, -6.1035156e-005];
};
 
_vehicle_501 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9470.6807, 11667.998, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_501 = _this;
  _this setPos [9470.6807, 11667.998, -9.1552734e-005];
};
 
_vehicle_502 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9463.5518, 11666.513, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_502 = _this;
  _this setPos [9463.5518, 11666.513, -7.6293945e-005];
};
 
_vehicle_503 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9462.3008, 11659.707, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_503 = _this;
  _this setPos [9462.3008, 11659.707, -9.1552734e-005];
};
 
_vehicle_504 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9469.3281, 11659.903, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_504 = _this;
  _this setPos [9469.3281, 11659.903, 0];
};
 
_vehicle_505 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9466.7412, 11659.635, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_505 = _this;
  _this setPos [9466.7412, 11659.635, -9.1552734e-005];
};
 
_vehicle_506 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9469.8379, 11670.849, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_506 = _this;
  _this setPos [9469.8379, 11670.849, 1.5258789e-005];
};
 
_vehicle_507 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9473.3535, 11677.979, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_507 = _this;
  _this setPos [9473.3535, 11677.979, -4.5776367e-005];
};
 
_vehicle_508 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9480.1602, 11682.011, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_508 = _this;
  _this setPos [9480.1602, 11682.011, 1.5258789e-005];
};
 
_vehicle_509 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9460.498, 11657.26, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_509 = _this;
  _this setPos [9460.498, 11657.26, 1.5258789e-005];
};
 
_vehicle_510 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9436.5576, 11615.931, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_510 = _this;
  _this setPos [9436.5576, 11615.931, -3.0517578e-005];
};
 
_vehicle_511 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9426.6914, 11611.518, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_511 = _this;
  _this setPos [9426.6914, 11611.518, -3.0517578e-005];
};
 
_vehicle_512 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9423.2979, 11604.581, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_512 = _this;
  _this setPos [9423.2979, 11604.581, 0];
};
 
_vehicle_513 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9385.3584, 11533.063, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_513 = _this;
  _this setPos [9385.3584, 11533.063, -1.5258789e-005];
};
 
_vehicle_514 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9381.4199, 11531.929, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_514 = _this;
  _this setPos [9381.4199, 11531.929, -7.6293945e-005];
};
 
_vehicle_515 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9385.7842, 11522.53, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_515 = _this;
  _this setPos [9385.7842, 11522.53, 0];
};
 
_vehicle_516 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9384.7627, 11523.163, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_516 = _this;
  _this setPos [9384.7627, 11523.163, 0];
};
 
_vehicle_517 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Feed", [9427.9395, 11534.707, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_517 = _this;
  _this setDir 91.229385;
  _this setPos [9427.9395, 11534.707, 3.0517578e-005];
};
 
_vehicle_518 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Feed", [9439.623, 11531.063, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_518 = _this;
  _this setDir -83.797302;
  _this setPos [9439.623, 11531.063, -1.5258789e-005];
};
 
_vehicle_519 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Feed", [9418.4678, 11488.657, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_519 = _this;
  _this setDir 55.9328;
  _this setPos [9418.4678, 11488.657, -6.1035156e-005];
};
 
_vehicle_524 = objNull;
if (true) then
{
  _this = createVehicle ["Land_pumpa", [9391.291, 11515.275, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_524 = _this;
  _this setDir -62.946785;
  _this setPos [9391.291, 11515.275, -0.00010681152];
};
 
_vehicle_528 = objNull;
if (true) then
{
  _this = createVehicle ["BRDMWreck", [9458.79, 11651.919, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_528 = _this;
  _this setDir 34.174862;
  _this setPos [9458.79, 11651.919, -7.6293945e-005];
};
 
_vehicle_529 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [9440.2051, 11622.556], [], 0, "CAN_COLLIDE"];
  _vehicle_529 = _this;
  _this setDir 38.261963;
  _this setPos [9440.2051, 11622.556];
};
 
_vehicle_530 = objNull;
if (true) then
{
  _this = createVehicle ["UH1Wreck", [9491.1309, 11673.445, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_530 = _this;
  _this setDir 2.084698;
  _this setPos [9491.1309, 11673.445, -9.1552734e-005];
};
 
_vehicle_531 = objNull;
if (true) then
{
  _this = createVehicle ["UH60_wreck_EP1", [9481.4902, 11575.169, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_531 = _this;
  _this setDir 30.032536;
  _this setPos [9481.4902, 11575.169, -6.1035156e-005];
};
 
_vehicle_532 = objNull;
if (true) then
{
  _this = createVehicle ["HeliHRescue", [9405.7969, 11462.864, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_532 = _this;
  _this setPos [9405.7969, 11462.864, -9.1552734e-005];
};
 
_vehicle_533 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9403.5518, 11417.617], [], 0, "CAN_COLLIDE"];
  _vehicle_533 = _this;
  _this setDir 55.446808;
  _this setPos [9403.5518, 11417.617];
};
 
_vehicle_534 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9411.9951, 11405.564, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_534 = _this;
  _this setDir 55.310585;
  _this setPos [9411.9951, 11405.564, 6.1035156e-005];
};
 
_vehicle_535 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9420.6738, 11393.037, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_535 = _this;
  _this setDir 55.528282;
  _this setPos [9420.6738, 11393.037, 7.6293945e-005];
};
 
_vehicle_536 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9429.2695, 11380.745, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_536 = _this;
  _this setDir 55.507328;
  _this setPos [9429.2695, 11380.745, 0.0001373291];
};
 
_vehicle_537 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9439.2959, 11379.755, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_537 = _this;
  _this setDir -38.677589;
  _this setPos [9439.2959, 11379.755, 6.1035156e-005];
};
 
_vehicle_538 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9451.4805, 11389.605], [], 0, "CAN_COLLIDE"];
  _vehicle_538 = _this;
  _this setDir -38.848099;
  _this setPos [9451.4805, 11389.605];
};
 
_vehicle_539 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9461.1318, 11397.272, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_539 = _this;
  _this setDir -39.324421;
  _this setPos [9461.1318, 11397.272, 4.5776367e-005];
};
 
_vehicle_540 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9479.3047, 11412.678, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_540 = _this;
  _this setDir -39.587399;
  _this setPos [9479.3047, 11412.678, 7.6293945e-005];
};
 
_vehicle_541 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9491.2783, 11422.67, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_541 = _this;
  _this setDir -40.055492;
  _this setPos [9491.2783, 11422.67, -6.1035156e-005];
};
 
_vehicle_542 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9503.2393, 11432.646], [], 0, "CAN_COLLIDE"];
  _vehicle_542 = _this;
  _this setDir -39.278271;
  _this setPos [9503.2393, 11432.646];
};
 
_vehicle_543 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9515.0996, 11442.415, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_543 = _this;
  _this setDir -39.165051;
  _this setPos [9515.0996, 11442.415, -0.00012207031];
};
 
_vehicle_544 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9526.4834, 11453.213, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_544 = _this;
  _this setDir 140.65855;
  _this setPos [9526.4834, 11453.213, 1.5258789e-005];
};
 
_vehicle_545 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9528.2119, 11462.473, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_545 = _this;
  _this setDir 56.533737;
  _this setPos [9528.2119, 11462.473, 1.5258789e-005];
};
 
_vehicle_546 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9517.2568, 11479.158, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_546 = _this;
  _this setDir 56.11412;
  _this setPos [9517.2568, 11479.158, -3.0517578e-005];
};
 
_vehicle_547 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9522.4834, 11471.35, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_547 = _this;
  _this setDir 56.658382;
  _this setPos [9522.4834, 11471.35, -6.1035156e-005];
};
 
_vehicle_548 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9472.6914, 11396.653, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_548 = _this;
  _this setDir 36.70787;
  _this setPos [9472.6914, 11396.653, -7.6293945e-005];
};
 
_vehicle_549 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [9478.9395, 11403.622], [], 0, "CAN_COLLIDE"];
  _vehicle_549 = _this;
  _this setDir 38.042191;
  _this setPos [9478.9395, 11403.622];
};
 
_vehicle_550 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [9438.8018, 11387.951, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_550 = _this;
  _this setDir -38.71384;
  _this setPos [9438.8018, 11387.951, -0.0001373291];
};
 
_vehicle_551 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [9433.4785, 11394.765, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_551 = _this;
  _this setDir -38.654346;
  _this setPos [9433.4785, 11394.765, 1.5258789e-005];
};
 
_vehicle_552 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [9520.584, 11455.189, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_552 = _this;
  _this setDir 141.31204;
  _this setPos [9520.584, 11455.189, -1.5258789e-005];
};
 
_vehicle_553 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [9515.4346, 11462.562, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_553 = _this;
  _this setDir 142.44843;
  _this setPos [9515.4346, 11462.562, 1.5258789e-005];
};
 
_vehicle_554 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [9520.0352, 11504.396, -0.14501217], [], 0, "CAN_COLLIDE"];
  _vehicle_554 = _this;
  _this setDir -163.25737;
  _this setPos [9520.0352, 11504.396, -0.14501217];
};
 
_vehicle_555 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hlidac_Budka_EP1", [9405.335, 11423.009, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_555 = _this;
  _this setDir 56.12212;
  _this setPos [9405.335, 11423.009, -3.0517578e-005];
};
 
_vehicle_556 = objNull;
if (true) then
{
  _this = createVehicle ["GunrackUS_EP1", [9405.6152, 11421.344, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_556 = _this;
  _this setDir -30.714596;
  _this setPos [9405.6152, 11421.344, -6.1035156e-005];
};
 
_vehicle_557 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hlidac_Budka_EP1", [9480.7441, 11391.691, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_557 = _this;
  _this setDir 126.27706;
  _this setPos [9480.7441, 11391.691, -3.0517578e-005];
};
 
_vehicle_558 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [9484.5635, 11389.021, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_558 = _this;
  _this setDir 123.10993;
  _this setPos [9484.5635, 11389.021, -3.0517578e-005];
};
 
_vehicle_559 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [9482.7002, 11390.605, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_559 = _this;
  _this setPos [9482.7002, 11390.605, 3.0517578e-005];
};
 
_vehicle_560 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9490.3584, 11394.369, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_560 = _this;
  _this setDir -57.33107;
  _this setPos [9490.3584, 11394.369, 1.5258789e-005];
};
 
_vehicle_561 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9491.5977, 11396.318, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_561 = _this;
  _this setDir -56.903099;
  _this setPos [9491.5977, 11396.318, 1.5258789e-005];
};
 
_vehicle_562 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9486.1221, 11388.097, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_562 = _this;
  _this setDir -55.783493;
  _this setPos [9486.1221, 11388.097, -9.1552734e-005];
};
 
_vehicle_563 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9484.668, 11385.966], [], 0, "CAN_COLLIDE"];
  _vehicle_563 = _this;
  _this setDir -56.112701;
  _this setPos [9484.668, 11385.966];
};
 
_vehicle_564 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9482.9492, 11385.761, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_564 = _this;
  _this setDir 35.766201;
  _this setPos [9482.9492, 11385.761, -1.5258789e-005];
};
 
_vehicle_565 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9481.0293, 11387.139, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_565 = _this;
  _this setDir 36.134586;
  _this setPos [9481.0293, 11387.139, 9.1552734e-005];
};
 
_vehicle_566 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9480.7256, 11388.772, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_566 = _this;
  _this setDir -54.305656;
  _this setPos [9480.7256, 11388.772, -1.5258789e-005];
};
 
_vehicle_567 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9479.4531, 11388.252, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_567 = _this;
  _this setDir 34.465378;
  _this setPos [9479.4531, 11388.252, 3.0517578e-005];
};
 
_vehicle_568 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9491.3887, 11398.228, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_568 = _this;
  _this setDir 37.34082;
  _this setPos [9491.3887, 11398.228, -1.5258789e-005];
};
 
_vehicle_569 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9489.3711, 11399.7, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_569 = _this;
  _this setDir 34.533134;
  _this setPos [9489.3711, 11399.7, -1.5258789e-005];
};
 
_vehicle_570 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9487.3301, 11401.169, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_570 = _this;
  _this setDir 35.641201;
  _this setPos [9487.3301, 11401.169, -4.5776367e-005];
};
 
_vehicle_571 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9485.7158, 11400.975, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_571 = _this;
  _this setDir -55.755497;
  _this setPos [9485.7158, 11400.975, -1.5258789e-005];
};
 
_vehicle_572 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [9477.3701, 11395.989, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_572 = _this;
  _this setDir -48.467491;
  _this setPos [9477.3701, 11395.989, -4.5776367e-005];
};
 
_vehicle_573 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [9476.4619, 11396.737, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_573 = _this;
  _this setDir -49.934494;
  _this setPos [9476.4619, 11396.737, -3.0517578e-005];
};
 
_vehicle_574 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9487.7236, 11399.704, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_574 = _this;
  _this setDir 37.444748;
  _this setPos [9487.7236, 11399.704, -3.0517578e-005];
};
 
_vehicle_575 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9489.2305, 11398.588, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_575 = _this;
  _this setDir 36.548397;
  _this setPos [9489.2305, 11398.588, -3.0517578e-005];
};
 
_vehicle_576 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [9464.3896, 11403.941], [], 0, "CAN_COLLIDE"];
  _vehicle_576 = _this;
  _this setDir -41.515373;
  _this setPos [9464.3896, 11403.941];
};
 
_vehicle_577 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9413.458, 11428.759, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_577 = _this;
  _this setDir 58.653404;
  _this setPos [9413.458, 11428.759, -7.6293945e-005];
};
 
_vehicle_578 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9412.0166, 11430.821, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_578 = _this;
  _this setDir 55.967342;
  _this setPos [9412.0166, 11430.821, 0.00010681152];
};
 
_vehicle_579 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9411.8535, 11430.847, 0.50482762], [], 0, "CAN_COLLIDE"];
  _vehicle_579 = _this;
  _this setDir 46.964043;
  _this setPos [9411.8535, 11430.847, 0.50482762];
};
 
_vehicle_580 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [9414.4844, 11426.564], [], 0, "CAN_COLLIDE"];
  _vehicle_580 = _this;
  _this setDir 69.4487;
  _this setPos [9414.4844, 11426.564];
};
 
_vehicle_581 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [9410.0186, 11419.884, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_581 = _this;
  _this setDir 55.603596;
  _this setPos [9410.0186, 11419.884, 6.1035156e-005];
};
 
_vehicle_582 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [9408.9727, 11419.306, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_582 = _this;
  _this setDir 56.420742;
  _this setPos [9408.9727, 11419.306, -4.5776367e-005];
};
 
_vehicle_584 = objNull;
if (true) then
{
  _this = createVehicle ["HeliHCivil", [9421.0879, 11412.99, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_584 = _this;
  _this setPos [9421.0879, 11412.99, 0];
};
 
_vehicle_585 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Feed", [9415.9678, 11408.457], [], 0, "CAN_COLLIDE"];
  _vehicle_585 = _this;
  _this setDir 61.307545;
  _this setPos [9415.9678, 11408.457];
};
 
_vehicle_586 = objNull;
if (true) then
{
  _this = createVehicle ["Land_SignB_Gov", [9416.292, 11408.864], [], 0, "CAN_COLLIDE"];
  _vehicle_586 = _this;
  _this setDir 58.58952;
  _this setPos [9416.292, 11408.864];
};
 
_vehicle_587 = objNull;
if (true) then
{
  _this = createVehicle ["Fence_Ind_long", [9486.1045, 11383.618, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_587 = _this;
  _this setDir -142.9671;
  _this setPos [9486.1045, 11383.618, 0.00012207031];
};
 
_vehicle_588 = objNull;
if (true) then
{
  _this = createVehicle ["Fence_Ind_long", [9493.3457, 11378.251, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_588 = _this;
  _this setDir -143.70589;
  _this setPos [9493.3457, 11378.251, -0.00010681152];
};
 
_vehicle_589 = objNull;
if (true) then
{
  _this = createVehicle ["Fence_Ind_long", [9500.7334, 11372.918], [], 0, "CAN_COLLIDE"];
  _vehicle_589 = _this;
  _this setDir -142.75952;
  _this setPos [9500.7334, 11372.918];
};
 
_vehicle_590 = objNull;
if (true) then
{
  _this = createVehicle ["SignM_FOB_Prostejov_EP1", [9503.5225, 11369.401, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_590 = _this;
  _this setDir -84.171265;
  _this setPos [9503.5225, 11369.401, -1.5258789e-005];
};
 
_vehicle_591 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_1L_Noentry", [9513.0928, 11369.42, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_591 = _this;
  _this setDir -50.239262;
  _this setPos [9513.0928, 11369.42, -3.0517578e-005];
};
 
_vehicle_592 = objNull;
if (true) then
{
  _this = createVehicle ["RoadBarrier_long", [9510.3779, 11368.377, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_592 = _this;
  _this setDir -44.390106;
  _this setPos [9510.3779, 11368.377, -6.1035156e-005];
};
 
_vehicle_593 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Danger", [9518.6523, 11373.983, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_593 = _this;
  _this setDir -56.319584;
  _this setPos [9518.6523, 11373.983, -1.5258789e-005];
};
 
_vehicle_594 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Checkpoint", [9517.3408, 11366.541, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_594 = _this;
  _this setDir -49.369606;
  _this setPos [9517.3408, 11366.541, -0.00010681152];
};
 
_vehicle_595 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [9501.3516, 11381.601, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_595 = _this;
  _this setDir 122.00944;
  _this setPos [9501.3516, 11381.601, 3.0517578e-005];
};
 
_vehicle_596 = objNull;
if (true) then
{
  _this = createVehicle ["BRDMWreck", [9511.6035, 11374.313, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_596 = _this;
  _this setDir 130.47845;
  _this setPos [9511.6035, 11374.313, -7.6293945e-005];
};
 
_vehicle_597 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [9522.3213, 11366.248, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_597 = _this;
  _this setDir 126.58411;
  _this setPos [9522.3213, 11366.248, -3.0517578e-005];
};
 
_vehicle_598 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [9532.2529, 11358.758, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_598 = _this;
  _this setDir 125.85526;
  _this setPos [9532.2529, 11358.758, -3.0517578e-005];
};
 
_vehicle_599 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [9543.1396, 11350.418, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_599 = _this;
  _this setDir 131.911;
  _this setPos [9543.1396, 11350.418, 3.0517578e-005];
};
 
_vehicle_600 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [9552.1885, 11342.004, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_600 = _this;
  _this setDir 137.31465;
  _this setPos [9552.1885, 11342.004, -4.5776367e-005];
};
 
_vehicle_601 = objNull;
if (true) then
{
  _this = createVehicle ["hiluxWreck", [9557.334, 11333.744, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_601 = _this;
  _this setDir 141.64717;
  _this setPos [9557.334, 11333.744, -1.5258789e-005];
};
 
_vehicle_602 = objNull;
if (true) then
{
  _this = createVehicle ["datsun02Wreck", [9533.4033, 11346.57, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_602 = _this;
  _this setPos [9533.4033, 11346.57, -3.0517578e-005];
};
 
_vehicle_603 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9499.6543, 11378.423, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_603 = _this;
  _this setPos [9499.6543, 11378.423, 3.0517578e-005];
};
 
_vehicle_604 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9497.2705, 11380.853, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_604 = _this;
  _this setPos [9497.2705, 11380.853, -1.5258789e-005];
};
 
_vehicle_605 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9530.3584, 11364.854, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_605 = _this;
  _this setPos [9530.3584, 11364.854, 0];
};
 
_vehicle_606 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9518.6357, 11363.771, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_606 = _this;
  _this setPos [9518.6357, 11363.771, 0];
};
 
_vehicle_607 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9542.0781, 11346.387, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_607 = _this;
  _this setPos [9542.0781, 11346.387, 4.5776367e-005];
};
 
_vehicle_608 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [9560.2168, 11336.535, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_608 = _this;
  _this setPos [9560.2168, 11336.535, -3.0517578e-005];
};
 
_vehicle_609 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9556.9639, 11339.7, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_609 = _this;
  _this setPos [9556.9639, 11339.7, 7.6293945e-005];
};
 
_vehicle_610 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9548.2334, 11345.902, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_610 = _this;
  _this setPos [9548.2334, 11345.902, -3.0517578e-005];
};
 
_vehicle_611 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9537.8945, 11356.711, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_611 = _this;
  _this setPos [9537.8945, 11356.711, 0];
};
 
_vehicle_612 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9526.9912, 11358.686, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_612 = _this;
  _this setPos [9526.9912, 11358.686, 3.0517578e-005];
};
 
_vehicle_613 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9530.7744, 11344.316, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_613 = _this;
  _this setPos [9530.7744, 11344.316, -6.1035156e-005];
};
 
_vehicle_614 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9516.875, 11368.753, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_614 = _this;
  _this setPos [9516.875, 11368.753, 1.5258789e-005];
};
 
_vehicle_615 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9508.7041, 11374.269, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_615 = _this;
  _this setPos [9508.7041, 11374.269, 4.5776367e-005];
};
 
_vehicle_616 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9511.666, 11376.818, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_616 = _this;
  _this setPos [9511.666, 11376.818, 0];
};
 
_vehicle_617 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [9499.7568, 11385.454, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_617 = _this;
  _this setPos [9499.7568, 11385.454, 1.5258789e-005];
};
 
_vehicle_619 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BarGate2", [9410.1602, 11422.268, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_619 = _this;
  _this setDir -32.267162;
  _this setPos [9410.1602, 11422.268, 7.6293945e-005];
};
 
_vehicle_620 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BarGate2", [9413.4658, 11424.268, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_620 = _this;
  _this setDir -32.407917;
  _this setPos [9413.4658, 11424.268, -3.0517578e-005];
};
 
_vehicle_621 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9485.6719, 11477.773, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_621 = _this;
  _this setDir 3.6535521;
  _this setPos [9485.6719, 11477.773, -7.6293945e-005];
};
 
_vehicle_622 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [9493.4238, 11477.263, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_622 = _this;
  _this setPos [9493.4238, 11477.263, 9.1552734e-005];
};
 
_vehicle_623 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1E_EP1", [9453.5654, 11451.202, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_623 = _this;
  _this setDir -33.800777;
  _this setPos [9453.5654, 11451.202, -3.0517578e-005];
};
 
_vehicle_624 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1E_EP1", [9457.1816, 11453.232, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_624 = _this;
  _this setDir -34.404686;
  _this setPos [9457.1816, 11453.232, -1.5258789e-005];
};
 
_vehicle_625 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1E_EP1", [9460.5576, 11455.25, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_625 = _this;
  _this setDir -33.496529;
  _this setPos [9460.5576, 11455.25, 9.1552734e-005];
};
 
_vehicle_626 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1E_EP1", [9463.6914, 11457.733, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_626 = _this;
  _this setDir -34.222321;
  _this setPos [9463.6914, 11457.733, -4.5776367e-005];
};
 
_vehicle_627 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1Bo_military", [9467.2158, 11459.907, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_627 = _this;
  _this setDir -32.908302;
  _this setPos [9467.2158, 11459.907, 1.5258789e-005];
};
 
_vehicle_628 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_small", [9451.8096, 11447.141, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_628 = _this;
  _this setDir -32.176525;
  _this setPos [9451.8096, 11447.141, 3.0517578e-005];
};
 
_vehicle_629 = objNull;
if (true) then
{
  _this = createVehicle ["ACamp", [9449.2441, 11457.444, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_629 = _this;
  _this setDir 55.525887;
  _this setPos [9449.2441, 11457.444, 1.5258789e-005];
};
 
_vehicle_630 = objNull;
if (true) then
{
  _this = createVehicle ["ACamp", [9452.1084, 11459.501], [], 0, "CAN_COLLIDE"];
  _vehicle_630 = _this;
  _this setDir 58.989914;
  _this setPos [9452.1084, 11459.501];
};
 
_vehicle_631 = objNull;
if (true) then
{
  _this = createVehicle ["ACamp", [9455.3701, 11461.584, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_631 = _this;
  _this setDir 58.1469;
  _this setPos [9455.3701, 11461.584, -1.5258789e-005];
};
 
_vehicle_632 = objNull;
if (true) then
{
  _this = createVehicle ["ACamp", [9458.4248, 11463.484, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_632 = _this;
  _this setDir 57.60675;
  _this setPos [9458.4248, 11463.484, -4.5776367e-005];
};
 
_vehicle_633 = objNull;
if (true) then
{
  _this = createVehicle ["ACamp", [9450.9824, 11470.533, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_633 = _this;
  _this setDir 40.890476;
  _this setPos [9450.9824, 11470.533, -7.6293945e-005];
};
 
_vehicle_635 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Campfire_burning", [9450.5264, 11464.908, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_635 = _this;
  _this setPos [9450.5264, 11464.908, -3.0517578e-005];
};
 
_vehicle_636 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [9454.0342, 11471.632, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_636 = _this;
  _this setDir 98.810699;
  _this setPos [9454.0342, 11471.632, -3.0517578e-005];
};
 
_vehicle_637 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [9447.1758, 11456.551, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_637 = _this;
  _this setDir 144.0092;
  _this setPos [9447.1758, 11456.551, -3.0517578e-005];
};
 
_vehicle_638 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta2", [9446.124, 11468.313, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_638 = _this;
  _this setPos [9446.124, 11468.313, -1.5258789e-005];
};
 
_vehicle_639 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [9456.9502, 11472.874], [], 0, "CAN_COLLIDE"];
  _vehicle_639 = _this;
  _this setDir 92.808449;
  _this setPos [9456.9502, 11472.874];
};
 
_vehicle_640 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Barrel_water", [9445.2676, 11468.866, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_640 = _this;
  _this setPos [9445.2676, 11468.866, -3.0517578e-005];
};
 
_vehicle_641 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Shed_wooden", [9441.7031, 11464.38, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_641 = _this;
  _this setDir -77.649071;
  _this setPos [9441.7031, 11464.38, -6.1035156e-005];
};
 
_vehicle_643 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hlidac_Budka_EP1", [9434.7607, 11442.617], [], 0, "CAN_COLLIDE"];
  _vehicle_643 = _this;
  _this setDir 57.711514;
  _this setPos [9434.7607, 11442.617];
};
 
_vehicle_644 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small_EP1", [9445.3994, 11450.886, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_644 = _this;
  _this setDir 149.73921;
  _this setPos [9445.3994, 11450.886, 1.5258789e-005];
};
 
_vehicle_645 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small_EP1", [9494.3594, 11395.837, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_645 = _this;
  _this setDir -145.74077;
  _this setPos [9494.3594, 11395.837, 1.5258789e-005];
};
 
_vehicle_646 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small_EP1", [9494.3125, 11391.665, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_646 = _this;
  _this setDir -54.288525;
  _this setPos [9494.3125, 11391.665, -1.5258789e-005];
};
 
_vehicle_647 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small_EP1", [9486.293, 11385.546, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_647 = _this;
  _this setDir 37.318378;
  _this setPos [9486.293, 11385.546, -7.6293945e-005];
};
 
_vehicle_648 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small_EP1", [9489.9199, 11383.641, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_648 = _this;
  _this setDir -49.894966;
  _this setPos [9489.9199, 11383.641, 9.1552734e-005];
};
 
_vehicle_649 = objNull;
if (true) then
{
  _this = createVehicle ["M1130_HQ_unfolded_Base_EP1", [9491.9756, 11442.802, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_649 = _this;
  _this setDir -1.6200744;
  _this setPos [9491.9756, 11442.802, -1.5258789e-005];
};
 
_vehicle_651 = objNull;
if (true) then
{
  _this = createVehicle ["US_WarfareBUAVterminal_Base_EP1", [9496.2695, 11459.474, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_651 = _this;
  _this setDir 14.783888;
  _this setPos [9496.2695, 11459.474, -3.0517578e-005];
};
 
_vehicle_652 = objNull;
if (true) then
{
  _this = createVehicle ["US_WarfareBVehicleServicePoint_Base_EP1", [9501.9023, 11439.612, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_652 = _this;
  _this setDir 49.959179;
  _this setPos [9501.9023, 11439.612, -3.0517578e-005];
};
 
_vehicle_653 = objNull;
if (true) then
{
  _this = createVehicle ["US_WarfareBLightFactory_base_EP1", [9479.2305, 11424.274, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_653 = _this;
  _this setDir 55.51432;
  _this setPos [9479.2305, 11424.274, -3.0517578e-005];
};
 
_vehicle_654 = objNull;
if (true) then
{
  _this = createVehicle ["US_WarfareBHeavyFactory_Base_EP1", [9471.4492, 11440.368, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_654 = _this;
  _this setDir -26.265646;
  _this setPos [9471.4492, 11440.368, -1.5258789e-005];
};
};


