private ["_player", "_puid"];
_player = _this;
_puid = getPlayerUID _player;
_name = if (alive _player) then {[(name _player),1] call fnc_sanitizeInput;} else {"unknown player";};
_playerUID = getPlayerUID _player;
_query = format ["SELECT coin_amount, vehclass FROM insurance_claims WHERE player_uid = '%1'", _puid];
_claims = [_query, 2, true] call fn_asyncCall;
PVDZE_claimInsuranceResult = _claims;
owner _this publicVariableClient "PVDZE_claimInsuranceResult";
PVDZE_claimInsuranceResult = 0;

_message = format["GARAGE: %1 (%2) has attempted to claim vehicle insurance.",_name,_playerUID];
diag_log _message;

// remove all claims once processed
_resetQ = format ["DELETE FROM insurance_claims WHERE player_uid = '%1'", _puid];
_result = [_resetQ, 1, true] call fn_asyncCall;
