if (isServer) then {	
_vehicle_0 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [13600.008, 3206.6367, 0.17106017], [], 0, "CAN_COLLIDE"];
  _vehicle_0 = _this;
  _this setDir -10.406147;
  _this setPos [13600.008, 3206.6367, 0.17106017];
};

_vehicle_3 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [13584.655, 3203.8855, 0.12498072], [], 0, "CAN_COLLIDE"];
  _vehicle_3 = _this;
  _this setDir -10.295178;
  _this setPos [13584.655, 3203.8855, 0.12498072];
};

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [13593.254, 3156.8865, -5.7220459e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir -11.689569;
  _this setPos [13593.254, 3156.8865, -5.7220459e-005];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [13608.512, 3160.1008, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setDir -11.972246;
  _this setPos [13608.512, 3160.1008, 4.5776367e-005];
};

_vehicle_6 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [13585.035, 3164.311, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_6 = _this;
  _this setDir -100.45635;
  _this setPos [13585.035, 3164.311, -1.5258789e-005];
};

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [13582.184, 3179.8296, -4.3869019e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setDir -100.1559;
  _this setPos [13582.184, 3179.8296, -4.3869019e-005];
};

_vehicle_8 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [13579.459, 3195.1243, 0.10231289], [], 0, "CAN_COLLIDE"];
  _vehicle_8 = _this;
  _this setDir -100.05178;
  _this setPos [13579.459, 3195.1243, 0.10231289];
};

_vehicle_9 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [13614.767, 3169.3591, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_9 = _this;
  _this setDir 79.410553;
  _this setPos [13614.767, 3169.3591, 7.6293945e-006];
};

_vehicle_10 = objNull;
if (true) then
{
  _this = createVehicle ["Base_WarfareBBarrier10xTall", [13608.851, 3201.5479, 0.21677847], [], 0, "CAN_COLLIDE"];
  _vehicle_10 = _this;
  _this setDir 79.515892;
  _this setPos [13608.851, 3201.5479, 0.21677847];
};

_vehicle_11 = objNull;
if (true) then
{
  _this = createVehicle ["Barrack2", [13588.214, 3197.7161, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_11 = _this;
  _this setDir -101.07639;
  _this setPos [13588.214, 3197.7161, -1.1444092e-005];
};

_vehicle_12 = objNull;
if (true) then
{
  _this = createVehicle ["Barrack2", [13593.651, 3165.2512, -4.196167e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_12 = _this;
  _this setDir -100.83065;
  _this setPos [13593.651, 3165.2512, -4.196167e-005];
};

_vehicle_14 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_IlluminantTower", [13588.294, 3159.0217, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_14 = _this;
  _this setDir -9.2336884;
  _this setPos [13588.294, 3159.0217, -3.0517578e-005];
};

_vehicle_15 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_IlluminantTower", [13580.472, 3201.2205, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_15 = _this;
  _this setDir -10.528242;
  _this setPos [13580.472, 3201.2205, -7.6293945e-006];
};

_vehicle_19 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fort_watchtower", [13617.571, 3180.2217], [], 0, "CAN_COLLIDE"];
  _vehicle_19 = _this;
  _this setDir -100.71156;
  _this setPos [13617.571, 3180.2217];
};

_vehicle_20 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fort_watchtower", [13615.135, 3191.7507, -1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_20 = _this;
  _this setDir -99.493408;
  _this setPos [13615.135, 3191.7507, -1.9073486e-005];
};

_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [13618.439, 3186.4685, -3.4332275e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir 79.364052;
  _this setPos [13618.439, 3186.4685, -3.4332275e-005];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Mil_ControlTower", [13590.983, 3177.0437, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir -280.25323;
  _this setPos [13590.983, 3177.0437, -4.5776367e-005];
};

_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_IlluminantTower", [13691.3,3292.53,0.00283384], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setPos [13691.3,3292.53,0.00283384];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_IlluminantTower", [13712.8,3258.98,0.00279593], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setPos [13712.8,3258.98,0.00279593];
};

_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_IlluminantTower", [13380.649, 2757.0288, 1.0967255e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir -24.433952;
  _this setPos [13380.649, 2757.0288, 1.0967255e-005];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_IlluminantTower", [13833.822, 2922.9561, 8.0108643e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setDir -101.27764;
  _this setPos [13833.822, 2922.9561, 8.0108643e-005];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_IlluminantTower", [13799.702, 2856.1763, -0.00017166138], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir -94.819923;
  _this setPos [13799.702, 2856.1763, -0.00017166138];
};
};


