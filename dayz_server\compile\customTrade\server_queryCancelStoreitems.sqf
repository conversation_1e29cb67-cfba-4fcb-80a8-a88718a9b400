private ["_player","_query","_result","_clientID","_playerUID"];

_player = _this select 0;
_clientID = owner _player;
_playerUID = getPlayerUID _player;

_query = format["SELECT Id, playerUID, Classname, Cost, Type, PlayerName FROM customTrade WHERE PlayerUID = '%1'",_playerUID];
_result = [_query, 2, true] call fn_asyncCall;

//diag_log format ["[O9 Custom Trading]: _result: %1",_result];

PVDZE_queryStoreResult = _result;

if (!isNull _player) then {
	_clientID publicVariableClient "PVDZE_queryStoreResult";
};
