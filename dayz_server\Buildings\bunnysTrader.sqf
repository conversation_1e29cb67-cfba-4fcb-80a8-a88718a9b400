/*
  Bunnys Vehicles at Balota Airfield
*/

if (isServer) then {

_vehicle_0 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [4616.6641, 2526.8025, -1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_0 = _this;
  _this setDir -54.442482;
  _this setPos [4616.6641, 2526.8025, -1.335144e-005];
};

_vehicle_2 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [4612.1167, 2519.8445, 1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2 = _this;
  _this setDir -54.075996;
  _this setPos [4612.1167, 2519.8445, 1.0490417e-005];
};

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [4612.0405, 2513.8333, -9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir 34.119331;
  _this setPos [4612.0405, 2513.8333, -9.5367432e-006];
};

_vehicle_6 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [4619.187, 2509.3008, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_6 = _this;
  _this setDir 34.107071;
  _this setPos [4619.187, 2509.3008, 3.0517578e-005];
};

_vehicle_8 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [4626.3071, 2504.7852, 1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_8 = _this;
  _this setDir -146.84952;
  _this setPos [4626.3071, 2504.7852, 1.1444092e-005];
};

_vehicle_10 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [4631.9575, 2507.5027, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_10 = _this;
  _this setDir -54.616459;
  _this setPos [4631.9575, 2507.5027, 1.5258789e-005];
};

_vehicle_12 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [4636.5181, 2514.6294, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_12 = _this;
  _this setDir -55.652298;
  _this setPos [4636.5181, 2514.6294, -3.8146973e-006];
};

_vehicle_14 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [4622.3286, 2529.3232, 1.6212463e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_14 = _this;
  _this setDir -145.44246;
  _this setPos [4622.3286, 2529.3232, 1.6212463e-005];
};

_vehicle_16 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [4629.4355, 2524.7737, 6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_16 = _this;
  _this setDir -145.19847;
  _this setPos [4629.4355, 2524.7737, 6.6757202e-006];
};

_vehicle_19 = objNull;
if (true) then
{
  _this = createVehicle ["mbg_brickhouse_01_EO", [4618.6802, 2520.4187, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_19 = _this;
  _this setDir -57.012211;
  _this setPos [4618.6802, 2520.4187, -5.7220459e-006];
};

_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1E_EP1", [4629.2705, 2508.8362, -1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setDir -147.43132;
  _this setPos [4629.2705, 2508.8362, -1.335144e-005];
};

_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Campfire_burning", [4624.6875, 2512.314, 2.7656555e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setDir 4.452054;
  _this setPos [4624.6875, 2512.314, 2.7656555e-005];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [4626.2314, 2508.2058, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setDir -146.69423;
  _this setPos [4626.2314, 2508.2058, 3.8146973e-006];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierGermany_EP1", [4640.2964, 2518.5984, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir 2.2891381;
  _this setPos [4640.2964, 2518.5984, 5.3405762e-005];
};

_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierBAF", [4638.5088, 2519.8528, 2.8610229e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir 4.452054;
  _this setPos [4638.5088, 2519.8528, 2.8610229e-005];
};

_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierUSA", [4639.4116, 2519.165, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir 4.452054;
  _this setPos [4639.4116, 2519.165, 2.8610229e-006];
};

_vehicle_38 = objNull;
if (true) then
{
  _this = createVehicle ["Helipad_Civil_DZ", [4654.1138, 2540.1431, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_38 = _this;
  _this setPos [4654.1138, 2540.1431, -2.8610229e-006];
};

};