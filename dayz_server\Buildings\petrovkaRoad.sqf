if (isserver) then {

_vehicle_2 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [5406.1846, 12303.466, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2 = _this;
  _this setDir 27.375465;
  _this setPos [5406.1846, 12303.466, -1.5258789e-005];
};

_vehicle_3 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5415.4917, 12324.898, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_3 = _this;
  _this setDir -162.8364;
  _this setPos [5415.4917, 12324.898, 0.00012207031];
};

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5418.9561, 12341.726], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir 186.99219;
  _this setPos [5418.9561, 12341.726];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5418.7998, 12341.43, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setDir 6.6761122;
  _this setPos [5418.7998, 12341.43, 0.00015258789];
};

_vehicle_6 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5424.8394, 12407.972, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_6 = _this;
  _this setDir 176.73306;
  _this setPos [5424.8394, 12407.972, -6.1035156e-005];
};

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5421.5596, 12366.064, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setDir 6.7536674;
  _this setPos [5421.5596, 12366.064, 0.00021362305];
};

_vehicle_8 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5422.1943, 12424.853, 0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_8 = _this;
  _this setDir 166.41216;
  _this setPos [5422.1943, 12424.853, 0.00033569336];
};

_vehicle_9 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5416.4297, 12440.759, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_9 = _this;
  _this setDir -24.488863;
  _this setPos [5416.4297, 12440.759, 6.1035156e-005];
};

_vehicle_10 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5416.3965, 12441.119, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_10 = _this;
  _this setDir 155.78899;
  _this setPos [5416.3965, 12441.119, 0.00012207031];
};

_vehicle_11 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5406.0723, 12463.224, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_11 = _this;
  _this setDir -25.156796;
  _this setPos [5406.0723, 12463.224, 9.1552734e-005];
};

_vehicle_12 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5399.9844, 12479.291, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_12 = _this;
  _this setDir -18.019152;
  _this setPos [5399.9844, 12479.291, 0.00018310547];
};

_vehicle_13 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5395.9683, 12496.146, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_13 = _this;
  _this setDir -8.1661549;
  _this setPos [5395.9683, 12496.146, 1.5258789e-005];
};

_vehicle_14 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5394.9785, 12513.443, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_14 = _this;
  _this setDir 1.0608176;
  _this setPos [5394.9785, 12513.443, -7.6293945e-005];
};

_vehicle_15 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5396.6982, 12530.498, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_15 = _this;
  _this setDir 11.267738;
  _this setPos [5396.6982, 12530.498, -7.6293945e-005];
};

_vehicle_17 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5401.3398, 12547.027, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_17 = _this;
  _this setDir 23.119061;
  _this setPos [5401.3398, 12547.027, 3.0517578e-005];
};

_vehicle_18 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5409.2324, 12562.216, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_18 = _this;
  _this setDir 35.925575;
  _this setPos [5409.2324, 12562.216, 0.00018310547];
};

_vehicle_19 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5420.3804, 12575.383, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_19 = _this;
  _this setDir 45.93174;
  _this setPos [5420.3804, 12575.383, 7.6293945e-005];
};

_vehicle_20 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5433.7256, 12586.366, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_20 = _this;
  _this setDir 58.079128;
  _this setPos [5433.7256, 12586.366, 1.5258789e-005];
};

_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5449.0166, 12594.29, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir 69.57225;
  _this setPos [5449.0166, 12594.29, 3.0517578e-005];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [5465.0859, 12598.956, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir 80.451668;
  _this setPos [5465.0859, 12598.956, 3.0517578e-005];
};

_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5494.6011, 12593.188, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir -79.021088;
  _this setPos [5494.6011, 12593.188, -4.5776367e-005];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [5513.8198, 12593.259], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir -101.47346;
  _this setPos [5513.8198, 12593.259];
};

_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5530.457, 12598.091, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setDir -111.26945;
  _this setPos [5530.457, 12598.091, 1.5258789e-005];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5530.1665, 12598.116], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir 68.820129;
  _this setPos [5530.1665, 12598.116];
};

_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5546.542, 12603.086, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setDir 79.072456;
  _this setPos [5546.542, 12603.086, -1.5258789e-005];
};

_vehicle_30 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [5563.6699, 12604.972], [], 0, "CAN_COLLIDE"];
  _vehicle_30 = _this;
  _this setDir 89.671463;
  _this setPos [5563.6699, 12604.972];
};

_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [5580.9404, 12604.966, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir 91.792191;
  _this setPos [5580.9404, 12604.966, 1.5258789e-005];
};

_vehicle_32 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [5598.2295, 12604.305, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_32 = _this;
  _this setDir 95.277054;
  _this setPos [5598.2295, 12604.305, 1.5258789e-005];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [5615.2065, 12602.464, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setDir 96.919098;
  _this setPos [5615.2065, 12602.464, 1.5258789e-005];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5632.4146, 12600.351, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir 98.250854;
  _this setPos [5632.4146, 12600.351, 1.5258789e-005];
};

_vehicle_46 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5681.5181, 12592.414, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_46 = _this;
  _this setDir -79.747673;
  _this setPos [5681.5181, 12592.414, 6.1035156e-005];
};

_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [5767.2446, 12584.595, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setDir -464.97824;
  _this setPos [5767.2446, 12584.595, 0.00015258789];
};

_vehicle_65 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [5857.6392, 12575.965, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_65 = _this;
  _this setDir -104.69996;
  _this setPos [5857.6392, 12575.965, 0.00019836426];
};

_vehicle_66 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [5875.1304, 12583.198, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_66 = _this;
  _this setDir -122.95198;
  _this setPos [5875.1304, 12583.198, 0.00010681152];
};

_vehicle_67 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [5874.8037, 12582.8, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_67 = _this;
  _this setDir 58.199562;
  _this setPos [5874.8037, 12582.8, -0.0001373291];
};

_vehicle_68 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5892.7656, 12589.595, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_68 = _this;
  _this setDir 82.028931;
  _this setPos [5892.7656, 12589.595, -0.0001373291];
};

_vehicle_71 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5934.0698, 12596.889, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_71 = _this;
  _this setDir -106.95348;
  _this setPos [5934.0698, 12596.889, -0.00012207031];
};

_vehicle_73 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [5933.8843, 12596.979, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_73 = _this;
  _this setDir 74.548042;
  _this setPos [5933.8843, 12596.979, -0.00015258789];
};

_vehicle_75 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [5946.585, 12599.354, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_75 = _this;
  _this setDir 84.560089;
  _this setPos [5946.585, 12599.354, 4.5776367e-005];
};

_vehicle_76 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5965.8496, 12597.394, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_76 = _this;
  _this setDir 107.30522;
  _this setPos [5965.8496, 12597.394, -0.0001373291];
};

_vehicle_77 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [5997.8228, 12584.197, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_77 = _this;
  _this setDir -71.777184;
  _this setPos [5997.8228, 12584.197, 0.00021362305];
};

_vehicle_78 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6014.4932, 12580.187, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_78 = _this;
  _this setDir -81.2798;
  _this setPos [6014.4932, 12580.187, 0.00012207031];
};

_vehicle_79 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [6031.6616, 12577.928, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_79 = _this;
  _this setDir -82.688103;
  _this setPos [6031.6616, 12577.928, 3.0517578e-005];
};

_vehicle_80 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [6048.8276, 12576.002, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_80 = _this;
  _this setDir -84.136681;
  _this setPos [6048.8276, 12576.002, 3.0517578e-005];
};

_vehicle_81 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [6065.7402, 12574.752, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_81 = _this;
  _this setDir -86.410919;
  _this setPos [6065.7402, 12574.752, 1.5258789e-005];
};

_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6090.1313, 12571.863, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setDir -83.376846;
  _this setPos [6090.1313, 12571.863, 3.0517578e-005];
};

_vehicle_91 = objNull;
if (true) then
{
  _this = createVehicle ["Land_bouda2_vnitrek", [5743.7798, 12608.972, 0.047300678], [], 0, "CAN_COLLIDE"];
  _vehicle_91 = _this;
  _this setDir 56.800453;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5743.7798, 12608.972, 0.047300678];
};

_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ruin_01", [5819.958, 12589.396, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setDir -528.72595;
  _this setPos [5819.958, 12589.396, 1.5258789e-005];
};

_vehicle_103 = objNull;
if (true) then
{
  _this = createVehicle ["dum_rasovna", [5756.3594, 12593.31, 0.31885406], [], 0, "CAN_COLLIDE"];
  _vehicle_103 = _this;
  _this setDir -89.379005;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5756.3594, 12593.31, 0.31885406];
};

_vehicle_110 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dulni_bs", [6002.0649, 12594.021, -0.491615], [], 0, "CAN_COLLIDE"];
  _vehicle_110 = _this;
  _this setDir -74.09024;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6002.0649, 12594.021, -0.491615];
};

_vehicle_111 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rubble_wood_02", [6004.1479, 12599.058, 0.27285689], [], 0, "CAN_COLLIDE"];
  _vehicle_111 = _this;
  _this setDir -5.2716107;
  _this setPos [6004.1479, 12599.058, 0.27285689];
};

_vehicle_112 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6114.7349, 12568.251, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_112 = _this;
  _this setDir -81.429527;
  _this setPos [6114.7349, 12568.251, 0.00015258789];
};

_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6131.9331, 12567.292, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setDir -91.237419;
  _this setPos [6131.9331, 12567.292, 0.00021362305];
};

_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6131.8081, 12567.397, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir 88.79631;
  _this setPos [6131.8081, 12567.397, -4.5776367e-005];
};

_vehicle_122 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6156.6123, 12568.035, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_122 = _this;
  _this setDir 90.878349;
  _this setPos [6156.6123, 12568.035, 9.1552734e-005];
};

_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6198.5981, 12568.758, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setDir 81.531708;
  _this setPos [6198.5981, 12568.758, 0.00015258789];
};

_vehicle_124 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6223.1025, 12572.656, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_124 = _this;
  _this setDir 80.753403;
  _this setPos [6223.1025, 12572.656, 0.00024414063];
};

_vehicle_125 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6291.167, 12572.113], [], 0, "CAN_COLLIDE"];
  _vehicle_125 = _this;
  _this setDir 101.64226;
  _this setPos [6291.167, 12572.113];
};

_vehicle_126 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6198.8179, 12568.704, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_126 = _this;
  _this setDir -97.910263;
  _this setPos [6198.8179, 12568.704, 7.6293945e-005];
};

_vehicle_127 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6247.624, 12576.668, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_127 = _this;
  _this setDir 78.941101;
  _this setPos [6247.624, 12576.668, 0.00010681152];
};

_vehicle_128 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6366.7944, 12569.564, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_128 = _this;
  _this setDir -107.02118;
  _this setPos [6366.7944, 12569.564, 3.0517578e-005];
};

_vehicle_129 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6349.873, 12565.878, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_129 = _this;
  _this setDir -97.10318;
  _this setPos [6349.873, 12565.878, 4.5776367e-005];
};

_vehicle_130 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6332.8232, 12565.17, -0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_130 = _this;
  _this setDir -88.278221;
  _this setPos [6332.8232, 12565.17, -0.00016784668];
};

_vehicle_131 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6291.4668, 12571.949, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_131 = _this;
  _this setDir -79.514656;
  _this setPos [6291.4668, 12571.949, 0.00010681152];
};

_vehicle_132 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6381.9702, 12576.054, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_132 = _this;
  _this setDir -116.85637;
  _this setPos [6381.9702, 12576.054, -3.0517578e-005];
};

_vehicle_133 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6381.6821, 12576.049], [], 0, "CAN_COLLIDE"];
  _vehicle_133 = _this;
  _this setDir 60.389248;
  _this setPos [6381.6821, 12576.049];
};

_vehicle_134 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6403.2197, 12588.452, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_134 = _this;
  _this setDir 58.824932;
  _this setPos [6403.2197, 12588.452, -0.00012207031];
};

_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6424.5122, 12601.428, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir 57.038639;
  _this setPos [6424.5122, 12601.428, -0.00024414063];
};

_vehicle_137 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6445.1328, 12615.027, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_137 = _this;
  _this setDir 56.580418;
  _this setPos [6445.1328, 12615.027, -0.00015258789];
};

_vehicle_138 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6465.7456, 12628.799, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_138 = _this;
  _this setDir 56.794533;
  _this setPos [6465.7456, 12628.799, 6.1035156e-005];
};

_vehicle_139 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6480.9214, 12637.003, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_139 = _this;
  _this setDir 67.001266;
  _this setPos [6480.9214, 12637.003, 9.1552734e-005];
};

_vehicle_140 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6497.4268, 12642.457, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_140 = _this;
  _this setDir 76.422722;
  _this setPos [6497.4268, 12642.457, 4.5776367e-005];
};

_vehicle_141 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6521.4458, 12648.293, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_141 = _this;
  _this setDir 76.422722;
  _this setPos [6521.4458, 12648.293, -3.0517578e-005];
};

_vehicle_142 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6545.4116, 12654.082, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_142 = _this;
  _this setDir 76.422722;
  _this setPos [6545.4116, 12654.082, -6.1035156e-005];
};

_vehicle_143 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6569.5215, 12659.99, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_143 = _this;
  _this setDir 76.558868;
  _this setPos [6569.5215, 12659.99, 0.00010681152];
};

_vehicle_146 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6593.5527, 12665.788, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_146 = _this;
  _this setDir 76.272621;
  _this setPos [6593.5527, 12665.788, -9.1552734e-005];
};

_vehicle_147 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6612.9771, 12666.502, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_147 = _this;
  _this setDir 97.453506;
  _this setPos [6612.9771, 12666.502, 7.6293945e-005];
};

_vehicle_148 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6631.3184, 12660.313, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_148 = _this;
  _this setDir 123.13435;
  _this setPos [6631.3184, 12660.313, 6.1035156e-005];
};

_vehicle_149 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6652.356, 12646.755, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_149 = _this;
  _this setDir 123.13435;
  _this setPos [6652.356, 12646.755, -4.5776367e-005];
};

_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6673.3584, 12633.08, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setDir 123.13435;
  _this setPos [6673.3584, 12633.08, 1.5258789e-005];
};

_vehicle_154 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6712.2979, 12612.293, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_154 = _this;
  _this setDir -79.763023;
  _this setPos [6712.2979, 12612.293, -6.1035156e-005];
};

_vehicle_156 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6712.1284, 12612.302, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_156 = _this;
  _this setDir 103.88974;
  _this setPos [6712.1284, 12612.302, -1.5258789e-005];
};

_vehicle_157 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6736.2725, 12606.409, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_157 = _this;
  _this setDir 103.88974;
  _this setPos [6736.2725, 12606.409, -3.0517578e-005];
};

_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6760.3896, 12600.486, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setDir 104.06479;
  _this setPos [6760.3896, 12600.486, -0.00015258789];
};

_vehicle_159 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6801.8667, 12592.783, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_159 = _this;
  _this setDir -89.097992;
  _this setPos [6801.8667, 12592.783, 7.6293945e-006];
};

_vehicle_160 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6819.0781, 12593.987, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_160 = _this;
  _this setDir -98.467613;
  _this setPos [6819.0781, 12593.987, 1.5258789e-005];
};

_vehicle_161 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6835.8545, 12598.113, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_161 = _this;
  _this setDir -108.45284;
  _this setPos [6835.8545, 12598.113, 2.2888184e-005];
};

_vehicle_163 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6835.6616, 12598.118, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_163 = _this;
  _this setDir 69.809669;
  _this setPos [6835.6616, 12598.118, -7.6293945e-006];
};

_vehicle_164 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6858.8247, 12606.743, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_164 = _this;
  _this setDir 69.809669;
  _this setPos [6858.8247, 12606.743, 1.5258789e-005];
};

_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6882.2285, 12615.472, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setDir 69.809669;
  _this setPos [6882.2285, 12615.472, 0.00010681152];
};

_vehicle_166 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6905.3589, 12624.052, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_166 = _this;
  _this setDir 71.090645;
  _this setPos [6905.3589, 12624.052, 7.6293945e-005];
};

_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6928.9248, 12631.869, 0.00016021729], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setDir 72.656082;
  _this setPos [6928.9248, 12631.869, 0.00016021729];
};

_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6952.5737, 12639.293, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setDir 74.322136;
  _this setPos [6952.5737, 12639.293, 3.0517578e-005];
};

_vehicle_169 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6976.2061, 12645.692, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_169 = _this;
  _this setDir 76.276505;
  _this setPos [6976.2061, 12645.692, -0.00024414063];
};

_vehicle_170 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7000.2373, 12651.72, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_170 = _this;
  _this setDir 76.276505;
  _this setPos [7000.2373, 12651.72, -1.5258789e-005];
};

_vehicle_171 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7024.1304, 12657.567, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_171 = _this;
  _this setDir 76.647369;
  _this setPos [7024.1304, 12657.567, -1.5258789e-005];
};

_vehicle_173 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7072.2617, 12669.132, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_173 = _this;
  _this setDir 76.647369;
  _this setPos [7072.2617, 12669.132, 2.2888184e-005];
};

_vehicle_174 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7096.1675, 12674.861, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_174 = _this;
  _this setDir 78.03891;
  _this setPos [7096.1675, 12674.861, -4.5776367e-005];
};

_vehicle_175 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7120.5391, 12680.219, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_175 = _this;
  _this setDir 78.140701;
  _this setPos [7120.5391, 12680.219, -6.1035156e-005];
};

_vehicle_176 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7144.7104, 12685.528, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_176 = _this;
  _this setDir 77.446526;
  _this setPos [7144.7104, 12685.528, -0.00024414063];
};

_vehicle_177 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7168.9619, 12690.938, 9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_177 = _this;
  _this setDir 77.728584;
  _this setPos [7168.9619, 12690.938, 9.9182129e-005];
};

_vehicle_178 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7217.6313, 12701.126, 0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_178 = _this;
  _this setDir 79.464203;
  _this setPos [7217.6313, 12701.126, 0.00020599365];
};

_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7193.2539, 12696.448, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setDir 79.301476;
  _this setPos [7193.2539, 12696.448, 7.6293945e-006];
};

_vehicle_180 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7252, 12701.76, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_180 = _this;
  _this setDir 102.92245;
  _this setPos [7252, 12701.76, 3.0517578e-005];
};

_vehicle_181 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7276.3022, 12696.289, -3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_181 = _this;
  _this setDir 104.87527;
  _this setPos [7276.3022, 12696.289, -3.8146973e-005];
};

_vehicle_186 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7234.9453, 12702.877, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_186 = _this;
  _this setDir 89.149223;
  _this setPos [7234.9453, 12702.877, 1.5258789e-005];
};

_vehicle_187 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [7300.1177, 12689.927, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_187 = _this;
  _this setDir 105.65679;
  _this setPos [7300.1177, 12689.927, 2.2888184e-005];
};

_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7336.1167, 12664.748, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setDir -48.744961;
  _this setPos [7336.1167, 12664.748, -2.2888184e-005];
};

_vehicle_193 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7354.873, 12648.788, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_193 = _this;
  _this setDir -49.806221;
  _this setPos [7354.873, 12648.788, 3.8146973e-005];
};

_vehicle_194 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7373.8867, 12632.845, 0.00016021729], [], 0, "CAN_COLLIDE"];
  _vehicle_194 = _this;
  _this setDir -49.806221;
  _this setPos [7373.8867, 12632.845, 0.00016021729];
};

_vehicle_195 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7373.8457, 12633.005, -6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_195 = _this;
  _this setDir 130.85071;
  _this setPos [7373.8457, 12633.005, -6.8664551e-005];
};

_vehicle_196 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7392.7969, 12616.783, 0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_196 = _this;
  _this setDir 130.50232;
  _this setPos [7392.7969, 12616.783, 0.00020599365];
};

_vehicle_197 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7411.6006, 12600.791, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_197 = _this;
  _this setDir 130.50232;
  _this setPos [7411.6006, 12600.791, 0.0002746582];
};

_vehicle_198 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7430.5601, 12584.6, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_198 = _this;
  _this setDir 130.50232;
  _this setPos [7430.5601, 12584.6, 3.8146973e-005];
};

_vehicle_199 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7449.4824, 12568.496, 0.00047302246], [], 0, "CAN_COLLIDE"];
  _vehicle_199 = _this;
  _this setDir 129.548;
  _this setPos [7449.4824, 12568.496, 0.00047302246];
};

_vehicle_200 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7468.751, 12552.643, 0.00095367432], [], 0, "CAN_COLLIDE"];
  _vehicle_200 = _this;
  _this setDir 129.548;
  _this setPos [7468.751, 12552.643, 0.00095367432];
};

_vehicle_201 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7487.8296, 12536.903, 0.00040435791], [], 0, "CAN_COLLIDE"];
  _vehicle_201 = _this;
  _this setDir 129.548;
  _this setPos [7487.8296, 12536.903, 0.00040435791];
};

_vehicle_202 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7507.0151, 12521.143, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_202 = _this;
  _this setDir 129.548;
  _this setPos [7507.0151, 12521.143, -3.0517578e-005];
};

_vehicle_203 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7544.6411, 12488.977, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_203 = _this;
  _this setDir 134.94363;
  _this setPos [7544.6411, 12488.977, -1.5258789e-005];
};

_vehicle_204 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7544.6851, 12488.787, 0.00045776367], [], 0, "CAN_COLLIDE"];
  _vehicle_204 = _this;
  _this setDir -47.854664;
  _this setPos [7544.6851, 12488.787, 0.00045776367];
};

_vehicle_207 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7580.0962, 12453.85, 0.00029754639], [], 0, "CAN_COLLIDE"];
  _vehicle_207 = _this;
  _this setDir -45.382912;
  _this setPos [7580.0962, 12453.85, 0.00029754639];
};

_vehicle_208 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7685.3569, 12348.254, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_208 = _this;
  _this setDir -44.742298;
  _this setPos [7685.3569, 12348.254, 6.1035156e-005];
};

_vehicle_209 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7667.8369, 12365.807, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_209 = _this;
  _this setDir -44.742298;
  _this setPos [7667.8369, 12365.807, -4.5776367e-005];
};

_vehicle_210 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7650.2842, 12383.398, 0.00032043457], [], 0, "CAN_COLLIDE"];
  _vehicle_210 = _this;
  _this setDir -44.742298;
  _this setPos [7650.2842, 12383.398, 0.00032043457];
};

_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7632.8296, 12400.915, 0.00032806396], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setDir -44.742298;
  _this setPos [7632.8296, 12400.915, 0.00032806396];
};

_vehicle_212 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7615.2373, 12418.551, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_212 = _this;
  _this setDir -44.742298;
  _this setPos [7615.2373, 12418.551, 0.00030517578];
};

_vehicle_213 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7597.6914, 12436.189, 0.00012969971], [], 0, "CAN_COLLIDE"];
  _vehicle_213 = _this;
  _this setDir -44.742298;
  _this setPos [7597.6914, 12436.189, 0.00012969971];
};

_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7826.4761, 12208.741, 0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setDir -45.195992;
  _this setPos [7826.4761, 12208.741, 0.00033569336];
};

_vehicle_215 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7808.9946, 12226, 0.00044250488], [], 0, "CAN_COLLIDE"];
  _vehicle_215 = _this;
  _this setDir -45.195992;
  _this setPos [7808.9946, 12226, 0.00044250488];
};

_vehicle_216 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7791.3213, 12243.458, 0.00032043457], [], 0, "CAN_COLLIDE"];
  _vehicle_216 = _this;
  _this setDir -45.195992;
  _this setPos [7791.3213, 12243.458, 0.00032043457];
};

_vehicle_217 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7773.605, 12260.875, 0.00035095215], [], 0, "CAN_COLLIDE"];
  _vehicle_217 = _this;
  _this setDir -45.195992;
  _this setPos [7773.605, 12260.875, 0.00035095215];
};

_vehicle_218 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7755.9248, 12278.398, 0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_218 = _this;
  _this setDir -45.195992;
  _this setPos [7755.9248, 12278.398, 0.00025939941];
};

_vehicle_219 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7738.1821, 12295.873, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_219 = _this;
  _this setDir -45.195992;
  _this setPos [7738.1821, 12295.873, -7.6293945e-005];
};

_vehicle_220 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7720.6069, 12313.354, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_220 = _this;
  _this setDir -45.195992;
  _this setPos [7720.6069, 12313.354, 4.5776367e-005];
};

_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7703.0991, 12330.759, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setDir -45.195992;
  _this setPos [7703.0991, 12330.759, 0.00015258789];
};

_vehicle_222 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7913.4321, 12132.322, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_222 = _this;
  _this setDir -56.807484;
  _this setPos [7913.4321, 12132.322, 0.00021362305];
};

_vehicle_223 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7879.2354, 12156.695, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_223 = _this;
  _this setDir -45.145641;
  _this setPos [7879.2354, 12156.695, 6.1035156e-005];
};

_vehicle_224 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7861.7983, 12173.852, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_224 = _this;
  _this setDir -45.145641;
  _this setPos [7861.7983, 12173.852, 0.00024414063];
};

_vehicle_225 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7844.1548, 12191.25, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_225 = _this;
  _this setDir -45.145641;
  _this setPos [7844.1548, 12191.25, 7.6293945e-005];
};

_vehicle_226 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7892.7021, 12145.797, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_226 = _this;
  _this setDir -55.572968;
  _this setPos [7892.7021, 12145.797, 7.6293945e-005];
};

_vehicle_227 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [8022.1182, 12072.902, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_227 = _this;
  _this setDir -63.838432;
  _this setPos [8022.1182, 12072.902, -0.0001373291];
};

_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [8000.0879, 12083.79, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setDir -62.621239;
  _this setPos [8000.0879, 12083.79, -0.0001373291];
};

_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7978.1128, 12095.101], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setDir -61.476513;
  _this setPos [7978.1128, 12095.101];
};

_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7956.1479, 12106.99, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setDir -59.984497;
  _this setPos [7956.1479, 12106.99, -1.5258789e-005];
};

_vehicle_231 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7934.665, 12119.365, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_231 = _this;
  _this setDir -58.421211;
  _this setPos [7934.665, 12119.365, 0.00016784668];
};

_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [8135.5659, 12026.195, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setDir -70.593224;
  _this setPos [8135.5659, 12026.195, -4.5776367e-005];
};

_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [8112.2324, 12034.314, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setDir -69.200989;
  _this setPos [8112.2324, 12034.314, -4.5776367e-005];
};

_vehicle_234 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [8089.2671, 12042.957, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_234 = _this;
  _this setDir -66.94101;
  _this setPos [8089.2671, 12042.957, -7.6293945e-005];
};

_vehicle_235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [8066.5503, 12052.581, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_235 = _this;
  _this setDir -65.624626;
  _this setPos [8066.5503, 12052.581, 0.00030517578];
};

_vehicle_236 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [8043.9473, 12062.751, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_236 = _this;
  _this setDir -64.611595;
  _this setPos [8043.9473, 12062.751, 0.0002746582];
};

_vehicle_237 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [8215.4209, 12008.256, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_237 = _this;
  _this setDir -85.927162;
  _this setPos [8215.4209, 12008.256, 0.00016784668];
};

_vehicle_238 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [8182.5713, 12011.521, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_238 = _this;
  _this setDir -73.497597;
  _this setPos [8182.5713, 12011.521, 0.0001373291];
};

_vehicle_239 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [8158.9707, 12018.488, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_239 = _this;
  _this setDir -71.756195;
  _this setPos [8158.9707, 12018.488, 0.00012207031];
};

_vehicle_240 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_50", [8191.0073, 12009.916, 0.00032043457], [], 0, "CAN_COLLIDE"];
  _vehicle_240 = _this;
  _this setDir -84.223457;
  _this setPos [8191.0073, 12009.916, 0.00032043457];
};

_vehicle_241 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [8221.2764, 12007.693, 0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_241 = _this;
  _this setDir -85.095718;
  _this setPos [8221.2764, 12007.693, 0.00025939941];
};

_vehicle_245 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6615.2393, 12682.421, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_245 = _this;
  _this setDir -30.300684;
  _this setPos [6615.2393, 12682.421, 4.5776367e-005];
};

_vehicle_247 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [6618.3086, 12664.06, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_247 = _this;
  _this setDir 4.1433973;
  _this setPos [6618.3086, 12664.06, 0.00015258789];
};

_vehicle_249 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [6615.2886, 12682.557, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_249 = _this;
  _this setDir 150.20926;
  _this setPos [6615.2886, 12682.557, 0.0001373291];
};

_vehicle_250 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6589.8721, 12725.018, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_250 = _this;
  _this setDir -30.537432;
  _this setPos [6589.8721, 12725.018, 1.5258789e-005];
};

_vehicle_251 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6602.54, 12703.82, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_251 = _this;
  _this setDir -30.537432;
  _this setPos [6602.54, 12703.82, -0.00012207031];
};

_vehicle_252 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6564.6211, 12767.619, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_252 = _this;
  _this setDir -28.042685;
  _this setPos [6564.6211, 12767.619, -0.00010681152];
};

_vehicle_253 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6577.209, 12746.33, -0.00035095215], [], 0, "CAN_COLLIDE"];
  _vehicle_253 = _this;
  _this setDir -30.188862;
  _this setPos [6577.209, 12746.33, -0.00035095215];
};

_vehicle_254 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6552.9111, 12789.388, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_254 = _this;
  _this setDir -27.863947;
  _this setPos [6552.9111, 12789.388, 9.1552734e-005];
};

_vehicle_255 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6527.772, 12845.072, -0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_255 = _this;
  _this setDir -2.742233;
  _this setPos [6527.772, 12845.072, -0.00022888184];
};

_vehicle_256 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6533.0273, 12826.509, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_256 = _this;
  _this setDir -26.743467;
  _this setPos [6533.0273, 12826.509, 3.0517578e-005];
};

_vehicle_257 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [6541.0815, 12811.275, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_257 = _this;
  _this setDir -27.873491;
  _this setPos [6541.0815, 12811.275, -0.0001373291];
};

_vehicle_258 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6528.0112, 12886.842, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_258 = _this;
  _this setDir 10.153477;
  _this setPos [6528.0112, 12886.842, 6.1035156e-005];
};

_vehicle_259 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6532.4741, 12911.27, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_259 = _this;
  _this setDir 11.278483;
  _this setPos [6532.4741, 12911.27, -7.6293945e-005];
};

_vehicle_260 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6538.6543, 12952.742, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_260 = _this;
  _this setDir -1.7500015;
  _this setPos [6538.6543, 12952.742, -0.00010681152];
};

_vehicle_261 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6537.7451, 12977.574, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_261 = _this;
  _this setDir -3.8964047;
  _this setPos [6537.7451, 12977.574, -0.00010681152];
};

_vehicle_267 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6535.873, 13002.317, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_267 = _this;
  _this setDir -5.5290747;
  _this setPos [6535.873, 13002.317, -0.0001373291];
};

_vehicle_268 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6533.3887, 13027.021, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_268 = _this;
  _this setDir -7.2663217;
  _this setPos [6533.3887, 13027.021, -1.5258789e-005];
};

_vehicle_269 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6538.8101, 12953.047, -0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_269 = _this;
  _this setDir -179.79736;
  _this setPos [6538.8101, 12953.047, -0.00016784668];
};

_vehicle_270 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6523.3301, 13069.445, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_270 = _this;
  _this setDir 147.48248;
  _this setPos [6523.3301, 13069.445, 6.1035156e-005];
};

_vehicle_271 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6454.4683, 13131.255, -0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_271 = _this;
  _this setDir -46.48233;
  _this setPos [6454.4683, 13131.255, -0.00028991699];
};

_vehicle_272 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6526.4507, 12869.692, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_272 = _this;
  _this setPos [6526.4507, 12869.692, -0.00021362305];
};

_vehicle_273 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6510.3828, 13083.271, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_273 = _this;
  _this setDir 124.93884;
  _this setPos [6510.3828, 13083.271, 0.00021362305];
};

_vehicle_274 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6510.6748, 13083.049, -0.00042724609], [], 0, "CAN_COLLIDE"];
  _vehicle_274 = _this;
  _this setDir -50.308949;
  _this setPos [6510.6748, 13083.049, -0.00042724609];
};

_vehicle_275 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6491.5088, 13098.967, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_275 = _this;
  _this setDir -49.620808;
  _this setPos [6491.5088, 13098.967, 3.0517578e-005];
};

_vehicle_276 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6472.9302, 13115.001, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_276 = _this;
  _this setDir -48.394707;
  _this setPos [6472.9302, 13115.001, 7.6293945e-005];
};

_vehicle_277 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6442.8218, 13144.201, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_277 = _this;
  _this setDir -36.05513;
  _this setPos [6442.8218, 13144.201, -4.5776367e-005];
};

_vehicle_278 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6428.2476, 13164.021, -0.00032043457], [], 0, "CAN_COLLIDE"];
  _vehicle_278 = _this;
  _this setDir -38.269741;
  _this setPos [6428.2476, 13164.021, -0.00032043457];
};

_vehicle_279 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6412.8662, 13183.199, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_279 = _this;
  _this setDir -35.652473;
  _this setPos [6412.8662, 13183.199, -9.1552734e-005];
};

_vehicle_280 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6398.4805, 13202.856, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_280 = _this;
  _this setDir -28.357281;
  _this setPos [6398.4805, 13202.856, -0.00015258789];
};

_vehicle_281 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6376.0552, 13238.166, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_281 = _this;
  _this setDir 136.51379;
  _this setPos [6376.0552, 13238.166, -3.0517578e-005];
};

_vehicle_282 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6376.1353, 13237.888, -0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_282 = _this;
  _this setDir -41.369247;
  _this setPos [6376.1353, 13237.888, -0.0002746582];
};

_vehicle_288 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6349.1621, 13270.093, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_288 = _this;
  _this setDir -29.624952;
  _this setPos [6349.1621, 13270.093, 4.5776367e-005];
};

_vehicle_289 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6336.9536, 13291.812, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_289 = _this;
  _this setDir -30.0811;
  _this setPos [6336.9536, 13291.812, -0.00018310547];
};

_vehicle_290 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6282.1606, 13368.644], [], 0, "CAN_COLLIDE"];
  _vehicle_290 = _this;
  _this setDir -59.87265;
  _this setPos [6282.1606, 13368.644];
};

_vehicle_291 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6359.5166, 13256.188, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_291 = _this;
  _this setDir -401.2915;
  _this setPos [6359.5166, 13256.188, -0.00021362305];
};

_vehicle_295 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6311.0894, 13327.476, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_295 = _this;
  _this setDir 125.14985;
  _this setPos [6311.0894, 13327.476, -6.1035156e-005];
};

_vehicle_297 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [6311.4976, 13327.136, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_297 = _this;
  _this setDir -44.633808;
  _this setPos [6311.4976, 13327.136, -9.1552734e-005];
};

_vehicle_299 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6296.5732, 13355.809, -0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_299 = _this;
  _this setDir 142.7887;
  _this setPos [6296.5732, 13355.809, -0.00016784668];
};

_vehicle_300 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6282.0869, 13368.621, -0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_300 = _this;
  _this setDir 119.96844;
  _this setPos [6282.0869, 13368.621, -0.0002746582];
};

_vehicle_301 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6260.6743, 13380.856, 0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_301 = _this;
  _this setDir -60.722;
  _this setPos [6260.6743, 13380.856, 0.00025939941];
};

_vehicle_302 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6238.8911, 13392.693, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_302 = _this;
  _this setDir -61.276405;
  _this setPos [6238.8911, 13392.693, -7.6293945e-005];
};

_vehicle_308 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5706.021, 12588.526, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_308 = _this;
  _this setDir -81.005371;
  _this setPos [5706.021, 12588.526, 3.0517578e-005];
};

_vehicle_317 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5730.6831, 12585.466, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_317 = _this;
  _this setDir -82.786552;
  _this setPos [5730.6831, 12585.466, 0.00016784668];
};

_vehicle_320 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [5747.835, 12583.243, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_320 = _this;
  _this setDir -82.504875;
  _this setPos [5747.835, 12583.243, 0.00015258789];
};

_vehicle_323 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5810.8276, 12579.565, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_323 = _this;
  _this setDir -77.754601;
  _this setPos [5810.8276, 12579.565, 0.00024414063];
};

_vehicle_324 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [5766.9863, 12584.4, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_324 = _this;
  _this setDir 77.587723;
  _this setPos [5766.9863, 12584.4, 4.5776367e-005];
};

_vehicle_325 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5810.0679, 12579.826, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_325 = _this;
  _this setDir 100.02172;
  _this setPos [5810.0679, 12579.826, 7.6293945e-005];
};

_vehicle_326 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [5832.5, 12575.978, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_326 = _this;
  _this setDir 101.64538;
  _this setPos [5832.5, 12575.978, -1.5258789e-005];
};

_vehicle_347 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV2_05_ruins", [6520.3481, 12928.449, -0.32990199], [], 0, "CAN_COLLIDE"];
  _vehicle_347 = _this;
  _this setDir 6.8808117;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6520.3481, 12928.449, -0.32990199];
};

_vehicle_351 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_chimney", [6457.3501, 13100.127, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_351 = _this;
  _this setDir -47.050274;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6457.3501, 13100.127, 0.00030517578];
};

_vehicle_353 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_corner_1", [6465.4492, 13094.205, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_353 = _this;
  _this setDir 42.295597;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6465.4492, 13094.205, -3.0517578e-005];
};

_vehicle_354 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_corner_2", [6454.1514, 13105.634, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_354 = _this;
  _this setDir 49.870155;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6454.1514, 13105.634, 0.00012207031];
};

_vehicle_356 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_corner_1", [6460.1123, 13110.826, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_356 = _this;
  _this setDir -134.58772;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6460.1123, 13110.826, 3.0517578e-005];
};

_vehicle_357 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_walldoor", [6469.1011, 13105.738], [], 0, "CAN_COLLIDE"];
  _vehicle_357 = _this;
  _this setDir 43.84906;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6469.1011, 13105.738];
};

_vehicle_359 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruiny_3_prasklina", [6472.5742, 13096.849, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_359 = _this;
  _this setDir 44.336784;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6472.5742, 13096.849, -0.00024414063];
};

_vehicle_360 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruiny_3_roh", [6460.9648, 13102.538, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_360 = _this;
  _this setDir -223.62827;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6460.9648, 13102.538, 0.00010681152];
};

_vehicle_362 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruiny_3_stenazbor", [6460.1108, 13107.902, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_362 = _this;
  _this setDir -129.28241;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6460.1108, 13107.902, -1.5258789e-005];
};

_vehicle_363 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruiny_3_stena", [6461.2827, 13106.146, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_363 = _this;
  _this setDir -129.09433;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6461.2827, 13106.146, 9.1552734e-005];
};

_vehicle_367 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kopa_2", [6462.4805, 13108.043, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_367 = _this;
  _this setDir -24.547741;
  _this setPos [6462.4805, 13108.043, 1.5258789e-005];
};

_vehicle_368 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kopa_3", [6466.8936, 13098.997, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_368 = _this;
  _this setDir -139.33594;
  _this setPos [6466.8936, 13098.997, -0.00015258789];
};

_vehicle_372 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rubble_wood_girder", [6456.1187, 13109.468, 0.33043891], [], 0, "CAN_COLLIDE"];
  _vehicle_372 = _this;
  _this setDir 6.2596755;
  _this setPos [6456.1187, 13109.468, 0.33043891];
};

_vehicle_373 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_1L1_ruins", [6397.8071, 13235.628, -2.1288154], [], 0, "CAN_COLLIDE"];
  _vehicle_373 = _this;
  _this setDir -39.540382;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6397.8071, 13235.628, -2.1288154];
};

_vehicle_375 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_hiluxT", [6391.2876, 13240.892, -0.50391144], [], 0, "CAN_COLLIDE"];
  _vehicle_375 = _this;
  _this setDir 164.65903;
  _this setPos [6391.2876, 13240.892, -0.50391144];
};

_vehicle_379 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_10_75", [6385.3687, 13230.776, -0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_379 = _this;
  _this setDir 35.301849;
  _this setPos [6385.3687, 13230.776, -0.0002746582];
};

_vehicle_380 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_10_75", [6384.2212, 13231.893, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_380 = _this;
  _this setDir 35.516533;
  _this setPos [6384.2212, 13231.893, -7.6293945e-005];
};

_vehicle_381 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_6konec", [6397.2251, 13246.157, -0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_381 = _this;
  _this setDir -133.4991;
  _this setPos [6397.2251, 13246.157, -0.00025939941];
};

_vehicle_402 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_6konec", [6398.3276, 13245.024, -0.032592773], [], 0, "CAN_COLLIDE"];
  _vehicle_402 = _this;
  _this setDir -133.4991;
  _this setPos [6398.3276, 13245.024, -0.032592773];
};

_vehicle_412 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_FallenTree2", [6477.3032, 13086.988, -0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_412 = _this;
  _this setDir -11.5749;
  _this setPos [6477.3032, 13086.988, -0.00028991699];
};

_vehicle_413 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub2", [6402.4043, 13243.913, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_413 = _this;
  _this setPos [6402.4043, 13243.913, -4.5776367e-005];
};

_vehicle_420 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_1L2_ruins", [6319.9351, 13290.74, -2.0591486], [], 0, "CAN_COLLIDE"];
  _vehicle_420 = _this;
  _this setDir -122.5851;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6319.9351, 13290.74, -2.0591486];
};

_vehicle_422 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_2L_ruins", [6319.29, 13296.614, -0.67140579], [], 0, "CAN_COLLIDE"];
  _vehicle_422 = _this;
  _this setDir -30.005075;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6319.29, 13296.614, -0.67140579];
};

_vehicle_423 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rubble_bricks_02", [6317.8105, 13297.728, -1.7074919], [], 0, "CAN_COLLIDE"];
  _vehicle_423 = _this;
  _this setDir 273.70099;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6317.8105, 13297.728, -1.7074919];
};

_vehicle_446 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barn_W_02_ruins", [6029.4277, 12601.714, -0.67145133], [], 0, "CAN_COLLIDE"];
  _vehicle_446 = _this;
  _this setDir 96.765137;
  _this setPos [6029.4277, 12601.714, -0.67145133];
};

_vehicle_460 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Land_Church_05R", [5907.1812, 12568.175, -0.12676907], [], 0, "CAN_COLLIDE"];
  _vehicle_460 = _this;
  _this setDir 5.4504662;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5907.1812, 12568.175, -0.12676907];
};

_vehicle_477 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_chimney", [5975.3262, 12571.658, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_477 = _this;
  _this setDir -59.166645;
  _this setPos [5975.3262, 12571.658, 1.5258789e-005];
};

_vehicle_479 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_corner_1", [5982.8892, 12570.138, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_479 = _this;
  _this setDir 26.999878;
  _this setPos [5982.8892, 12570.138, -6.1035156e-005];
};

_vehicle_480 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_corner_2", [5970.4873, 12576.147], [], 0, "CAN_COLLIDE"];
  _vehicle_480 = _this;
  _this setDir 27.432726;
  _this setPos [5970.4873, 12576.147];
};

_vehicle_481 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_walldoor", [5980.4917, 12580.854, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_481 = _this;
  _this setDir 26.427443;
  _this setPos [5980.4917, 12580.854, -0.00015258789];
};

_vehicle_483 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_wall", [5978.7129, 12574.061, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_483 = _this;
  _this setDir -62.897293;
  _this setPos [5978.7129, 12574.061, -1.5258789e-005];
};

_vehicle_496 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_3I2_ruins", [5992.668, 12674.444, -0.59217358], [], 0, "CAN_COLLIDE"];
  _vehicle_496 = _this;
  _this setDir -76.024361;
  _this setPos [5992.668, 12674.444, -0.59217358];
};

_vehicle_497 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_Garage01_ruins", [5989.7183, 12668.716, -0.2958329], [], 0, "CAN_COLLIDE"];
  _vehicle_497 = _this;
  _this setDir 107.41418;
  _this setPos [5989.7183, 12668.716, -0.2958329];
};

_vehicle_501 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_25", [6012.0605, 12583.106, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_501 = _this;
  _this setDir 3.9268303;
  _this setPos [6012.0605, 12583.106, -9.1552734e-005];
};

_vehicle_502 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_25", [6013.4648, 12583.018, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_502 = _this;
  _this setDir 3.9268303;
  _this setPos [6013.4648, 12583.018, 0.00015258789];
};

_vehicle_503 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_25", [6013.7202, 12607.803, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_503 = _this;
  _this setPos [6013.7202, 12607.803, 3.0517578e-005];
};

_vehicle_504 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_25", [6015.1108, 12607.809, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_504 = _this;
  _this setPos [6015.1108, 12607.809, -7.6293945e-005];
};

_vehicle_505 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_30_25", [6011.4575, 12645.158, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_505 = _this;
  _this setDir 148.31212;
  _this setPos [6011.4575, 12645.158, -0.00010681152];
};

_vehicle_506 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_30_25", [6010.0791, 12644.404, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_506 = _this;
  _this setDir 148.31212;
  _this setPos [6010.0791, 12644.404, -0.00018310547];
};

_vehicle_509 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_30_25", [6000.7896, 12652.228, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_509 = _this;
  _this setDir 116.28421;
  _this setPos [6000.7896, 12652.228, 6.1035156e-005];
};

_vehicle_510 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_30_25", [6001.6323, 12653.466, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_510 = _this;
  _this setDir 115.23952;
  _this setPos [6001.6323, 12653.466, 3.0517578e-005];
};

_vehicle_511 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_0_2000", [6000.9199, 12652.112, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_511 = _this;
  _this setDir -58.14537;
  _this setPos [6000.9199, 12652.112, 0.0001373291];
};

_vehicle_512 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_0_2000", [6001.666, 12653.342, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_512 = _this;
  _this setDir -58.14537;
  _this setPos [6001.666, 12653.342, -4.5776367e-005];
};

_vehicle_514 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_6konec", [5980.9736, 12664.826, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_514 = _this;
  _this setDir 123.35094;
  _this setPos [5980.9736, 12664.826, -3.0517578e-005];
};

_vehicle_515 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_path_6konec", [5981.751, 12665.967], [], 0, "CAN_COLLIDE"];
  _vehicle_515 = _this;
  _this setDir 123.35094;
  _this setPos [5981.751, 12665.967];
};

_vehicle_519 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_corner_1", [5991.9185, 12677.963, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_519 = _this;
  _this setDir -167.4872;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5991.9185, 12677.963, -3.0517578e-005];
};

_vehicle_520 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_corner_2", [5993.3022, 12668.469, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_520 = _this;
  _this setDir -73.466293;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5993.3022, 12668.469, 4.5776367e-005];
};

_vehicle_523 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6174.7188, 13610.46, -0.00079345703], [], 0, "CAN_COLLIDE"];
  _vehicle_523 = _this;
  _this setDir 59.74963;
  _this setPos [6174.7188, 13610.46, -0.00079345703];
};

_vehicle_525 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6196.1748, 13623.117, -0.00079345703], [], 0, "CAN_COLLIDE"];
  _vehicle_525 = _this;
  _this setDir 59.74963;
  _this setPos [6196.1748, 13623.117, -0.00079345703];
};

_vehicle_526 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6217.6074, 13635.742, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_526 = _this;
  _this setDir 59.74963;
  _this setPos [6217.6074, 13635.742, -9.1552734e-005];
};

_vehicle_527 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6238.8906, 13648.355, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_527 = _this;
  _this setDir 59.74963;
  _this setPos [6238.8906, 13648.355, -9.1552734e-005];
};

_vehicle_528 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6216.96, 13404.495, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_528 = _this;
  _this setDir -61.389015;
  _this setPos [6216.96, 13404.495, -6.1035156e-005];
};

_vehicle_529 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6202.4971, 13413.965, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_529 = _this;
  _this setDir -51.626015;
  _this setPos [6202.4971, 13413.965, 0.0001373291];
};

_vehicle_530 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6189.8252, 13425.799, -0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_530 = _this;
  _this setDir -40.628254;
  _this setPos [6189.8252, 13425.799, -0.00016784668];
};

_vehicle_532 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6179.6333, 13439.868, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_532 = _this;
  _this setDir -30.842718;
  _this setPos [6179.6333, 13439.868, -0.00012207031];
};

_vehicle_547 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6120.3105, 13540.082, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_547 = _this;
  _this setDir -22.093256;
  _this setPos [6120.3105, 13540.082, -0.00021362305];
};

_vehicle_549 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6128.0547, 13524.636, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_549 = _this;
  _this setDir -31.49202;
  _this setPos [6128.0547, 13524.636, -9.1552734e-005];
};

_vehicle_551 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6174.7451, 13610.508, -0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_551 = _this;
  _this setDir -120.22861;
  _this setPos [6174.7451, 13610.508, -0.00030517578];
};

_vehicle_552 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6156.5435, 13604.335, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_552 = _this;
  _this setDir -97.36689;
  _this setPos [6156.5435, 13604.335, 0.00024414063];
};

_vehicle_553 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6137.4341, 13605.663], [], 0, "CAN_COLLIDE"];
  _vehicle_553 = _this;
  _this setDir -72.484665;
  _this setPos [6137.4341, 13605.663];
};

_vehicle_555 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6120.4614, 13614.995, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_555 = _this;
  _this setDir -50.152626;
  _this setPos [6120.4614, 13614.995, -9.1552734e-005];
};

_vehicle_556 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6108.1216, 13627.072, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_556 = _this;
  _this setDir -40.000149;
  _this setPos [6108.1216, 13627.072, 6.1035156e-005];
};

_vehicle_557 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6098.1167, 13641.047, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_557 = _this;
  _this setDir -30.428371;
  _this setPos [6098.1167, 13641.047, -0.00015258789];
};

_vehicle_558 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6085.4292, 13662.428, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_558 = _this;
  _this setDir -30.428371;
  _this setPos [6085.4292, 13662.428, -0.00018310547];
};

_vehicle_559 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6072.687, 13683.813, -0.00039672852], [], 0, "CAN_COLLIDE"];
  _vehicle_559 = _this;
  _this setDir -30.428371;
  _this setPos [6072.687, 13683.813, -0.00039672852];
};

_vehicle_560 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6060.0601, 13705.1, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_560 = _this;
  _this setDir -30.428371;
  _this setPos [6060.0601, 13705.1, -0.00015258789];
};

_vehicle_561 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6047.5, 13726.37, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_561 = _this;
  _this setDir -28.354479;
  _this setPos [6047.5, 13726.37, 6.1035156e-005];
};

_vehicle_562 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6035.6797, 13747.893, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_562 = _this;
  _this setDir -25.23753;
  _this setPos [6035.6797, 13747.893, -0.00018310547];
};

_vehicle_563 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6025.0127, 13770.236, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_563 = _this;
  _this setDir -22.510456;
  _this setPos [6025.0127, 13770.236, -3.0517578e-005];
};

_vehicle_564 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6010.3188, 13809.688], [], 0, "CAN_COLLIDE"];
  _vehicle_564 = _this;
  _this setDir -10.604297;
  _this setPos [6010.3188, 13809.688];
};

_vehicle_565 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6015.3833, 13793.194, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_565 = _this;
  _this setDir -21.753588;
  _this setPos [6015.3833, 13793.194, -9.1552734e-005];
};

_vehicle_566 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [6004.7915, 13839.852, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_566 = _this;
  _this setDir -189.85698;
  _this setPos [6004.7915, 13839.852, 3.0517578e-005];
};

_vehicle_567 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6166.8672, 13461.052, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_567 = _this;
  _this setDir -30.904984;
  _this setPos [6166.8672, 13461.052, 3.0517578e-005];
};

_vehicle_568 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6153.9922, 13482.365, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_568 = _this;
  _this setDir -31.313211;
  _this setPos [6153.9922, 13482.365, -3.0517578e-005];
};

_vehicle_569 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6140.9751, 13503.384, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_569 = _this;
  _this setDir -31.168077;
  _this setPos [6140.9751, 13503.384, 0.00012207031];
};

_vehicle_572 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6116.771, 13559.089, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_572 = _this;
  _this setDir 0.34776366;
  _this setPos [6116.771, 13559.089, -9.1552734e-005];
};

_vehicle_573 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6118.3096, 13576.319, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_573 = _this;
  _this setDir 10.561463;
  _this setPos [6118.3096, 13576.319, -9.1552734e-005];
};

_vehicle_576 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [6122.8594, 13593.05, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_576 = _this;
  _this setDir 20.260244;
  _this setPos [6122.8594, 13593.05, -0.00015258789];
};

_vehicle_577 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [6130.9775, 13609.752, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_577 = _this;
  _this setDir -151.84534;
  _this setPos [6130.9775, 13609.752, 9.1552734e-005];
};

_vehicle_579 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6260.2344, 13660.958, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_579 = _this;
  _this setDir 62.439011;
  _this setPos [6260.2344, 13660.958, 6.1035156e-005];
};

_vehicle_580 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6282.1206, 13672.43, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_580 = _this;
  _this setDir 64.700844;
  _this setPos [6282.1206, 13672.43, -0.00021362305];
};

_vehicle_581 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6304.3486, 13683.115, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_581 = _this;
  _this setDir 66.787476;
  _this setPos [6304.3486, 13683.115, -3.0517578e-005];
};

_vehicle_582 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6327.0747, 13692.987, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_582 = _this;
  _this setDir 69.402397;
  _this setPos [6327.0747, 13692.987, -0.00018310547];
};

_vehicle_583 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6349.8491, 13701.774, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_583 = _this;
  _this setDir 71.215683;
  _this setPos [6349.8491, 13701.774, 6.1035156e-005];
};

_vehicle_584 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6373.3447, 13709.917, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_584 = _this;
  _this setDir 69.65107;
  _this setPos [6373.3447, 13709.917, -6.1035156e-005];
};

_vehicle_585 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6471.9712, 13754.939, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_585 = _this;
  _this setDir -133.86998;
  _this setPos [6471.9712, 13754.939, 6.1035156e-005];
};

_vehicle_586 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6458.583, 13744.114, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_586 = _this;
  _this setDir -121.76985;
  _this setPos [6458.583, 13744.114, -6.1035156e-005];
};

_vehicle_587 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6490.9155, 13783.115, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_587 = _this;
  _this setDir -156.27567;
  _this setPos [6490.9155, 13783.115, 3.0517578e-005];
};

_vehicle_588 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6482.9614, 13768.291], [], 0, "CAN_COLLIDE"];
  _vehicle_588 = _this;
  _this setDir -145.0965;
  _this setPos [6482.9614, 13768.291];
};

_vehicle_590 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6396.6499, 13718.535], [], 0, "CAN_COLLIDE"];
  _vehicle_590 = _this;
  _this setDir 69.433464;
  _this setPos [6396.6499, 13718.535];
};

_vehicle_591 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6419.9238, 13727.399, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_591 = _this;
  _this setDir 69.433464;
  _this setPos [6419.9238, 13727.399, 6.1035156e-005];
};

_vehicle_604 = objNull;
if (true) then
{
  _this = createVehicle ["dum_rasovna", [7908.3184, 12097.268, 0.40999404], [], 0, "CAN_COLLIDE"];
  _vehicle_604 = _this;
  _this setDir -143.16446;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7908.3184, 12097.268, 0.40999404];
};

_vehicle_610 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1L2", [7926.3745, 12068.574, -0.12406424], [], 0, "CAN_COLLIDE"];
  _vehicle_610 = _this;
  _this setDir -306.98117;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7926.3745, 12068.574, -0.12406424];
};

_vehicle_617 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stodola_old_open", [7880.9634, 12054.408, 0.3870123], [], 0, "CAN_COLLIDE"];
  _vehicle_617 = _this;
  _this setDir -59.455048;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7880.9634, 12054.408, 0.3870123];
};

_vehicle_618 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7940.6841, 12117.927, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_618 = _this;
  _this setDir -147.34251;
  _this setPos [7940.6841, 12117.927, 0.0001373291];
};

_vehicle_621 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_0_2000", [7928.1616, 12098.308, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_621 = _this;
  _this setDir 32.237507;
  _this setPos [7928.1616, 12098.308, 9.1552734e-005];
};

_vehicle_623 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_30_25", [7924.8335, 12085.877, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_623 = _this;
  _this setPos [7924.8335, 12085.877, 0.00022888184];
};

_vehicle_624 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_30_25", [7928.1958, 12073.498, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_624 = _this;
  _this setDir -30.050558;
  _this setPos [7928.1958, 12073.498, 0.00016784668];
};

_vehicle_625 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7931.6709, 12068.484, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_625 = _this;
  _this setDir -35.596027;
  _this setPos [7931.6709, 12068.484, 0.00010681152];
};

_vehicle_626 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7928.1113, 12098.185, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_626 = _this;
  _this setDir -152.05943;
  _this setPos [7928.1113, 12098.185, 3.0517578e-005];
};

_vehicle_627 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7918.6846, 12083.613, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_627 = _this;
  _this setDir -140.88429;
  _this setPos [7918.6846, 12083.613, 0.00016784668];
};

_vehicle_629 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_30_25", [7906.5649, 12071.111, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_629 = _this;
  _this setDir -130.06578;
  _this setPos [7906.5649, 12071.111, 7.6293945e-005];
};

_vehicle_630 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7888.6982, 12064.646, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_630 = _this;
  _this setDir 80.638985;
  _this setPos [7888.6982, 12064.646, -6.1035156e-005];
};

_vehicle_631 = objNull;
if (true) then
{
  _this = createVehicle ["RampConcrete", [7891.7637, 12057.229, -0.35323882], [], 0, "CAN_COLLIDE"];
  _vehicle_631 = _this;
  _this setDir 119.75028;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7891.7637, 12057.229, -0.35323882];
};

_vehicle_632 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7883.5991, 12060.991, -0.50529468], [], 0, "CAN_COLLIDE"];
  _vehicle_632 = _this;
  _this setDir 30.610456;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7883.5991, 12060.991, -0.50529468];
};

_vehicle_633 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [7919.1719, 12062.108, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_633 = _this;
  _this setDir -142.05858;
  _this setPos [7919.1719, 12062.108, 0.00012207031];
};

_vehicle_634 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7918.5884, 12062.394, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_634 = _this;
  _this setPos [7918.5884, 12062.394, 7.6293945e-005];
};

_vehicle_635 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7879.377, 12064.321, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_635 = _this;
  _this setDir 27.801258;
  _this setPos [7879.377, 12064.321, 0.00022888184];
};

_vehicle_636 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7880.3877, 12065.83, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_636 = _this;
  _this setDir 31.601162;
  _this setPos [7880.3877, 12065.83, 0.00015258789];
};

_vehicle_637 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7878.1201, 12065.057, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_637 = _this;
  _this setDir 27.933014;
  _this setPos [7878.1201, 12065.057, 0.00030517578];
};

_vehicle_638 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7878.9834, 12066.567, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_638 = _this;
  _this setDir 27.546534;
  _this setPos [7878.9834, 12066.567, 0.00016784668];
};

_vehicle_639 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7877.2607, 12066.929, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_639 = _this;
  _this setPos [7877.2607, 12066.929, 0.0002746582];
};

_vehicle_640 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7879.1709, 12065.253, 1.2634079], [], 0, "CAN_COLLIDE"];
  _vehicle_640 = _this;
  _this setDir 30.179138;
  _this setPos [7879.1709, 12065.253, 1.2634079];
};

_vehicle_641 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [7882.9536, 12066.545, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_641 = _this;
  _this setDir -140.71812;
  _this setPos [7882.9536, 12066.545, 4.5776367e-005];
};

_vehicle_642 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [7884.1411, 12065.693, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_642 = _this;
  _this setDir 37.255844;
  _this setPos [7884.1411, 12065.693, 0.00018310547];
};

_vehicle_643 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [7885.8477, 12064.782, -0.70531946], [], 0, "CAN_COLLIDE"];
  _vehicle_643 = _this;
  _this setDir 13.015587;
  _this setPos [7885.8477, 12064.782, -0.70531946];
};

_vehicle_644 = objNull;
if (true) then
{
  _this = createVehicle ["Land_bags_stack_EP1", [7882.3262, 12046.925, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_644 = _this;
  _this setPos [7882.3262, 12046.925, 0.00016784668];
};

_vehicle_645 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Wheel_cart_EP1", [7884.6196, 12044.591, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_645 = _this;
  _this setDir -33.437572;
  _this setPos [7884.6196, 12044.591, 3.0517578e-005];
};

_vehicle_646 = objNull;
if (true) then
{
  _this = createVehicle ["Axe_woodblock", [7923.7373, 12060.055, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_646 = _this;
  _this setPos [7923.7373, 12060.055, 0.00012207031];
};

_vehicle_647 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_crates_EP1", [7874.3433, 12052.08, -0.76272875], [], 0, "CAN_COLLIDE"];
  _vehicle_647 = _this;
  _this setDir 28.220533;
  _this setPos [7874.3433, 12052.08, -0.76272875];
};

_vehicle_648 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_crates_EP1", [7872.4282, 12053.045, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_648 = _this;
  _this setDir 27.330778;
  _this setPos [7872.4282, 12053.045, 0.00016784668];
};

_vehicle_649 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_crates_EP1", [7873.4121, 12052.567, 0.79669845], [], 0, "CAN_COLLIDE"];
  _vehicle_649 = _this;
  _this setDir -149.53181;
  _this setPos [7873.4121, 12052.567, 0.79669845];
};

_vehicle_652 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [7923.8721, 12060.686, 0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_652 = _this;
  _this setDir -127.42853;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7923.8721, 12060.686, 0.00028991699];
};

_vehicle_674 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7901.3481, 12089.194, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_674 = _this;
  _this setPos [7901.3481, 12089.194, 0.00024414063];
};

_vehicle_675 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin2", [7899.4629, 12087.01, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_675 = _this;
  _this setPos [7899.4629, 12087.01, 9.1552734e-005];
};

_vehicle_676 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7900.1616, 12086.605, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_676 = _this;
  _this setPos [7900.1616, 12086.605, 0.00015258789];
};

_vehicle_677 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7901.1758, 12086.226, 0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_677 = _this;
  _this setPos [7901.1758, 12086.226, 0.00033569336];
};

_vehicle_678 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7903.0278, 12088.373, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_678 = _this;
  _this setPos [7903.0278, 12088.373, 0.00030517578];
};

_vehicle_679 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7902.1416, 12084.869, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_679 = _this;
  _this setPos [7902.1416, 12084.869, 0.00010681152];
};

_vehicle_681 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7903.292, 12086.75, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_681 = _this;
  _this setPos [7903.292, 12086.75, 0.00015258789];
};

_vehicle_683 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7909.6782, 12097.749, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_683 = _this;
  _this setDir 94.664612;
  _this setPos [7909.6782, 12097.749, 0.00016784668];
};

_vehicle_684 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7907.9697, 12098.415, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_684 = _this;
  _this setDir -109.55701;
  _this setPos [7907.9697, 12098.415, 0.0001373291];
};

_vehicle_685 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7906.5835, 12099.563, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_685 = _this;
  _this setDir -102.90317;
  _this setPos [7906.5835, 12099.563, 0.00012207031];
};

_vehicle_686 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2_noRoad", [7908.6382, 12100.342, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_686 = _this;
  _this setDir 130.31346;
  _this setPos [7908.6382, 12100.342, 0.00021362305];
};

_vehicle_687 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7908.373, 12091.88, -1.3991482], [], 0, "CAN_COLLIDE"];
  _vehicle_687 = _this;
  _this setDir -53.077576;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7908.373, 12091.88, -1.3991482];
};

_vehicle_688 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7907.4956, 12101.389, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_688 = _this;
  _this setPos [7907.4956, 12101.389, 0.0001373291];
};

_vehicle_689 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7908.5864, 12102.587, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_689 = _this;
  _this setDir -70.813431;
  _this setPos [7908.5864, 12102.587, 0.00022888184];
};

_vehicle_690 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7910.4536, 12101.597, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_690 = _this;
  _this setDir 101.16373;
  _this setPos [7910.4536, 12101.597, 0.00024414063];
};

_vehicle_691 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7911.4712, 12100.603, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_691 = _this;
  _this setPos [7911.4712, 12100.603, 3.0517578e-005];
};

_vehicle_692 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7908.3203, 12100.517, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_692 = _this;
  _this setPos [7908.3203, 12100.517, 7.6293945e-005];
};

_vehicle_693 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2w", [7919.4756, 12076.489, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_693 = _this;
  _this setPos [7919.4756, 12076.489, 0.00016784668];
};

_vehicle_695 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2W", [7919.373, 12112.787], [], 0, "CAN_COLLIDE"];
  _vehicle_695 = _this;
  _this setPos [7919.373, 12112.787];
};

_vehicle_697 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7945.374, 12091.453, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_697 = _this;
  _this setDir -102.11398;
  _this setPos [7945.374, 12091.453, 0.00018310547];
};

_vehicle_698 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7945.5918, 12083.234, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_698 = _this;
  _this setDir 35.116123;
  _this setPos [7945.5918, 12083.234, 3.0517578e-005];
};

_vehicle_699 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7952.5313, 12088.776, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_699 = _this;
  _this setDir -17.335356;
  _this setPos [7952.5313, 12088.776, -1.5258789e-005];
};

_vehicle_700 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [7887.4546, 12101.87, -3.2710981], [], 0, "CAN_COLLIDE"];
  _vehicle_700 = _this;
  _this setPos [7887.4546, 12101.87, -3.2710981];
};

_vehicle_701 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [7896.5234, 12107.769, -0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_701 = _this;
  _this setPos [7896.5234, 12107.769, -0.00016784668];
};

_vehicle_702 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [7894.4556, 12118.676, -1.8035202], [], 0, "CAN_COLLIDE"];
  _vehicle_702 = _this;
  _this setDir -165.87878;
  _this setPos [7894.4556, 12118.676, -1.8035202];
};

_vehicle_739 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1I4", [7541.7026, 12523.39, 0.36397985], [], 0, "CAN_COLLIDE"];
  _vehicle_739 = _this;
  _this setDir 120.72939;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7541.7026, 12523.39, 0.36397985];
};

_vehicle_740 = objNull;
if (true) then
{
  _this = createVehicle ["Land_bouda2_vnitrek", [7560.0347, 12542.06, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_740 = _this;
  _this setDir 132.13287;
  _this setPos [7560.0347, 12542.06, -7.6293945e-006];
};

_vehicle_742 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7537.7944, 12496.488, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_742 = _this;
  _this setDir 41.383896;
  _this setPos [7537.7944, 12496.488, -7.6293945e-005];
};

_vehicle_743 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7552.1421, 12515.198, 0.00017547607], [], 0, "CAN_COLLIDE"];
  _vehicle_743 = _this;
  _this setDir -148.87268;
  _this setPos [7552.1421, 12515.198, 0.00017547607];
};

_vehicle_744 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7555.2466, 12520.519, -5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_744 = _this;
  _this setDir 210.37816;
  _this setPos [7555.2466, 12520.519, -5.3405762e-005];
};

_vehicle_745 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_WellPump", [7532.104, 12520.248, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_745 = _this;
  _this setDir 49.288486;
  _this setPos [7532.104, 12520.248, 0.00024414063];
};

_vehicle_747 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pneu", [7545.3804, 12517.921, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_747 = _this;
  _this setPos [7545.3804, 12517.921, 0.00016784668];
};

_vehicle_751 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_P_bedna", [7548.584, 12523.434, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_751 = _this;
  _this setDir -120.46432;
  _this setPos [7548.584, 12523.434, 9.1552734e-005];
};

_vehicle_752 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_P_bedna", [7549.2417, 12523.548, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_752 = _this;
  _this setDir 60.582584;
  _this setPos [7549.2417, 12523.548, 0.00012207031];
};

_vehicle_753 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_P_bedna", [7548.9028, 12523.549, 0.40977529], [], 0, "CAN_COLLIDE"];
  _vehicle_753 = _this;
  _this setDir 47.800613;
  _this setPos [7548.9028, 12523.549, 0.40977529];
};

_vehicle_754 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pneu", [7546.2896, 12517.278, 0.00012969971], [], 0, "CAN_COLLIDE"];
  _vehicle_754 = _this;
  _this setPos [7546.2896, 12517.278, 0.00012969971];
};

_vehicle_755 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pneu", [7545.2061, 12518.007, 0.23928858], [], 0, "CAN_COLLIDE"];
  _vehicle_755 = _this;
  _this setPos [7545.2061, 12518.007, 0.23928858];
};

_vehicle_756 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pneu", [7546.2559, 12517.375, 0.22789831], [], 0, "CAN_COLLIDE"];
  _vehicle_756 = _this;
  _this setPos [7546.2559, 12517.375, 0.22789831];
};

_vehicle_760 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_2L", [7116.8477, 12701.366, 0.081392907], [], 0, "CAN_COLLIDE"];
  _vehicle_760 = _this;
  _this setDir 238.56129;
  _this setPos [7116.8477, 12701.366, 0.081392907];
};

_vehicle_762 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7135.6489, 12684.45, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_762 = _this;
  _this setDir -21.098465;
  _this setPos [7135.6489, 12684.45, 0.00012207031];
};

_vehicle_763 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7124.7905, 12705.196, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_763 = _this;
  _this setDir 145.14984;
  _this setPos [7124.7905, 12705.196, 6.8664551e-005];
};

_vehicle_764 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7121.373, 12710.295, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_764 = _this;
  _this setDir -213.63087;
  _this setPos [7121.373, 12710.295, 3.8146973e-005];
};

_vehicle_766 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_6konec", [7309.5938, 12686.352, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_766 = _this;
  _this setDir 70.172104;
  _this setPos [7309.5938, 12686.352, -1.5258789e-005];
};

_vehicle_768 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [7315.4038, 12688.429, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_768 = _this;
  _this setDir 69.993958;
  _this setPos [7315.4038, 12688.429, -1.5258789e-005];
};

_vehicle_770 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_22_50", [7332.1304, 12692.975, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_770 = _this;
  _this setDir 79.384377;
  _this setPos [7332.1304, 12692.975, -3.0517578e-005];
};

_vehicle_771 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [7351.5107, 12692.813, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_771 = _this;
  _this setDir 102.15769;
  _this setPos [7351.5107, 12692.813, 6.8664551e-005];
};

_vehicle_772 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_25", [7367.9282, 12687.695, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_772 = _this;
  _this setDir 109.81522;
  _this setPos [7367.9282, 12687.695, 4.5776367e-005];
};

_vehicle_773 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_25", [7408.1826, 12674.923, 9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_773 = _this;
  _this setDir 100.8882;
  _this setPos [7408.1826, 12674.923, 9.9182129e-005];
};

_vehicle_774 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [7408.2583, 12674.84, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_774 = _this;
  _this setDir -79.845955;
  _this setPos [7408.2583, 12674.84, 6.8664551e-005];
};

_vehicle_776 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [7432.4868, 12670.17, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_776 = _this;
  _this setDir 99.53289;
  _this setPos [7432.4868, 12670.17, -6.1035156e-005];
};

_vehicle_777 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_6konec", [7455.2036, 12663.649, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_777 = _this;
  _this setDir -70.383865;
  _this setPos [7455.2036, 12663.649, -6.1035156e-005];
};

_vehicle_778 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Timbers", [7407.1221, 12686.379, 0.093738683], [], 0, "CAN_COLLIDE"];
  _vehicle_778 = _this;
  _this setDir 96.655716;
  _this setPos [7407.1221, 12686.379, 0.093738683];
};

_vehicle_779 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Timbers", [7433.2744, 12663.135, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_779 = _this;
  _this setDir -78.939217;
  _this setPos [7433.2744, 12663.135, 4.5776367e-005];
};

_vehicle_780 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Timbers", [7433.8252, 12664.998, 0.045600418], [], 0, "CAN_COLLIDE"];
  _vehicle_780 = _this;
  _this setDir -76.983902;
  _this setPos [7433.8252, 12664.998, 0.045600418];
};

_vehicle_781 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Timbers", [7345.5581, 12704.465, 0.046645679], [], 0, "CAN_COLLIDE"];
  _vehicle_781 = _this;
  _this setDir -92.241516;
  _this setPos [7345.5581, 12704.465, 0.046645679];
};

_vehicle_782 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Timbers", [7440.6436, 12680.482, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_782 = _this;
  _this setDir 95.204575;
  _this setPos [7440.6436, 12680.482, 5.3405762e-005];
};

_vehicle_784 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7383.0649, 12691.03], [], 0, "CAN_COLLIDE"];
  _vehicle_784 = _this;
  _this setDir -47.650299;
  _this setPos [7383.0649, 12691.03];
};

_vehicle_785 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7405.8271, 12696.212, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_785 = _this;
  _this setPos [7405.8271, 12696.212, -4.5776367e-005];
};

_vehicle_786 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7393.7554, 12687.942, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_786 = _this;
  _this setDir -136.56184;
  _this setPos [7393.7554, 12687.942, -2.2888184e-005];
};

_vehicle_787 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7375.1499, 12712.804], [], 0, "CAN_COLLIDE"];
  _vehicle_787 = _this;
  _this setDir -38.74894;
  _this setPos [7375.1499, 12712.804];
};

_vehicle_788 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7359.9048, 12702.717, -5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_788 = _this;
  _this setDir 102.685;
  _this setPos [7359.9048, 12702.717, -5.3405762e-005];
};

_vehicle_789 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7340.6729, 12715.242, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_789 = _this;
  _this setPos [7340.6729, 12715.242, -7.6293945e-005];
};

_vehicle_790 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7340.6079, 12703.401, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_790 = _this;
  _this setPos [7340.6079, 12703.401, -2.2888184e-005];
};

_vehicle_791 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7333.0825, 12689.521, -0.19318305], [], 0, "CAN_COLLIDE"];
  _vehicle_791 = _this;
  _this setPos [7333.0825, 12689.521, -0.19318305];
};

_vehicle_792 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7329.2549, 12699.843, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_792 = _this;
  _this setDir -25.941328;
  _this setPos [7329.2549, 12699.843, -1.5258789e-005];
};

_vehicle_793 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7324.082, 12705.74, -3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_793 = _this;
  _this setPos [7324.082, 12705.74, -3.8146973e-005];
};

_vehicle_794 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7312.7593, 12715.841, -6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_794 = _this;
  _this setDir 74.447327;
  _this setPos [7312.7593, 12715.841, -6.8664551e-005];
};

_vehicle_795 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7303.2778, 12707.885, -5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_795 = _this;
  _this setDir -4.8278809;
  _this setPos [7303.2778, 12707.885, -5.3405762e-005];
};

_vehicle_796 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7308.791, 12698.841, -3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_796 = _this;
  _this setPos [7308.791, 12698.841, -3.8146973e-005];
};

_vehicle_797 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7314.2471, 12728.625, -5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_797 = _this;
  _this setPos [7314.2471, 12728.625, -5.3405762e-005];
};

_vehicle_798 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7354.5986, 12711.714, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_798 = _this;
  _this setPos [7354.5986, 12711.714, -7.6293945e-005];
};

_vehicle_799 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7351.1978, 12678.576, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_799 = _this;
  _this setPos [7351.1978, 12678.576, 0];
};

_vehicle_800 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7332.8965, 12676.002, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_800 = _this;
  _this setPos [7332.8965, 12676.002, 6.8664551e-005];
};

_vehicle_801 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7323.8906, 12687.194, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_801 = _this;
  _this setPos [7323.8906, 12687.194, -3.0517578e-005];
};

_vehicle_802 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7347.584, 12668.498, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_802 = _this;
  _this setDir -25.219971;
  _this setPos [7347.584, 12668.498, 2.2888184e-005];
};

_vehicle_803 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7341.8203, 12675.549, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_803 = _this;
  _this setPos [7341.8203, 12675.549, 2.2888184e-005];
};

_vehicle_804 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7371.2285, 12668.393, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_804 = _this;
  _this setPos [7371.2285, 12668.393, 3.8146973e-005];
};

_vehicle_805 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7355.1816, 12662.899, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_805 = _this;
  _this setDir 29.369335;
  _this setPos [7355.1816, 12662.899, 1.5258789e-005];
};

_vehicle_806 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7416.1152, 12661.299, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_806 = _this;
  _this setPos [7416.1152, 12661.299, -7.6293945e-006];
};

_vehicle_807 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7423.543, 12666.204, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_807 = _this;
  _this setPos [7423.543, 12666.204, 4.5776367e-005];
};

_vehicle_808 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7420.9517, 12677.491, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_808 = _this;
  _this setDir -92.648651;
  _this setPos [7420.9517, 12677.491, 5.3405762e-005];
};

_vehicle_809 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7409.4165, 12681.403, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_809 = _this;
  _this setDir -118.2382;
  _this setPos [7409.4165, 12681.403, -7.6293945e-006];
};

_vehicle_810 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7421.2969, 12698.707, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_810 = _this;
  _this setDir 67.752991;
  _this setPos [7421.2969, 12698.707, -7.6293945e-006];
};

_vehicle_811 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7431.1064, 12692.663, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_811 = _this;
  _this setPos [7431.1064, 12692.663, -2.2888184e-005];
};

_vehicle_812 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7451.6807, 12651.654, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_812 = _this;
  _this setPos [7451.6807, 12651.654, -1.5258789e-005];
};

_vehicle_813 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7444.4146, 12654.843, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_813 = _this;
  _this setDir 23.770739;
  _this setPos [7444.4146, 12654.843, -2.2888184e-005];
};

_vehicle_814 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7448.6685, 12669.862, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_814 = _this;
  _this setDir -96.201759;
  _this setPos [7448.6685, 12669.862, -4.5776367e-005];
};

_vehicle_815 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7464.2954, 12655.168, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_815 = _this;
  _this setDir -166.0014;
  _this setPos [7464.2954, 12655.168, 1.5258789e-005];
};

_vehicle_816 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7473.6216, 12646.974, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_816 = _this;
  _this setPos [7473.6216, 12646.974, 3.0517578e-005];
};

_vehicle_817 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7459.2773, 12647.058, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_817 = _this;
  _this setPos [7459.2773, 12647.058, 2.2888184e-005];
};

_vehicle_818 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7471.2544, 12660.911, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_818 = _this;
  _this setPos [7471.2544, 12660.911, -1.5258789e-005];
};

_vehicle_819 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7460.2256, 12671.007, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_819 = _this;
  _this setDir 54.661251;
  _this setPos [7460.2256, 12671.007, 3.8146973e-005];
};

_vehicle_820 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7450.6816, 12684.008, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_820 = _this;
  _this setPos [7450.6816, 12684.008, -6.1035156e-005];
};

_vehicle_821 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7400.6563, 12673.527], [], 0, "CAN_COLLIDE"];
  _vehicle_821 = _this;
  _this setDir -7.7448096;
  _this setPos [7400.6563, 12673.527];
};

_vehicle_822 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7398.0381, 12660.974], [], 0, "CAN_COLLIDE"];
  _vehicle_822 = _this;
  _this setDir 44.761646;
  _this setPos [7398.0381, 12660.974];
};

_vehicle_823 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7387.2451, 12666.45, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_823 = _this;
  _this setPos [7387.2451, 12666.45, -1.5258789e-005];
};

_vehicle_824 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_stub1", [7381.9023, 12678.842, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_824 = _this;
  _this setDir 185.39917;
  _this setPos [7381.9023, 12678.842, -4.5776367e-005];
};

_vehicle_825 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_crates_EP1", [7115.6792, 12706.531, -0.74704027], [], 0, "CAN_COLLIDE"];
  _vehicle_825 = _this;
  _this setDir -95.845161;
  _this setPos [7115.6792, 12706.531, -0.74704027];
};

_vehicle_829 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7898.4546, 12090.521, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_829 = _this;
  _this setDir 127.08426;
  _this setPos [7898.4546, 12090.521, 0.00015258789];
};

_vehicle_830 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [7896.9194, 12088.508, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_830 = _this;
  _this setDir 127.34264;
  _this setPos [7896.9194, 12088.508, 0.0002746582];
};

_vehicle_832 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7897.1631, 12086.746, 0.00032043457], [], 0, "CAN_COLLIDE"];
  _vehicle_832 = _this;
  _this setDir 37.001583;
  _this setPos [7897.1631, 12086.746, 0.00032043457];
};

_vehicle_833 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7899.2212, 12085.239, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_833 = _this;
  _this setDir 36.863873;
  _this setPos [7899.2212, 12085.239, 9.1552734e-005];
};

_vehicle_834 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7901.2661, 12083.71, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_834 = _this;
  _this setDir 36.697521;
  _this setPos [7901.2661, 12083.71, 0.0001373291];
};

_vehicle_835 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7903.3271, 12082.212, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_835 = _this;
  _this setDir 36.538548;
  _this setPos [7903.3271, 12082.212, 0.00018310547];
};

_vehicle_836 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7905.3672, 12080.682, 0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_836 = _this;
  _this setDir 36.861874;
  _this setPos [7905.3672, 12080.682, 0.00033569336];
};

_vehicle_837 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7913.6392, 12100.215, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_837 = _this;
  _this setDir -143.25352;
  _this setPos [7913.6392, 12100.215, 0.00024414063];
};

_vehicle_838 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7906.2344, 12100.922, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_838 = _this;
  _this setDir 127.0224;
  _this setPos [7906.2344, 12100.922, 0.00016784668];
};

_vehicle_839 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood_sloupek", [7905.4067, 12099.903, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_839 = _this;
  _this setDir -51.741524;
  _this setPos [7905.4067, 12099.903, 0.00018310547];
};

_vehicle_841 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7907.7759, 12102.981, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_841 = _this;
  _this setDir 127.03714;
  _this setPos [7907.7759, 12102.981, 6.1035156e-005];
};

_vehicle_842 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1b", [7909.5693, 12103.264, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_842 = _this;
  _this setDir -143.67877;
  _this setPos [7909.5693, 12103.264, 6.1035156e-005];
};

_vehicle_843 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7911.5962, 12101.74, 0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_843 = _this;
  _this setDir -142.84961;
  _this setPos [7911.5962, 12101.74, 0.00025939941];
};

_vehicle_844 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7915.7637, 12098.716, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_844 = _this;
  _this setDir -142.92761;
  _this setPos [7915.7637, 12098.716, 0.00015258789];
};

_vehicle_845 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7917.8008, 12097.186, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_845 = _this;
  _this setDir -143.13052;
  _this setPos [7917.8008, 12097.186, 0.00010681152];
};

_vehicle_846 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7919.8447, 12095.658, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_846 = _this;
  _this setDir -142.87498;
  _this setPos [7919.8447, 12095.658, 7.6293945e-005];
};

_vehicle_847 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7909.1567, 12079.399, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_847 = _this;
  _this setDir -53.362812;
  _this setPos [7909.1567, 12079.399, 3.0517578e-005];
};

_vehicle_849 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7907.4185, 12079.191, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_849 = _this;
  _this setDir 36.694397;
  _this setPos [7907.4185, 12079.191, 0.00019836426];
};

_vehicle_850 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7910.7119, 12081.471, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_850 = _this;
  _this setDir -52.715183;
  _this setPos [7910.7119, 12081.471, 0.00012207031];
};

_vehicle_851 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7912.2427, 12083.473, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_851 = _this;
  _this setDir -53.013939;
  _this setPos [7912.2427, 12083.473, 0.00019836426];
};

_vehicle_852 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7918.4346, 12091.888, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_852 = _this;
  _this setDir -52.347645;
  _this setPos [7918.4346, 12091.888, 0.0001373291];
};

_vehicle_853 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7919.9927, 12093.885, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_853 = _this;
  _this setDir -51.613041;
  _this setPos [7919.9927, 12093.885, 9.1552734e-005];
};

_vehicle_854 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7913.7363, 12085.569, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_854 = _this;
  _this setDir -52.854755;
  _this setPos [7913.7363, 12085.569, 0.00012207031];
};

_vehicle_855 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood", [7916.9233, 12089.862, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_855 = _this;
  _this setDir -52.939281;
  _this setPos [7916.9233, 12089.862, 0.00015258789];
};

_vehicle_856 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood_sloupek", [7914.4712, 12086.653, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_856 = _this;
  _this setDir -55.494053;
  _this setPos [7914.4712, 12086.653, 0.00024414063];
};

_vehicle_881 = objNull;
if (true) then
{
  _this = createVehicle ["Land_dum_mesto2", [7568.1563, 12208.695, 0.81927115], [], 0, "CAN_COLLIDE"];
  _vehicle_881 = _this;
  _this setDir -67.744102;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7568.1563, 12208.695, 0.81927115];
};

_vehicle_891 = objNull;
if (true) then
{
  _this = createVehicle ["Land_sara_Domek_sedy", [7711.584, 12354.436, 0.37571341], [], 0, "CAN_COLLIDE"];
  _vehicle_891 = _this;
  _this setDir 43.887821;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7711.584, 12354.436, 0.37571341];
};

_vehicle_895 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sara_domek_podhradi_1", [7446.9961, 12589.05, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_895 = _this;
  _this setDir 37.258945;
  _this setPos [7446.9961, 12589.05, 4.5776367e-005];
};

_vehicle_898 = objNull;
if (true) then
{
  _this = createVehicle ["Land_sara_domek_zluty", [7828.6343, 12175.072, 0.18794981], [], 0, "CAN_COLLIDE"];
  _vehicle_898 = _this;
  _this setDir -493.02634;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7828.6343, 12175.072, 0.18794981];
};

_vehicle_899 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_statek_hl_bud", [7333.4067, 12641.439, 0.12545092], [], 0, "CAN_COLLIDE"];
  _vehicle_899 = _this;
  _this setDir -46.078716;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7333.4067, 12641.439, 0.12545092];
};

_vehicle_902 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1I1", [7048.9492, 12651.45, 0.12158216], [], 0, "CAN_COLLIDE"];
  _vehicle_902 = _this;
  _this setDir 162.09894;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7048.9492, 12651.45, 0.12158216];
};

_vehicle_903 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_1I2", [6647.6909, 12662.621, 0.20495421], [], 0, "CAN_COLLIDE"];
  _vehicle_903 = _this;
  _this setDir 31.172535;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6647.6909, 12662.621, 0.20495421];
};

_vehicle_921 = objNull;
if (true) then
{
  _this = createVehicle ["hruzdum", [7077.2749, 12654.813, 0.39779881], [], 0, "CAN_COLLIDE"];
  _vehicle_921 = _this;
  _this setDir -274.87961;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7077.2749, 12654.813, 0.39779881];
};

_vehicle_924 = objNull;
if (true) then
{
  _this = createVehicle ["Land_sara_Domek_sedy", [6831.4961, 12586.26, -0.022906099], [], 0, "CAN_COLLIDE"];
  _vehicle_924 = _this;
  _this setDir -196.36569;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6831.4961, 12586.26, -0.022906099];
};

_vehicle_925 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_3I4", [5995.5059, 13819.764, 0.1474331], [], 0, "CAN_COLLIDE"];
  _vehicle_925 = _this;
  _this setDir -12.887174;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5995.5059, 13819.764, 0.1474331];
};

_vehicle_926 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_3I3", [6019.231, 13821.485, 0.30363986], [], 0, "CAN_COLLIDE"];
  _vehicle_926 = _this;
  _this setDir -98.927078;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6019.231, 13821.485, 0.30363986];
};

_vehicle_927 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_3I2", [6075.8555, 13700.716, -0.56560785], [], 0, "CAN_COLLIDE"];
  _vehicle_927 = _this;
  _this setDir -211.45193;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6075.8555, 13700.716, -0.56560785];
};

_vehicle_928 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_3I1", [6064.8813, 13568.712, -0.19060472], [], 0, "CAN_COLLIDE"];
  _vehicle_928 = _this;
  _this setDir -142.41376;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6064.8813, 13568.712, -0.19060472];
};

_vehicle_929 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_2T2", [6115.106, 13640.341, 0.3094849], [], 0, "CAN_COLLIDE"];
  _vehicle_929 = _this;
  _this setDir 47.977215;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6115.106, 13640.341, 0.3094849];
};

_vehicle_931 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_2L", [6039.7256, 13715.978, 0.22721241], [], 0, "CAN_COLLIDE"];
  _vehicle_931 = _this;
  _this setDir -117.89539;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6039.7256, 13715.978, 0.22721241];
};

_vehicle_932 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_2I", [6000.3169, 13786.104, 0.40583807], [], 0, "CAN_COLLIDE"];
  _vehicle_932 = _this;
  _this setDir -198.08867;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6000.3169, 13786.104, 0.40583807];
};

_vehicle_934 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1L2", [6098.5122, 13543.68, 0.34522942], [], 0, "CAN_COLLIDE"];
  _vehicle_934 = _this;
  _this setDir -255.58656;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6098.5122, 13543.68, 0.34522942];
};

_vehicle_935 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1L2", [6053.3364, 13689.659, -0.09984161], [], 0, "CAN_COLLIDE"];
  _vehicle_935 = _this;
  _this setDir 149.11546;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6053.3364, 13689.659, -0.09984161];
};

_vehicle_936 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_1L1", [6079.9863, 13593.896, -0.40354204], [], 0, "CAN_COLLIDE"];
  _vehicle_936 = _this;
  _this setDir -53.518021;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6079.9863, 13593.896, -0.40354204];
};

_vehicle_937 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1I4", [6089.0005, 13632.042, 0.4535079], [], 0, "CAN_COLLIDE"];
  _vehicle_937 = _this;
  _this setDir -32.372906;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6089.0005, 13632.042, 0.4535079];
};

_vehicle_938 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1I4", [6125.7456, 13501.406, 0.53613031], [], 0, "CAN_COLLIDE"];
  _vehicle_938 = _this;
  _this setDir -34.977306;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6125.7456, 13501.406, 0.53613031];
};

_vehicle_939 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_1I2", [6130.1929, 13620.468, -0.023220522], [], 0, "CAN_COLLIDE"];
  _vehicle_939 = _this;
  _this setDir 31.401049;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6130.1929, 13620.468, -0.023220522];
};

_vehicle_940 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1I1", [6030.2651, 13783.517, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_940 = _this;
  _this setDir 66.778389;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6030.2651, 13783.517, -0.00015258789];
};

_vehicle_941 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV2_05", [6060.1309, 13726.397, -0.43088126], [], 0, "CAN_COLLIDE"];
  _vehicle_941 = _this;
  _this setDir -30.075552;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6060.1309, 13726.397, -0.43088126];
};

_vehicle_944 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV2_02_Interier", [6067.3491, 13660.401, 0.49042189], [], 0, "CAN_COLLIDE"];
  _vehicle_944 = _this;
  _this setDir 238.91731;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6067.3491, 13660.401, 0.49042189];
};

_vehicle_946 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV2_01B", [6097.4951, 13673.67, -0.42182511], [], 0, "CAN_COLLIDE"];
  _vehicle_946 = _this;
  _this setDir 56.986397;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6097.4951, 13673.67, -0.42182511];
};

_vehicle_947 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV2_01A", [5993.2065, 13714.601, -0.27797189], [], 0, "CAN_COLLIDE"];
  _vehicle_947 = _this;
  _this setDir -57.641476;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5993.2065, 13714.601, -0.27797189];
};

_vehicle_948 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV2_04_interier", [6110.1641, 13603.138, 0.64420348], [], 0, "CAN_COLLIDE"];
  _vehicle_948 = _this;
  _this setDir 307.59595;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6110.1641, 13603.138, 0.64420348];
};

_vehicle_952 = objNull;
if (true) then
{
  _this = createVehicle ["Land_bouda2_vnitrek", [6069.189, 13625.756, 0.10236659], [], 0, "CAN_COLLIDE"];
  _vehicle_952 = _this;
  _this setDir -31.758785;
  _this setPos [6069.189, 13625.756, 0.10236659];
};

_vehicle_954 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sara_domek_podhradi_1", [6018.3389, 13749.573, 0.4966976], [], 0, "CAN_COLLIDE"];
  _vehicle_954 = _this;
  _this setDir -116.30467;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6018.3389, 13749.573, 0.4966976];
};

_vehicle_960 = objNull;
if (true) then
{
  _this = createVehicle ["Land_sara_Domek_sedy", [6103.3818, 13571.558, 0.40918991], [], 0, "CAN_COLLIDE"];
  _vehicle_960 = _this;
  _this setDir 32.653915;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6103.3818, 13571.558, 0.40918991];
};

_vehicle_962 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [6118.313, 13555.353, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_962 = _this;
  _this setDir -82.552849;
  _this setPos [6118.313, 13555.353, -0.00012207031];
};

_vehicle_963 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6112.4517, 13556.145, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_963 = _this;
  _this setDir -80.619484;
  _this setPos [6112.4517, 13556.145, -0.00010681152];
};

_vehicle_967 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [6094.3677, 13562.94, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_967 = _this;
  _this setDir -54.00666;
  _this setPos [6094.3677, 13562.94, -4.5776367e-005];
};

_vehicle_968 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [6080.4673, 13572.989, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_968 = _this;
  _this setDir -48.766541;
  _this setPos [6080.4673, 13572.989, -7.6293945e-005];
};

_vehicle_969 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [6062.834, 13588.543, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_969 = _this;
  _this setDir 132.57951;
  _this setPos [6062.834, 13588.543, -6.1035156e-005];
};

_vehicle_972 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_molo_drevo_end", [6098.2505, 13575.836, -1.661869], [], 0, "CAN_COLLIDE"];
  _vehicle_972 = _this;
  _this setDir -147.37135;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6098.2505, 13575.836, -1.661869];
};

_vehicle_973 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [6101.3394, 13571.88, -0.8309412], [], 0, "CAN_COLLIDE"];
  _vehicle_973 = _this;
  _this setDir 32.670853;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6101.3394, 13571.88, -0.8309412];
};

_vehicle_975 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [6062.7188, 13656.448, -0.49374419], [], 0, "CAN_COLLIDE"];
  _vehicle_975 = _this;
  _this setDir -30.86305;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6062.7188, 13656.448, -0.49374419];
};

_vehicle_976 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [6121.0405, 13513.876, -1.5609391], [], 0, "CAN_COLLIDE"];
  _vehicle_976 = _this;
  _this setDir -391.60092;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6121.0405, 13513.876, -1.5609391];
};

_vehicle_979 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_molo_drevo_bs", [6122.4561, 13604.456, -2.6978264], [], 0, "CAN_COLLIDE"];
  _vehicle_979 = _this;
  _this setDir 127.45544;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6122.4561, 13604.456, -2.6978264];
};

_vehicle_984 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [6019.0034, 13827.321, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_984 = _this;
  _this setPos [6019.0034, 13827.321, 3.0517578e-005];
};

_vehicle_985 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [6058.3633, 13657.697, 0.97825497], [], 0, "CAN_COLLIDE"];
  _vehicle_985 = _this;
  _this setDir -31.12166;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6058.3633, 13657.697, 0.97825497];
};

_vehicle_986 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [6057.939, 13655.924, 1.1038861], [], 0, "CAN_COLLIDE"];
  _vehicle_986 = _this;
  _this setDir -121.0742;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6057.939, 13655.924, 1.1038861];
};

_vehicle_987 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [6060.9307, 13653.431, 1.1611797], [], 0, "CAN_COLLIDE"];
  _vehicle_987 = _this;
  _this setDir -209.82089;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6060.9307, 13653.431, 1.1611797];
};

_vehicle_988 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [6059.1572, 13653.873, 1.1949415], [], 0, "CAN_COLLIDE"];
  _vehicle_988 = _this;
  _this setDir -121.07071;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6059.1572, 13653.873, 1.1949415];
};

_vehicle_991 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [6011.5688, 13644.618, -1.593703], [], 0, "CAN_COLLIDE"];
  _vehicle_991 = _this;
  _this setDir -77.167732;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6011.5688, 13644.618, -1.593703];
};

_vehicle_994 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Shed", [6028.1841, 13736.797, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_994 = _this;
  _this setDir -26.719368;
  _this setPos [6028.1841, 13736.797, 0.00018310547];
};

_vehicle_996 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Feed", [6034.2808, 13739.964, 0.2045223], [], 0, "CAN_COLLIDE"];
  _vehicle_996 = _this;
  _this setDir 58.7593;
  _this setPos [6034.2808, 13739.964, 0.2045223];
};

_vehicle_997 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Feed", [6022.1743, 13733.5, 0.19340816], [], 0, "CAN_COLLIDE"];
  _vehicle_997 = _this;
  _this setDir 58.35759;
  _this setPos [6022.1743, 13733.5, 0.19340816];
};

_vehicle_998 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Feed", [6028.2539, 13736.717, 0.25184759], [], 0, "CAN_COLLIDE"];
  _vehicle_998 = _this;
  _this setDir 58.421516;
  _this setPos [6028.2539, 13736.717, 0.25184759];
};

_vehicle_1000 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_spoj", [6026.4893, 13739.538, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1000 = _this;
  _this setDir -27.644012;
  _this setPos [6026.4893, 13739.538, -0.00012207031];
};

_vehicle_1002 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_spoj_2", [6017.5884, 13757.222, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1002 = _this;
  _this setDir -25.045517;
  _this setPos [6017.5884, 13757.222, -3.0517578e-005];
};

_vehicle_1003 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [6024.2314, 13752.147, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_1003 = _this;
  _this setPos [6024.2314, 13752.147, -0.00018310547];
};

_vehicle_1004 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [6024.2529, 13743.23, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1004 = _this;
  _this setPos [6024.2529, 13743.23, 0];
};

_vehicle_1005 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [6031.5029, 13735.751, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1005 = _this;
  _this setPos [6031.5029, 13735.751, -3.0517578e-005];
};

_vehicle_1006 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [6026.7935, 13733.474, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1006 = _this;
  _this setPos [6026.7935, 13733.474, -9.1552734e-005];
};

_vehicle_1007 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6020.2393, 13753.014, -0.53498042], [], 0, "CAN_COLLIDE"];
  _vehicle_1007 = _this;
  _this setDir 62.611576;
  _this setPos [6020.2393, 13753.014, -0.53498042];
};

_vehicle_1008 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6021.7236, 13750.16, -0.52216357], [], 0, "CAN_COLLIDE"];
  _vehicle_1008 = _this;
  _this setDir 62.948231;
  _this setPos [6021.7236, 13750.16, -0.52216357];
};

_vehicle_1009 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6023.1636, 13747.238, -0.55773789], [], 0, "CAN_COLLIDE"];
  _vehicle_1009 = _this;
  _this setDir 63.725021;
  _this setPos [6023.1636, 13747.238, -0.55773789];
};

_vehicle_1010 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6018.7739, 13755.865, -0.56970423], [], 0, "CAN_COLLIDE"];
  _vehicle_1010 = _this;
  _this setDir 63.675529;
  _this setPos [6018.7739, 13755.865, -0.56970423];
};

_vehicle_1012 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [6044.4731, 13734.671, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1012 = _this;
  _this setDir -116.91826;
  _this setPos [6044.4731, 13734.671, -9.1552734e-005];
};

_vehicle_1013 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [6039.145, 13731.851, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1013 = _this;
  _this setDir -116.91826;
  _this setPos [6039.145, 13731.851, 3.0517578e-005];
};

_vehicle_1015 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6008.5464, 13711.764, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1015 = _this;
  _this setDir 39.414711;
  _this setPos [6008.5464, 13711.764, -6.1035156e-005];
};

_vehicle_1016 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [6000.0771, 13694.51, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1016 = _this;
  _this setDir 14.726666;
  _this setPos [6000.0771, 13694.51, 0.00015258789];
};

_vehicle_1018 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5988.1646, 13646.36, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_1018 = _this;
  _this setDir 13.528717;
  _this setPos [5988.1646, 13646.36, 0.0002746582];
};

_vehicle_1019 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5993.9072, 13670.437, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1019 = _this;
  _this setDir 14.29849;
  _this setPos [5993.9072, 13670.437, -6.1035156e-005];
};

_vehicle_1020 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5982.0356, 13622.186, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1020 = _this;
  _this setDir 14.092086;
  _this setPos [5982.0356, 13622.186, 3.0517578e-005];
};

_vehicle_1023 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_SideRoof", [6104.9287, 13607.708, 0.38365519], [], 0, "CAN_COLLIDE"];
  _vehicle_1023 = _this;
  _this setDir -141.73073;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6104.9287, 13607.708, 0.38365519];
};

_vehicle_1027 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ZalChata", [6015.1348, 13694.012, -0.27973551], [], 0, "CAN_COLLIDE"];
  _vehicle_1027 = _this;
  _this setDir 113.43382;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6015.1348, 13694.012, -0.27973551];
};

_vehicle_1032 = objNull;
if (true) then
{
  _this = createVehicle ["Land_repair_center", [6014.4722, 13763.527, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1032 = _this;
  _this setDir -114.7989;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6014.4722, 13763.527, 3.0517578e-005];
};

_vehicle_1037 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_WaterStation", [5997.8462, 13617.116, 0.40655407], [], 0, "CAN_COLLIDE"];
  _vehicle_1037 = _this;
  _this setDir -76.702629;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5997.8462, 13617.116, 0.40655407];
};

_vehicle_1040 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Shed_W4", [6068.021, 13602.071, -0.4884744], [], 0, "CAN_COLLIDE"];
  _vehicle_1040 = _this;
  _this setDir 37.868053;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6068.021, 13602.071, -0.4884744];
};

_vehicle_1041 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Shed_W4", [5992.8618, 13836.413, -0.62527949], [], 0, "CAN_COLLIDE"];
  _vehicle_1041 = _this;
  _this setDir -87.711418;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5992.8618, 13836.413, -0.62527949];
};

_vehicle_1047 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Garage01", [6118.6689, 13515.538, 0.351408], [], 0, "CAN_COLLIDE"];
  _vehicle_1047 = _this;
  _this setDir -121.57566;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6118.6689, 13515.538, 0.351408];
};

_vehicle_1051 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpa", [6031.3979, 13709.98], [], 0, "CAN_COLLIDE"];
  _vehicle_1051 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6031.3979, 13709.98];
};

_vehicle_1053 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_Office01", [6001.7119, 13634.441, 0.63977528], [], 0, "CAN_COLLIDE"];
  _vehicle_1053 = _this;
  _this setDir 102.80448;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6001.7119, 13634.441, 0.63977528];
};

_vehicle_1054 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [6017.1929, 13759.453, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1054 = _this;
  _this setPos [6017.1929, 13759.453, -9.1552734e-005];
};

_vehicle_1056 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [6007.7603, 13755.499, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1056 = _this;
  _this setPos [6007.7603, 13755.499, -9.1552734e-005];
};

_vehicle_1057 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [6007.2007, 13758.577, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1057 = _this;
  _this setPos [6007.2007, 13758.577, -6.1035156e-005];
};

_vehicle_1058 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [6005.9194, 13761.25, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1058 = _this;
  _this setPos [6005.9194, 13761.25, 0.00015258789];
};

_vehicle_1059 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_spoj", [6004.4502, 13661.294, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1059 = _this;
  _this setDir -76.883469;
  _this setPos [6004.4502, 13661.294, -6.1035156e-005];
};

_vehicle_1118 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [5998.71, 13619.592, -1.6620201], [], 0, "CAN_COLLIDE"];
  _vehicle_1118 = _this;
  _this setDir -76.822433;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5998.71, 13619.592, -1.6620201];
};

_vehicle_1125 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [6009.6768, 13636.271, -1.1265414], [], 0, "CAN_COLLIDE"];
  _vehicle_1125 = _this;
  _this setDir -77.167732;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6009.6768, 13636.271, -1.1265414];
};

_vehicle_1128 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [6003.395, 13661.929], [], 0, "CAN_COLLIDE"];
  _vehicle_1128 = _this;
  _this setPos [6003.395, 13661.929];
};

_vehicle_1129 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [6006.9048, 13658.485, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1129 = _this;
  _this setPos [6006.9048, 13658.485, -9.1552734e-005];
};

_vehicle_1130 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [6014.6353, 13666.963, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1130 = _this;
  _this setPos [6014.6353, 13666.963, -6.1035156e-005];
};

_vehicle_1131 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [6010.6201, 13668.302, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1131 = _this;
  _this setPos [6010.6201, 13668.302, 0];
};

_vehicle_1132 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [6007.7017, 13669.399, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1132 = _this;
  _this setPos [6007.7017, 13669.399, 0];
};

_vehicle_1133 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [6014.71, 13667.614, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1133 = _this;
  _this setPos [6014.71, 13667.614, -3.0517578e-005];
};

_vehicle_1134 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [6014.1997, 13665.255, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1134 = _this;
  _this setPos [6014.1997, 13665.255, 0];
};

_vehicle_1135 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5998.2134, 13671.068, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1135 = _this;
  _this setPos [5998.2134, 13671.068, 6.1035156e-005];
};

_vehicle_1136 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5997.9463, 13654.245, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1136 = _this;
  _this setPos [5997.9463, 13654.245, 3.0517578e-005];
};

_vehicle_1137 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5994.5718, 13654.951, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1137 = _this;
  _this setPos [5994.5718, 13654.951, -0.00012207031];
};

_vehicle_1138 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5994.4795, 13656.76, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1138 = _this;
  _this setPos [5994.4795, 13656.76, 9.1552734e-005];
};

_vehicle_1140 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kasna_new", [5981.8496, 13667.282, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1140 = _this;
  _this setDir 13.395106;
  _this setPos [5981.8496, 13667.282, -3.0517578e-005];
};

_vehicle_1142 = objNull;
if (true) then
{
  _this = createVehicle ["Land_kulna", [6028.1094, 13822.425, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1142 = _this;
  _this setDir 60.776871;
  _this setPos [6028.1094, 13822.425, -3.0517578e-005];
};

_vehicle_1143 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ChickenCoop", [6083.0449, 13706.407, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1143 = _this;
  _this setDir -29.137497;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6083.0449, 13706.407, 3.0517578e-005];
};

_vehicle_1144 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ChickenCoop", [5984.1255, 13822.65, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_1144 = _this;
  _this setDir -7.1581354;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5984.1255, 13822.65, -0.00024414063];
};

_vehicle_1145 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ChickenCoop", [6063.0225, 13557.137, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1145 = _this;
  _this setDir -161.34673;
  _this setPos [6063.0225, 13557.137, 6.1035156e-005];
};

_vehicle_1146 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Greenhouse", [6044.6426, 13705.773, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1146 = _this;
  _this setDir -19.910173;
  _this setPos [6044.6426, 13705.773, 3.0517578e-005];
};

_vehicle_1147 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Hutch", [6039.7896, 13685.216, 0.06712389], [], 0, "CAN_COLLIDE"];
  _vehicle_1147 = _this;
  _this setDir -105.03712;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6039.7896, 13685.216, 0.06712389];
};

_vehicle_1148 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Hutch", [6111.9326, 13503.434, 0.21956478], [], 0, "CAN_COLLIDE"];
  _vehicle_1148 = _this;
  _this setDir -527.13165;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6111.9326, 13503.434, 0.21956478];
};

_vehicle_1149 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_psi_bouda", [6043.3325, 13711.345, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1149 = _this;
  _this setDir 51.43095;
  _this setPos [6043.3325, 13711.345, 3.0517578e-005];
};

_vehicle_1152 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_GasMeterExt", [6000.2378, 13616.595, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_1152 = _this;
  _this setDir -76.850471;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6000.2378, 13616.595, 0.00021362305];
};

_vehicle_1155 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_P_Stavebni_kozy", [6052.5132, 13683.285, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1155 = _this;
  _this setDir 48.189526;
  _this setPos [6052.5132, 13683.285, -6.1035156e-005];
};

_vehicle_1159 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Piskoviste", [6019.1968, 13628.961, 0.1252792], [], 0, "CAN_COLLIDE"];
  _vehicle_1159 = _this;
  _this setDir 10.855493;
  _this setPos [6019.1968, 13628.961, 0.1252792];
};

_vehicle_1161 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Houpacka", [6020.686, 13634.795], [], 0, "CAN_COLLIDE"];
  _vehicle_1161 = _this;
  _this setDir -79.166229;
  _this setPos [6020.686, 13634.795];
};

_vehicle_1162 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Houpacka", [6020.8848, 13637.407, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1162 = _this;
  _this setDir -83.014816;
  _this setPos [6020.8848, 13637.407, -3.0517578e-005];
};

_vehicle_1169 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_brana", [6017.7046, 13615.708, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1169 = _this;
  _this setDir 192.9623;
  _this setPos [6017.7046, 13615.708, -9.1552734e-005];
};

_vehicle_1170 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_brana", [6010.6865, 13587.744, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1170 = _this;
  _this setDir 12.68626;
  _this setPos [6010.6865, 13587.744, -3.0517578e-005];
};

_vehicle_1171 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5991.2344, 13639.767, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1171 = _this;
  _this setPos [5991.2344, 13639.767, 9.1552734e-005];
};

_vehicle_1172 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5990.2056, 13635.107, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1172 = _this;
  _this setPos [5990.2056, 13635.107, 9.1552734e-005];
};

_vehicle_1173 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5990.7148, 13637.313, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1173 = _this;
  _this setPos [5990.7148, 13637.313, 0];
};

_vehicle_1174 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [6063.4146, 13589.896, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1174 = _this;
  _this setDir -15.555264;
  _this setPos [6063.4146, 13589.896, -6.1035156e-005];
};

_vehicle_1175 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6065.0981, 13583.908], [], 0, "CAN_COLLIDE"];
  _vehicle_1175 = _this;
  _this setDir -15.555264;
  _this setPos [6065.0981, 13583.908];
};

_vehicle_1180 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Church_03", [5954.9072, 13674.816, 0.56534344], [], 0, "CAN_COLLIDE"];
  _vehicle_1180 = _this;
  _this setDir -166.15146;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5954.9072, 13674.816, 0.56534344];
};

_vehicle_1191 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5981.7573, 13667.258, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1191 = _this;
  _this setPos [5981.7573, 13667.258, 0.00012207031];
};

_vehicle_1192 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [5974.062, 13669.321, -1.4127737], [], 0, "CAN_COLLIDE"];
  _vehicle_1192 = _this;
  _this setDir -76.156052;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5974.062, 13669.321, -1.4127737];
};

_vehicle_1198 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [5972.3184, 13670.566, -1.6162295], [], 0, "CAN_COLLIDE"];
  _vehicle_1198 = _this;
  _this setDir -166.16565;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5972.3184, 13670.566, -1.6162295];
};

_vehicle_1200 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5977.9121, 13668.356, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1200 = _this;
  _this setPos [5977.9121, 13668.356, 6.1035156e-005];
};

_vehicle_1201 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5980.1411, 13670.707, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1201 = _this;
  _this setPos [5980.1411, 13670.707, 0];
};

_vehicle_1202 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5978.8696, 13665.424, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1202 = _this;
  _this setPos [5978.8696, 13665.424, 6.1035156e-005];
};

_vehicle_1203 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5981.7275, 13664.441, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1203 = _this;
  _this setPos [5981.7275, 13664.441, 3.0517578e-005];
};

_vehicle_1204 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5983.4414, 13669.537, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1204 = _this;
  _this setPos [5983.4414, 13669.537, -3.0517578e-005];
};

_vehicle_1205 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5984.5278, 13665.916, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1205 = _this;
  _this setPos [5984.5278, 13665.916, -3.0517578e-005];
};

_vehicle_1206 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5988.2061, 13665.656, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1206 = _this;
  _this setPos [5988.2061, 13665.656, 0];
};

_vehicle_1207 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5985.5205, 13668.454, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1207 = _this;
  _this setPos [5985.5205, 13668.454, 0];
};

_vehicle_1208 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5984.083, 13663.776, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1208 = _this;
  _this setPos [5984.083, 13663.776, -3.0517578e-005];
};

_vehicle_1209 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_12", [6003.3252, 13835.686, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1209 = _this;
  _this setDir -84.345947;
  _this setPos [6003.3252, 13835.686, -9.1552734e-005];
};

_vehicle_1217 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5984.1025, 13667.861, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1217 = _this;
  _this setPos [5984.1025, 13667.861, -9.1552734e-005];
};

_vehicle_1218 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5984.0796, 13668.642, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1218 = _this;
  _this setPos [5984.0796, 13668.642, 0];
};

_vehicle_1219 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5982.6514, 13668.97, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1219 = _this;
  _this setPos [5982.6514, 13668.97, -9.1552734e-005];
};

_vehicle_1221 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5983.5029, 13669.105, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1221 = _this;
  _this setPos [5983.5029, 13669.105, -3.0517578e-005];
};

_vehicle_1222 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5981.6138, 13669.393, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1222 = _this;
  _this setPos [5981.6138, 13669.393, -9.1552734e-005];
};

_vehicle_1224 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5980.7842, 13669.675, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1224 = _this;
  _this setPos [5980.7842, 13669.675, -6.1035156e-005];
};

_vehicle_1225 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5980.0181, 13668.242, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1225 = _this;
  _this setPos [5980.0181, 13668.242, 6.1035156e-005];
};

_vehicle_1226 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5979.6245, 13666.553, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1226 = _this;
  _this setPos [5979.6245, 13666.553, 0];
};

_vehicle_1227 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5979.7632, 13667.063, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1227 = _this;
  _this setPos [5979.7632, 13667.063, 3.0517578e-005];
};

_vehicle_1228 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5980.1641, 13665.609, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1228 = _this;
  _this setPos [5980.1641, 13665.609, 0];
};

_vehicle_1229 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5981.4922, 13665.411, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1229 = _this;
  _this setPos [5981.4922, 13665.411, -6.1035156e-005];
};

_vehicle_1230 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5982.7866, 13665.02, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1230 = _this;
  _this setPos [5982.7866, 13665.02, 0];
};

_vehicle_1231 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5983.6001, 13665.552, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1231 = _this;
  _this setPos [5983.6001, 13665.552, 3.0517578e-005];
};

_vehicle_1232 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [5983.8711, 13666.649, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1232 = _this;
  _this setPos [5983.8711, 13666.649, -9.1552734e-005];
};

_vehicle_1233 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2W", [5984.3486, 13679.921], [], 0, "CAN_COLLIDE"];
  _vehicle_1233 = _this;
  _this setPos [5984.3486, 13679.921];
};

_vehicle_1234 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2W", [5978.6289, 13656.633, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1234 = _this;
  _this setDir -108.46943;
  _this setPos [5978.6289, 13656.633, -6.1035156e-005];
};

_vehicle_1235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_quercus2f", [5978.4985, 13694.919, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1235 = _this;
  _this setDir -69.157684;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5978.4985, 13694.919, -3.0517578e-005];
};

_vehicle_1236 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [5994.6519, 13707.729], [], 0, "CAN_COLLIDE"];
  _vehicle_1236 = _this;
  _this setPos [5994.6519, 13707.729];
};

_vehicle_1237 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [5997.9902, 13712.925, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1237 = _this;
  _this setDir 81.602737;
  _this setPos [5997.9902, 13712.925, -3.0517578e-005];
};

_vehicle_1238 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6046.1992, 13716.23, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_1238 = _this;
  _this setPos [6046.1992, 13716.23, -0.00018310547];
};

_vehicle_1239 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6045.4282, 13717.798, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1239 = _this;
  _this setDir 52.43325;
  _this setPos [6045.4282, 13717.798, -3.0517578e-005];
};

_vehicle_1240 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6044.7617, 13719.241, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1240 = _this;
  _this setDir -52.24107;
  _this setPos [6044.7617, 13719.241, -9.1552734e-005];
};

_vehicle_1241 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6043.7676, 13720.893, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1241 = _this;
  _this setPos [6043.7676, 13720.893, -3.0517578e-005];
};

_vehicle_1244 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [6059.5986, 13688.819, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1244 = _this;
  _this setDir 59.182755;
  _this setPos [6059.5986, 13688.819, -9.1552734e-005];
};

_vehicle_1245 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6054.2773, 13685.643, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_1245 = _this;
  _this setDir 59.007069;
  _this setPos [6054.2773, 13685.643, 0.00021362305];
};

_vehicle_1246 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6003.7876, 13706.018, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1246 = _this;
  _this setDir 116.8453;
  _this setPos [6003.7876, 13706.018, -3.0517578e-005];
};

_vehicle_1247 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6020.4683, 13697.718, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1247 = _this;
  _this setDir -63.546463;
  _this setPos [6020.4683, 13697.718, 9.1552734e-005];
};

_vehicle_1248 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [6014.9238, 13700.464, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1248 = _this;
  _this setDir -63.835087;
  _this setPos [6014.9238, 13700.464, 3.0517578e-005];
};

_vehicle_1254 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6093.9243, 13651.849, 0.34692383], [], 0, "CAN_COLLIDE"];
  _vehicle_1254 = _this;
  _this setDir 59.577515;
  _this setPos [6093.9243, 13651.849, 0.34692383];
};

_vehicle_1255 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6109.9414, 13661.387, -0.24008179], [], 0, "CAN_COLLIDE"];
  _vehicle_1255 = _this;
  _this setDir -120.81428;
  _this setPos [6109.9414, 13661.387, -0.24008179];
};

_vehicle_1256 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [6104.6382, 13658.196, 0.046722412], [], 0, "CAN_COLLIDE"];
  _vehicle_1256 = _this;
  _this setDir -121.1029;
  _this setPos [6104.6382, 13658.196, 0.046722412];
};

_vehicle_1262 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6098.021, 13609.415, 0.48153687], [], 0, "CAN_COLLIDE"];
  _vehicle_1262 = _this;
  _this setDir 40.720543;
  _this setPos [6098.021, 13609.415, 0.48153687];
};

_vehicle_1263 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6110.1362, 13623.623, -0.16729736], [], 0, "CAN_COLLIDE"];
  _vehicle_1263 = _this;
  _this setDir -139.67122;
  _this setPos [6110.1362, 13623.623, -0.16729736];
};

_vehicle_1264 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [6106.1353, 13618.89, 0.040710449], [], 0, "CAN_COLLIDE"];
  _vehicle_1264 = _this;
  _this setDir -139.95984;
  _this setPos [6106.1353, 13618.89, 0.040710449];
};

_vehicle_1270 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6106.4917, 13557.329, -0.027053833], [], 0, "CAN_COLLIDE"];
  _vehicle_1270 = _this;
  _this setDir -163.76982;
  _this setPos [6106.4917, 13557.329, -0.027053833];
};

_vehicle_1271 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6101.4136, 13539.333, -0.014480591], [], 0, "CAN_COLLIDE"];
  _vehicle_1271 = _this;
  _this setDir -344.16165;
  _this setPos [6101.4136, 13539.333, -0.014480591];
};

_vehicle_1272 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [6103.0898, 13545.281, -0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_1272 = _this;
  _this setDir -344.45029;
  _this setPos [6103.0898, 13545.281, -0.00019836426];
};

_vehicle_1278 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6129.4058, 13521.784, -0.16079712], [], 0, "CAN_COLLIDE"];
  _vehicle_1278 = _this;
  _this setDir -121.20488;
  _this setPos [6129.4058, 13521.784, -0.16079712];
};

_vehicle_1280 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [6118.7427, 13515.314, -0.0098114014], [], 0, "CAN_COLLIDE"];
  _vehicle_1280 = _this;
  _this setDir -301.6687;
  _this setPos [6118.7427, 13515.314, -0.0098114014];
};

_vehicle_1285 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2f", [6098.8203, 13618.514, -3.8223326], [], 0, "CAN_COLLIDE"];
  _vehicle_1285 = _this;
  _this setPos [6098.8203, 13618.514, -3.8223326];
};

_vehicle_1286 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2s", [6023.8682, 13710.119, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1286 = _this;
  _this setDir -21.071548;
  _this setPos [6023.8682, 13710.119, -0.00012207031];
};

_vehicle_1287 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_alnus2s", [6084.7646, 13557.016, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1287 = _this;
  _this setPos [6084.7646, 13557.016, 0.00015258789];
};

_vehicle_1289 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [6110.5547, 13583.271, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1289 = _this;
  _this setPos [6110.5547, 13583.271, 4.5776367e-005];
};

_vehicle_1290 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [6090.0703, 13599.667, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1290 = _this;
  _this setDir -44.155144;
  _this setPos [6090.0703, 13599.667, 1.5258789e-005];
};

_vehicle_1291 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [6106.5894, 13586.351], [], 0, "CAN_COLLIDE"];
  _vehicle_1291 = _this;
  _this setPos [6106.5894, 13586.351];
};

_vehicle_1292 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [6102.6084, 13589.563, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_1292 = _this;
  _this setPos [6102.6084, 13589.563, 0.00021362305];
};

_vehicle_1293 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [6098.4312, 13592.786, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1293 = _this;
  _this setPos [6098.4312, 13592.786, 6.1035156e-005];
};

_vehicle_1294 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [6094.522, 13596.135, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1294 = _this;
  _this setDir 112.63329;
  _this setPos [6094.522, 13596.135, -0.00015258789];
};

_vehicle_1295 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pyrus2s", [6074.0518, 13587.522, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_1295 = _this;
  _this setPos [6074.0518, 13587.522, 0.00030517578];
};

_vehicle_1296 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_quercus3s", [5947.1108, 13647.223, -0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_1296 = _this;
  _this setDir 0.33950946;
  _this setPos [5947.1108, 13647.223, -0.00025939941];
};

_vehicle_1298 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_quercus2f", [6081.6313, 13621.896, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1298 = _this;
  _this setPos [6081.6313, 13621.896, 3.0517578e-005];
};

_vehicle_1299 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pyrus2s", [6121.1572, 13623.871, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1299 = _this;
  _this setDir 74.561378;
  _this setPos [6121.1572, 13623.871, -6.1035156e-005];
};

_vehicle_1300 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pyrus2s", [6090.9604, 13690.473, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1300 = _this;
  _this setDir -69.895111;
  _this setPos [6090.9604, 13690.473, -0.00012207031];
};

_vehicle_1302 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [6071.3408, 13715.321, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_1302 = _this;
  _this setPos [6071.3408, 13715.321, 0.00021362305];
};

_vehicle_1303 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [6073.4341, 13640.089, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1303 = _this;
  _this setPos [6073.4341, 13640.089, 9.1552734e-005];
};

_vehicle_1304 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [6090.6665, 13662.576, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1304 = _this;
  _this setDir 144.3947;
  _this setPos [6090.6665, 13662.576, -6.1035156e-005];
};

_vehicle_1307 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2s", [6018.6001, 13835.274, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_1307 = _this;
  _this setPos [6018.6001, 13835.274, 0.00021362305];
};

_vehicle_1308 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2W", [6000.8716, 13769.694, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1308 = _this;
  _this setDir -229.27408;
  _this setPos [6000.8716, 13769.694, -9.1552734e-005];
};

_vehicle_1311 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6064.8892, 13682.165, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1311 = _this;
  _this setDir -65.615982;
  _this setPos [6064.8892, 13682.165, -0.00012207031];
};

_vehicle_1312 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6066.917, 13683.436, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1312 = _this;
  _this setPos [6066.917, 13683.436, 3.0517578e-005];
};

_vehicle_1313 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6060.8838, 13679.51], [], 0, "CAN_COLLIDE"];
  _vehicle_1313 = _this;
  _this setDir -143.94066;
  _this setPos [6060.8838, 13679.51];
};

_vehicle_1314 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6062.854, 13680.956, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_1314 = _this;
  _this setDir 38.874844;
  _this setPos [6062.854, 13680.956, 0.00024414063];
};

_vehicle_1315 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6056.1113, 13676.816, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_1315 = _this;
  _this setPos [6056.1113, 13676.816, 0.00018310547];
};

_vehicle_1316 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6058.4321, 13678.069, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1316 = _this;
  _this setDir -65.461914;
  _this setPos [6058.4321, 13678.069, -6.1035156e-005];
};

_vehicle_1317 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6057.9761, 13691.261, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1317 = _this;
  _this setPos [6057.9761, 13691.261, 3.0517578e-005];
};

_vehicle_1318 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6050.2085, 13698.35, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1318 = _this;
  _this setDir 170.04601;
  _this setPos [6050.2085, 13698.35, 9.1552734e-005];
};

_vehicle_1319 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6048.3667, 13697.212, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_1319 = _this;
  _this setPos [6048.3667, 13697.212, 0.00021362305];
};

_vehicle_1320 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6046.9473, 13696.301, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_1320 = _this;
  _this setDir -190.43047;
  _this setPos [6046.9473, 13696.301, 0.00030517578];
};

_vehicle_1322 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_P_Stavebni_kozy", [6072.2847, 13629.071, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1322 = _this;
  _this setDir 58.542591;
  _this setPos [6072.2847, 13629.071, 6.1035156e-005];
};

_vehicle_1324 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [5980.7231, 13616.322, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1324 = _this;
  _this setDir 12.388364;
  _this setPos [5980.7231, 13616.322, 0.00012207031];
};

_vehicle_1325 = objNull;
if (true) then
{
  _this = createVehicle ["Land_deutshe_mini", [5963.4121, 13607.877, 0.19190459], [], 0, "CAN_COLLIDE"];
  _vehicle_1325 = _this;
  _this setDir -288.9834;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5963.4121, 13607.877, 0.19190459];
};

_vehicle_1344 = objNull;
if (true) then
{
  _this = createVehicle ["Land_dum_mesto2", [5969.6152, 13638.549, 0.70600009], [], 0, "CAN_COLLIDE"];
  _vehicle_1344 = _this;
  _this setDir 14.699138;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5969.6152, 13638.549, 0.70600009];
};

_vehicle_1347 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [5973.8789, 13641.043, -1.5002239], [], 0, "CAN_COLLIDE"];
  _vehicle_1347 = _this;
  _this setDir -255.20219;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [5973.8789, 13641.043, -1.5002239];
};

_vehicle_1349 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [5974.4761, 13635.69], [], 0, "CAN_COLLIDE"];
  _vehicle_1349 = _this;
  _this setDir 34.603813;
  _this setPos [5974.4761, 13635.69];
};

_vehicle_1350 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [5976.1753, 13635.256], [], 0, "CAN_COLLIDE"];
  _vehicle_1350 = _this;
  _this setDir -2.5501244;
  _this setPos [5976.1753, 13635.256];
};

_vehicle_1351 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [5977.0542, 13636.803, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1351 = _this;
  _this setDir 129.9882;
  _this setPos [5977.0542, 13636.803, -3.0517578e-005];
};

_vehicle_1352 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [5977.23, 13638.377, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1352 = _this;
  _this setDir -43.737011;
  _this setPos [5977.23, 13638.377, 6.1035156e-005];
};

_vehicle_1354 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [5977.7764, 13640.167, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1354 = _this;
  _this setDir 78.271645;
  _this setPos [5977.7764, 13640.167, -3.0517578e-005];
};

_vehicle_1355 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [5978.4727, 13641.73, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1355 = _this;
  _this setDir -167.89815;
  _this setPos [5978.4727, 13641.73, -6.1035156e-005];
};

_vehicle_1356 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [6042.0078, 13678.683, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1356 = _this;
  _this setDir -44.810047;
  _this setPos [6042.0078, 13678.683, 3.0517578e-005];
};

_vehicle_1357 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [6042.6738, 13679.327, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1357 = _this;
  _this setDir 15.628068;
  _this setPos [6042.6738, 13679.327, -3.0517578e-005];
};

_vehicle_1358 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [6043.3887, 13679.9, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1358 = _this;
  _this setDir -18.655758;
  _this setPos [6043.3887, 13679.9, -3.0517578e-005];
};

_vehicle_1359 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [6040.8579, 13678.5, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1359 = _this;
  _this setDir 32.84745;
  _this setPos [6040.8579, 13678.5, 3.0517578e-005];
};

_vehicle_1360 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [6042.2241, 13677.123, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1360 = _this;
  _this setDir 32.819889;
  _this setPos [6042.2241, 13677.123, -3.0517578e-005];
};

_vehicle_1361 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [6043.0718, 13677.946, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1361 = _this;
  _this setDir -10.914433;
  _this setPos [6043.0718, 13677.946, -6.1035156e-005];
};

_vehicle_1362 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [6043.6572, 13678.66, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1362 = _this;
  _this setDir 50.416023;
  _this setPos [6043.6572, 13678.66, 3.0517578e-005];
};

_vehicle_1363 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [6044.5908, 13679.243, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1363 = _this;
  _this setDir 10.913174;
  _this setPos [6044.5908, 13679.243, -3.0517578e-005];
};

_vehicle_1364 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [6043.1221, 13678.084, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1364 = _this;
  _this setDir -166.86745;
  _this setPos [6043.1221, 13678.084, 6.1035156e-005];
};

_vehicle_1365 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [6045.0835, 13676.484], [], 0, "CAN_COLLIDE"];
  _vehicle_1365 = _this;
  _this setPos [6045.0835, 13676.484];
};

_vehicle_1366 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [6046.1758, 13677.244, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1366 = _this;
  _this setDir 56.84449;
  _this setPos [6046.1758, 13677.244, 3.0517578e-005];
};

_vehicle_1368 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [6047.1099, 13678.463, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1368 = _this;
  _this setDir -43.530884;
  _this setPos [6047.1099, 13678.463, -6.1035156e-005];
};

_vehicle_1369 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_TyreHeapEP1", [6005.8628, 13761.476, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1369 = _this;
  _this setDir 52.292553;
  _this setPos [6005.8628, 13761.476, -3.0517578e-005];
};

_vehicle_1370 = objNull;
if (true) then
{
  _this = createVehicle ["Land_tires_EP1", [6068.6865, 13597.156, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1370 = _this;
  _this setPos [6068.6865, 13597.156, 3.0517578e-005];
};

_vehicle_1371 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Crates_stack_EP1", [6052.6362, 13686.323, -0.66150469], [], 0, "CAN_COLLIDE"];
  _vehicle_1371 = _this;
  _this setDir -122.81707;
  _this setPos [6052.6362, 13686.323, -0.66150469];
};

_vehicle_1372 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Crates_stack_EP1", [6061.3657, 13566.649, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1372 = _this;
  _this setDir -52.428352;
  _this setPos [6061.3657, 13566.649, -3.0517578e-005];
};

_vehicle_1374 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [6114.2876, 13514.307, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1374 = _this;
  _this setDir 145.58679;
  _this setPos [6114.2876, 13514.307, -6.1035156e-005];
};

_vehicle_1375 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [6090.9438, 13540.767, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1375 = _this;
  _this setDir -76.190239;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6090.9438, 13540.767, -7.6293945e-005];
};

_vehicle_1376 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta2", [6007.9629, 13759.278, 0.19356053], [], 0, "CAN_COLLIDE"];
  _vehicle_1376 = _this;
  _this setDir -103.8363;
  _this setPos [6007.9629, 13759.278, 0.19356053];
};

_vehicle_1377 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta1", [6094.4722, 13538.677, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1377 = _this;
  _this setDir 14.582728;
  _this setPos [6094.4722, 13538.677, -3.0517578e-005];
};

_vehicle_1378 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta1", [6006.7227, 13758.863, -0.010879155], [], 0, "CAN_COLLIDE"];
  _vehicle_1378 = _this;
  _this setDir -10.964031;
  _this setPos [6006.7227, 13758.863, -0.010879155];
};

_vehicle_1379 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Barrel_water", [6065.7163, 13571.151, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1379 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6065.7163, 13571.151, 0.00012207031];
};

_vehicle_1381 = objNull;
if (true) then
{
  _this = createVehicle ["Loudspeakers_EP1", [6012.1558, 13650.188, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1381 = _this;
  _this setPos [6012.1558, 13650.188, 3.0517578e-005];
};

_vehicle_1383 = objNull;
if (true) then
{
  _this = createVehicle ["Land_bags_stack_EP1", [6011.5, 13690.384, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1383 = _this;
  _this setDir 24.567501;
  _this setPos [6011.5, 13690.384, -0.00015258789];
};

_vehicle_1384 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [6011.4897, 13755.527], [], 0, "CAN_COLLIDE"];
  _vehicle_1384 = _this;
  _this setDir 64.632446;
  _this setPos [6011.4897, 13755.527];
};

_vehicle_1385 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [6008.6592, 13753.887, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1385 = _this;
  _this setDir 19.80324;
  _this setPos [6008.6592, 13753.887, -9.1552734e-005];
};

_vehicle_1386 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [5994.5537, 13618.62, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1386 = _this;
  _this setDir -73.563766;
  _this setPos [5994.5537, 13618.62, 6.1035156e-005];
};

_vehicle_1395 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5967.3428, 13615.673, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1395 = _this;
  _this setPos [5967.3428, 13615.673, 9.1552734e-005];
};

_vehicle_1396 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5962.709, 13613.925, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1396 = _this;
  _this setPos [5962.709, 13613.925, -6.1035156e-005];
};

_vehicle_1397 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5965.7109, 13614.463, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1397 = _this;
  _this setPos [5965.7109, 13614.463, -9.1552734e-005];
};

_vehicle_1398 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5968.2661, 13612.427, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1398 = _this;
  _this setPos [5968.2661, 13612.427, -9.1552734e-005];
};

_vehicle_1399 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5965.8784, 13610.285, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1399 = _this;
  _this setPos [5965.8784, 13610.285, 0.00012207031];
};

_vehicle_1400 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5962.666, 13611.045, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1400 = _this;
  _this setPos [5962.666, 13611.045, -0.00012207031];
};

_vehicle_1401 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5963.9487, 13608.147, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1401 = _this;
  _this setPos [5963.9487, 13608.147, -3.0517578e-005];
};

_vehicle_1402 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5965.2534, 13606.641, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1402 = _this;
  _this setPos [5965.2534, 13606.641, 3.0517578e-005];
};

_vehicle_1403 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5967.8926, 13608.7, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1403 = _this;
  _this setPos [5967.8926, 13608.7, 0];
};

_vehicle_1404 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5970.2046, 13608.622, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1404 = _this;
  _this setPos [5970.2046, 13608.622, 0.00012207031];
};

_vehicle_1406 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5964.7925, 13612.391, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1406 = _this;
  _this setPos [5964.7925, 13612.391, -3.0517578e-005];
};

_vehicle_1407 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [5967.5557, 13607.586, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1407 = _this;
  _this setPos [5967.5557, 13607.586, -3.0517578e-005];
};

_vehicle_1409 = objNull;
if (true) then
{
  _this = createVehicle ["FlagPole_EP1", [5991.6602, 13642.515, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1409 = _this;
  _this setPos [5991.6602, 13642.515, -6.1035156e-005];
};

_vehicle_1410 = objNull;
if (true) then
{
  _this = createVehicle ["FlagPole_EP1", [6009.8413, 13724.228, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1410 = _this;
  _this setPos [6009.8413, 13724.228, -0.00015258789];
};

_vehicle_1411 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_ConcPipeline_EP1", [6027.3081, 13629.819, -0.30443829], [], 0, "CAN_COLLIDE"];
  _vehicle_1411 = _this;
  _this setDir 4.31738;
  _this setPos [6027.3081, 13629.819, -0.30443829];
};

_vehicle_1412 = objNull;
if (true) then
{
  _this = createVehicle ["Bleacher_EP1", [6028.4028, 13604.327, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1412 = _this;
  _this setDir 102.26669;
  _this setPos [6028.4028, 13604.327, -6.1035156e-005];
};

_vehicle_1413 = objNull;
if (true) then
{
  _this = createVehicle ["Bleacher_EP1", [6027.353, 13599.253, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1413 = _this;
  _this setDir 103.6171;
  _this setPos [6027.353, 13599.253, -6.1035156e-005];
};

_vehicle_1414 = objNull;
if (true) then
{
  _this = createVehicle ["Bleacher_EP1", [6026.019, 13594.055, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_1414 = _this;
  _this setDir 103.96822;
  _this setPos [6026.019, 13594.055, -0.00024414063];
};

_vehicle_1416 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel4", [6065.937, 13604.829, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1416 = _this;
  _this setPos [6065.937, 13604.829, -3.0517578e-005];
};

_vehicle_1417 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel4", [6065.3047, 13604.401, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1417 = _this;
  _this setPos [6065.3047, 13604.401, 6.1035156e-005];
};

_vehicle_1418 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel5", [6066.2188, 13605.433], [], 0, "CAN_COLLIDE"];
  _vehicle_1418 = _this;
  _this setPos [6066.2188, 13605.433];
};

_vehicle_1422 = objNull;
if (true) then
{
  _this = createVehicle ["Land_kulna", [5952.1519, 13606.867], [], 0, "CAN_COLLIDE"];
  _vehicle_1422 = _this;
  _this setDir -91.260834;
  _this setPos [5952.1519, 13606.867];
};

_vehicle_1429 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_PostBox", [6025.2793, 13778.01, -0.3404409], [], 0, "CAN_COLLIDE"];
  _vehicle_1429 = _this;
  _this setDir -13.591762;
  _this setPos [6025.2793, 13778.01, -0.3404409];
};

_vehicle_1430 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_PostBox", [6003.813, 13827.511], [], 0, "CAN_COLLIDE"];
  _vehicle_1430 = _this;
  _this setDir 160.65665;
  _this setPos [6003.813, 13827.511];
};

_vehicle_1431 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_PostBox", [6065.7051, 13687.501, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1431 = _this;
  _this setDir -207.04742;
  _this setPos [6065.7051, 13687.501, -6.1035156e-005];
};

_vehicle_1432 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_PostBox", [6094.2109, 13656.899, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1432 = _this;
  _this setDir -29.204292;
  _this setPos [6094.2109, 13656.899, -3.0517578e-005];
};

_vehicle_1433 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_PostBox", [6097.833, 13557.369, -0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_1433 = _this;
  _this setDir 117.04944;
  _this setPos [6097.833, 13557.369, -0.00019836426];
};

_vehicle_1434 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_PostBox", [6131.8428, 13511.883, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1434 = _this;
  _this setDir 140.01321;
  _this setPos [6131.8428, 13511.883, -3.0517578e-005];
};

_vehicle_1435 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_PostBox", [5983.9365, 13642.39, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1435 = _this;
  _this setDir 190.92223;
  _this setPos [5983.9365, 13642.39, -3.0517578e-005];
};

_vehicle_1440 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [5970.4111, 13651.103, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1440 = _this;
  _this setDir 104.83884;
  _this setPos [5970.4111, 13651.103, -9.1552734e-005];
};

_vehicle_1441 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [5988.4272, 13646.415, 0.18218994], [], 0, "CAN_COLLIDE"];
  _vehicle_1441 = _this;
  _this setDir -75.552895;
  _this setPos [5988.4272, 13646.415, 0.18218994];
};

_vehicle_1442 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [5982.4312, 13647.946, 0.13259888], [], 0, "CAN_COLLIDE"];
  _vehicle_1442 = _this;
  _this setDir -75.841515;
  _this setPos [5982.4312, 13647.946, 0.13259888];
};

_vehicle_1448 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [5981.8779, 13620.388, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1448 = _this;
  _this setDir 198.87639;
  _this setPos [5981.8779, 13620.388, -6.1035156e-005];
};

_vehicle_1449 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [5965.0542, 13593.748, 0.52238464], [], 0, "CAN_COLLIDE"];
  _vehicle_1449 = _this;
  _this setDir 44.968204;
  _this setPos [5965.0542, 13593.748, 0.52238464];
};

_vehicle_1456 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [5979.8833, 13614.557, -0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1456 = _this;
  _this setDir -158.76462;
  _this setPos [5979.8833, 13614.557, -0.00016784668];
};

_vehicle_1457 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6773.2715, 12596.969, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1457 = _this;
  _this setDir -162.54501;
  _this setPos [6773.2715, 12596.969, 7.6293945e-005];
};

_vehicle_1459 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [6771.4292, 12591.12, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1459 = _this;
  _this setDir -160.05017;
  _this setPos [6771.4292, 12591.12, 1.5258789e-005];
};

_vehicle_1460 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [6729.6201, 12551.769, 0.00061035156], [], 0, "CAN_COLLIDE"];
  _vehicle_1460 = _this;
  _this setDir -316.69833;
  _this setPos [6729.6201, 12551.769, 0.00061035156];
};

_vehicle_1461 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [6761.3076, 12574.479, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_1461 = _this;
  _this setDir -137.10901;
  _this setPos [6761.3076, 12574.479, 0.00030517578];
};

_vehicle_1465 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [6677.9873, 12508.988, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1465 = _this;
  _this setDir 56.093788;
  _this setPos [6677.9873, 12508.988, 7.6293945e-005];
};

_vehicle_1466 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [6712.292, 12533.776, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1466 = _this;
  _this setDir 43.904724;
  _this setPos [6712.292, 12533.776, 0.00016784668];
};

_vehicle_1467 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [6712.3462, 12533.817, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_1467 = _this;
  _this setDir -133.8369;
  _this setPos [6712.3462, 12533.817, 0.00036621094];
};

_vehicle_1468 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [6664.5215, 12498.038, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1468 = _this;
  _this setDir 45.939735;
  _this setPos [6664.5215, 12498.038, 0.00015258789];
};

_vehicle_1469 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [6653.4185, 12484.673, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1469 = _this;
  _this setDir 34.737404;
  _this setPos [6653.4185, 12484.673, 0.00010681152];
};

_vehicle_1470 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [6645.2241, 12467.03, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1470 = _this;
  _this setDir 13.561423;
  _this setPos [6645.2241, 12467.03, -0.00015258789];
};

_vehicle_1471 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [6605.4048, 12380.023, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1471 = _this;
  _this setDir 5.8777809;
  _this setPos [6605.4048, 12380.023, 0.00016784668];
};

_vehicle_1472 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [6642.6938, 12449.848, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1472 = _this;
  _this setDir 3.3907964;
  _this setPos [6642.6938, 12449.848, -4.5776367e-005];
};

_vehicle_1475 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_30_25", [6642.7178, 12449.914, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1475 = _this;
  _this setDir 186.11726;
  _this setPos [6642.7178, 12449.914, 0.00015258789];
};

_vehicle_1476 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [6623.7407, 12417.392, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1476 = _this;
  _this setDir 34.847931;
  _this setPos [6623.7407, 12417.392, 1.5258789e-005];
};

_vehicle_1477 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [6611.0396, 12398.446, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1477 = _this;
  _this setDir 33.871101;
  _this setPos [6611.0396, 12398.446, -7.6293945e-005];
};

_vehicle_1478 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [6603.207, 12355.206, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1478 = _this;
  _this setDir 5.1001778;
  _this setPos [6603.207, 12355.206, -1.5258789e-005];
};

_vehicle_1479 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [6602.3169, 12337.93, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_1479 = _this;
  _this setDir -4.1746597;
  _this setPos [6602.3169, 12337.93, 0.00024414063];
};

_vehicle_1480 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [6602.3013, 12338.005, 0.00048828125], [], 0, "CAN_COLLIDE"];
  _vehicle_1480 = _this;
  _this setDir 176.00723;
  _this setPos [6602.3013, 12338.005, 0.00048828125];
};

_vehicle_1481 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [6597.5811, 12299.456, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1481 = _this;
  _this setDir -4.3185639;
  _this setPos [6597.5811, 12299.456, 3.0517578e-005];
};

_vehicle_1482 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [6602.8892, 12280.749, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1482 = _this;
  _this setDir -27.094145;
  _this setPos [6602.8892, 12280.749, 0.00012207031];
};

_vehicle_1483 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [6614.7236, 12265.297, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1483 = _this;
  _this setDir -48.71109;
  _this setPos [6614.7236, 12265.297, 6.1035156e-005];
};

_vehicle_1484 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6619.4053, 12261.202, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1484 = _this;
  _this setDir -49.00172;
  _this setPos [6619.4053, 12261.202, 0.00016784668];
};

_vehicle_1493 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_WaterStation", [6610.9038, 12261.74, -0.32181829], [], 0, "CAN_COLLIDE"];
  _vehicle_1493 = _this;
  _this setDir 38.917126;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6610.9038, 12261.74, -0.32181829];
};

_vehicle_1497 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [7060.5103, 12666.29, 0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_1497 = _this;
  _this setDir -104.09238;
  _this setPos [7060.5103, 12666.29, 0.00028991699];
};

_vehicle_1500 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [7066.3564, 12667.845, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1500 = _this;
  _this setDir 78.555191;
  _this setPos [7066.3564, 12667.845, 7.6293945e-006];
};

_vehicle_1501 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [7048.396, 12663.362, 9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1501 = _this;
  _this setDir 77.319862;
  _this setPos [7048.396, 12663.362, 9.9182129e-005];
};

_vehicle_1502 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [7062.0703, 12660.3, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1502 = _this;
  _this setDir 166.03969;
  _this setPos [7062.0703, 12660.3, 7.6293945e-005];
};

_vehicle_1503 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [7061.9785, 12647.55, 0.00012969971], [], 0, "CAN_COLLIDE"];
  _vehicle_1503 = _this;
  _this setDir -161.85419;
  _this setPos [7061.9785, 12647.55, 0.00012969971];
};

_vehicle_1504 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [7054.9351, 12636.869, 0.0001449585], [], 0, "CAN_COLLIDE"];
  _vehicle_1504 = _this;
  _this setDir -133.22215;
  _this setPos [7054.9351, 12636.869, 0.0001449585];
};

_vehicle_1505 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7038.4619, 12626.563, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_1505 = _this;
  _this setDir -110.47129;
  _this setPos [7038.4619, 12626.563, 0.00022888184];
};

_vehicle_1506 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [6991.8193, 12608.744, 0.0008392334], [], 0, "CAN_COLLIDE"];
  _vehicle_1506 = _this;
  _this setDir 68.969383;
  _this setPos [6991.8193, 12608.744, 0.0008392334];
};

_vehicle_1508 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [6976.4038, 12600.821, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1508 = _this;
  _this setDir 58.411217;
  _this setPos [6976.4038, 12600.821, 0.00012207031];
};

_vehicle_1509 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [6971.3018, 12597.431, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1509 = _this;
  _this setDir 57.056942;
  _this setPos [6971.3018, 12597.431, 0.0001373291];
};

_vehicle_1510 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6975.4058, 12599.894, 0.00011444092], [], 0, "CAN_COLLIDE"];
  _vehicle_1510 = _this;
  _this setDir -121.8873;
  _this setPos [6975.4058, 12599.894, 0.00011444092];
};

_vehicle_1512 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [6970.1123, 12596.624, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1512 = _this;
  _this setDir -121.80616;
  _this setPos [6970.1123, 12596.624, 0.00012207031];
};

_vehicle_1514 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [6927.1353, 12586.135, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1514 = _this;
  _this setDir 81.585106;
  _this setPos [6927.1353, 12586.135, -9.1552734e-005];
};

_vehicle_1515 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6921.0547, 12585.356, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1515 = _this;
  _this setDir 82.177528;
  _this setPos [6921.0547, 12585.356, 0.00010681152];
};

_vehicle_1516 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_stodola_old_open", [6921.8286, 12595.678, 0.53553927], [], 0, "CAN_COLLIDE"];
  _vehicle_1516 = _this;
  _this setDir 71.347984;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6921.8286, 12595.678, 0.53553927];
};

_vehicle_1517 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6906.4751, 12623.252], [], 0, "CAN_COLLIDE"];
  _vehicle_1517 = _this;
  _this setDir -203.58159;
  _this setPos [6906.4751, 12623.252];
};

_vehicle_1518 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6916.4873, 12604.683, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1518 = _this;
  _this setDir -34.206474;
  _this setPos [6916.4873, 12604.683, -1.5258789e-005];
};

_vehicle_1520 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_50", [6913.0679, 12609.833], [], 0, "CAN_COLLIDE"];
  _vehicle_1520 = _this;
  _this setDir -33.268749;
  _this setPos [6913.0679, 12609.833];
};

_vehicle_1522 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1L2", [6981.959, 12614.967, 0.44451934], [], 0, "CAN_COLLIDE"];
  _vehicle_1522 = _this;
  _this setDir -473.64267;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [6981.959, 12614.967, 0.44451934];
};

_vehicle_1524 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6982.4941, 12605.068, 8.392334e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1524 = _this;
  _this setDir -30.238621;
  _this setPos [6982.4941, 12605.068, 8.392334e-005];
};

_vehicle_1525 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6976.168, 12615.826, 0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_1525 = _this;
  _this setDir 149.10025;
  _this setPos [6976.168, 12615.826, 0.00025939941];
};

_vehicle_1527 = objNull;
if (true) then
{
  _this = createVehicle ["dum_rasovna", [7026.6182, 12632.911, 0.29594436], [], 0, "CAN_COLLIDE"];
  _vehicle_1527 = _this;
  _this setDir -109.04535;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7026.6182, 12632.911, 0.29594436];
};

_vehicle_1531 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [7007.377, 12642.69, 0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_1531 = _this;
  _this setPos [7007.377, 12642.69, 0.00020599365];
};

_vehicle_1533 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7015.5737, 12646.202, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1533 = _this;
  _this setPos [7015.5737, 12646.202, 0.00012207031];
};

_vehicle_1534 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7018.5259, 12648.098, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1534 = _this;
  _this setPos [7018.5259, 12648.098, 7.6293945e-005];
};

_vehicle_1535 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2s", [7070.0674, 12645.347, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1535 = _this;
  _this setPos [7070.0674, 12645.347, 0.00016784668];
};

_vehicle_1536 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula1f", [7041.7788, 12656.22, -0.15930462], [], 0, "CAN_COLLIDE"];
  _vehicle_1536 = _this;
  _this setPos [7041.7788, 12656.22, -0.15930462];
};

_vehicle_1537 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_alnus2s", [7038.6865, 12641.632, 8.392334e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1537 = _this;
  _this setPos [7038.6865, 12641.632, 8.392334e-005];
};

_vehicle_1538 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pyrus2s", [7069.2002, 12661.312, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1538 = _this;
  _this setPos [7069.2002, 12661.312, -7.6293945e-006];
};

_vehicle_1543 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_misc", [7074.7197, 12649.212, 0.0001449585], [], 0, "CAN_COLLIDE"];
  _vehicle_1543 = _this;
  _this setDir -173.44803;
  _this setPos [7074.7197, 12649.212, 0.0001449585];
};

_vehicle_1547 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [7023.9307, 12626.081, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1547 = _this;
  _this setPos [7023.9307, 12626.081, 0];
};

_vehicle_1555 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sekyraspalek", [7016.5176, 12627.025, 0.0001449585], [], 0, "CAN_COLLIDE"];
  _vehicle_1555 = _this;
  _this setPos [7016.5176, 12627.025, 0.0001449585];
};

_vehicle_1556 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel7", [6919.9976, 12589.287, 0.00016021729], [], 0, "CAN_COLLIDE"];
  _vehicle_1556 = _this;
  _this setDir 0.95847416;
  _this setPos [6919.9976, 12589.287, 0.00016021729];
};

_vehicle_1557 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel5", [6919.3354, 12589.112, 0.0001449585], [], 0, "CAN_COLLIDE"];
  _vehicle_1557 = _this;
  _this setPos [6919.3354, 12589.112, 0.0001449585];
};

_vehicle_1558 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel3", [6918.6558, 12588.609, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1558 = _this;
  _this setPos [6918.6558, 12588.609, 3.0517578e-005];
};

_vehicle_1560 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_P_bedna", [6923.769, 12602.603, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_1560 = _this;
  _this setDir -24.109573;
  _this setPos [6923.769, 12602.603, 0.00024414063];
};

_vehicle_1561 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_box_c", [6978.8096, 12616.048, 0.27342093], [], 0, "CAN_COLLIDE"];
  _vehicle_1561 = _this;
  _this setDir 55.008629;
  _this setPos [6978.8096, 12616.048, 0.27342093];
};

_vehicle_1562 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_box_c", [6978.2998, 12616.627, 0.29642415], [], 0, "CAN_COLLIDE"];
  _vehicle_1562 = _this;
  _this setDir -22.766268;
  _this setPos [6978.2998, 12616.627, 0.29642415];
};

_vehicle_1563 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_P_bedna", [6923.9365, 12603.602, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1563 = _this;
  _this setDir -1.5700045;
  _this setPos [6923.9365, 12603.602, -3.0517578e-005];
};

_vehicle_1564 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_P_bedna", [6923.9565, 12603.222, 0.43221718], [], 0, "CAN_COLLIDE"];
  _vehicle_1564 = _this;
  _this setDir -50.937374;
  _this setPos [6923.9565, 12603.222, 0.43221718];
};

_vehicle_1569 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_drevena_bedna", [7044.4321, 12647.429, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1569 = _this;
  _this setDir -10.80087;
  _this setPos [7044.4321, 12647.429, 5.3405762e-005];
};

_vehicle_1580 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_paletaA", [6932.2217, 12593.206, 0.045610491], [], 0, "CAN_COLLIDE"];
  _vehicle_1580 = _this;
  _this setDir 71.433372;
  _this setPos [6932.2217, 12593.206, 0.045610491];
};

_vehicle_1581 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_paletyC", [6930.1152, 12592.49, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1581 = _this;
  _this setDir 69.350365;
  _this setPos [6930.1152, 12592.49, 0.00010681152];
};

_vehicle_1583 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_paletaA", [6932.4404, 12592.699, 0.18214826], [], 0, "CAN_COLLIDE"];
  _vehicle_1583 = _this;
  _this setPos [6932.4404, 12592.699, 0.18214826];
};

_vehicle_1584 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_paletaA", [6932.5703, 12592.572, 0.37318233], [], 0, "CAN_COLLIDE"];
  _vehicle_1584 = _this;
  _this setDir -100.86541;
  _this setPos [6932.5703, 12592.572, 0.37318233];
};

_vehicle_1587 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Wheel_cart_EP1", [7015.064, 12626.888, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_1587 = _this;
  _this setDir 28.398815;
  _this setPos [7015.064, 12626.888, 0.00022888184];
};

_vehicle_1590 = objNull;
if (true) then
{
  _this = createVehicle ["Land_tires_EP1", [6934.1421, 12593.898, 0.00025177002], [], 0, "CAN_COLLIDE"];
  _vehicle_1590 = _this;
  _this setDir -0.11649225;
  _this setPos [6934.1421, 12593.898, 0.00025177002];
};

_vehicle_1591 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [7016.4077, 12630.525, 0.079801075], [], 0, "CAN_COLLIDE"];
  _vehicle_1591 = _this;
  _this setDir -18.513771;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7016.4077, 12630.525, 0.079801075];
};

_vehicle_1592 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_can", [7028.061, 12631.343, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1592 = _this;
  _this setPos [7028.061, 12631.343, 7.6293945e-005];
};

_vehicle_1593 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Garb_Heap_EP1", [7028.3242, 12629.147, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1593 = _this;
  _this setPos [7028.3242, 12629.147, 1.5258789e-005];
};

_vehicle_1594 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_can", [7028.1738, 12632.01, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1594 = _this;
  _this setPos [7028.1738, 12632.01, -7.6293945e-005];
};

_vehicle_1597 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [6976.6162, 12630.46, 0.00012969971], [], 0, "CAN_COLLIDE"];
  _vehicle_1597 = _this;
  _this setPos [6976.6162, 12630.46, 0.00012969971];
};

_vehicle_1598 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [6982.3911, 12633.292, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1598 = _this;
  _this setPos [6982.3911, 12633.292, 7.6293945e-005];
};

_vehicle_1599 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [6964.5586, 12620.292, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1599 = _this;
  _this setPos [6964.5586, 12620.292, 0.0001373291];
};

_vehicle_1600 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [6958.5718, 12620.516, -3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1600 = _this;
  _this setPos [6958.5718, 12620.516, -3.8146973e-005];
};

_vehicle_1601 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6843.2485, 12600.711, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1601 = _this;
  _this setDir -194.30385;
  _this setPos [6843.2485, 12600.711, 7.6293945e-005];
};

_vehicle_1602 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [6848.4526, 12582.913, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_1602 = _this;
  _this setDir -18.277222;
  _this setPos [6848.4526, 12582.913, 0.00018310547];
};

_vehicle_1603 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [6846.5264, 12588.803, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1603 = _this;
  _this setDir -16.634195;
  _this setPos [6846.5264, 12588.803, 5.3405762e-005];
};

_vehicle_1604 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6836.3042, 12588.746, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1604 = _this;
  _this setDir 9.9132576;
  _this setPos [6836.3042, 12588.746, 3.0517578e-005];
};

_vehicle_1605 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [6830.8813, 12586.778, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1605 = _this;
  _this setDir -136.05931;
  _this setPos [6830.8813, 12586.778, -1.5258789e-005];
};

_vehicle_1608 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Wheel_cart_EP1", [6856.7471, 12567.686, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1608 = _this;
  _this setDir 131.05878;
  _this setPos [6856.7471, 12567.686, 0.0001373291];
};

_vehicle_1622 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Shed_W01_ruins", [6860.874, 12562.051, 0.00038146973], [], 0, "CAN_COLLIDE"];
  _vehicle_1622 = _this;
  _this setPos [6860.874, 12562.051, 0.00038146973];
};

_vehicle_1625 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_misc", [6640.0513, 12659.713, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1625 = _this;
  _this setDir 71.632889;
  _this setPos [6640.0513, 12659.713, 0.00015258789];
};

_vehicle_1626 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pneu", [6651.0049, 12659.593, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1626 = _this;
  _this setPos [6651.0049, 12659.593, -1.5258789e-005];
};

_vehicle_1627 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pneu", [6651.6602, 12660.42, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1627 = _this;
  _this setPos [6651.6602, 12660.42, 6.1035156e-005];
};

_vehicle_1628 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pneu", [6651.3501, 12659.839, 0.28007406], [], 0, "CAN_COLLIDE"];
  _vehicle_1628 = _this;
  _this setPos [6651.3501, 12659.839, 0.28007406];
};

_vehicle_1633 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7352.6621, 12649.608, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1633 = _this;
  _this setDir -140.00778;
  _this setPos [7352.6621, 12649.608, -1.5258789e-005];
};

_vehicle_1634 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7337.021, 12630.915, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1634 = _this;
  _this setDir 39.951077;
  _this setPos [7337.021, 12630.915, 7.6293945e-005];
};

_vehicle_1636 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_12", [7340.6836, 12635.255, 9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1636 = _this;
  _this setDir 39.639019;
  _this setPos [7340.6836, 12635.255, 9.9182129e-005];
};

_vehicle_1637 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7321.7559, 12651.75, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1637 = _this;
  _this setDir -55.115162;
  _this setPos [7321.7559, 12651.75, 1.5258789e-005];
};

_vehicle_1638 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7322.5142, 12651.148, 9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1638 = _this;
  _this setDir 40.469501;
  _this setPos [7322.5142, 12651.148, 9.9182129e-005];
};

_vehicle_1639 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7322.7197, 12652.261, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1639 = _this;
  _this setDir 151.06787;
  _this setPos [7322.7197, 12652.261, -1.5258789e-005];
};

_vehicle_1640 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7324.207, 12648.983, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1640 = _this;
  _this setDir -25.16869;
  _this setPos [7324.207, 12648.983, 7.6293945e-005];
};

_vehicle_1641 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7324.6431, 12651.287, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1641 = _this;
  _this setDir -84.955795;
  _this setPos [7324.6431, 12651.287, 3.0517578e-005];
};

_vehicle_1642 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7324.4829, 12650.752, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1642 = _this;
  _this setDir -208.063;
  _this setPos [7324.4829, 12650.752, 3.0517578e-005];
};

_vehicle_1643 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7325.0752, 12653.567, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1643 = _this;
  _this setDir -14.416996;
  _this setPos [7325.0752, 12653.567, 2.2888184e-005];
};

_vehicle_1644 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7319.6929, 12654.809, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1644 = _this;
  _this setDir 6.9112043;
  _this setPos [7319.6929, 12654.809, -4.5776367e-005];
};

_vehicle_1646 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7321.2417, 12654.271], [], 0, "CAN_COLLIDE"];
  _vehicle_1646 = _this;
  _this setPos [7321.2417, 12654.271];
};

_vehicle_1647 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7322.1938, 12655.635, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1647 = _this;
  _this setPos [7322.1938, 12655.635, 3.8146973e-005];
};

_vehicle_1648 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [7322.8569, 12657.938, 8.392334e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1648 = _this;
  _this setDir -77.482391;
  _this setPos [7322.8569, 12657.938, 8.392334e-005];
};

_vehicle_1649 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_flower_02", [7320.1929, 12646.714, -0.47969633], [], 0, "CAN_COLLIDE"];
  _vehicle_1649 = _this;
  _this setDir -67.316551;
  _this setPos [7320.1929, 12646.714, -0.47969633];
};

_vehicle_1650 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_flower_02", [7319.4805, 12646.25, -0.27590176], [], 0, "CAN_COLLIDE"];
  _vehicle_1650 = _this;
  _this setDir -13.886146;
  _this setPos [7319.4805, 12646.25, -0.27590176];
};

_vehicle_1651 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_flower_02", [7321.0337, 12647.313, -0.47823164], [], 0, "CAN_COLLIDE"];
  _vehicle_1651 = _this;
  _this setDir 177.71091;
  _this setPos [7321.0337, 12647.313, -0.47823164];
};

_vehicle_1653 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [7318.6743, 12648.626, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1653 = _this;
  _this setDir 9.6967964;
  _this setPos [7318.6743, 12648.626, 5.3405762e-005];
};

_vehicle_1654 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [7319.5278, 12649.479, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1654 = _this;
  _this setDir -6.7895379;
  _this setPos [7319.5278, 12649.479, 7.6293945e-006];
};

_vehicle_1655 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [7317.8916, 12648.08, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1655 = _this;
  _this setPos [7317.8916, 12648.08, 0.00010681152];
};

_vehicle_1656 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [7318.5073, 12651.311, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1656 = _this;
  _this setDir -8.3848543;
  _this setPos [7318.5073, 12651.311, 6.8664551e-005];
};

_vehicle_1657 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [7316.8892, 12650.427, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1657 = _this;
  _this setDir 5.0580406;
  _this setPos [7316.8892, 12650.427, 7.6293945e-005];
};

_vehicle_1658 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [7316.3647, 12649.834, 0.00012969971], [], 0, "CAN_COLLIDE"];
  _vehicle_1658 = _this;
  _this setDir -3.8152609;
  _this setPos [7316.3647, 12649.834, 0.00012969971];
};

_vehicle_1659 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [7317.6807, 12650.781, 9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1659 = _this;
  _this setDir 14.684963;
  _this setPos [7317.6807, 12650.781, 9.9182129e-005];
};

_vehicle_1663 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_urtica", [7315.5098, 12651.997, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1663 = _this;
  _this setDir -92.116547;
  _this setPos [7315.5098, 12651.997, 4.5776367e-005];
};

_vehicle_1664 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_urtica", [7316.9219, 12652.838, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1664 = _this;
  _this setDir -7.1883774;
  _this setPos [7316.9219, 12652.838, 7.6293945e-005];
};

_vehicle_1665 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_urtica", [7316.3345, 12652.73, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1665 = _this;
  _this setDir -179.16905;
  _this setPos [7316.3345, 12652.73, 2.2888184e-005];
};

_vehicle_1666 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_urtica", [7314.5635, 12651.461, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1666 = _this;
  _this setDir 32.632683;
  _this setPos [7314.5635, 12651.461, 9.1552734e-005];
};

_vehicle_1667 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_flower_02", [7318.4507, 12645.489, -0.25029564], [], 0, "CAN_COLLIDE"];
  _vehicle_1667 = _this;
  _this setPos [7318.4507, 12645.489, -0.25029564];
};

_vehicle_1668 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Boogieman", [7319.7676, 12651.01, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1668 = _this;
  _this setDir -136.21242;
  _this setPos [7319.7676, 12651.01, 6.8664551e-005];
};

_vehicle_1670 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7325.251, 12651.578, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1670 = _this;
  _this setPos [7325.251, 12651.578, 3.0517578e-005];
};

_vehicle_1671 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7322.0806, 12649.69, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1671 = _this;
  _this setPos [7322.0806, 12649.69, -1.5258789e-005];
};

_vehicle_1672 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7319.3047, 12647.257, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1672 = _this;
  _this setPos [7319.3047, 12647.257, 7.6293945e-005];
};

_vehicle_1673 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7322.6201, 12656.266, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1673 = _this;
  _this setPos [7322.6201, 12656.266, 7.6293945e-006];
};

_vehicle_1675 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7320.8486, 12654.381, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1675 = _this;
  _this setPos [7320.8486, 12654.381, 0.00010681152];
};

_vehicle_1676 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7318.1763, 12653.028, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1676 = _this;
  _this setPos [7318.1763, 12653.028, 7.6293945e-005];
};

_vehicle_1677 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7316.0771, 12651.115, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1677 = _this;
  _this setPos [7316.0771, 12651.115, 4.5776367e-005];
};

_vehicle_1678 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7317.0845, 12648.686, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1678 = _this;
  _this setPos [7317.0845, 12648.686, 9.1552734e-005];
};

_vehicle_1679 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7320.0659, 12650.748, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1679 = _this;
  _this setPos [7320.0659, 12650.748, -2.2888184e-005];
};

_vehicle_1680 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7322.3623, 12652.264, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1680 = _this;
  _this setPos [7322.3623, 12652.264, 3.0517578e-005];
};

_vehicle_1681 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7324.5122, 12653.726, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1681 = _this;
  _this setPos [7324.5122, 12653.726, -7.6293945e-006];
};

_vehicle_1682 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7322.1279, 12648.418, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1682 = _this;
  _this setPos [7322.1279, 12648.418, 7.6293945e-006];
};

_vehicle_1683 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7324.3687, 12649.81, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1683 = _this;
  _this setPos [7324.3687, 12649.81, 3.8146973e-005];
};

_vehicle_1684 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [7326.5532, 12641.519, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_1684 = _this;
  _this setDir 219.30113;
  _this setPos [7326.5532, 12641.519, 0.00024414063];
};

_vehicle_1685 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [7327.7002, 12640.423, -0.72844279], [], 0, "CAN_COLLIDE"];
  _vehicle_1685 = _this;
  _this setDir 49.638664;
  _this setPos [7327.7002, 12640.423, -0.72844279];
};

_vehicle_1686 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7431.4277, 12585.092, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1686 = _this;
  _this setDir 41.540382;
  _this setPos [7431.4277, 12585.092, 2.2888184e-005];
};

_vehicle_1688 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [7435.5254, 12589.688], [], 0, "CAN_COLLIDE"];
  _vehicle_1688 = _this;
  _this setDir 40.169151;
  _this setPos [7435.5254, 12589.688];
};

_vehicle_1689 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7443.5771, 12599, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1689 = _this;
  _this setDir -138.62413;
  _this setPos [7443.5771, 12599, -7.6293945e-006];
};

_vehicle_1690 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [7690.6338, 12352.288, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1690 = _this;
  _this setDir 41.616348;
  _this setPos [7690.6338, 12352.288, 0.00010681152];
};

_vehicle_1691 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [7699.9473, 12355.394, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1691 = _this;
  _this setDir 102.85555;
  _this setPos [7699.9473, 12355.394, 0.0001373291];
};

_vehicle_1692 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [7707.2363, 12348.612, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1692 = _this;
  _this setDir 164.07605;
  _this setPos [7707.2363, 12348.612, -9.1552734e-005];
};

_vehicle_1693 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7686.4189, 12347.741, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1693 = _this;
  _this setDir 42.745411;
  _this setPos [7686.4189, 12347.741, 0.00016784668];
};

_vehicle_1694 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7700.3945, 12334.595, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1694 = _this;
  _this setDir 45.469757;
  _this setPos [7700.3945, 12334.595, 0.00012207031];
};

_vehicle_1695 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7707.4399, 12357.737, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1695 = _this;
  _this setPos [7707.4399, 12357.737, 7.6293945e-005];
};

_vehicle_1696 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7706.1553, 12359.051, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1696 = _this;
  _this setDir -109.80006;
  _this setPos [7706.1553, 12359.051, 0.0001373291];
};

_vehicle_1697 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7704.2764, 12361.042, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1697 = _this;
  _this setDir 73.641235;
  _this setPos [7704.2764, 12361.042, 1.5258789e-005];
};

_vehicle_1699 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7699.6265, 12340.964, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1699 = _this;
  _this setDir 104.79282;
  _this setPos [7699.6265, 12340.964, 0.00012207031];
};

_vehicle_1700 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7701.2515, 12347.41, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_1700 = _this;
  _this setDir -57.255985;
  _this setPos [7701.2515, 12347.41, 0.00019836426];
};

_vehicle_1701 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7694.8242, 12345.735, 0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_1701 = _this;
  _this setDir -161.01334;
  _this setPos [7694.8242, 12345.735, 0.00025939941];
};

_vehicle_1702 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7826.1855, 12176.85, -1.4174494], [], 0, "CAN_COLLIDE"];
  _vehicle_1702 = _this;
  _this setDir 46.993423;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7826.1855, 12176.85, -1.4174494];
};

_vehicle_1703 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7824.6226, 12176.384, -1.7403976], [], 0, "CAN_COLLIDE"];
  _vehicle_1703 = _this;
  _this setDir 47.102135;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7824.6226, 12176.384, -1.7403976];
};

_vehicle_1704 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7824.1768, 12176.874, -1.9090143], [], 0, "CAN_COLLIDE"];
  _vehicle_1704 = _this;
  _this setDir 47.067951;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7824.1768, 12176.874, -1.9090143];
};

_vehicle_1706 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_12", [7822.4692, 12185.909, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1706 = _this;
  _this setDir 48.060043;
  _this setPos [7822.4692, 12185.909, 7.6293945e-005];
};

_vehicle_1707 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7817.8315, 12181.754, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1707 = _this;
  _this setDir 48.166641;
  _this setPos [7817.8315, 12181.754, -1.5258789e-005];
};

_vehicle_1708 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7836.3506, 12198.377, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1708 = _this;
  _this setDir -131.99306;
  _this setPos [7836.3506, 12198.377, -6.1035156e-005];
};

_vehicle_1709 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7765.4565, 12267.523, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1709 = _this;
  _this setDir -133.18451;
  _this setPos [7765.4565, 12267.523, 0.00015258789];
};

_vehicle_1710 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7760.9248, 12263.293], [], 0, "CAN_COLLIDE"];
  _vehicle_1710 = _this;
  _this setDir -131.52203;
  _this setPos [7760.9248, 12263.293];
};

_vehicle_1711 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7746.9653, 12253.009, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1711 = _this;
  _this setDir -121.46101;
  _this setPos [7746.9653, 12253.009, 0.0001373291];
};

_vehicle_1712 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7731.3931, 12245.272, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1712 = _this;
  _this setDir -110.05588;
  _this setPos [7731.3931, 12245.272, -0.0001373291];
};

_vehicle_1713 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7636.7422, 12213.873, 0.00039672852], [], 0, "CAN_COLLIDE"];
  _vehicle_1713 = _this;
  _this setDir 73.15461;
  _this setPos [7636.7422, 12213.873, 0.00039672852];
};

_vehicle_1714 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7660.5889, 12221.062, -0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_1714 = _this;
  _this setDir 72.192276;
  _this setPos [7660.5889, 12221.062, -0.00019836426];
};

_vehicle_1715 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7684.2705, 12228.653, -0.00048828125], [], 0, "CAN_COLLIDE"];
  _vehicle_1715 = _this;
  _this setDir 71.150986;
  _this setPos [7684.2705, 12228.653, -0.00048828125];
};

_vehicle_1716 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7636.8608, 12213.962, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_1716 = _this;
  _this setDir -104.80172;
  _this setPos [7636.8608, 12213.962, 0.00021362305];
};

_vehicle_1717 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7619.8711, 12211.088, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1717 = _this;
  _this setDir -91.943146;
  _this setPos [7619.8711, 12211.088, 9.1552734e-005];
};

_vehicle_1719 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7602.5952, 12212.023, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1719 = _this;
  _this setDir -80.371262;
  _this setPos [7602.5952, 12212.023, 9.1552734e-005];
};

_vehicle_1720 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7585.8154, 12216.403, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1720 = _this;
  _this setDir -69.561058;
  _this setPos [7585.8154, 12216.403, -9.1552734e-005];
};

_vehicle_1721 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [7565.0562, 12232.411, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1721 = _this;
  _this setDir 0.17388183;
  _this setPos [7565.0562, 12232.411, 3.0517578e-005];
};

_vehicle_1722 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [7570.0518, 12241.022, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1722 = _this;
  _this setDir 60.336784;
  _this setPos [7570.0518, 12241.022, 0.00015258789];
};

_vehicle_1723 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [7580.0063, 12240.974, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1723 = _this;
  _this setDir 119.66552;
  _this setPos [7580.0063, 12240.974, 0.00016784668];
};

_vehicle_1724 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [7570.1309, 12223.862, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_1724 = _this;
  _this setDir -60.619057;
  _this setPos [7570.1309, 12223.862, 0.00018310547];
};

_vehicle_1726 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_30_25", [7588.6855, 12219.971, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_1726 = _this;
  _this setDir -31.463047;
  _this setPos [7588.6855, 12219.971, 0.00022888184];
};

_vehicle_1727 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_30_25", [7599.0127, 12212.62, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1727 = _this;
  _this setDir -69.406456;
  _this setPos [7599.0127, 12212.62, 0.00010681152];
};

_vehicle_1728 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_molo_drevo_end", [7573.8066, 12212.765, -1.0530604], [], 0, "CAN_COLLIDE"];
  _vehicle_1728 = _this;
  _this setDir 202.29356;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7573.8066, 12212.765, -1.0530604];
};

_vehicle_1729 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_molo_drevo_end", [7567.1162, 12213.778, -1.4188623], [], 0, "CAN_COLLIDE"];
  _vehicle_1729 = _this;
  _this setDir 112.04086;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7567.1162, 12213.778, -1.4188623];
};

_vehicle_1730 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stodola_old_open", [7531.3516, 12205.277, 0.64860314], [], 0, "CAN_COLLIDE"];
  _vehicle_1730 = _this;
  _this setDir -72.245796;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7531.3516, 12205.277, 0.64860314];
};

_vehicle_1731 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7533.5698, 12211.871, -0.13345061], [], 0, "CAN_COLLIDE"];
  _vehicle_1731 = _this;
  _this setDir 17.562695;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7533.5698, 12211.871, -0.13345061];
};

_vehicle_1732 = objNull;
if (true) then
{
  _this = createVehicle ["RampConcrete", [7525.4731, 12215.833, -0.33457062], [], 0, "CAN_COLLIDE"];
  _vehicle_1732 = _this;
  _this setDir -71.300262;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7525.4731, 12215.833, -0.33457062];
};

_vehicle_1733 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7540.3784, 12209.746, 0.17226371], [], 0, "CAN_COLLIDE"];
  _vehicle_1733 = _this;
  _this setDir 17.465654;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7540.3784, 12209.746, 0.17226371];
};

_vehicle_1734 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7475.6411, 12264.571, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1734 = _this;
  _this setPos [7475.6411, 12264.571, 0.00016784668];
};

_vehicle_1735 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7479.4033, 12233.572, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1735 = _this;
  _this setPos [7479.4033, 12233.572, 0.0001373291];
};

_vehicle_1736 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7442.8447, 12256.979, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1736 = _this;
  _this setPos [7442.8447, 12256.979, 0.00010681152];
};

_vehicle_1737 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7461.2583, 12281.235, 0.00076293945], [], 0, "CAN_COLLIDE"];
  _vehicle_1737 = _this;
  _this setPos [7461.2583, 12281.235, 0.00076293945];
};

_vehicle_1738 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7490.2441, 12296.327, 0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_1738 = _this;
  _this setPos [7490.2441, 12296.327, 0.00028991699];
};

_vehicle_1739 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7457.0581, 12302.726, 0.00082397461], [], 0, "CAN_COLLIDE"];
  _vehicle_1739 = _this;
  _this setPos [7457.0581, 12302.726, 0.00082397461];
};

_vehicle_1740 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7499.0615, 12259.199, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1740 = _this;
  _this setPos [7499.0615, 12259.199, 1.5258789e-005];
};

_vehicle_1741 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7493.7959, 12322.882, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1741 = _this;
  _this setPos [7493.7959, 12322.882, 0.00016784668];
};

_vehicle_1742 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7470.686, 12334.016, 0.00047302246], [], 0, "CAN_COLLIDE"];
  _vehicle_1742 = _this;
  _this setPos [7470.686, 12334.016, 0.00047302246];
};

_vehicle_1743 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7417.3237, 12268.26, 0.00071716309], [], 0, "CAN_COLLIDE"];
  _vehicle_1743 = _this;
  _this setPos [7417.3237, 12268.26, 0.00071716309];
};

_vehicle_1744 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7438.3662, 12289.996, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1744 = _this;
  _this setPos [7438.3662, 12289.996, -0.00012207031];
};

_vehicle_1745 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7384.1855, 12282.176, 0.0007019043], [], 0, "CAN_COLLIDE"];
  _vehicle_1745 = _this;
  _this setPos [7384.1855, 12282.176, 0.0007019043];
};

_vehicle_1746 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7355.5645, 12305.945, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_1746 = _this;
  _this setPos [7355.5645, 12305.945, 0.00021362305];
};

_vehicle_1747 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7334.0117, 12332.626, 0.00076293945], [], 0, "CAN_COLLIDE"];
  _vehicle_1747 = _this;
  _this setPos [7334.0117, 12332.626, 0.00076293945];
};

_vehicle_1748 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7341.4819, 12363.61, 0.00074768066], [], 0, "CAN_COLLIDE"];
  _vehicle_1748 = _this;
  _this setPos [7341.4819, 12363.61, 0.00074768066];
};

_vehicle_1749 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7378.1006, 12337.94, 0.00093078613], [], 0, "CAN_COLLIDE"];
  _vehicle_1749 = _this;
  _this setPos [7378.1006, 12337.94, 0.00093078613];
};

_vehicle_1750 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7401.3667, 12311.251, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1750 = _this;
  _this setPos [7401.3667, 12311.251, -9.1552734e-005];
};

_vehicle_1751 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7435.4492, 12320.271, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_1751 = _this;
  _this setPos [7435.4492, 12320.271, 0.00021362305];
};

_vehicle_1752 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7445.791, 12346.355, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1752 = _this;
  _this setPos [7445.791, 12346.355, -1.5258789e-005];
};

_vehicle_1753 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7412.5215, 12353.208, -0.00044250488], [], 0, "CAN_COLLIDE"];
  _vehicle_1753 = _this;
  _this setPos [7412.5215, 12353.208, -0.00044250488];
};

_vehicle_1754 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7378.9043, 12373.565, 0.00061035156], [], 0, "CAN_COLLIDE"];
  _vehicle_1754 = _this;
  _this setPos [7378.9043, 12373.565, 0.00061035156];
};

_vehicle_1755 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7409.6641, 12392.464, 0.0005645752], [], 0, "CAN_COLLIDE"];
  _vehicle_1755 = _this;
  _this setPos [7409.6641, 12392.464, 0.0005645752];
};

_vehicle_1756 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7386.0654, 12406.75, 0.00050354004], [], 0, "CAN_COLLIDE"];
  _vehicle_1756 = _this;
  _this setPos [7386.0654, 12406.75, 0.00050354004];
};

_vehicle_1757 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7435.8647, 12373.946, 0.00038146973], [], 0, "CAN_COLLIDE"];
  _vehicle_1757 = _this;
  _this setPos [7435.8647, 12373.946, 0.00038146973];
};

_vehicle_1758 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7433.1401, 12417.366, 0.00080871582], [], 0, "CAN_COLLIDE"];
  _vehicle_1758 = _this;
  _this setPos [7433.1401, 12417.366, 0.00080871582];
};

_vehicle_1759 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7459.5039, 12401.949, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1759 = _this;
  _this setPos [7459.5039, 12401.949, -7.6293945e-005];
};

_vehicle_1760 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7463.3433, 12369.198, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_1760 = _this;
  _this setPos [7463.3433, 12369.198, 0.00022888184];
};

_vehicle_1761 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7490.9937, 12390.855, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1761 = _this;
  _this setPos [7490.9937, 12390.855, 0.0001373291];
};

_vehicle_1762 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7484.8032, 12359.292, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1762 = _this;
  _this setPos [7484.8032, 12359.292, 1.5258789e-005];
};

_vehicle_1763 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7505.4683, 12348.31, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_1763 = _this;
  _this setPos [7505.4683, 12348.31, 0.00018310547];
};

_vehicle_1764 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7513.0693, 12380.049, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_1764 = _this;
  _this setPos [7513.0693, 12380.049, 0.00022888184];
};

_vehicle_1765 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack_small", [7330.165, 12290.457, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_1765 = _this;
  _this setPos [7330.165, 12290.457, 0.00022888184];
};

_vehicle_1766 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_GContainer_Big", [7546.5303, 12197.002, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1766 = _this;
  _this setDir -49.007706;
  _this setPos [7546.5303, 12197.002, -3.0517578e-005];
};

_vehicle_1767 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [7556.0059, 12198.78, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1767 = _this;
  _this setPos [7556.0059, 12198.78, 9.1552734e-005];
};

_vehicle_1769 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7557.4766, 12198.658, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1769 = _this;
  _this setPos [7557.4766, 12198.658, 4.5776367e-005];
};

_vehicle_1771 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Coil_EP1", [7522.6694, 12197.723, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_1771 = _this;
  _this setPos [7522.6694, 12197.723, 0.0002746582];
};

_vehicle_1772 = objNull;
if (true) then
{
  _this = createVehicle ["Barrels", [7545.9253, 12208.395, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_1772 = _this;
  _this setDir 13.767712;
  _this setPos [7545.9253, 12208.395, 0.00021362305];
};

_vehicle_1773 = objNull;
if (true) then
{
  _this = createVehicle ["Barrels", [7546.3999, 12210.052, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1773 = _this;
  _this setDir -71.189568;
  _this setPos [7546.3999, 12210.052, 9.1552734e-005];
};

_vehicle_1774 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta2", [7545.1167, 12200.378, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_1774 = _this;
  _this setPos [7545.1167, 12200.378, 0.00036621094];
};

_vehicle_1775 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [7576.4092, 12204.708, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1775 = _this;
  _this setDir 21.545992;
  _this setPos [7576.4092, 12204.708, 0.00016784668];
};

_vehicle_1776 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [7569.873, 12203.007, 0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_1776 = _this;
  _this setDir -157.82106;
  _this setPos [7569.873, 12203.007, 0.00025939941];
};

_vehicle_1777 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [7571.5068, 12202.266, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1777 = _this;
  _this setDir 1.8421766;
  _this setPos [7571.5068, 12202.266, -1.5258789e-005];
};

_vehicle_1778 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pyrus2s", [7599.7227, 12190.005, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1778 = _this;
  _this setPos [7599.7227, 12190.005, -9.1552734e-005];
};

_vehicle_1780 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2s", [7576.5562, 12188.487, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1780 = _this;
  _this setPos [7576.5562, 12188.487, 0.00010681152];
};

_vehicle_1781 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2f", [7545.46, 12228.386, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1781 = _this;
  _this setPos [7545.46, 12228.386, -0.00015258789];
};

_vehicle_1782 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2w", [7581.5459, 12210.235, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1782 = _this;
  _this setDir 7.6652908;
  _this setPos [7581.5459, 12210.235, -3.0517578e-005];
};

_vehicle_1783 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_carpinus2s", [7576.877, 12232.393], [], 0, "CAN_COLLIDE"];
  _vehicle_1783 = _this;
  _this setPos [7576.877, 12232.393];
};

_vehicle_1784 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7615.792, 12250.606, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1784 = _this;
  _this setDir 31.935427;
  _this setPos [7615.792, 12250.606, -0.0001373291];
};

_vehicle_1785 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7608.6841, 12253.661, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_1785 = _this;
  _this setPos [7608.6841, 12253.661, 0.00021362305];
};

_vehicle_1786 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7622.2007, 12248.117, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_1786 = _this;
  _this setDir 65.930191;
  _this setPos [7622.2007, 12248.117, 0.00019836426];
};

_vehicle_1787 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7629.0239, 12245.282, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1787 = _this;
  _this setDir -44.733139;
  _this setPos [7629.0239, 12245.282, 0.0001373291];
};

_vehicle_1788 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7634.9194, 12243.197, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_1788 = _this;
  _this setPos [7634.9194, 12243.197, 0.00030517578];
};

_vehicle_1789 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7636.5913, 12249.988, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1789 = _this;
  _this setDir -45.873459;
  _this setPos [7636.5913, 12249.988, 3.0517578e-005];
};

_vehicle_1790 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7630.4116, 12252.008, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1790 = _this;
  _this setPos [7630.4116, 12252.008, 0.0001373291];
};

_vehicle_1791 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7624.1934, 12254.725, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1791 = _this;
  _this setDir 108.38704;
  _this setPos [7624.1934, 12254.725, 3.0517578e-005];
};

_vehicle_1792 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7617.7578, 12257.648, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1792 = _this;
  _this setDir -36.310795;
  _this setPos [7617.7578, 12257.648, 0.00012207031];
};

_vehicle_1793 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7611.5977, 12260.502, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1793 = _this;
  _this setPos [7611.5977, 12260.502, -9.1552734e-005];
};

_vehicle_1794 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7601.6963, 12257.029, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_1794 = _this;
  _this setDir -59.944;
  _this setPos [7601.6963, 12257.029, 0.00019836426];
};

_vehicle_1795 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7604.8745, 12262.647, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1795 = _this;
  _this setDir 34.569817;
  _this setPos [7604.8745, 12262.647, -4.5776367e-005];
};

_vehicle_1796 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7638.9995, 12257.244, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1796 = _this;
  _this setDir -100.66045;
  _this setPos [7638.9995, 12257.244, 0.00010681152];
};

_vehicle_1797 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7632.8037, 12259.177, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_1797 = _this;
  _this setPos [7632.8037, 12259.177, 0.00018310547];
};

_vehicle_1798 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7625.9932, 12261.597, 0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_1798 = _this;
  _this setPos [7625.9932, 12261.597, 0.00025939941];
};

_vehicle_1799 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7619.4229, 12264.06, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1799 = _this;
  _this setDir 148.77048;
  _this setPos [7619.4229, 12264.06, 0.00012207031];
};

_vehicle_1800 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7613.75, 12267.251, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1800 = _this;
  _this setDir -98.279091;
  _this setPos [7613.75, 12267.251, -0.00010681152];
};

_vehicle_1801 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7607.8423, 12270.078, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1801 = _this;
  _this setDir 26.98481;
  _this setPos [7607.8423, 12270.078, 0.00016784668];
};

_vehicle_1802 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7647.5474, 12388.468, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_1802 = _this;
  _this setDir 39.155903;
  _this setPos [7647.5474, 12388.468, 0.00018310547];
};

_vehicle_1803 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7661.6001, 12407.456, 0.00053405762], [], 0, "CAN_COLLIDE"];
  _vehicle_1803 = _this;
  _this setDir -149.5408;
  _this setPos [7661.6001, 12407.456, 0.00053405762];
};

_vehicle_1804 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7668.9438, 12423.213, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_1804 = _this;
  _this setDir -160.06282;
  _this setPos [7668.9438, 12423.213, 0.00024414063];
};

_vehicle_1805 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7668.9014, 12423.127, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1805 = _this;
  _this setDir 18.3491;
  _this setPos [7668.9014, 12423.127, 9.1552734e-005];
};

_vehicle_1807 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7676.7627, 12446.779, 0.00026702881], [], 0, "CAN_COLLIDE"];
  _vehicle_1807 = _this;
  _this setDir 18.3491;
  _this setPos [7676.7627, 12446.779, 0.00026702881];
};

_vehicle_1808 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7684.624, 12470.414, 0.0001449585], [], 0, "CAN_COLLIDE"];
  _vehicle_1808 = _this;
  _this setDir 19.527296;
  _this setPos [7684.624, 12470.414, 0.0001449585];
};

_vehicle_1809 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7692.9629, 12493.973, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1809 = _this;
  _this setDir 19.541533;
  _this setPos [7692.9629, 12493.973, 5.3405762e-005];
};

_vehicle_1810 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7700.1885, 12509.758, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1810 = _this;
  _this setDir 29.455067;
  _this setPos [7700.1885, 12509.758, 3.8146973e-005];
};

_vehicle_1811 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7710.0073, 12524.089, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1811 = _this;
  _this setDir 40.489464;
  _this setPos [7710.0073, 12524.089, -7.6293945e-006];
};

_vehicle_1812 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7726.2124, 12543.053, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1812 = _this;
  _this setDir 39.816498;
  _this setPos [7726.2124, 12543.053, 6.8664551e-005];
};

_vehicle_1813 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7738.4912, 12555.334, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1813 = _this;
  _this setDir 50.912697;
  _this setPos [7738.4912, 12555.334, 7.6293945e-006];
};

_vehicle_1814 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7752.8774, 12565.07, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1814 = _this;
  _this setDir 62.451569;
  _this setPos [7752.8774, 12565.07, 0.0001373291];
};

_vehicle_1817 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7768.8867, 12571.738, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1817 = _this;
  _this setDir 74.682693;
  _this setPos [7768.8867, 12571.738, 0.00010681152];
};

_vehicle_1822 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7799.0264, 12579.769, 0.00022125244], [], 0, "CAN_COLLIDE"];
  _vehicle_1822 = _this;
  _this setDir -103.20091;
  _this setPos [7799.0264, 12579.769, 0.00022125244];
};


};