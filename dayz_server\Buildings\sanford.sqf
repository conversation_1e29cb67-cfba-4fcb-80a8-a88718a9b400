if (isServer) then {

_vehicle_0 = objNull;
if (true) then
{
  _this = createVehicle ["TentStorage", [9912.8926, 5436.6812, 0.058055971], [], 0, "CAN_COLLIDE"];
  _vehicle_0 = _this;
  _this setDir -0.14028268;
  _this setPos [9912.8926, 5436.6812, 0.058055971];
};

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["Rubbish5", [9910.8506, 5423.9985, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setPos [9910.8506, 5423.9985, -4.5776367e-005];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["Rubbish5", [9910.7021, 5426.2212], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setPos [9910.7021, 5426.2212];
};

_vehicle_6 = objNull;
if (true) then
{
  _this = createVehicle ["Stove", [9908.3633, 5432.6426, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_6 = _this;
  _this setDir 0.68547076;
  _this setPos [9908.3633, 5432.6426, 1.5258789e-005];
};

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["VaultStorage", [9907.0859, 5429.582], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setDir -83.011688;
  _this setPos [9907.0859, 5429.582];
};

_vehicle_10 = objNull;
if (true) then
{
  _this = createVehicle ["ItemStorage", [9907.4609, 5428.3628, -0.034978073], [], 0, "CAN_COLLIDE"];
  _vehicle_10 = _this;
  _this setDir 101.79906;
  _this setPos [9907.4609, 5428.3628, -0.034978073];
};

_vehicle_17 = objNull;
if (true) then
{
  _this = createVehicle ["TargetEpopup", [9908.0078, 5425.9829, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_17 = _this;
  _this setDir 9.2431917;
  _this setPos [9908.0078, 5425.9829, -1.5258789e-005];
};

_vehicle_19 = objNull;
if (true) then
{
  _this = createVehicle ["TargetEpopup", [9908.709, 5425.833, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_19 = _this;
  _this setDir 9.2431917;
  _this setPos [9908.709, 5425.833, 1.5258789e-005];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["ItemStorage", [9907.2705, 5427.3706], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir 101.79906;
  _this setPos [9907.2705, 5427.3706];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["ItemStorage", [9907.3525, 5427.8491, 0.29761362], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir 101.79906;
  _this setPos [9907.3525, 5427.8491, 0.29761362];
};

_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["UAZWreck", [9910.0986, 5438.5459], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setPos [9910.0986, 5438.5459];
};

_vehicle_30 = objNull;
if (true) then
{
  _this = createVehicle ["T72Wreck", [9909.9014, 5419.1016, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_30 = _this;
  _this setPos [9909.9014, 5419.1016, 6.1035156e-005];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["Loudspeaker", [9907.625, 5430.7222, 3.3183823], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir -77.050476;
  _this setPos [9907.625, 5430.7222, 3.3183823];
};

_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["GunRack_DZ", [9907.8887, 5430.6372, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir 8.6717949;
  _this setPos [9907.8887, 5430.6372, 1.5258789e-005];
};

_vehicle_37 = objNull;
if (true) then
{
  _this = createVehicle ["MetalFence_7", [9916.4131, 5417.1807, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_37 = _this;
  _this setDir 121.82781;
  _this setPos [9916.4131, 5417.1807, -1.5258789e-005];
};

_vehicle_38 = objNull;
if (true) then
{
  _this = createVehicle ["MetalFence_6", [9913.96, 5412.9668, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_38 = _this;
  _this setDir -237.07593;
  _this setPos [9913.96, 5412.9668, -3.0517578e-005];
};

_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["MetalFence_1", [9918.2607, 5421.3442, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setDir 468.57031;
  _this setPos [9918.2607, 5421.3442, 1.5258789e-005];
};

_vehicle_42 = objNull;
if (true) then
{
  _this = createVehicle ["MetalFence_6", [9910.7627, 5409.4028, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_42 = _this;
  _this setDir -218.52751;
  _this setPos [9910.7627, 5409.4028, -0.00010681152];
};

_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["MetalFence_6", [9906.251, 5407.7158], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setDir -182.70139;
  _this setPos [9906.251, 5407.7158];
};

_vehicle_49 = objNull;
if (true) then
{
  _this = createVehicle ["MetalFence_7", [9921.0742, 5435.2754, 0.0081329346], [], 0, "CAN_COLLIDE"];
  _vehicle_49 = _this;
  _this setDir 81.168373;
  _this setPos [9921.0742, 5435.2754, 0.0081329346];
};

_vehicle_50 = objNull;
if (true) then
{
  _this = createVehicle ["MetalFence_6", [9915.5469, 5442.9077, 0.00064086914], [], 0, "CAN_COLLIDE"];
  _vehicle_50 = _this;
  _this setDir -334.38617;
  _this setPos [9915.5469, 5442.9077, 0.00064086914];
};

_vehicle_56 = objNull;
if (true) then
{
  _this = createVehicle ["MetalFence_6", [9919.3545, 5439.832], [], 0, "CAN_COLLIDE"];
  _vehicle_56 = _this;
  _this setDir -304.06714;
  _this setPos [9919.3545, 5439.832];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["OutHouse_DZ", [9909.0439, 5435.8496, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir -82.214897;
  _this setPos [9909.0439, 5435.8496, 1.5258789e-005];
};

_vehicle_60 = objNull;
if (true) then
{
  _this = createVehicle ["MetalGate_DZ", [9918.916, 5423.8345, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_60 = _this;
  _this setDir 104.1103;
  _this setPos [9918.916, 5423.8345, 3.0517578e-005];
};

_vehicle_66 = objNull;
if (true) then
{
  _this = createVehicle ["MetalFence_1", [9920.708, 5430.2412, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_66 = _this;
  _this setDir 465.2045;
  _this setPos [9920.708, 5430.2412, -1.5258789e-005];
};

_vehicle_68 = objNull;
if (true) then
{
  _this = createVehicle ["ParkBench_DZ", [9916.2686, 5437.5698, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_68 = _this;
  _this setPos [9916.2686, 5437.5698, 1.5258789e-005];
};

_vehicle_70 = objNull;
if (true) then
{
  _this = createVehicle ["ParkBench_DZ", [9914.5068, 5436.8828, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_70 = _this;
  _this setDir -47.623066;
  _this setPos [9914.5068, 5436.8828, 4.5776367e-005];
};

_vehicle_73 = objNull;
if (true) then
{
  _this = createVehicle ["ParkBench_DZ", [9918.04, 5436.8105, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_73 = _this;
  _this setDir -136.49701;
  _this setPos [9918.04, 5436.8105, 3.0517578e-005];
};

_vehicle_75 = objNull;
if (true) then
{
  _this = createVehicle ["WoodCrate_DZ", [9911.4893, 5438.3794, -0.0058398391], [], 0, "CAN_COLLIDE"];
  _vehicle_75 = _this;
  _this setPos [9911.4893, 5438.3794, -0.0058398391];
};

_vehicle_77 = objNull;
if (true) then
{
  _this = createVehicle ["WoodCrate_DZ", [9911.5039, 5437.2983, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_77 = _this;
  _this setPos [9911.5039, 5437.2983, 4.5776367e-005];
};

_vehicle_80 = objNull;
if (true) then
{
  _this = createVehicle ["WoodCrate_DZ", [9911.4756, 5437.9121, 0.93481213], [], 0, "CAN_COLLIDE"];
  _vehicle_80 = _this;
  _this setDir -33.607109;
  _this setPos [9911.4756, 5437.9121, 0.93481213];
};

_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Campfire_burning", [9916.5029, 5435.6436, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setPos [9916.5029, 5435.6436, 1.5258789e-005];
};

_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierUSA_EP1", [9913.7754, 5443.3853, 2.8495307], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setPos [9913.7754, 5443.3853, 2.8495307];
};

_vehicle_88 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierBAF", [9904.8428, 5408.1519, 2.7339697], [], 0, "CAN_COLLIDE"];
  _vehicle_88 = _this;
  _this setPos [9904.8428, 5408.1519, 2.7339697];
};

_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierRedCross_EP1", [9920.3496, 5435.9946, 3.0514462], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir 6.0471778;
  //_this SetFlagTexture "graphics\slkflag.jpg";
  _this setPos [9920.3496, 5435.9946, 3.0514462];
};

_vehicle_90 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierRedCross_EP1", [9916.0801, 5417.3984, 2.8280666], [], 0, "CAN_COLLIDE"];
  _vehicle_90 = _this;
  //_this SetFlagTexture "graphics\canflag.jpg";
  _this setPos [9916.0801, 5417.3984, 2.8280666];
};

_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_TyreHeap", [9906.7734, 5417.9585, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setPos [9906.7734, 5417.9585, 3.0517578e-005];
};

_vehicle_98 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [9913.9385, 5440.0532, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_98 = _this;
  _this setDir 6.0471778;
  _this setVehicleLock "UNLOCKED";
  _this setPos [9913.9385, 5440.0532, 1.5258789e-005];
};

_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_misc", [9908.4648, 5424.106, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setPos [9908.4648, 5424.106, 1.5258789e-005];
};

_vehicle_110 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_popelnice", [9909.3164, 5434.8184, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_110 = _this;
  _this setPos [9909.3164, 5434.8184, 1.5258789e-005];
};

_vehicle_111 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sekyraspalek", [9909.2168, 5425.8423, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_111 = _this;
  _this setPos [9909.2168, 5425.8423, 4.5776367e-005];
};

_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Water_pipe_EP1", [9917.8057, 5434.8574], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setPos [9917.8057, 5434.8574];
};

_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Water_pipe_EP1", [9914.9531, 5435.3408, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setPos [9914.9531, 5435.3408, 1.5258789e-005];
};

_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Table_EP1", [9916.127, 5432.8311], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir 453.98666;
  _this setPos [9916.127, 5432.8311];
};

};
