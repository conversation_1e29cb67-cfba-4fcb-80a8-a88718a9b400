/*
	Celle spawn point definitions
	
	Notes: Markers are used to manually-specify exact locations to use as spawn points. Markers are used for spawn points when there are no/not enough buildings within 300m of trigger center to use as spawn positions.
	
	Last updated: 11:05 PM 7/5/2013
	
*/

//begin markers

_this = createMarker ["ETHS1", [10451.422, 11695.715, -2.7656555e-005]];
_this setMarkerType "Empty";
_this setMarkerBrush "Solid";
_marker_2 = _this;

_this = createMarker ["ETHS2", [10608.365, 11680.685, -9.5367432e-007]];
_this setMarkerType "Empty";
_this setMarkerBrush "Solid";
_marker_3 = _this;

_this = createMarker ["ETHS3", [10233.635, 11605.618, 1.4305115e-005]];
_this setMarkerType "Empty";
_this setMarkerBrush "Solid";
_marker_4 = _this;

_this = createMarker ["ETHS4", [10768.843, 11588.769, 9.5367432e-007]];
_this setMarkerType "Empty";
_this setMarkerBrush "Solid";
_marker_5 = _this;

//end markers
