if (isServer) then {
	_vehicle_1 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1652.4974, 4350.8096, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_1 = _this;
	  _this setDir -219.76158;
	  _this setPos [1652.4974, 4350.8096, 3.0517578e-005];
	};

	_vehicle_7 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1671.0514, 4350.4697], [], 0, "CAN_COLLIDE"];
	  _vehicle_7 = _this;
	  _this setDir -133.91566;
	  _this setPos [1671.0514, 4350.4697];
	};

	_vehicle_9 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1656.0077, 4366.1953, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_9 = _this;
	  _this setDir -133.91566;
	  _this setPos [1656.0077, 4366.1953, 3.0517578e-005];
	};

	_vehicle_11 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1685.6324, 4334.7607, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_11 = _this;
	  _this setDir -133.91566;
	  _this setPos [1685.6324, 4334.7607, 6.1035156e-005];
	};

	_vehicle_13 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1700.4454, 4318.7617], [], 0, "CAN_COLLIDE"];
	  _vehicle_13 = _this;
	  _this setDir -133.91566;
	  _this setPos [1700.4454, 4318.7617];
	};

	_vehicle_15 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1696.6058, 4300.4834, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_15 = _this;
	  _this setDir -43.279865;
	  _this setPos [1696.6058, 4300.4834, 1.5258789e-005];
	};

	_vehicle_20 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1680.0051, 4285.6196, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_20 = _this;
	  _this setDir -42.328331;
	  _this setPos [1680.0051, 4285.6196, -3.0517578e-005];
	};

	_vehicle_22 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1662.8938, 4270.5063], [], 0, "CAN_COLLIDE"];
	  _vehicle_22 = _this;
	  _this setDir -42.838615;
	  _this setPos [1662.8938, 4270.5063];
	};

	_vehicle_24 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1646.1893, 4255.3911], [], 0, "CAN_COLLIDE"];
	  _vehicle_24 = _this;
	  _this setDir -42.587677;
	  _this setPos [1646.1893, 4255.3911];
	};

	_vehicle_28 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1627.9246, 4258.1074, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_28 = _this;
	  _this setDir 49.411915;
	  _this setPos [1627.9246, 4258.1074, 1.5258789e-005];
	};

	_vehicle_33 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1613.7233, 4274.9233, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_33 = _this;
	  _this setDir 49.411915;
	  _this setPos [1613.7233, 4274.9233, -3.0517578e-005];
	};

	_vehicle_35 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1598.8992, 4291.5615, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_35 = _this;
	  _this setDir 49.411915;
	  _this setPos [1598.8992, 4291.5615, 3.0517578e-005];
	};

	_vehicle_37 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1584.6873, 4308.2412], [], 0, "CAN_COLLIDE"];
	  _vehicle_37 = _this;
	  _this setDir 49.411915;
	  _this setPos [1584.6873, 4308.2412];
	};

	_vehicle_40 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1587.5123, 4326.4111, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_40 = _this;
	  _this setDir -219.64977;
	  _this setPos [1587.5123, 4326.4111, -1.5258789e-005];
	};

	_vehicle_43 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1604.6663, 4340.0078, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_43 = _this;
	  _this setDir -219.64977;
	  _this setPos [1604.6663, 4340.0078, 3.0517578e-005];
	};

	_vehicle_45 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1621.4905, 4353.5869], [], 0, "CAN_COLLIDE"];
	  _vehicle_45 = _this;
	  _this setDir -219.64977;
	  _this setPos [1621.4905, 4353.5869];
	};

	_vehicle_47 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fort_rampart", [1637.7288, 4366.6807, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_47 = _this;
	  _this setDir -219.64977;
	  _this setPos [1637.7288, 4366.6807, -1.5258789e-005];
	};

	_vehicle_49 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1693.7531, 4327.5664, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_49 = _this;
	  _this setDir 47.272903;
	  _this setPos [1693.7531, 4327.5664, -1.5258789e-005];
	};

	_vehicle_53 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1703.684, 4308.9805], [], 0, "CAN_COLLIDE"];
	  _vehicle_53 = _this;
	  _this setDir 106.10439;
	  _this setPos [1703.684, 4308.9805];
	};

	_vehicle_56 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1679.2964, 4343.4395, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_56 = _this;
	  _this setDir 47.301834;
	  _this setPos [1679.2964, 4343.4395, 1.5258789e-005];
	};

	_vehicle_58 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1664.4849, 4359.7422, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_58 = _this;
	  _this setDir 47.272903;
	  _this setPos [1664.4849, 4359.7422, 1.5258789e-005];
	};

	_vehicle_60 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1646.8956, 4372.1035, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_60 = _this;
	  _this setDir -2.2790561;
	  _this setPos [1646.8956, 4372.1035, 9.1552734e-005];
	};

	_vehicle_62 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1628.5676, 4361.7217], [], 0, "CAN_COLLIDE"];
	  _vehicle_62 = _this;
	  _this setDir 140.31512;
	  _this setPos [1628.5676, 4361.7217];
	};

	_vehicle_65 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1611.7534, 4347.6182, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_65 = _this;
	  _this setDir 140.31512;
	  _this setPos [1611.7534, 4347.6182, 3.0517578e-005];
	};

	_vehicle_67 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1595.0507, 4334.1738], [], 0, "CAN_COLLIDE"];
	  _vehicle_67 = _this;
	  _this setDir 140.31512;
	  _this setPos [1595.0507, 4334.1738];
	};

	_vehicle_69 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1580.5181, 4318.0723], [], 0, "CAN_COLLIDE"];
	  _vehicle_69 = _this;
	  _this setDir 98.228836;
	  _this setPos [1580.5181, 4318.0723];
	};

	_vehicle_72 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1590.9539, 4298.9785, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_72 = _this;
	  _this setDir 47.272903;
	  _this setPos [1590.9539, 4298.9785, 3.0517578e-005];
	};

	_vehicle_74 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1605.2161, 4282.3184, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_74 = _this;
	  _this setDir 47.272903;
	  _this setPos [1605.2161, 4282.3184, 3.0517578e-005];
	};

	_vehicle_76 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1620.1293, 4265.7759, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_76 = _this;
	  _this setDir 47.272903;
	  _this setPos [1620.1293, 4265.7759, 1.5258789e-005];
	};

	_vehicle_78 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1636.6796, 4251.5356, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_78 = _this;
	  _this setDir 12.743114;
	  _this setPos [1636.6796, 4251.5356, -1.5258789e-005];
	};

	_vehicle_80 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1689.4844, 4292.188, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_80 = _this;
	  _this setDir 136.97031;
	  _this setPos [1689.4844, 4292.188, 1.5258789e-005];
	};

	_vehicle_82 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1672.323, 4277.1304], [], 0, "CAN_COLLIDE"];
	  _vehicle_82 = _this;
	  _this setDir 136.23628;
	  _this setPos [1672.323, 4277.1304];
	};

	_vehicle_84 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hhedgehog_concreteBig", [1655.5132, 4261.8564, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_84 = _this;
	  _this setDir 133.91002;
	  _this setPos [1655.5132, 4261.8564, -1.5258789e-005];
	};

	_vehicle_86 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fortified_nest_small", [1646.439, 4361.2188, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_86 = _this;
	  _this setDir -181.89149;
	  _this setPos [1646.439, 4361.2188, 1.5258789e-005];
	};

	_vehicle_88 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fortified_nest_small", [1589.8667, 4317.1533, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_88 = _this;
	  _this setDir -263.10822;
	  _this setPos [1589.8667, 4317.1533, -3.0517578e-005];
	};

	_vehicle_90 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fortified_nest_small", [1638.6135, 4261.9175, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_90 = _this;
	  _this setDir -351.50403;
	  _this setPos [1638.6135, 4261.9175, 1.5258789e-005];
	};

	_vehicle_92 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_fortified_nest_small", [1697.2676, 4311.5986, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_92 = _this;
	  _this setDir -82.427368;
	  _this setPos [1697.2676, 4311.5986, -6.1035156e-005];
	};

	_vehicle_96 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1658.9037, 4348.7949, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_96 = _this;
	  _this setDir -130.66449;
	  _this setPos [1658.9037, 4348.7949, 1.5258789e-005];
	};

	_vehicle_98 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1664.7853, 4342.3486], [], 0, "CAN_COLLIDE"];
	  _vehicle_98 = _this;
	  _this setDir -130.66449;
	  _this setPos [1664.7853, 4342.3486];
	};

	_vehicle_100 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1670.9642, 4335.9502, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_100 = _this;
	  _this setDir -130.66449;
	  _this setPos [1670.9642, 4335.9502, -4.5776367e-005];
	};

	_vehicle_102 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1676.9243, 4329.6182, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_102 = _this;
	  _this setDir -130.66449;
	  _this setPos [1676.9243, 4329.6182, -1.5258789e-005];
	};

	_vehicle_104 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1683.0363, 4323.041, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_104 = _this;
	  _this setDir -130.66449;
	  _this setPos [1683.0363, 4323.041, -3.0517578e-005];
	};

	_vehicle_106 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1684.4719, 4316.7988], [], 0, "CAN_COLLIDE"];
	  _vehicle_106 = _this;
	  _this setDir -42.9981;
	  _this setPos [1684.4719, 4316.7988];
	};

	_vehicle_116 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1641.9333, 4351.0498, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_116 = _this;
	  _this setDir 230.95633;
	  _this setPos [1641.9333, 4351.0498, 4.5776367e-005];
	};

	_vehicle_117 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1635.6047, 4350.0459, 0.084899902], [], 0, "CAN_COLLIDE"];
	  _vehicle_117 = _this;
	  _this setDir 143.28992;
	  _this setPos [1635.6047, 4350.0459, 0.084899902];
	};

	_vehicle_118 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1628.624, 4344.4063, 0.12327576], [], 0, "CAN_COLLIDE"];
	  _vehicle_118 = _this;
	  _this setDir 143.28992;
	  _this setPos [1628.624, 4344.4063, 0.12327576];
	};

	_vehicle_119 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1621.8943, 4338.8965, 0.16278076], [], 0, "CAN_COLLIDE"];
	  _vehicle_119 = _this;
	  _this setDir 143.28992;
	  _this setPos [1621.8943, 4338.8965, 0.16278076];
	};

	_vehicle_120 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1615.0875, 4333.1709, 0.19345093], [], 0, "CAN_COLLIDE"];
	  _vehicle_120 = _this;
	  _this setDir 143.28992;
	  _this setPos [1615.0875, 4333.1709, 0.19345093];
	};

	_vehicle_121 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1608.3456, 4327.5732, 0.22503662], [], 0, "CAN_COLLIDE"];
	  _vehicle_121 = _this;
	  _this setDir 143.28992;
	  _this setPos [1608.3456, 4327.5732, 0.22503662];
	};

	_vehicle_122 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1605.7986, 4321.4941, 0.19015503], [], 0, "CAN_COLLIDE"];
	  _vehicle_122 = _this;
	  _this setDir 54.192772;
	  _this setPos [1605.7986, 4321.4941, 0.19015503];
	};

	_vehicle_130 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1604.1422, 4309.3105, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_130 = _this;
	  _this setDir -218.6235;
	  _this setPos [1604.1422, 4309.3105, 1.5258789e-005];
	};

	_vehicle_131 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1605.2717, 4303.2129, 0.022399902], [], 0, "CAN_COLLIDE"];
	  _vehicle_131 = _this;
	  _this setDir -308.24591;
	  _this setPos [1605.2717, 4303.2129, 0.022399902];
	};

	_vehicle_132 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1610.7653, 4296.4854, -0.013534546], [], 0, "CAN_COLLIDE"];
	  _vehicle_132 = _this;
	  _this setDir -308.01404;
	  _this setPos [1610.7653, 4296.4854, -0.013534546];
	};

	_vehicle_133 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1616.1716, 4289.8623, -0.045898438], [], 0, "CAN_COLLIDE"];
	  _vehicle_133 = _this;
	  _this setDir -307.67615;
	  _this setPos [1616.1716, 4289.8623, -0.045898438];
	};

	_vehicle_134 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1621.3132, 4283.5864, -0.088592529], [], 0, "CAN_COLLIDE"];
	  _vehicle_134 = _this;
	  _this setDir -307.8606;
	  _this setPos [1621.3132, 4283.5864, -0.088592529];
	};

	_vehicle_135 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1626.8574, 4276.9604, -0.1378479], [], 0, "CAN_COLLIDE"];
	  _vehicle_135 = _this;
	  _this setDir -306.2901;
	  _this setPos [1626.8574, 4276.9604, -0.1378479];
	};

	_vehicle_136 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1632.5601, 4274.8174, -0.20210266], [], 0, "CAN_COLLIDE"];
	  _vehicle_136 = _this;
	  _this setDir -399.21014;
	  _this setPos [1632.5601, 4274.8174, -0.20210266];
	};

	_vehicle_144 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1645.0349, 4271.6055, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_144 = _this;
	  _this setDir 48.586983;
	  _this setPos [1645.0349, 4271.6055, 3.0517578e-005];
	};

	_vehicle_145 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1651.3159, 4272.8677, 0.025924683], [], 0, "CAN_COLLIDE"];
	  _vehicle_145 = _this;
	  _this setDir -39.079422;
	  _this setPos [1651.3159, 4272.8677, 0.025924683];
	};

	_vehicle_146 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1658.059, 4278.7959, -0.028625488], [], 0, "CAN_COLLIDE"];
	  _vehicle_146 = _this;
	  _this setDir -39.079422;
	  _this setPos [1658.059, 4278.7959, -0.028625488];
	};

	_vehicle_147 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1664.5544, 4284.5781, -0.082122803], [], 0, "CAN_COLLIDE"];
	  _vehicle_147 = _this;
	  _this setDir -39.079422;
	  _this setPos [1664.5544, 4284.5781, -0.082122803];
	};

	_vehicle_148 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1671.1189, 4290.5757, -0.088174194], [], 0, "CAN_COLLIDE"];
	  _vehicle_148 = _this;
	  _this setDir -39.079422;
	  _this setPos [1671.1189, 4290.5757, -0.088174194];
	};

	_vehicle_149 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1677.6257, 4296.4526, -0.19090271], [], 0, "CAN_COLLIDE"];
	  _vehicle_149 = _this;
	  _this setDir -39.079422;
	  _this setPos [1677.6257, 4296.4526, -0.19090271];
	};

	_vehicle_150 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [1679.9171, 4302.6279, -0.25100708], [], 0, "CAN_COLLIDE"];
	  _vehicle_150 = _this;
	  _this setDir -128.17656;
	  _this setPos [1679.9171, 4302.6279, -0.25100708];
	};

	_vehicle_162 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Barrack2", [1632.5688, 4283.1787, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_162 = _this;
	  _this setDir -130.15477;
	  _this setPos [1632.5688, 4283.1787, 1.5258789e-005];
	};

	_vehicle_165 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Barrack2", [1627.6174, 4289.2749, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_165 = _this;
	  _this setDir -130.15477;
	  _this setPos [1627.6174, 4289.2749, 3.0517578e-005];
	};

	_vehicle_167 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Barrack2", [1622.7877, 4294.875], [], 0, "CAN_COLLIDE"];
	  _vehicle_167 = _this;
	  _this setDir -130.15477;
	  _this setPos [1622.7877, 4294.875];
	};

	_vehicle_169 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Barrack2", [1617.6572, 4301.1738, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_169 = _this;
	  _this setDir -130.15477;
	  _this setPos [1617.6572, 4301.1738, 1.5258789e-005];
	};

	_vehicle_171 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Barrack2", [1612.4418, 4307.7285, -0.068825215], [], 0, "CAN_COLLIDE"];
	  _vehicle_171 = _this;
	  _this setDir -130.15477;
	  _this setPos [1612.4418, 4307.7285, -0.068825215];
	};

	_vehicle_173 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_CamoNetVar_EAST", [1670.9875, 4298.1611, 0.39475349], [], 0, "CAN_COLLIDE"];
	  _vehicle_173 = _this;
	  _this setDir -40.922451;
	  _this setPos [1670.9875, 4298.1611, 0.39475349];
	};

	_vehicle_175 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_CamoNetVar_EAST", [1660.2336, 4288.5366, 0.70817268], [], 0, "CAN_COLLIDE"];
	  _vehicle_175 = _this;
	  _this setDir -41.442364;
	  _this setPos [1660.2336, 4288.5366, 0.70817268];
	};

	_vehicle_177 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_CamoNetVar_EAST", [1649.5188, 4279.27, 0.86491859], [], 0, "CAN_COLLIDE"];
	  _vehicle_177 = _this;
	  _this setDir -40.922451;
	  _this setPos [1649.5188, 4279.27, 0.86491859];
	};

	_vehicle_179 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Misc_Cargo1Bo_military", [1636.5677, 4339.3672], [], 0, "CAN_COLLIDE"];
	  _vehicle_179 = _this;
	  _this setDir 15.413855;
	  _this setPos [1636.5677, 4339.3672];
	};

	_vehicle_180 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_Misc_Cargo1Eo_EP1", [1629.2976, 4335.2832, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_180 = _this;
	  _this setDir 108.45533;
	  _this setPos [1629.2976, 4335.2832, 1.5258789e-005];
	};

	_vehicle_181 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_Misc_Cargo1E_EP1", [1631.1989, 4340.1006, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_181 = _this;
	  _this setDir 48.383904;
	  _this setPos [1631.1989, 4340.1006, -1.5258789e-005];
	};

	_vehicle_182 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_Misc_Cargo1E_EP1", [1640.1255, 4344.9961, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_182 = _this;
	  _this setDir -39.247154;
	  _this setPos [1640.1255, 4344.9961, 1.5258789e-005];
	};

	_vehicle_184 = objNull;
	if (true) then
	{
	  _this = createVehicle ["PowGen_Big", [1646.2565, 4278.9663, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_184 = _this;
	  _this setDir 143.14679;
	  _this setPos [1646.2565, 4278.9663, 1.5258789e-005];
	};

	_vehicle_186 = objNull;
	if (true) then
	{
	  _this = createVehicle ["CampEast", [1654.2631, 4301.9468, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_186 = _this;
	  _this setDir -39.920364;
	  _this setPos [1654.2631, 4301.9468, 6.1035156e-005];
	};

	_vehicle_187 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Camp_EP1", [1611.3488, 4322.5381, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_187 = _this;
	  _this setDir 141.8138;
	  _this setPos [1611.3488, 4322.5381, 7.6293945e-005];
	};

	_vehicle_189 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Camp_EP1", [1616.6348, 4326.7695, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_189 = _this;
	  _this setDir 143.72325;
	  _this setPos [1616.6348, 4326.7695, 3.0517578e-005];
	};

	_vehicle_191 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Camp_EP1", [1621.8677, 4331.4248, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_191 = _this;
	  _this setDir 143.72325;
	  _this setPos [1621.8677, 4331.4248, 3.0517578e-005];
	};

	_vehicle_193 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MASH", [1678.6803, 4318.2705, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_193 = _this;
	  _this setDir -133.24112;
	  _this setPos [1678.6803, 4318.2705, 3.0517578e-005];
	};

	_vehicle_195 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MASH", [1673.4877, 4325.0264, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_195 = _this;
	  _this setDir -133.24112;
	  _this setPos [1673.4877, 4325.0264, 7.6293945e-005];
	};

	_vehicle_198 = objNull;
	if (true) then
	{
	  _this = createVehicle ["CampEast", [1663.0752, 4309.1924], [], 0, "CAN_COLLIDE"];
	  _vehicle_198 = _this;
	  _this setDir -39.920364;
	  _this setPos [1663.0752, 4309.1924];
	};

	_vehicle_201 = objNull;
	if (true) then
	{
	  _this = createVehicle ["CampEast", [1646.892, 4295.7031, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_201 = _this;
	  _this setDir -39.920364;
	  _this setPos [1646.892, 4295.7031, 9.1552734e-005];
	};

	_vehicle_205 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_A_tent", [1673.8485, 4302.9141, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_205 = _this;
	  _this setDir -244.38847;
	  _this setPos [1673.8485, 4302.9141, 1.5258789e-005];
	};

	_vehicle_207 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_A_tent", [1671.9753, 4299.1343, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_207 = _this;
	  _this setDir -200.86267;
	  _this setPos [1671.9753, 4299.1343, 3.0517578e-005];
	};

	_vehicle_209 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_A_tent", [1668.3904, 4298.6758, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_209 = _this;
	  _this setDir -146.43512;
	  _this setPos [1668.3904, 4298.6758, 3.0517578e-005];
	};

	_vehicle_222 = objNull;
	if (true) then
	{
	  _this = createVehicle ["LAV25_HQ_unfolded", [1631.9912, 4312.2227, -0.32589313], [], 0, "CAN_COLLIDE"];
	  _vehicle_222 = _this;
	  _this setDir -129.80147;
	  _this setPos [1631.9912, 4312.2227, -0.32589313];
	};

	_vehicle_223 = objNull;
	if (true) then
	{
	  _this = createVehicle ["USMC_WarfareBLightFactory", [1645.9849, 4311.292, -0.29644758], [], 0, "CAN_COLLIDE"];
	  _vehicle_223 = _this;
	  _this setDir -41.102131;
	  _this setPos [1645.9849, 4311.292, -0.29644758];
	};

	_vehicle_229 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_Mil_Barracks_i", [1663.689, 4326.5479, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_229 = _this;
	  _this setDir 137.15683;
	  _this setPos [1663.689, 4326.5479, 9.1552734e-005];
	};

	_vehicle_231 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_Mil_Barracks_i", [1656.8585, 4333.791, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_231 = _this;
	  _this setDir 137.15683;
	  _this setPos [1656.8585, 4333.791, 9.1552734e-005];
	};

	_vehicle_240 = objNull;
	if (true) then
	{
	  _this = createVehicle ["CampEast", [1654.3181, 4343.8281, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_240 = _this;
	  _this setDir 48.4035;
	  _this setPos [1654.3181, 4343.8281, -1.5258789e-005];
	};
};