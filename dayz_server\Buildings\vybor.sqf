/*
  CHERNARUS ENHANCEMENTS - Vybor
  -------------------------------------------------
    Expanded Vybor by <PERSON>, blackwiddow
    Email: <EMAIL>
    Steam: blackwiddow20
*/


if (isServer) then {

_vehicle_431 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_02", [4207.5503, 8947.8301, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_431 = _this;
  _this setDir 186.43533;
  _this setPos [4207.5503, 8947.8301, 9.1552734e-005];
};

_vehicle_435 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_L", [4136.3618, 8957.0762, -0.071089007], [], 0, "CAN_COLLIDE"];
  _vehicle_435 = _this;
  _this setDir 11.717071;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4136.3618, 8957.0762, -0.071089007];
};

_vehicle_438 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_L", [4174.8154, 8890.4707, -0.07711482], [], 0, "CAN_COLLIDE"];
  _vehicle_438 = _this;
  _this setDir -350.78851;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4174.8154, 8890.4707, -0.07711482];
};

_vehicle_443 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [3963.6001, 8804.7461, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_443 = _this;
  _this setDir 7.35502;
  _this setPos [3963.6001, 8804.7461, -3.0517578e-005];
};

_vehicle_444 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [3964.3743, 8810.8232], [], 0, "CAN_COLLIDE"];
  _vehicle_444 = _this;
  _this setDir 4.841733;
  _this setPos [3964.3743, 8810.8232];
};

_vehicle_446 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [3966.3892, 8835.6445, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_446 = _this;
  _this setDir 4.841733;
  _this setPos [3966.3892, 8835.6445, 6.1035156e-005];
};

_vehicle_450 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [3969.198, 8874.334, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_450 = _this;
  _this setDir 182.61652;
  _this setPos [3969.198, 8874.334, -6.1035156e-005];
};

_vehicle_453 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_50", [3968.9255, 8868.5518, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_453 = _this;
  _this setDir -181.30661;
  _this setPos [3968.9255, 8868.5518, -6.1035156e-005];
};

_vehicle_457 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [3967.967, 8904.6299, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_457 = _this;
  _this setDir -2.1350124;
  _this setPos [3967.967, 8904.6299, 3.0517578e-005];
};

_vehicle_459 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [3969.4597, 8880.2588, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_459 = _this;
  _this setDir -3.3420122;
  _this setPos [3969.4597, 8880.2588, 3.0517578e-005];
};

_vehicle_462 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [3989.5806, 8944.4082, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_462 = _this;
  _this setDir 78.649399;
  _this setPos [3989.5806, 8944.4082, -3.0517578e-005];
};

_vehicle_464 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [3969.6665, 8923.3672], [], 0, "CAN_COLLIDE"];
  _vehicle_464 = _this;
  _this setDir 21.836283;
  _this setPos [3969.6665, 8923.3672];
};

_vehicle_465 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [3979.1501, 8938.6582], [], 0, "CAN_COLLIDE"];
  _vehicle_465 = _this;
  _this setDir 47.854935;
  _this setPos [3979.1501, 8938.6582];
};

_vehicle_467 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4008.8855, 8945.8281, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_467 = _this;
  _this setDir 89.038605;
  _this setPos [4008.8855, 8945.8281, -3.0517578e-005];
};

_vehicle_470 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4033.7983, 8946.3154, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_470 = _this;
  _this setDir 89.038605;
  _this setPos [4033.7983, 8946.3154, 3.0517578e-005];
};

_vehicle_472 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4058.6165, 8946.7402], [], 0, "CAN_COLLIDE"];
  _vehicle_472 = _this;
  _this setDir 89.038605;
  _this setPos [4058.6165, 8946.7402];
};

_vehicle_474 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4083.3098, 8947.2686, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_474 = _this;
  _this setDir 90.700089;
  _this setPos [4083.3098, 8947.2686, -3.0517578e-005];
};

_vehicle_477 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4108.0576, 8947.043, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_477 = _this;
  _this setDir 95.949203;
  _this setPos [4108.0576, 8947.043, -3.0517578e-005];
};

_vehicle_480 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [4138.8491, 8943.4795], [], 0, "CAN_COLLIDE"];
  _vehicle_480 = _this;
  _this setDir -80.717819;
  _this setPos [4138.8491, 8943.4795];
};

_vehicle_486 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [3975.1575, 8874.0488, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_486 = _this;
  _this setDir 95.517433;
  _this setPos [3975.1575, 8874.0488, -9.1552734e-005];
};

_vehicle_488 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [3999.7278, 8871.6992, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_488 = _this;
  _this setDir 91.708542;
  _this setPos [3999.7278, 8871.6992, -0.00012207031];
};

_vehicle_491 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4024.606, 8870.9775], [], 0, "CAN_COLLIDE"];
  _vehicle_491 = _this;
  _this setDir 91.708542;
  _this setPos [4024.606, 8870.9775];
};

_vehicle_493 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4049.4873, 8870.3555, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_493 = _this;
  _this setDir 91.708542;
  _this setPos [4049.4873, 8870.3555, -3.0517578e-005];
};

_vehicle_496 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4074.0845, 8869.6602], [], 0, "CAN_COLLIDE"];
  _vehicle_496 = _this;
  _this setDir 86.522194;
  _this setPos [4074.0845, 8869.6602];
};

_vehicle_499 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4093.4788, 8868.3887, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_499 = _this;
  _this setDir 98.190926;
  _this setPos [4093.4788, 8868.3887, 6.1035156e-005];
};

_vehicle_502 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4116.1836, 8865.1299, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_502 = _this;
  _this setDir 98.190926;
  _this setPos [4116.1836, 8865.1299, 9.1552734e-005];
};

_vehicle_506 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [4172.2617, 8863.0059, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_506 = _this;
  _this setDir -146.95946;
  _this setPos [4172.2617, 8863.0059, -3.0517578e-005];
};

_vehicle_516 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [4189.5181, 8907.0205, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_516 = _this;
  _this setDir 181.24693;
  _this setPos [4189.5181, 8907.0205, -3.0517578e-005];
};

_vehicle_523 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_2", [4196.5205, 8900.4844, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_523 = _this;
  _this setDir -364.60611;
  _this setPos [4196.5205, 8900.4844, 3.0517578e-005];
};

_vehicle_525 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_L", [4189.2427, 8899.8477, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_525 = _this;
  _this setPos [4189.2427, 8899.8477, -3.0517578e-005];
};

_vehicle_529 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_R", [4189.2686, 8899.8594, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_529 = _this;
  _this setDir -5.9451084;
  _this setPos [4189.2686, 8899.8594, 3.0517578e-005];
};

_vehicle_538 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [4189.3584, 8900.9375, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_538 = _this;
  _this setDir -175.4256;
  _this setPos [4189.3584, 8900.9375, 3.0517578e-005];
};

_vehicle_540 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [4182.0493, 8899.8809, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_540 = _this;
  _this setDir -179.34128;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4182.0493, 8899.8809, 3.0517578e-005];
};

_vehicle_541 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D_2", [4180.7896, 8900.0107, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_541 = _this;
  _this setDir -174.83987;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4180.7896, 8900.0107, 3.0517578e-005];
};

_vehicle_542 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ural_wrecked", [4181.4717, 8903.8076], [], 0, "CAN_COLLIDE"];
  _vehicle_542 = _this;
  _this setDir -15.840092;
  _this setPos [4181.4717, 8903.8076];
};

_vehicle_564 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rubble_bricks_03", [4181.1382, 8899.3418, -1.9450601], [], 0, "CAN_COLLIDE"];
  _vehicle_564 = _this;
  _this setDir -359.19522;
  _this setPos [4181.1382, 8899.3418, -1.9450601];
};

_vehicle_571 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_02", [4173.1255, 8906.7959, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_571 = _this;
  _this setDir 98.509468;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4173.1255, 8906.7959, -3.0517578e-005];
};

_vehicle_576 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [4175.6157, 8868.0625], [], 0, "CAN_COLLIDE"];
  _vehicle_576 = _this;
  _this setDir 213.38043;
  _this setPos [4175.6157, 8868.0625];
};

_vehicle_578 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_12", [4178.3418, 8872.1895], [], 0, "CAN_COLLIDE"];
  _vehicle_578 = _this;
  _this setDir 29.538397;
  _this setPos [4178.3418, 8872.1895];
};

_vehicle_589 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [4198.8179, 8858.9766, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_589 = _this;
  _this setDir -83.526497;
  _this setPos [4198.8179, 8858.9766, 3.0517578e-005];
};

_vehicle_595 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [4210.1709, 8861.1787, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_595 = _this;
  _this setDir -114.34738;
  _this setPos [4210.1709, 8861.1787, 3.0517578e-005];
};

_vehicle_598 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [4219.5063, 8869.54, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_598 = _this;
  _this setDir -145.97986;
  _this setPos [4219.5063, 8869.54, -3.0517578e-005];
};

_vehicle_604 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4228.0474, 8886.7559], [], 0, "CAN_COLLIDE"];
  _vehicle_604 = _this;
  _this setDir 199.25331;
  _this setPos [4228.0474, 8886.7559];
};

_vehicle_606 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [4227.8257, 8886.1631, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_606 = _this;
  _this setDir 11.703266;
  _this setPos [4227.8257, 8886.1631, -9.1552734e-005];
};

_vehicle_609 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2s", [4196.813, 8893.9746, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_609 = _this;
  _this setPos [4196.813, 8893.9746, 3.0517578e-005];
};

_vehicle_610 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2W", [4210.8438, 8871.4717], [], 0, "CAN_COLLIDE"];
  _vehicle_610 = _this;
  _this setPos [4210.8438, 8871.4717];
};

_vehicle_624 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4202.1445, 8869.3516], [], 0, "CAN_COLLIDE"];
  _vehicle_624 = _this;
  _this setDir -145.94389;
  _this setPos [4202.1445, 8869.3516];
};

_vehicle_625 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2W", [4193.9771, 8869.8203, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_625 = _this;
  _this setPos [4193.9771, 8869.8203, 3.0517578e-005];
};

_vehicle_626 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [4187.335, 8873.46, -0.116885], [], 0, "CAN_COLLIDE"];
  _vehicle_626 = _this;
  _this setDir -150.10112;
  _this setPos [4187.335, 8873.46, -0.116885];
};

_vehicle_628 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4226.9951, 8863.8467, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_628 = _this;
  _this setDir 56.302387;
  _this setPos [4226.9951, 8863.8467, 6.1035156e-005];
};

_vehicle_630 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4298.23, 8879.4395, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_630 = _this;
  _this setPos [4298.23, 8879.4395, 3.0517578e-005];
};

_vehicle_632 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4170.8599, 8874.6885, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_632 = _this;
  _this setDir 123.1104;
  _this setPos [4170.8599, 8874.6885, 9.1552734e-005];
};

_vehicle_634 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4246.8496, 8877.1523, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_634 = _this;
  _this setPos [4246.8496, 8877.1523, 3.0517578e-005];
};

_vehicle_639 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runwayold_40_main", [4116.4663, 8905.6914, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_639 = _this;
  _this setDir 9.3174486;
  _this setPos [4116.4663, 8905.6914, 6.1035156e-005];
};

_vehicle_643 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4117.312, 8902.3135, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_643 = _this;
  _this setPos [4117.312, 8902.3135, 3.0517578e-005];
};

_vehicle_645 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4128.4209, 8906.8135, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_645 = _this;
  _this setPos [4128.4209, 8906.8135, -6.1035156e-005];
};

_vehicle_647 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4130.4912, 8916.1865, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_647 = _this;
  _this setPos [4130.4912, 8916.1865, 3.0517578e-005];
};

_vehicle_649 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4123.6924, 8920.0947, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_649 = _this;
  _this setPos [4123.6924, 8920.0947, 6.1035156e-005];
};

_vehicle_651 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4110.9961, 8913.6162, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_651 = _this;
  _this setPos [4110.9961, 8913.6162, 0];
};

_vehicle_653 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4110.6357, 8920.8213, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_653 = _this;
  _this setPos [4110.6357, 8920.8213, 3.0517578e-005];
};

_vehicle_655 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4105.957, 8921.0332, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_655 = _this;
  _this setPos [4105.957, 8921.0332, 3.0517578e-005];
};

_vehicle_657 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4103.8755, 8911.0176, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_657 = _this;
  _this setPos [4103.8755, 8911.0176, -3.0517578e-005];
};

_vehicle_659 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4107.2969, 8896.0068, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_659 = _this;
  _this setPos [4107.2969, 8896.0068, 0];
};

_vehicle_661 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4099.9414, 8895.8672, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_661 = _this;
  _this setPos [4099.9414, 8895.8672, -6.1035156e-005];
};

_vehicle_663 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [4135.5898, 8914.1982], [], 0, "CAN_COLLIDE"];
  _vehicle_663 = _this;
  _this setDir -170.65376;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4135.5898, 8914.1982];
};

_vehicle_671 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Shed_Ind02", [4103.7744, 8918.585, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_671 = _this;
  _this setDir -170.61113;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4103.7744, 8918.585, -3.0517578e-005];
};

_vehicle_690 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [4106.748, 8887.0557, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_690 = _this;
  _this setDir -170.59993;
  _this setPos [4106.748, 8887.0557, -3.0517578e-005];
};

_vehicle_695 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4100.8594, 8888.0156, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_695 = _this;
  _this setDir -170.56134;
  _this setPos [4100.8594, 8888.0156, 3.0517578e-005];
};

_vehicle_697 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [4095.8196, 8897.2275, -0.089391619], [], 0, "CAN_COLLIDE"];
  _vehicle_697 = _this;
  _this setDir -350.63129;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4095.8196, 8897.2275, -0.089391619];
};

_vehicle_703 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [4096.2891, 8906.0117, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_703 = _this;
  _this setDir -260.73535;
  _this setPos [4096.2891, 8906.0117, 3.0517578e-005];
};

_vehicle_706 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [4096.2197, 8905.752], [], 0, "CAN_COLLIDE"];
  _vehicle_706 = _this;
  _this setDir -440.86752;
  _this setPos [4096.2197, 8905.752];
};

_vehicle_709 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_2", [4220.1641, 8957.0068, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_709 = _this;
  _this setDir -537.01874;
  _this setPos [4220.1641, 8957.0068, 6.1035156e-005];
};

_vehicle_715 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_2", [4235.5874, 8955.8281], [], 0, "CAN_COLLIDE"];
  _vehicle_715 = _this;
  _this setDir -533.95642;
  _this setPos [4235.5874, 8955.8281];
};

_vehicle_717 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_2", [4241.4375, 8955.1768], [], 0, "CAN_COLLIDE"];
  _vehicle_717 = _this;
  _this setDir -533.4422;
  _this setPos [4241.4375, 8955.1768];
};

_vehicle_720 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_2", [4247.5908, 8954.499, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_720 = _this;
  _this setDir -534.17371;
  _this setPos [4247.5908, 8954.499, -0.00012207031];
};

_vehicle_722 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_2", [4249.8047, 8946.5957], [], 0, "CAN_COLLIDE"];
  _vehicle_722 = _this;
  _this setDir -625.14709;
  _this setPos [4249.8047, 8946.5957];
};

_vehicle_725 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_2", [4249.2964, 8940.416, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_725 = _this;
  _this setDir -625.14709;
  _this setPos [4249.2964, 8940.416, 3.0517578e-005];
};

_vehicle_727 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_L", [4249.9258, 8949.7852, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_727 = _this;
  _this setDir -264.80344;
  _this setPos [4249.9258, 8949.7852, 3.0517578e-005];
};

_vehicle_745 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_L", [4120.0059, 8927.6758, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_745 = _this;
  _this setDir -192.89705;
  _this setPos [4120.0059, 8927.6758, 3.0517578e-005];
};

_vehicle_746 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_R", [4119.9619, 8927.6611, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_746 = _this;
  _this setDir -199.15466;
  _this setPos [4119.9619, 8927.6611, 9.1552734e-005];
};

_vehicle_750 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [4112.9697, 8926.582, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_750 = _this;
  _this setDir -171.39383;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4112.9697, 8926.582, 9.1552734e-005];
};

_vehicle_753 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [4113.5996, 8926.5166, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_753 = _this;
  _this setDir -351.58234;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4113.5996, 8926.5166, 6.1035156e-005];
};

_vehicle_756 = objNull;
if (true) then
{
  _this = createVehicle ["Land_pumpa", [4165.478, 8928.7158, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_756 = _this;
  _this setDir 222.09743;
  _this setPos [4165.478, 8928.7158, 3.0517578e-005];
};

_vehicle_759 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [4120.9424, 8924.1885, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_759 = _this;
  _this setDir -15.669023;
  _this setPos [4120.9424, 8924.1885, 6.1035156e-005];
};

_vehicle_760 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_Pec_03b", [4228.6807, 8925.0166, -0.0029344372], [], 0, "CAN_COLLIDE"];
  _vehicle_760 = _this;
  _this setDir -175.41872;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4228.6807, 8925.0166, -0.0029344372];
};

_vehicle_762 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_Pec_03b", [4143.6094, 8888.8398, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_762 = _this;
  _this setDir -80.446411;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4143.6094, 8888.8398, -9.1552734e-005];
};

_vehicle_764 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Bo", [4243.2036, 8914.6318, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_764 = _this;
  _this setDir -181.46428;
  _this setPos [4243.2036, 8914.6318, 0.00015258789];
};

_vehicle_765 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [4223.7676, 8948.7344, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_765 = _this;
  _this setDir -265.54965;
  _this setPos [4223.7676, 8948.7344, -3.0517578e-005];
};

_vehicle_766 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Bo", [4234.3608, 8947.6973, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_766 = _this;
  _this setDir 95.401138;
  _this setPos [4234.3608, 8947.6973, -3.0517578e-005];
};

_vehicle_767 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1B", [4234.1533, 8947.7119, 2.5530138], [], 0, "CAN_COLLIDE"];
  _vehicle_767 = _this;
  _this setDir -81.825859;
  _this setPos [4234.1533, 8947.7119, 2.5530138];
};

_vehicle_772 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_25", [4119.2612, 8930.1934], [], 0, "CAN_COLLIDE"];
  _vehicle_772 = _this;
  _this setDir -15.277316;
  _this setPos [4119.2612, 8930.1934];
};

_vehicle_775 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_25", [4118.4873, 8934.458], [], 0, "CAN_COLLIDE"];
  _vehicle_775 = _this;
  _this setDir -5.7735615;
  _this setPos [4118.4873, 8934.458];
};

_vehicle_778 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [4118.895, 8945.0293, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_778 = _this;
  _this setDir 184.0905;
  _this setPos [4118.895, 8945.0293, 3.0517578e-005];
};

_vehicle_786 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_2", [4247.5078, 8914.7461], [], 0, "CAN_COLLIDE"];
  _vehicle_786 = _this;
  _this setDir -806.24884;
  _this setPos [4247.5078, 8914.7461];
};

_vehicle_792 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D_2", [4225.8706, 8956.7441], [], 0, "CAN_COLLIDE"];
  _vehicle_792 = _this;
  _this setDir -177.55592;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4225.8706, 8956.7441];
};

_vehicle_797 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [4229.8062, 8956.3896, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_797 = _this;
  _this setDir -174.78528;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4229.8062, 8956.3896, 9.1552734e-005];
};

_vehicle_804 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_2", [4246.8101, 8903.0518, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_804 = _this;
  _this setDir -805.93634;
  _this setPos [4246.8101, 8903.0518, 3.0517578e-005];
};

_vehicle_805 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_2", [4247.1753, 8908.9463, 0.0051879883], [], 0, "CAN_COLLIDE"];
  _vehicle_805 = _this;
  _this setDir -806.87384;
  _this setPos [4247.1753, 8908.9463, 0.0051879883];
};

_vehicle_809 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_2", [4239.0469, 8900.7598], [], 0, "CAN_COLLIDE"];
  _vehicle_809 = _this;
  _this setDir -895.66956;
  _this setPos [4239.0469, 8900.7598];
};

_vehicle_812 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_L", [4242.1826, 8900.5283, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_812 = _this;
  _this setDir -175.65009;
  _this setPos [4242.1826, 8900.5283, 3.0517578e-005];
};

_vehicle_816 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [4247.8735, 8920.5049, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_816 = _this;
  _this setDir -86.248352;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4247.8735, 8920.5049, 3.0517578e-005];
};

_vehicle_817 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D_2", [4248.0532, 8922.3291, 0.0028686523], [], 0, "CAN_COLLIDE"];
  _vehicle_817 = _this;
  _this setDir -88.416298;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4248.0532, 8922.3291, 0.0028686523];
};

_vehicle_821 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D_2", [4219.4902, 8989.2021, -0.32153106], [], 0, "CAN_COLLIDE"];
  _vehicle_821 = _this;
  _this setDir -164.08046;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4219.4902, 8989.2021, -0.32153106];
};

_vehicle_824 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rubble_bricks_03", [4224.8125, 8986.707, -1.8090132], [], 0, "CAN_COLLIDE"];
  _vehicle_824 = _this;
  _this setDir -340.97382;
  _this setPos [4224.8125, 8986.707, -1.8090132];
};

_vehicle_827 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rubble_bricks_03", [4224.2837, 8988.2676, -1.8356818], [], 0, "CAN_COLLIDE"];
  _vehicle_827 = _this;
  _this setDir -517.03479;
  _this setPos [4224.2837, 8988.2676, -1.8356818];
};

_vehicle_830 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ural_wrecked", [4225.7075, 8990.1201, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_830 = _this;
  _this setDir 38.811718;
  _this setVehicleInit "this setvectorup [0.1,0,0]";
  _this setPos [4225.7075, 8990.1201, -6.1035156e-005];
};

_vehicle_835 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_majak_podesta", [4252.9155, 8927.3926, -2.1403892], [], 0, "CAN_COLLIDE"];
  _vehicle_835 = _this;
  _this setDir -535.50201;
  _this setVehicleInit "this allowDammage false; this enableSimulation false;";
  _this setPos [4252.9155, 8927.3926, -2.1403892];
};

_vehicle_841 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_L", [4200.3052, 8966.3779, -0.097312599], [], 0, "CAN_COLLIDE"];
  _vehicle_841 = _this;
  _this setDir -264.24384;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4200.3052, 8966.3779, -0.097312599];
};

_vehicle_846 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_Hospital", [4159.0059, 8826.7266], [], 0, "CAN_COLLIDE"];
  _vehicle_846 = _this;
  _this setDir -622.54645;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4159.0059, 8826.7266];
};

_vehicle_847 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Panelak", [4110.0605, 8852.8086, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_847 = _this;
  _this setDir 9.0273886;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4110.0605, 8852.8086, 0.00024414063];
};

_vehicle_849 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Panelak", [4091.3147, 8847.4707, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_849 = _this;
  _this setDir -81.369408;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4091.3147, 8847.4707, 6.1035156e-005];
};

_vehicle_852 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Panelak", [4068.6787, 8849.2949, -0.058304112], [], 0, "CAN_COLLIDE"];
  _vehicle_852 = _this;
  _this setDir 90.91201;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4068.6787, 8849.2949, -0.058304112];
};

_vehicle_864 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4074.217, 8849.1094, -1.6741961], [], 0, "CAN_COLLIDE"];
  _vehicle_864 = _this;
  _this setDir 91.438766;
  _this setPos [4074.217, 8849.1094, -1.6741961];
};

_vehicle_866 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4074.3494, 8857.6777, -1.6894437], [], 0, "CAN_COLLIDE"];
  _vehicle_866 = _this;
  _this setDir 90.813736;
  _this setPos [4074.3494, 8857.6777, -1.6894437];
};

_vehicle_869 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4085.2344, 8857.3018, -1.6880583], [], 0, "CAN_COLLIDE"];
  _vehicle_869 = _this;
  _this setDir 100.08597;
  _this setPos [4085.2344, 8857.3018, -1.6880583];
};

_vehicle_872 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4083.9226, 8849.0947, -1.7172902], [], 0, "CAN_COLLIDE"];
  _vehicle_872 = _this;
  _this setDir 98.25573;
  _this setPos [4083.9226, 8849.0947, -1.7172902];
};

_vehicle_875 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4075.7253, 8865.1279, -1.6778854], [], 0, "CAN_COLLIDE"];
  _vehicle_875 = _this;
  _this setDir 179.6609;
  _this setPos [4075.7253, 8865.1279, -1.6778854];
};

_vehicle_877 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4084.0061, 8863.1621, -1.6775552], [], 0, "CAN_COLLIDE"];
  _vehicle_877 = _this;
  _this setDir 365.14313;
  _this setPos [4084.0061, 8863.1621, -1.6775552];
};

_vehicle_879 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4092.2974, 8862.2305, -1.6954236], [], 0, "CAN_COLLIDE"];
  _vehicle_879 = _this;
  _this setDir 7.6998076;
  _this setPos [4092.2974, 8862.2305, -1.6954236];
};

_vehicle_881 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4100.9937, 8862.665, -1.7088045], [], 0, "CAN_COLLIDE"];
  _vehicle_881 = _this;
  _this setDir 187.70549;
  _this setPos [4100.9937, 8862.665, -1.7088045];
};

_vehicle_883 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4109.5137, 8861.5049, -1.698295], [], 0, "CAN_COLLIDE"];
  _vehicle_883 = _this;
  _this setDir 187.95583;
  _this setPos [4109.5137, 8861.5049, -1.698295];
};

_vehicle_885 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4117.7031, 8858.667, -1.6849611], [], 0, "CAN_COLLIDE"];
  _vehicle_885 = _this;
  _this setDir 8.6751642;
  _this setPos [4117.7031, 8858.667, -1.6849611];
};

_vehicle_889 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Piskoviste", [4078.9746, 8861.5098, 0.13071278], [], 0, "CAN_COLLIDE"];
  _vehicle_889 = _this;
  _this setDir 0.015840322;
  _this setVehicleInit "this allowDammage false;this enableSimulation false;";
  _this setPos [4078.9746, 8861.5098, 0.13071278];
};

_vehicle_891 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Piskoviste", [4082.8076, 8861.3633, 0.49859411], [], 0, "CAN_COLLIDE"];
  _vehicle_891 = _this;
  _this setDir 4.5689578;
  _this setVehicleInit "this allowDammage false;this enableSimulation false;";
  _this setPos [4082.8076, 8861.3633, 0.49859411];
};

_vehicle_893 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_sambucus", [4079.4333, 8861.1709, 0.14118484], [], 0, "CAN_COLLIDE"];
  _vehicle_893 = _this;
  _this setDir -71.760963;
  _this setPos [4079.4333, 8861.1709, 0.14118484];
};

_vehicle_894 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_pmugo", [4082.9006, 8860.9336, 0.53860307], [], 0, "CAN_COLLIDE"];
  _vehicle_894 = _this;
  _this setDir 78.118217;
  _this setPos [4082.9006, 8860.9336, 0.53860307];
};

_vehicle_901 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [4083.281, 8861.1982, 0.69444668], [], 0, "CAN_COLLIDE"];
  _vehicle_901 = _this;
  _this setPos [4083.281, 8861.1982, 0.69444668];
};

_vehicle_906 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [4081.9556, 8861.9609, 0.54150146], [], 0, "CAN_COLLIDE"];
  _vehicle_906 = _this;
  _this setDir -53.519505;
  _this setPos [4081.9556, 8861.9609, 0.54150146];
};

_vehicle_908 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4067.302, 8865.1709, -1.7339569], [], 0, "CAN_COLLIDE"];
  _vehicle_908 = _this;
  _this setDir 181.10017;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4067.302, 8865.1709, -1.7339569];
};

_vehicle_910 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rail_najazdovarampa", [4158.1406, 8825.8086, -1.2877095], [], 0, "CAN_COLLIDE"];
  _vehicle_910 = _this;
  _this setDir 187.80344;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4158.1406, 8825.8086, -1.2877095];
};

_vehicle_912 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Panelak", [4062.5723, 8882.5938, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_912 = _this;
  _this setDir 181.03839;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4062.5723, 8882.5938, -0.00012207031];
};

_vehicle_918 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4059.063, 8876.4238, -1.7590214], [], 0, "CAN_COLLIDE"];
  _vehicle_918 = _this;
  _this setDir 181.22348;
  _this setPos [4059.063, 8876.4238, -1.7590214];
};

_vehicle_921 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4065.8203, 8876.335, -1.6662267], [], 0, "CAN_COLLIDE"];
  _vehicle_921 = _this;
  _this setDir 180.2831;
  _this setPos [4065.8203, 8876.335, -1.6662267];
};

_vehicle_926 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [4128.5771, 8765.0605], [], 0, "CAN_COLLIDE"];
  _vehicle_926 = _this;
  _this setDir -348.68799;
  _this setPos [4128.5771, 8765.0605];
};

_vehicle_927 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4147.5474, 8829.0156, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_927 = _this;
  _this setDir 7.68262;
  _this setPos [4147.5474, 8829.0156, 3.0517578e-005];
};

_vehicle_929 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [4151.7754, 8859.7002, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_929 = _this;
  _this setDir 278.90277;
  _this setPos [4151.7754, 8859.7002, 3.0517578e-005];
};

_vehicle_931 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [4163.5337, 8858.3799, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_931 = _this;
  _this setDir -85.023117;
  _this setPos [4163.5337, 8858.3799, 6.1035156e-005];
};

_vehicle_933 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [4146.626, 8860.4678], [], 0, "CAN_COLLIDE"];
  _vehicle_933 = _this;
  _this setDir -78.709984;
  _this setPos [4146.626, 8860.4678];
};

_vehicle_936 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4144.6782, 8807.9199, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_936 = _this;
  _this setDir 7.9710989;
  _this setPos [4144.6782, 8807.9199, -9.1552734e-005];
};

_vehicle_938 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2_noRoad", [4126.2485, 8852.5117, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_938 = _this;
  _this setDir -171.30623;
  _this setPos [4126.2485, 8852.5117, 3.0517578e-005];
};

_vehicle_940 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2_noRoad", [4126.6709, 8855.6768], [], 0, "CAN_COLLIDE"];
  _vehicle_940 = _this;
  _this setDir -351.77264;
  _this setPos [4126.6709, 8855.6768];
};

_vehicle_943 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2_noRoad", [4128.1494, 8853.8906, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_943 = _this;
  _this setDir -261.90509;
  _this setPos [4128.1494, 8853.8906, -3.0517578e-005];
};

_vehicle_951 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4143.3823, 8843.6377, -0.44728178], [], 0, "CAN_COLLIDE"];
  _vehicle_951 = _this;
  _this setDir -170.93459;
  _this setPos [4143.3823, 8843.6377, -0.44728178];
};

_vehicle_953 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4138.4644, 8844.4189, -0.43788233], [], 0, "CAN_COLLIDE"];
  _vehicle_953 = _this;
  _this setDir -170.93459;
  _this setPos [4138.4644, 8844.4189, -0.43788233];
};

_vehicle_957 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4135.167, 8837.5693, -0.42732072], [], 0, "CAN_COLLIDE"];
  _vehicle_957 = _this;
  _this setDir -262.0405;
  _this setPos [4135.167, 8837.5693, -0.42732072];
};

_vehicle_958 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4135.8555, 8842.4932, -0.44050413], [], 0, "CAN_COLLIDE"];
  _vehicle_958 = _this;
  _this setDir -262.0405;
  _this setPos [4135.8555, 8842.4932, -0.44050413];
};

_vehicle_961 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4133.7012, 8827.7275, -0.41242111], [], 0, "CAN_COLLIDE"];
  _vehicle_961 = _this;
  _this setDir -261.34232;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4133.7012, 8827.7275, -0.41242111];
};

_vehicle_962 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4134.4512, 8832.6533, -0.43902057], [], 0, "CAN_COLLIDE"];
  _vehicle_962 = _this;
  _this setDir -261.34232;
  _this setPos [4134.4512, 8832.6533, -0.43902057];
};

_vehicle_967 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4134.1934, 8815.5137, -0.32605192], [], 0, "CAN_COLLIDE"];
  _vehicle_967 = _this;
  _this setDir -170.93459;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4134.1934, 8815.5137, -0.32605192];
};

_vehicle_968 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4139.1201, 8814.7598, -0.35984731], [], 0, "CAN_COLLIDE"];
  _vehicle_968 = _this;
  _this setDir -170.93459;
  _this setPos [4139.1201, 8814.7598, -0.35984731];
};

_vehicle_972 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4132.9727, 8822.8906, -0.38974142], [], 0, "CAN_COLLIDE"];
  _vehicle_972 = _this;
  _this setDir -261.34232;
  _this setPos [4132.9727, 8822.8906, -0.38974142];
};

_vehicle_975 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4132.248, 8818.1631, -0.30062771], [], 0, "CAN_COLLIDE"];
  _vehicle_975 = _this;
  _this setDir -261.34232;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4132.248, 8818.1631, -0.30062771];
};

_vehicle_980 = objNull;
if (true) then
{
  _this = createVehicle ["hiluxWreck", [4139.8213, 8841.4512], [], 0, "CAN_COLLIDE"];
  _vehicle_980 = _this;
  _this setDir 108.74065;
  _this setPos [4139.8213, 8841.4512];
};

_vehicle_981 = objNull;
if (true) then
{
  _this = createVehicle ["LADAWreck", [4137.585, 8827.3047], [], 0, "CAN_COLLIDE"];
  _vehicle_981 = _this;
  _this setDir -92.139999;
  _this setPos [4137.585, 8827.3047];
};

_vehicle_986 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_sambucus", [4132.6777, 8815.0713, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_986 = _this;
  _this setDir -71.760963;
  _this setPos [4132.6777, 8815.0713, -6.1035156e-005];
};

_vehicle_988 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [4136.4517, 8813.5742], [], 0, "CAN_COLLIDE"];
  _vehicle_988 = _this;
  _this setPos [4136.4517, 8813.5742];
};

_vehicle_989 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4133.3564, 8828.1895, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_989 = _this;
  _this setPos [4133.3564, 8828.1895, 6.1035156e-005];
};

_vehicle_995 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4137.9292, 8814.3203, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_995 = _this;
  _this setPos [4137.9292, 8814.3203, -6.1035156e-005];
};

_vehicle_997 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4138.9341, 8814.3545, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_997 = _this;
  _this setPos [4138.9341, 8814.3545, 3.0517578e-005];
};

_vehicle_999 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4133.4902, 8815.0576, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_999 = _this;
  _this setPos [4133.4902, 8815.0576, -6.1035156e-005];
};

_vehicle_1001 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4142.1577, 8843.4092, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1001 = _this;
  _this setPos [4142.1577, 8843.4092, 0];
};

_vehicle_1003 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4142.9658, 8843.2891, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1003 = _this;
  _this setPos [4142.9658, 8843.2891, 3.0517578e-005];
};

_vehicle_1005 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_craet1", [4135.0854, 8844.4619], [], 0, "CAN_COLLIDE"];
  _vehicle_1005 = _this;
  _this setPos [4135.0854, 8844.4619];
};

_vehicle_1006 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_sorbus2s", [4132.2407, 8821.7041, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1006 = _this;
  _this setPos [4132.2407, 8821.7041, -3.0517578e-005];
};

_vehicle_1022 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [4145.1704, 8818.5215, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1022 = _this;
  _this setDir -81.202026;
  _this setPos [4145.1704, 8818.5215, 3.0517578e-005];
};

_vehicle_1023 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [4138.9917, 8819.4551], [], 0, "CAN_COLLIDE"];
  _vehicle_1023 = _this;
  _this setDir -81.182419;
  _this setPos [4138.9917, 8819.4551];
};

_vehicle_1026 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [4146.5898, 8828.165, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1026 = _this;
  _this setDir -81.202026;
  _this setPos [4146.5898, 8828.165, -3.0517578e-005];
};

_vehicle_1027 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [4140.4111, 8829.0986, -0.040466309], [], 0, "CAN_COLLIDE"];
  _vehicle_1027 = _this;
  _this setDir -81.182419;
  _this setPos [4140.4111, 8829.0986, -0.040466309];
};

_vehicle_1030 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [4148.0762, 8838.1084], [], 0, "CAN_COLLIDE"];
  _vehicle_1030 = _this;
  _this setDir -81.202026;
  _this setPos [4148.0762, 8838.1084];
};

_vehicle_1031 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [4141.8975, 8839.042, -0.069000244], [], 0, "CAN_COLLIDE"];
  _vehicle_1031 = _this;
  _this setDir -81.182419;
  _this setPos [4141.8975, 8839.042, -0.069000244];
};

_vehicle_1036 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4135.9097, 8824.8457, -0.41226345], [], 0, "CAN_COLLIDE"];
  _vehicle_1036 = _this;
  _this setDir -170.93459;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4135.9097, 8824.8457, -0.41226345];
};

_vehicle_1039 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [4137.4072, 8834.6592, -0.44431835], [], 0, "CAN_COLLIDE"];
  _vehicle_1039 = _this;
  _this setDir -170.30956;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4137.4072, 8834.6592, -0.44431835];
};

_vehicle_1041 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_End1", [4141.1538, 8834.04, -0.44520953], [], 0, "CAN_COLLIDE"];
  _vehicle_1041 = _this;
  _this setDir -171.3862;
  _this setPos [4141.1538, 8834.04, -0.44520953];
};

_vehicle_1042 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_End2", [4139.7168, 8824.2432, -0.44181585], [], 0, "CAN_COLLIDE"];
  _vehicle_1042 = _this;
  _this setDir -171.00163;
  _this setPos [4139.7168, 8824.2432, -0.44181585];
};

_vehicle_1044 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4138.1523, 8834.8945, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1044 = _this;
  _this setPos [4138.1523, 8834.8945, 9.1552734e-005];
};

_vehicle_1046 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4133.2305, 8829.0313, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1046 = _this;
  _this setPos [4133.2305, 8829.0313, 0];
};

_vehicle_1048 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4127.207, 8853.9922, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1048 = _this;
  _this setPos [4127.207, 8853.9922, 0];
};

_vehicle_1050 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4127.3667, 8854.6924, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1050 = _this;
  _this setPos [4127.3667, 8854.6924, -6.1035156e-005];
};

_vehicle_1054 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4089.646, 8860.5117, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1054 = _this;
  _this setPos [4089.646, 8860.5117, 0];
};

_vehicle_1056 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4072.2798, 8861.2354, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1056 = _this;
  _this setPos [4072.2798, 8861.2354, -3.0517578e-005];
};

_vehicle_1058 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4071.1191, 8861.2109, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1058 = _this;
  _this setPos [4071.1191, 8861.2109, 0];
};

_vehicle_1060 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4075.1599, 8844.1309, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1060 = _this;
  _this setPos [4075.1599, 8844.1309, -6.1035156e-005];
};

_vehicle_1062 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4083.2422, 8858.7598, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1062 = _this;
  _this setPos [4083.2422, 8858.7598, 3.0517578e-005];
};

_vehicle_1064 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4082.0256, 8859.1182, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1064 = _this;
  _this setPos [4082.0256, 8859.1182, 0];
};

_vehicle_1066 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4091.6377, 8893.2051, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1066 = _this;
  _this setPos [4091.6377, 8893.2051, -3.0517578e-005];
};

_vehicle_1068 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4123.6768, 8928.9316, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1068 = _this;
  _this setPos [4123.6768, 8928.9316, 0];
};

_vehicle_1070 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4124.1431, 8929.6006, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1070 = _this;
  _this setPos [4124.1431, 8929.6006, -3.0517578e-005];
};

_vehicle_1072 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4126.4678, 8941.4648, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1072 = _this;
  _this setPos [4126.4678, 8941.4648, 3.0517578e-005];
};

_vehicle_1074 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4127.3477, 8948.9502, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1074 = _this;
  _this setPos [4127.3477, 8948.9502, 6.1035156e-005];
};

_vehicle_1076 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4193.2554, 8899.709, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1076 = _this;
  _this setPos [4193.2554, 8899.709, -3.0517578e-005];
};

_vehicle_1078 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4194.1455, 8899.7861, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1078 = _this;
  _this setPos [4194.1455, 8899.7861, 3.0517578e-005];
};

_vehicle_1080 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4241.1655, 8900.2432, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1080 = _this;
  _this setPos [4241.1655, 8900.2432, 3.0517578e-005];
};

_vehicle_1082 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4239.8926, 8900.4561, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1082 = _this;
  _this setPos [4239.8926, 8900.4561, 3.0517578e-005];
};

_vehicle_1084 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4232.0864, 8956.5889, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1084 = _this;
  _this setPos [4232.0864, 8956.5889, 6.1035156e-005];
};

_vehicle_1086 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4230.6343, 8956.5742, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1086 = _this;
  _this setPos [4230.6343, 8956.5742, 0.00012207031];
};

_vehicle_1088 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [4194.5874, 8950.7695, -0.11520055], [], 0, "CAN_COLLIDE"];
  _vehicle_1088 = _this;
  _this setDir -173.7476;
  _this setPos [4194.5874, 8950.7695, -0.11520055];
};

_vehicle_1092 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [4245.6797, 8941.3076, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1092 = _this;
  _this setDir -177.15794;
  _this setPos [4245.6797, 8941.3076, 3.0517578e-005];
};

_vehicle_1095 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1B", [4245.6758, 8941.4824, 2.5509584], [], 0, "CAN_COLLIDE"];
  _vehicle_1095 = _this;
  _this setDir -355.10028;
  _this setPos [4245.6758, 8941.4824, 2.5509584];
};

_vehicle_1098 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo2A", [4240.2773, 8914.1494], [], 0, "CAN_COLLIDE"];
  _vehicle_1098 = _this;
  _this setDir 3.4405456;
  _this setPos [4240.2773, 8914.1494];
};

_vehicle_1100 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo2B", [4242.8003, 8941.792, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1100 = _this;
  _this setDir 7.4602656;
  _this setPos [4242.8003, 8941.792, 6.1035156e-005];
};

_vehicle_1101 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo2E", [4234.7896, 8950.3486], [], 0, "CAN_COLLIDE"];
  _vehicle_1101 = _this;
  _this setDir -265.94556;
  _this setPos [4234.7896, 8950.3486];
};

_vehicle_1106 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [4117.4077, 8899.2959, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1106 = _this;
  _this setDir -254.33984;
  _this setPos [4117.4077, 8899.2959, 6.1035156e-005];
};

_vehicle_1118 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D_2", [4164.9131, 8898.2148, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1118 = _this;
  _this setDir -259.3172;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4164.9131, 8898.2148, 3.0517578e-005];
};

_vehicle_1122 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D_2", [4165.2056, 8899.6455], [], 0, "CAN_COLLIDE"];
  _vehicle_1122 = _this;
  _this setDir -80.722084;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4165.2056, 8899.6455];
};

_vehicle_1130 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4193.7964, 8864.0322, -0.070831299], [], 0, "CAN_COLLIDE"];
  _vehicle_1130 = _this;
  _this setDir -347.22754;
  _this setPos [4193.7964, 8864.0322, -0.070831299];
};

_vehicle_1131 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [4188.1587, 8865.9258, -0.092376709], [], 0, "CAN_COLLIDE"];
  _vehicle_1131 = _this;
  _this setDir -338.54175;
  _this setPos [4188.1587, 8865.9258, -0.092376709];
};

_vehicle_1136 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4199.6694, 8863.1504, -0.038900621], [], 0, "CAN_COLLIDE"];
  _vehicle_1136 = _this;
  _this setDir -359.8956;
  _this setPos [4199.6694, 8863.1504, -0.038900621];
};

_vehicle_1139 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [4205.5313, 8863.7783, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1139 = _this;
  _this setDir -379.75183;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4205.5313, 8863.7783, -3.0517578e-005];
};

_vehicle_1142 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [4212.6465, 8867.6035, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1142 = _this;
  _this setDir -935.67871;
  _this setPos [4212.6465, 8867.6035, -3.0517578e-005];
};

_vehicle_1148 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [4207.5962, 8864.501, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1148 = _this;
  _this setDir -203.12018;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4207.5962, 8864.501, 6.1035156e-005];
};

_vehicle_1151 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4216.4609, 8871.8574, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1151 = _this;
  _this setDir -234.37857;
  _this setPos [4216.4609, 8871.8574, -3.0517578e-005];
};

_vehicle_1154 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4219.5718, 8876.918, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1154 = _this;
  _this setDir -240.2155;
  _this setPos [4219.5718, 8876.918, 6.1035156e-005];
};

_vehicle_1157 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4222.2344, 8882.2725, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1157 = _this;
  _this setDir -245.17175;
  _this setPos [4222.2344, 8882.2725, -0.00015258789];
};

_vehicle_1160 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4223.6216, 8887.375, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1160 = _this;
  _this setDir -86.117599;
  _this setPos [4223.6216, 8887.375, 0.00012207031];
};

_vehicle_1162 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_green_brank_o", [4223.2358, 8884.667, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1162 = _this;
  _this setDir -70.543694;
  _this setPos [4223.2358, 8884.667, 3.0517578e-005];
};

_vehicle_1164 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [4200.2026, 8896.5371], [], 0, "CAN_COLLIDE"];
  _vehicle_1164 = _this;
  _this setDir -453.21024;
  _this setPos [4200.2026, 8896.5371];
};

_vehicle_1167 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4198.8096, 8891.0264, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1167 = _this;
  _this setDir -426.8187;
  _this setPos [4198.8096, 8891.0264, -3.0517578e-005];
};

_vehicle_1177 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind2A_L", [4196.1572, 8885.5469, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1177 = _this;
  _this setDir 115.73003;
  _this setPos [4196.1572, 8885.5469, 9.1552734e-005];
};

_vehicle_1178 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind2A_R", [4196.1572, 8885.542, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1178 = _this;
  _this setDir -247.77054;
  _this setPos [4196.1572, 8885.542, 6.1035156e-005];
};

_vehicle_1180 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4198.1191, 8890.9336, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1180 = _this;
  _this setPos [4198.1191, 8890.9336, 9.1552734e-005];
};

_vehicle_1182 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4198.1108, 8890.1631, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1182 = _this;
  _this setPos [4198.1108, 8890.1631, 6.1035156e-005];
};

_vehicle_1184 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4192.7114, 8863.9619, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1184 = _this;
  _this setPos [4192.7114, 8863.9619, 0];
};

_vehicle_1186 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4200.0298, 8862.7959, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1186 = _this;
  _this setPos [4200.0298, 8862.7959, 0];
};

_vehicle_1188 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4201.0879, 8862.8721, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1188 = _this;
  _this setPos [4201.0879, 8862.8721, 3.0517578e-005];
};

_vehicle_1190 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4224.3018, 8890.7695, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1190 = _this;
  _this setPos [4224.3018, 8890.7695, 6.1035156e-005];
};

_vehicle_1192 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4225.1719, 8890.9863, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1192 = _this;
  _this setPos [4225.1719, 8890.9863, 9.1552734e-005];
};

_vehicle_1195 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_Pole", [4193.7261, 8879.7393, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1195 = _this;
  _this setDir 28.103975;
  _this setPos [4193.7261, 8879.7393, 3.0517578e-005];
};

_vehicle_1197 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [4193.8057, 8879.8828, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1197 = _this;
  _this setDir -604.16913;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4193.8057, 8879.8828, -3.0517578e-005];
};

_vehicle_1204 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [4207.8511, 8881.6357, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1204 = _this;
  _this setDir -238.84976;
  _this setPos [4207.8511, 8881.6357, -6.1035156e-005];
};

_vehicle_1205 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [4213.2129, 8878.4199, -0.082855225], [], 0, "CAN_COLLIDE"];
  _vehicle_1205 = _this;
  _this setDir -238.83014;
  _this setPos [4213.2129, 8878.4199, -0.082855225];
};

_vehicle_1210 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [4219.1602, 8889.1826], [], 0, "CAN_COLLIDE"];
  _vehicle_1210 = _this;
  _this setDir -355.86017;
  _this setPos [4219.1602, 8889.1826];
};

_vehicle_1211 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [4219.5933, 8895.4209, -0.083007813], [], 0, "CAN_COLLIDE"];
  _vehicle_1211 = _this;
  _this setDir -355.84055;
  _this setPos [4219.5933, 8895.4209, -0.083007813];
};

_vehicle_1216 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [4206.6914, 8888.75, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1216 = _this;
  _this setDir -364.31015;
  _this setPos [4206.6914, 8888.75, -3.0517578e-005];
};

_vehicle_1217 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [4206.2021, 8894.9814, -0.081420898], [], 0, "CAN_COLLIDE"];
  _vehicle_1217 = _this;
  _this setDir -364.29053;
  _this setPos [4206.2021, 8894.9814, -0.081420898];
};

_vehicle_1256 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4214.606, 8897.9014, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1256 = _this;
  _this setPos [4214.606, 8897.9014, 0.00012207031];
};

_vehicle_1258 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4211.2651, 8900.1309, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1258 = _this;
  _this setPos [4211.2651, 8900.1309, 9.1552734e-005];
};

_vehicle_1261 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [4216.7593, 8882.4639, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1261 = _this;
  _this setDir -199.90735;
  _this setPos [4216.7593, 8882.4639, 3.0517578e-005];
};

_vehicle_1264 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_craet1", [4200.481, 8891.2314, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1264 = _this;
  _this setDir 284.2345;
  _this setPos [4200.481, 8891.2314, 6.1035156e-005];
};

_vehicle_1266 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4210.6284, 8899.6836, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1266 = _this;
  _this setDir -182.70227;
  _this setPos [4210.6284, 8899.6836, 9.1552734e-005];
};

_vehicle_1268 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4210.8628, 8895.7842], [], 0, "CAN_COLLIDE"];
  _vehicle_1268 = _this;
  _this setDir -183.95233;
  _this setPos [4210.8628, 8895.7842];
};

_vehicle_1272 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4214.835, 8896.0186, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1272 = _this;
  _this setDir -176.10031;
  _this setPos [4214.835, 8896.0186, -3.0517578e-005];
};

_vehicle_1273 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4215.1143, 8899.9189, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1273 = _this;
  _this setDir -176.10031;
  _this setPos [4215.1143, 8899.9189, 6.1035156e-005];
};

_vehicle_1276 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4215.6377, 8882.084, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1276 = _this;
  _this setDir -59.347328;
  _this setPos [4215.6377, 8882.084, 6.1035156e-005];
};

_vehicle_1277 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4218.9302, 8880.1162, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1277 = _this;
  _this setDir -59.347328;
  _this setPos [4218.9302, 8880.1162, -9.1552734e-005];
};

_vehicle_1280 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4210.9116, 8874.2373, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1280 = _this;
  _this setDir -59.549862;
  _this setPos [4210.9116, 8874.2373, 3.0517578e-005];
};

_vehicle_1281 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4214.2134, 8872.3125, -0.0024108887], [], 0, "CAN_COLLIDE"];
  _vehicle_1281 = _this;
  _this setDir -60.302692;
  _this setPos [4214.2134, 8872.3125, -0.0024108887];
};

_vehicle_1285 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_rust_brank_o", [4213.624, 8901.8193], [], 0, "CAN_COLLIDE"];
  _vehicle_1285 = _this;
  _this setDir -175.74002;
  _this setPos [4213.624, 8901.8193];
};

_vehicle_1297 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_Pole", [4214.7476, 8901.7715, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1297 = _this;
  _this setDir -174.32864;
  _this setPos [4214.7476, 8901.7715, -3.0517578e-005];
};

_vehicle_1301 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4215.0005, 8901.167, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1301 = _this;
  _this setPos [4215.0005, 8901.167, 0];
};

_vehicle_1303 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4211.7954, 8901.3799, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1303 = _this;
  _this setPos [4211.7954, 8901.3799, 0];
};

_vehicle_1315 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_heracleum", [4223.937, 8886.5586, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1315 = _this;
  _this setDir -52.025455;
  _this setPos [4223.937, 8886.5586, -3.0517578e-005];
};

_vehicle_1322 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ural_wrecked", [4217.3545, 8897.1357], [], 0, "CAN_COLLIDE"];
  _vehicle_1322 = _this;
  _this setDir -529.98987;
  _this setPos [4217.3545, 8897.1357];
};

_vehicle_1324 = objNull;
if (true) then
{
  _this = createVehicle ["datsun02Wreck", [4202.813, 8897.0469, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1324 = _this;
  _this setDir -378.97287;
  _this setPos [4202.813, 8897.0469, 3.0517578e-005];
};

_vehicle_1327 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [4172.2866, 8819.9863, -0.03896166], [], 0, "CAN_COLLIDE"];
  _vehicle_1327 = _this;
  _this setDir -82.487083;
  _this setPos [4172.2866, 8819.9863, -0.03896166];
};

_vehicle_1333 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [4171.6333, 8815.1338, -0.11688761], [], 0, "CAN_COLLIDE"];
  _vehicle_1333 = _this;
  _this setDir -81.818207;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4171.6333, 8815.1338, -0.11688761];
};

_vehicle_1338 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_R", [4167.0234, 8813.5186, 0.027460858], [], 0, "CAN_COLLIDE"];
  _vehicle_1338 = _this;
  _this setDir 6.8293438;
  _this setPos [4167.0234, 8813.5186, 0.027460858];
};

_vehicle_1340 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [4172.7231, 8824.8799, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1340 = _this;
  _this setDir -88.146774;
  _this setPos [4172.7231, 8824.8799, 3.0517578e-005];
};

_vehicle_1342 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [4172.8486, 8827.5234, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1342 = _this;
  _this setDir -269.72525;
  _this setPos [4172.8486, 8827.5234, 0.00012207031];
};

_vehicle_1345 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2_noRoad", [4201.2554, 8870.2197, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1345 = _this;
  _this setDir -371.24811;
  _this setPos [4201.2554, 8870.2197, -3.0517578e-005];
};

_vehicle_1348 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2_noRoad", [4193.0508, 8877.5518], [], 0, "CAN_COLLIDE"];
  _vehicle_1348 = _this;
  _this setDir -239.69659;
  _this setPos [4193.0508, 8877.5518];
};

_vehicle_1365 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4175.2417, 8875.5537, 0.14361924], [], 0, "CAN_COLLIDE"];
  _vehicle_1365 = _this;
  _this setDir -328.46265;
  _this setPos [4175.2417, 8875.5537, 0.14361924];
};

_vehicle_1368 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4170.9229, 8868.6465, 0.12234551], [], 0, "CAN_COLLIDE"];
  _vehicle_1368 = _this;
  _this setDir -326.89999;
  _this setPos [4170.9229, 8868.6465, 0.12234551];
};

_vehicle_1371 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4173.103, 8872.0742, 0.13636579], [], 0, "CAN_COLLIDE"];
  _vehicle_1371 = _this;
  _this setDir -328.46265;
  _this setPos [4173.103, 8872.0742, 0.13636579];
};

_vehicle_1373 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4168.4399, 8865.4639, 0.15469134], [], 0, "CAN_COLLIDE"];
  _vehicle_1373 = _this;
  _this setDir -317.047;
  _this setPos [4168.4399, 8865.4639, 0.15469134];
};

_vehicle_1375 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4165.1338, 8863.3711, 0.13636583], [], 0, "CAN_COLLIDE"];
  _vehicle_1375 = _this;
  _this setDir -286.05652;
  _this setPos [4165.1338, 8863.3711, 0.13636583];
};

_vehicle_1378 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4161.1899, 8862.9453, 0.13540153], [], 0, "CAN_COLLIDE"];
  _vehicle_1378 = _this;
  _this setDir -264.97498;
  _this setPos [4161.1899, 8862.9453, 0.13540153];
};

_vehicle_1381 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4157.2114, 8863.3486, 0.13555732], [], 0, "CAN_COLLIDE"];
  _vehicle_1381 = _this;
  _this setDir -263.44083;
  _this setPos [4157.2114, 8863.3486, 0.13555732];
};

_vehicle_1384 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4153.2017, 8863.8857, 0.1363658], [], 0, "CAN_COLLIDE"];
  _vehicle_1384 = _this;
  _this setDir -261.25113;
  _this setPos [4153.2017, 8863.8857, 0.1363658];
};

_vehicle_1386 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patnik", [4150.9966, 8864.2393, 0.13786434], [], 0, "CAN_COLLIDE"];
  _vehicle_1386 = _this;
  _this setDir 9.5064068;
  _this setPos [4150.9966, 8864.2393, 0.13786434];
};

_vehicle_1388 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4151.4785, 8867.4307, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1388 = _this;
  _this setPos [4151.4785, 8867.4307, 0];
};

_vehicle_1390 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4152.3062, 8867.3643, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1390 = _this;
  _this setPos [4152.3062, 8867.3643, 0];
};

_vehicle_1392 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4158.6465, 8863.1602, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1392 = _this;
  _this setPos [4158.6465, 8863.1602, 0];
};

_vehicle_1395 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_craet1", [4168.2231, 8869.0068, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1395 = _this;
  _this setDir 238.23756;
  _this setPos [4168.2231, 8869.0068, 3.0517578e-005];
};

_vehicle_1398 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_heracleum", [4166.1753, 8863.3672, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1398 = _this;
  _this setDir -52.025455;
  _this setPos [4166.1753, 8863.3672, 0];
};

_vehicle_1401 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4166.144, 8863.4707, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1401 = _this;
  _this setPos [4166.144, 8863.4707, 3.0517578e-005];
};

_vehicle_1403 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4167.4116, 8863.6807, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1403 = _this;
  _this setPos [4167.4116, 8863.6807, 3.0517578e-005];
};

_vehicle_1405 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4175.9971, 8876.75, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1405 = _this;
  _this setPos [4175.9971, 8876.75, 3.0517578e-005];
};

_vehicle_1407 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4159.3691, 8863.0029, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1407 = _this;
  _this setPos [4159.3691, 8863.0029, 0];
};

_vehicle_1409 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4171.75, 8869.8809, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1409 = _this;
  _this setPos [4171.75, 8869.8809, 3.0517578e-005];
};

_vehicle_1411 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4176.4072, 8877.3164, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1411 = _this;
  _this setPos [4176.4072, 8877.3164, 0];
};

_vehicle_1413 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4217.6548, 8873.082, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1413 = _this;
  _this setPos [4217.6548, 8873.082, 3.0517578e-005];
};

_vehicle_1415 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4217.8701, 8873.4102, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1415 = _this;
  _this setPos [4217.8701, 8873.4102, -3.0517578e-005];
};

_vehicle_1417 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4223.855, 8886.5117, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1417 = _this;
  _this setPos [4223.855, 8886.5117, 6.1035156e-005];
};

_vehicle_1419 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4224.1758, 8885.7686, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1419 = _this;
  _this setPos [4224.1758, 8885.7686, 0];
};

_vehicle_1421 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4223.1147, 8886.2021, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1421 = _this;
  _this setPos [4223.1147, 8886.2021, 0];
};

_vehicle_1423 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4223.3364, 8886.6094, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1423 = _this;
  _this setPos [4223.3364, 8886.6094, -3.0517578e-005];
};

_vehicle_1425 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4216.3623, 8915.1104, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1425 = _this;
  _this setPos [4216.3623, 8915.1104, 0];
};

_vehicle_1427 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4216.6572, 8915.0928, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1427 = _this;
  _this setPos [4216.6572, 8915.0928, 3.0517578e-005];
};

_vehicle_1429 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4216.7153, 8915.7725, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1429 = _this;
  _this setPos [4216.7153, 8915.7725, 6.1035156e-005];
};

_vehicle_1431 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4140.7114, 8949.7568, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1431 = _this;
  _this setPos [4140.7114, 8949.7568, 3.0517578e-005];
};

_vehicle_1433 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4140.2891, 8949.3057, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1433 = _this;
  _this setPos [4140.2891, 8949.3057, 0];
};

_vehicle_1435 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4141.0435, 8949.4053, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1435 = _this;
  _this setPos [4141.0435, 8949.4053, 3.0517578e-005];
};

_vehicle_1437 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4112.4834, 8927.0928, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1437 = _this;
  _this setPos [4112.4834, 8927.0928, 3.0517578e-005];
};

_vehicle_1439 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4111.9683, 8927.0889, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1439 = _this;
  _this setPos [4111.9683, 8927.0889, 6.1035156e-005];
};

_vehicle_1441 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4095.7803, 8905.9932, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1441 = _this;
  _this setPos [4095.7803, 8905.9932, 0];
};

_vehicle_1443 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4095.6387, 8905.3174, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1443 = _this;
  _this setPos [4095.6387, 8905.3174, 0];
};

_vehicle_1445 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4145.7173, 8843.5938, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1445 = _this;
  _this setPos [4145.7173, 8843.5938, 9.1552734e-005];
};

_vehicle_1447 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4145.251, 8843.6182, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1447 = _this;
  _this setPos [4145.251, 8843.6182, -3.0517578e-005];
};

_vehicle_1449 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4193.4072, 8863.7607, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1449 = _this;
  _this setPos [4193.4072, 8863.7607, 3.0517578e-005];
};

_vehicle_1451 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4191.8022, 8864.1885, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1451 = _this;
  _this setPos [4191.8022, 8864.1885, 3.0517578e-005];
};

_vehicle_1453 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4186.103, 8866.3867, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1453 = _this;
  _this setPos [4186.103, 8866.3867, 3.0517578e-005];
};

_vehicle_1455 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4131.7593, 8821.2197, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1455 = _this;
  _this setPos [4131.7593, 8821.2197, -3.0517578e-005];
};

_vehicle_1457 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4132.2339, 8821.499, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1457 = _this;
  _this setPos [4132.2339, 8821.499, -6.1035156e-005];
};

_vehicle_1459 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4132.1646, 8822.1084, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1459 = _this;
  _this setPos [4132.1646, 8822.1084, 3.0517578e-005];
};

_vehicle_1461 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4131.3296, 8822.1406, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1461 = _this;
  _this setPos [4131.3296, 8822.1406, -6.1035156e-005];
};

_vehicle_1466 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4145.0444, 8809.3105, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1466 = _this;
  _this setDir -169.10849;
  _this setPos [4145.0444, 8809.3105, -0.00012207031];
};

_vehicle_1468 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4129.5498, 8770.4238, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1468 = _this;
  _this setDir 16.828405;
  _this setPos [4129.5498, 8770.4238, -9.1552734e-005];
};

_vehicle_1472 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [4139.3247, 8791.5635, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1472 = _this;
  _this setDir -156.56863;
  _this setPos [4139.3247, 8791.5635, -9.1552734e-005];
};

_vehicle_1474 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_majak_podesta", [4133.4873, 8874.8359, -2.1588283], [], 0, "CAN_COLLIDE"];
  _vehicle_1474 = _this;
  _this setDir -80.432358;
  _this setPos [4133.4873, 8874.8359, -2.1588283];
};

_vehicle_1480 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_WaterStation", [4143.3906, 8871.8701, 0.11688496], [], 0, "CAN_COLLIDE"];
  _vehicle_1480 = _this;
  _this setDir 279.53113;
  _this setPos [4143.3906, 8871.8701, 0.11688496];
};

_vehicle_1488 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Kontejner", [4141.5806, 8868.3457, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1488 = _this;
  _this setDir 91.440399;
  _this setPos [4141.5806, 8868.3457, 0.00012207031];
};

_vehicle_1490 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Kontejner", [4143.6646, 8868.1592, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1490 = _this;
  _this setDir 101.86236;
  _this setPos [4143.6646, 8868.1592, 3.0517578e-005];
};

_vehicle_1492 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4143.0386, 8867.4414, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1492 = _this;
  _this setPos [4143.0386, 8867.4414, 9.1552734e-005];
};

_vehicle_1493 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4141.3994, 8868.0313, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1493 = _this;
  _this setPos [4141.3994, 8868.0313, 0];
};

_vehicle_1494 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4128.0293, 8855.7617, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1494 = _this;
  _this setPos [4128.0293, 8855.7617, 0];
};

_vehicle_1505 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_ConcPipeline_EP1", [4253.4722, 8930.4668, -1.7528073], [], 0, "CAN_COLLIDE"];
  _vehicle_1505 = _this;
  _this setVehicleInit "this setvectorup [0,0.1,0.001]";
  _this setPos [4253.4722, 8930.4668, -1.7528073];
};

_vehicle_1517 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_pmugo", [4253.8979, 8930.6494, 0.343559], [], 0, "CAN_COLLIDE"];
  _vehicle_1517 = _this;
  _this setDir 71.483757;
  _this setPos [4253.8979, 8930.6494, 0.343559];
};

_vehicle_1519 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [4253.4063, 8930.6279, -0.055294611], [], 0, "CAN_COLLIDE"];
  _vehicle_1519 = _this;
  _this setDir -117.46883;
  _this setPos [4253.4063, 8930.6279, -0.055294611];
};

_vehicle_1525 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_pmugo", [4253.0127, 8930.0225, 0.37392172], [], 0, "CAN_COLLIDE"];
  _vehicle_1525 = _this;
  _this setDir 602.80707;
  _this setPos [4253.0127, 8930.0225, 0.37392172];
};

_vehicle_1530 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4253.3091, 8930.3057, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1530 = _this;
  _this setDir 80.312668;
  _this setPos [4253.3091, 8930.3057, -3.0517578e-005];
};

_vehicle_1532 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4253.6382, 8929.9023, 0.37402627], [], 0, "CAN_COLLIDE"];
  _vehicle_1532 = _this;
  _this setDir 168.32924;
  _this setPos [4253.6382, 8929.9023, 0.37402627];
};

_vehicle_1544 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_GasMeterExt", [4249.4019, 8935.3047, 0.18931925], [], 0, "CAN_COLLIDE"];
  _vehicle_1544 = _this;
  _this setDir -85.450645;
  _this setPos [4249.4019, 8935.3047, 0.18931925];
};

_vehicle_1545 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_FuseBox", [4249.1069, 8933.8154, 0.36987513], [], 0, "CAN_COLLIDE"];
  _vehicle_1545 = _this;
  _this setDir -84.994347;
  _this setPos [4249.1069, 8933.8154, 0.36987513];
};

_vehicle_1548 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_misc", [4240.1748, 8923.7734, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1548 = _this;
  _this setDir -60.153572;
  _this setPos [4240.1748, 8923.7734, -3.0517578e-005];
};

_vehicle_1550 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4240.5679, 8921.3877, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1550 = _this;
  _this setPos [4240.5679, 8921.3877, 9.1552734e-005];
};

_vehicle_1552 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4242.7471, 8923.9873, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1552 = _this;
  _this setPos [4242.7471, 8923.9873, 0];
};

_vehicle_1554 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4243.1948, 8910.3457, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1554 = _this;
  _this setPos [4243.1948, 8910.3457, 0];
};

_vehicle_1566 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [4255.064, 8924.9541, 0.16354382], [], 0, "CAN_COLLIDE"];
  _vehicle_1566 = _this;
  _this setDir -88.069565;
  _this setVehicleLock "LOCKED";
  _this setVehicleInit "this allowDammage false; this enableSimulation false";
  _this setPos [4255.064, 8924.9541, 0.16354382];
};

_vehicle_1568 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [4252.5044, 8925.1445, 0.16314501], [], 0, "CAN_COLLIDE"];
  _vehicle_1568 = _this;
  _this setDir -84.499802;
  _this setVehicleLock "LOCKED";
  _this setVehicleInit "this allowDammage false; this enableSimulation false";
  _this setPos [4252.5044, 8925.1445, 0.16314501];
};

_vehicle_1572 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [4253.2407, 8935.5332, 0.1558466], [], 0, "CAN_COLLIDE"];
  _vehicle_1572 = _this;
  _this setDir -266.53049;
  _this setVehicleLock "LOCKED";
  _this setVehicleInit "this allowDammage false; this enableSimulation false";
  _this setPos [4253.2407, 8935.5332, 0.1558466];
};

_vehicle_1573 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [4255.792, 8935.2705, 0.15544987], [], 0, "CAN_COLLIDE"];
  _vehicle_1573 = _this;
  _this setDir -262.96072;
  _this setVehicleLock "LOCKED";
  _this setVehicleInit "this allowDammage false; this enableSimulation false";
  _this setPos [4255.792, 8935.2705, 0.15544987];
};

_vehicle_1577 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4258.5205, 8928.3223], [], 0, "CAN_COLLIDE"];
  _vehicle_1577 = _this;
  _this setDir -30.7939;
  _this setPos [4258.5205, 8928.3223];
};

_vehicle_1579 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4258.2476, 8929.2217, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1579 = _this;
  _this setDir -30.7939;
  _this setPos [4258.2476, 8929.2217, 3.0517578e-005];
};

_vehicle_1581 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4256.6948, 8936.2461, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1581 = _this;
  _this setPos [4256.6948, 8936.2461, 0];
};

_vehicle_1583 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4255.5151, 8936.2676, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1583 = _this;
  _this setPos [4255.5151, 8936.2676, 3.0517578e-005];
};

_vehicle_1586 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [4257.9883, 8927.2793], [], 0, "CAN_COLLIDE"];
  _vehicle_1586 = _this;
  _this setDir -199.90735;
  _this setPos [4257.9883, 8927.2793];
};

_vehicle_1589 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4257.7871, 8925.9258, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1589 = _this;
  _this setPos [4257.7871, 8925.9258, 3.0517578e-005];
};

_vehicle_1591 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4253.791, 8924.2871, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1591 = _this;
  _this setPos [4253.791, 8924.2871, 3.0517578e-005];
};

_vehicle_1593 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4254.1079, 8924.2988, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1593 = _this;
  _this setPos [4254.1079, 8924.2988, 3.0517578e-005];
};

_vehicle_1595 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4251.7363, 8924.5713, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1595 = _this;
  _this setPos [4251.7363, 8924.5713, -3.0517578e-005];
};

_vehicle_1597 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4257.9272, 8929.9121, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1597 = _this;
  _this setPos [4257.9272, 8929.9121, 0];
};

_vehicle_1599 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4258.0669, 8931.4473, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1599 = _this;
  _this setPos [4258.0669, 8931.4473, 0];
};

_vehicle_1601 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4249.564, 8936.7129, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1601 = _this;
  _this setPos [4249.564, 8936.7129, 6.1035156e-005];
};

_vehicle_1603 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4249.4902, 8937.1045, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1603 = _this;
  _this setPos [4249.4902, 8937.1045, 3.0517578e-005];
};

_vehicle_1605 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4249.8604, 8936.5791, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1605 = _this;
  _this setPos [4249.8604, 8936.5791, 3.0517578e-005];
};

_vehicle_1608 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_heracleum", [4258.0674, 8935.9551, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1608 = _this;
  _this setDir -94.320442;
  _this setPos [4258.0674, 8935.9551, 3.0517578e-005];
};

_vehicle_1610 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_heracleum", [4252.7173, 8924.0791, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1610 = _this;
  _this setDir -184.4516;
  _this setPos [4252.7173, 8924.0791, 3.0517578e-005];
};

_vehicle_1612 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_heracleum", [4249.0483, 8937.6494], [], 0, "CAN_COLLIDE"];
  _vehicle_1612 = _this;
  _this setDir -79.397743;
  _this setPos [4249.0483, 8937.6494];
};

_vehicle_1615 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [4257.2764, 8932.0225, 0.17263724], [], 0, "CAN_COLLIDE"];
  _vehicle_1615 = _this;
  _this setDir -176.40498;
  _this setVehicleLock "LOCKED";
  _this setVehicleInit "this allowDammage false; this enableSimulation false";
  _this setPos [4257.2764, 8932.0225, 0.17263724];
};

_vehicle_1623 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [4251.0186, 8924.3789, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1623 = _this;
  _this setDir -318.59918;
  _this setPos [4251.0186, 8924.3789, 3.0517578e-005];
};

_vehicle_1626 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4193.7319, 8876.9785, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1626 = _this;
  _this setPos [4193.7319, 8876.9785, 6.1035156e-005];
};

_vehicle_1628 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4202.5122, 8871.0391, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1628 = _this;
  _this setPos [4202.5122, 8871.0391, 0];
};

_vehicle_1630 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_can", [4203.5063, 8870.1318, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1630 = _this;
  _this setDir -130.01459;
  _this setPos [4203.5063, 8870.1318, 3.0517578e-005];
};

_vehicle_1631 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4255.0068, 8923.8848, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1631 = _this;
  _this setPos [4255.0068, 8923.8848, 3.0517578e-005];
};

_vehicle_1633 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4258.2876, 8924.3076, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1633 = _this;
  _this setPos [4258.2876, 8924.3076, 0];
};

_vehicle_1636 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_can", [4256.6279, 8925.0898, 0.17532748], [], 0, "CAN_COLLIDE"];
  _vehicle_1636 = _this;
  _this setDir -130.01459;
  _this setPos [4256.6279, 8925.0898, 0.17532748];
};

_vehicle_1641 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4182.9902, 8898.1035, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1641 = _this;
  _this setPos [4182.9902, 8898.1035, 0];
};

_vehicle_1643 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4180.1387, 8898.959, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1643 = _this;
  _this setPos [4180.1387, 8898.959, 0];
};

_vehicle_1645 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4199.1543, 8962.8398, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1645 = _this;
  _this setPos [4199.1543, 8962.8398, 0];
};

_vehicle_1647 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4212.4966, 8955.8047, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1647 = _this;
  _this setPos [4212.4966, 8955.8047, 6.1035156e-005];
};

_vehicle_1649 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4230.3271, 8947.8564, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1649 = _this;
  _this setPos [4230.3271, 8947.8564, 3.0517578e-005];
};

_vehicle_1651 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4209.6592, 8932.3984, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1651 = _this;
  _this setPos [4209.6592, 8932.3984, 3.0517578e-005];
};

_vehicle_1653 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4145.7627, 8867.5371, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1653 = _this;
  _this setPos [4145.7627, 8867.5371, 0];
};

_vehicle_1657 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [4137.4448, 8870.2031, 0.15472184], [], 0, "CAN_COLLIDE"];
  _vehicle_1657 = _this;
  _this setDir -258.76114;
  _this setVehicleLock "LOCKED";
  _this setVehicleInit "this allowDammage false; this enableSimulation false";
  _this setPos [4137.4448, 8870.2031, 0.15472184];
};

_vehicle_1658 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [4134.918, 8870.6514, 0.15511857], [], 0, "CAN_COLLIDE"];
  _vehicle_1658 = _this;
  _this setDir -262.3309;
  _this setVehicleLock "LOCKED";
  _this setVehicleInit "this allowDammage false; this enableSimulation false";
  _this setPos [4134.918, 8870.6514, 0.15511857];
};

_vehicle_1665 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_WaterStation", [4142.1255, 8876.5537, -3.574584], [], 0, "CAN_COLLIDE"];
  _vehicle_1665 = _this;
  _this setDir 279.49338;
  _this setPos [4142.1255, 8876.5537, -3.574584];
};

_vehicle_1671 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pletivo_wired_branaP", [4138.2246, 8876.4736, 0.24082378], [], 0, "CAN_COLLIDE"];
  _vehicle_1671 = _this;
  _this setDir -34.01873;
  _this setPos [4138.2246, 8876.4736, 0.24082378];
};

_vehicle_1682 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pletivo_wired", [4143.2012, 8874.709, 0.27045804], [], 0, "CAN_COLLIDE"];
  _vehicle_1682 = _this;
  _this setDir 9.4167566;
  _this setPos [4143.2012, 8874.709, 0.27045804];
};

_vehicle_1685 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [4131.3682, 8875.4717, 0.15662727], [], 0, "CAN_COLLIDE"];
  _vehicle_1685 = _this;
  _this setDir -348.18649;
  _this setVehicleLock "LOCKED";
  _this setVehicleInit "this allowDammage false; this enableSimulation false";
  _this setPos [4131.3682, 8875.4717, 0.15662727];
};

_vehicle_1688 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4253.6401, 8936.6318, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1688 = _this;
  _this setPos [4253.6401, 8936.6318, -9.1552734e-005];
};

_vehicle_1690 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4258.2397, 8932.4229, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1690 = _this;
  _this setPos [4258.2397, 8932.4229, -3.0517578e-005];
};

_vehicle_1692 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4135.1675, 8869.6396, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1692 = _this;
  _this setPos [4135.1675, 8869.6396, -3.0517578e-005];
};

_vehicle_1694 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [4130.4487, 8875.4727, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1694 = _this;
  _this setPos [4130.4487, 8875.4727, -3.0517578e-005];
};

_vehicle_1696 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [4147.2466, 8865.8086], [], 0, "CAN_COLLIDE"];
  _vehicle_1696 = _this;
  _this setDir 6.3517933;
  _this setPos [4147.2466, 8865.8086];
};

_vehicle_1701 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [4188.1782, 8887.4434, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1701 = _this;
  _this setDir 104.61735;
  _this setPos [4188.1782, 8887.4434, 3.0517578e-005];
};

_vehicle_1704 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [4200.2588, 8884.5137, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1704 = _this;
  _this setDir -77.451248;
  _this setPos [4200.2588, 8884.5137, 3.0517578e-005];
};

_vehicle_1708 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4101.8101, 8854.8906, -1.7160164], [], 0, "CAN_COLLIDE"];
  _vehicle_1708 = _this;
  _this setDir 278.50909;
  _this setPos [4101.8101, 8854.8906, -1.7160164];
};

_vehicle_1711 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4101.9038, 8848.8115, -1.773937], [], 0, "CAN_COLLIDE"];
  _vehicle_1711 = _this;
  _this setDir 189.28218;
  _this setPos [4101.9038, 8848.8115, -1.773937];
};

_vehicle_1714 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4108.8745, 8843.8428, -1.7897618], [], 0, "CAN_COLLIDE"];
  _vehicle_1714 = _this;
  _this setDir 278.84409;
  _this setPos [4108.8745, 8843.8428, -1.7897618];
};

_vehicle_1717 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4111.6958, 8843.4619, -1.7964381], [], 0, "CAN_COLLIDE"];
  _vehicle_1717 = _this;
  _this setDir 99.366821;
  _this setPos [4111.6958, 8843.4619, -1.7964381];
};

_vehicle_1725 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice_roh", [4104.7339, 8839.9531, -0.060661189], [], 0, "CAN_COLLIDE"];
  _vehicle_1725 = _this;
  _this setDir -170.7177;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4104.7339, 8839.9531, -0.060661189];
};

_vehicle_1726 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4107.1079, 8839.5576, -0.019511348], [], 0, "CAN_COLLIDE"];
  _vehicle_1726 = _this;
  _this setDir -260.97043;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4107.1079, 8839.5576, -0.019511348];
};

_vehicle_1728 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4107.1323, 8839.7051, -0.01948083], [], 0, "CAN_COLLIDE"];
  _vehicle_1728 = _this;
  _this setDir -440.37473;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4107.1323, 8839.7051, -0.01948083];
};

_vehicle_1738 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4109.126, 8839.376, -0.01948083], [], 0, "CAN_COLLIDE"];
  _vehicle_1738 = _this;
  _this setDir -440.37473;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4109.126, 8839.376, -0.01948083];
};

_vehicle_1739 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4109.1025, 8839.2334, -0.019541865], [], 0, "CAN_COLLIDE"];
  _vehicle_1739 = _this;
  _this setDir -260.97043;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4109.1025, 8839.2334, -0.019541865];
};

_vehicle_1742 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4111.1221, 8839.0449, -0.03571485], [], 0, "CAN_COLLIDE"];
  _vehicle_1742 = _this;
  _this setDir -440.99979;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4111.1221, 8839.0449, -0.03571485];
};

_vehicle_1743 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4111.1084, 8838.8926, -0.018803375], [], 0, "CAN_COLLIDE"];
  _vehicle_1743 = _this;
  _this setDir -260.97043;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4111.1084, 8838.8926, -0.018803375];
};

_vehicle_1747 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4112.1177, 8838.8848, -0.044558134], [], 0, "CAN_COLLIDE"];
  _vehicle_1747 = _this;
  _this setDir -441.16147;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4112.1177, 8838.8848, -0.044558134];
};

_vehicle_1750 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice_roh", [4114.4438, 8838.3594, -0.058411982], [], 0, "CAN_COLLIDE"];
  _vehicle_1750 = _this;
  _this setDir -260.94183;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4114.4438, 8838.3594, -0.058411982];
};

_vehicle_1753 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4114.8022, 8840.7412, -0.075457707], [], 0, "CAN_COLLIDE"];
  _vehicle_1753 = _this;
  _this setDir -711.7533;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4114.8022, 8840.7412, -0.075457707];
};

_vehicle_1756 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4114.6509, 8840.7637, -0.081170112], [], 0, "CAN_COLLIDE"];
  _vehicle_1756 = _this;
  _this setDir -892.20154;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4114.6509, 8840.7637, -0.081170112];
};

_vehicle_1760 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4105.2349, 8842.2861, -0.076191686], [], 0, "CAN_COLLIDE"];
  _vehicle_1760 = _this;
  _this setDir -711.7533;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4105.2349, 8842.2861, -0.076191686];
};

_vehicle_1761 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4105.0884, 8842.3105, -0.10122672], [], 0, "CAN_COLLIDE"];
  _vehicle_1761 = _this;
  _this setDir -891.81549;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4105.0884, 8842.3105, -0.10122672];
};

_vehicle_1768 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4115.082, 8842.7363, -0.08763323], [], 0, "CAN_COLLIDE"];
  _vehicle_1768 = _this;
  _this setDir -711.7533;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4115.082, 8842.7363, -0.08763323];
};

_vehicle_1771 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ohrada_popelnice", [4105.542, 8844.2705, -0.10668074], [], 0, "CAN_COLLIDE"];
  _vehicle_1771 = _this;
  _this setDir -710.74225;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4105.542, 8844.2705, -0.10668074];
};

_vehicle_1774 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Kontejner", [4106.8223, 8840.4668, 0.077304475], [], 0, "CAN_COLLIDE"];
  _vehicle_1774 = _this;
  _this setDir -74.041718;
  _this setPos [4106.8223, 8840.4668, 0.077304475];
};

_vehicle_1776 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Kontejner", [4109.0747, 8840.2959, 0.07789278], [], 0, "CAN_COLLIDE"];
  _vehicle_1776 = _this;
  _this setDir -89.846245;
  _this setPos [4109.0747, 8840.2959, 0.07789278];
};

_vehicle_1778 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Kontejner", [4113.9824, 8840.667, 0.043104798], [], 0, "CAN_COLLIDE"];
  _vehicle_1778 = _this;
  _this setDir -157.40869;
  _this setPos [4113.9824, 8840.667, 0.043104798];
};

_vehicle_1783 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_misc", [4110.5747, 8840.4414, 1.8380582e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1783 = _this;
  _this setDir -178.35143;
  _this setPos [4110.5747, 8840.4414, 1.8380582e-005];
};

_vehicle_1790 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4108.2837, 8838.2871, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1790 = _this;
  _this setDir 23.80056;
  _this setPos [4108.2837, 8838.2871, -3.0517578e-005];
};

_vehicle_1793 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4105.2559, 8844.4326, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1793 = _this;
  _this setPos [4105.2559, 8844.4326, 3.0517578e-005];
};

_vehicle_1795 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4105.1694, 8843.7979, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1795 = _this;
  _this setPos [4105.1694, 8843.7979, 3.0517578e-005];
};

_vehicle_1799 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4114.8442, 8839.6289, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1799 = _this;
  _this setPos [4114.8442, 8839.6289, 0];
};

_vehicle_1801 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4114.9111, 8840.1406, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1801 = _this;
  _this setPos [4114.9111, 8840.1406, 0];
};

_vehicle_1803 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4111.4326, 8838.6338, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1803 = _this;
  _this setPos [4111.4326, 8838.6338, 0];
};

_vehicle_1812 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_heracleum", [4108.4658, 8838.3018], [], 0, "CAN_COLLIDE"];
  _vehicle_1812 = _this;
  _this setDir -59.431377;
  _this setPos [4108.4658, 8838.3018];
};

_vehicle_1818 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [4106.7856, 8838.6064, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1818 = _this;
  _this setDir -185.72113;
  _this setPos [4106.7856, 8838.6064, 6.1035156e-005];
};

_vehicle_1821 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4114.8633, 8838.9404, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1821 = _this;
  _this setPos [4114.8633, 8838.9404, 0];
};

_vehicle_1823 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4104.502, 8840.4307, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1823 = _this;
  _this setPos [4104.502, 8840.4307, 6.1035156e-005];
};

_vehicle_1825 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4116.0483, 8844.1074, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1825 = _this;
  _this setPos [4116.0483, 8844.1074, 3.0517578e-005];
};

_vehicle_1827 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4126.0913, 8851.0889, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1827 = _this;
  _this setPos [4126.0913, 8851.0889, 0];
};

_vehicle_1829 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4103.9668, 8843.7861, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1829 = _this;
  _this setPos [4103.9668, 8843.7861, 0];
};

_vehicle_1833 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4142.1958, 8833.749, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1833 = _this;
  _this setPos [4142.1958, 8833.749, 3.0517578e-005];
};

_vehicle_1835 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4239.1104, 8947.918, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1835 = _this;
  _this setPos [4239.1104, 8947.918, 6.1035156e-005];
};

_vehicle_1837 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4191.7891, 8957.959, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1837 = _this;
  _this setPos [4191.7891, 8957.959, 3.0517578e-005];
};

_vehicle_1839 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4174.6094, 8910.0273, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1839 = _this;
  _this setPos [4174.6094, 8910.0273, 6.1035156e-005];
};

_vehicle_1841 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4139.6812, 8955.6338, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1841 = _this;
  _this setPos [4139.6812, 8955.6338, 3.0517578e-005];
};

_vehicle_1843 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4103.6709, 8931.1406, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1843 = _this;
  _this setPos [4103.6709, 8931.1406, 6.1035156e-005];
};

_vehicle_1845 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4079.8191, 8858.3867, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1845 = _this;
  _this setPos [4079.8191, 8858.3867, -3.0517578e-005];
};

_vehicle_1847 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4215.5972, 8892.3457, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1847 = _this;
  _this setPos [4215.5972, 8892.3457, 0];
};

_vehicle_1851 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4166.0532, 8823.9199, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1851 = _this;
  _this setPos [4166.0532, 8823.9199, 9.1552734e-005];
};

_vehicle_1853 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4165.7578, 8820.7344, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1853 = _this;
  _this setPos [4165.7578, 8820.7344, 0];
};

_vehicle_1856 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Kontejner", [4165.8887, 8822.6387, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1856 = _this;
  _this setDir -7.3202796;
  _this setPos [4165.8887, 8822.6387, 3.0517578e-005];
};

_vehicle_1862 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Kontejner", [4165.4209, 8819.8604], [], 0, "CAN_COLLIDE"];
  _vehicle_1862 = _this;
  _this setDir 10.08017;
  _this setPos [4165.4209, 8819.8604];
};

_vehicle_1868 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_MalyKomin", [4244.3066, 8933.1113, 6.4377589], [], 0, "CAN_COLLIDE"];
  _vehicle_1868 = _this;
  _this setDir 4.6138349;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4244.3066, 8933.1113, 6.4377589];
};

_vehicle_1876 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_Stack_Big", [4120.2505, 8913.5117, -0.56062037], [], 0, "CAN_COLLIDE"];
  _vehicle_1876 = _this;
  _this setDir -80.264931;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4120.2505, 8913.5117, -0.56062037];
};

_vehicle_1881 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4245.3862, 8930.9141, 4.6130209], [], 0, "CAN_COLLIDE"];
  _vehicle_1881 = _this;
  _this setDir -85.41333;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4245.3862, 8930.9141, 4.6130209];
};

_vehicle_1886 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_sphere25cm_EP1", [4120.2603, 8913.4834, 56.989567], [], 0, "CAN_COLLIDE"];
  _vehicle_1886 = _this;
  _this setDir -146.46721;
  _this setVehicleInit "BIS_Effects_Burn = compile preprocessFile ""\ca\Data\ParticleEffects\SCRIPTS\destruction\burn.sqf""; nul = [this, 3, time, false, false] spawn BIS_Effects_Burn;";
  _this setPos [4120.2603, 8913.4834, 56.989567];
};

_vehicle_1894 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4177.6421, 8858.8857, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1894 = _this;
  _this setDir 56.302387;
  _this setPos [4177.6421, 8858.8857, -3.0517578e-005];
};

_vehicle_1897 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4175.6753, 8860.7178, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1897 = _this;
  _this setDir -324.46912;
  _this setPos [4175.6753, 8860.7178, 3.0517578e-005];
};

_vehicle_1900 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4178.5845, 8861.0908, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1900 = _this;
  _this setDir -239.71941;
  _this setPos [4178.5845, 8861.0908, 6.1035156e-005];
};

_vehicle_1902 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4172.9268, 8857.8086, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1902 = _this;
  _this setDir -308.09171;
  _this setPos [4172.9268, 8857.8086, 3.0517578e-005];
};

_vehicle_1906 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4169.4995, 8855.7217, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1906 = _this;
  _this setDir -293.77014;
  _this setPos [4169.4995, 8855.7217, 3.0517578e-005];
};

_vehicle_1907 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4165.6558, 8854.6533, 0.002532959], [], 0, "CAN_COLLIDE"];
  _vehicle_1907 = _this;
  _this setDir -276.73517;
  _this setPos [4165.6558, 8854.6533, 0.002532959];
};

_vehicle_1911 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4161.6147, 8854.5635, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1911 = _this;
  _this setDir -265.47147;
  _this setPos [4161.6147, 8854.5635, 3.0517578e-005];
};

_vehicle_1914 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4157.666, 8854.9238, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1914 = _this;
  _this setDir -263.745;
  _this setPos [4157.666, 8854.9238, 0.00012207031];
};

_vehicle_1917 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4155.1743, 8853.3857, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1917 = _this;
  _this setDir -350.53738;
  _this setPos [4155.1743, 8853.3857, 3.0517578e-005];
};

_vehicle_1926 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_a_stationhouse", [3889.9395, 8782.5664, 0.15590769], [], 0, "CAN_COLLIDE"];
  _vehicle_1926 = _this;
  _this setDir -62.257149;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3889.9395, 8782.5664, 0.15590769];
};

_vehicle_1928 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [4154.5645, 8849.3848], [], 0, "CAN_COLLIDE"];
  _vehicle_1928 = _this;
  _this setDir -352.10004;
  _this setPos [4154.5645, 8849.3848];
};

_vehicle_1931 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patnik", [4154.2319, 8847.1533, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1931 = _this;
  _this setDir 7.3187785;
  _this setPos [4154.2319, 8847.1533, -3.0517578e-005];
};

_vehicle_1934 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4155.6631, 8854.9707, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1934 = _this;
  _this setPos [4155.6631, 8854.9707, 0.00012207031];
};

_vehicle_1936 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4155.8438, 8855.1279, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1936 = _this;
  _this setPos [4155.8438, 8855.1279, 0.00012207031];
};

_vehicle_1938 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4154.9473, 8851.3672, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1938 = _this;
  _this setPos [4154.9473, 8851.3672, 9.1552734e-005];
};

_vehicle_1940 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4171.4307, 8856.5059, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1940 = _this;
  _this setPos [4171.4307, 8856.5059, -3.0517578e-005];
};

_vehicle_1942 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [4171.7827, 8856.6973, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1942 = _this;
  _this setPos [4171.7827, 8856.6973, 0];
};

_vehicle_1945 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4174.1392, 8858.5732, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1945 = _this;
  _this setPos [4174.1392, 8858.5732, -3.0517578e-005];
};

_vehicle_1947 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4170.8994, 8856.2344, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1947 = _this;
  _this setPos [4170.8994, 8856.2344, 0];
};

_vehicle_1950 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [4155.3516, 8852.6602], [], 0, "CAN_COLLIDE"];
  _vehicle_1950 = _this;
  _this setDir -385.05655;
  _this setPos [4155.3516, 8852.6602];
};

_vehicle_1953 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [4166.6436, 8854.1455, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1953 = _this;
  _this setDir -295.64832;
  _this setPos [4166.6436, 8854.1455, 3.0517578e-005];
};

_vehicle_1959 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [3913.3228, 8819.751, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1959 = _this;
  _this setDir -165.69;
  _this setPos [3913.3228, 8819.751, -3.0517578e-005];
};

_vehicle_1960 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [3896.1455, 8768.625], [], 0, "CAN_COLLIDE"];
  _vehicle_1960 = _this;
  _this setDir 27.183249;
  _this setPos [3896.1455, 8768.625];
};

_vehicle_1962 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [3911.4424, 8809.7461, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1962 = _this;
  _this setDir 180.47849;
  _this setPos [3911.4424, 8809.7461, 0.00012207031];
};

_vehicle_1963 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [3911.4429, 8809.5576, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1963 = _this;
  _this setPos [3911.4429, 8809.5576, 3.0517578e-005];
};

_vehicle_1965 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [3899.3418, 8776.4619, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1965 = _this;
  _this setDir -61.395069;
  _this setPos [3899.3418, 8776.4619, 6.1035156e-005];
};

_vehicle_1968 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [3902.3, 8782.0449, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1968 = _this;
  _this setDir -61.395069;
  _this setPos [3902.3, 8782.0449, 6.1035156e-005];
};

_vehicle_1970 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [3905.207, 8787.5791, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1970 = _this;
  _this setDir -61.395069;
  _this setPos [3905.207, 8787.5791, 3.0517578e-005];
};

_vehicle_1973 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3898.884, 8787.3438, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1973 = _this;
  _this setPos [3898.884, 8787.3438, 0];
};

_vehicle_1975 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3895.7517, 8781.8906, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1975 = _this;
  _this setPos [3895.7517, 8781.8906, 0];
};

_vehicle_1977 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3896.7488, 8781.4951, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1977 = _this;
  _this setPos [3896.7488, 8781.4951, 3.0517578e-005];
};

_vehicle_1980 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [3899.4832, 8787.1279, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1980 = _this;
  _this setPos [3899.4832, 8787.1279, 3.0517578e-005];
};

_vehicle_1982 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [3899.8801, 8787.0166, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1982 = _this;
  _this setPos [3899.8801, 8787.0166, 0];
};

_vehicle_1984 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [3901.1724, 8792.8486, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1984 = _this;
  _this setPos [3901.1724, 8792.8486, 0];
};

_vehicle_1986 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [3901.3044, 8793.1738, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1986 = _this;
  _this setPos [3901.3044, 8793.1738, 3.0517578e-005];
};

_vehicle_1992 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [3888.5015, 8800.4883, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1992 = _this;
  _this setDir -513.35242;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3888.5015, 8800.4883, 9.1552734e-005];
};

_vehicle_1993 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [3881.0273, 8802.1396, -0.046539307], [], 0, "CAN_COLLIDE"];
  _vehicle_1993 = _this;
  _this setDir -602.10132;
  _this setPos [3881.0273, 8802.1396, -0.046539307];
};

_vehicle_1994 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [3885.478, 8802, -0.0092714131], [], 0, "CAN_COLLIDE"];
  _vehicle_1994 = _this;
  _this setDir -513.28479;
  _this setPos [3885.478, 8802, -0.0092714131];
};

_vehicle_2002 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [3878.2622, 8796.8613, -0.038900629], [], 0, "CAN_COLLIDE"];
  _vehicle_2002 = _this;
  _this setDir -602.10132;
  _this setPos [3878.2622, 8796.8613, -0.038900629];
};

_vehicle_2005 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [3875.4978, 8791.5889, -0.038961656], [], 0, "CAN_COLLIDE"];
  _vehicle_2005 = _this;
  _this setDir -602.33002;
  _this setPos [3875.4978, 8791.5889, -0.038961656];
};

_vehicle_2009 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [3872.7063, 8786.3066, -0.038961656], [], 0, "CAN_COLLIDE"];
  _vehicle_2009 = _this;
  _this setDir -602.10132;
  _this setPos [3872.7063, 8786.3066, -0.038961656];
};

_vehicle_2010 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [3869.9417, 8781.0283, -0.039135471], [], 0, "CAN_COLLIDE"];
  _vehicle_2010 = _this;
  _this setDir -602.10132;
  _this setPos [3869.9417, 8781.0283, -0.039135471];
};

_vehicle_2014 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [3867.2678, 8775.8613, -0.019132946], [], 0, "CAN_COLLIDE"];
  _vehicle_2014 = _this;
  _this setDir -602.33002;
  _this setPos [3867.2678, 8775.8613, -0.019132946];
};

_vehicle_2017 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [3864.5337, 8770.6025], [], 0, "CAN_COLLIDE"];
  _vehicle_2017 = _this;
  _this setDir -603.0965;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3864.5337, 8770.6025];
};

_vehicle_2020 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [3866.1643, 8769.6357, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2020 = _this;
  _this setDir -512.19592;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3866.1643, 8769.6357, 3.0517578e-005];
};

_vehicle_2024 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2s", [3865.0071, 8785.751], [], 0, "CAN_COLLIDE"];
  _vehicle_2024 = _this;
  _this setPos [3865.0071, 8785.751];
};

_vehicle_2032 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_TankSmall2", [3879.5059, 8795.9727, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2032 = _this;
  _this setDir -242.53976;
  _this setPos [3879.5059, 8795.9727, -6.1035156e-005];
};

_vehicle_2038 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [3846.9021, 8809.5615, 0.23536155], [], 0, "CAN_COLLIDE"];
  _vehicle_2038 = _this;
  _this setDir 208.32445;
  _this setPos [3846.9021, 8809.5615, 0.23536155];
};

_vehicle_2040 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV2_02_Interier", [3921.3103, 8837.7832, 0.22783023], [], 0, "CAN_COLLIDE"];
  _vehicle_2040 = _this;
  _this setDir -162.07596;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3921.3103, 8837.7832, 0.22783023];
};

_vehicle_2041 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV2_04_interier", [3686.1223, 8908.4873, -0.23383221], [], 0, "CAN_COLLIDE"];
  _vehicle_2041 = _this;
  _this setDir -151.74358;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3686.1223, 8908.4873, -0.23383221];
};

_vehicle_2043 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV2_04_interier", [3838.5115, 8795.5391, -0.1753885], [], 0, "CAN_COLLIDE"];
  _vehicle_2043 = _this;
  _this setDir -331.18127;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3838.5115, 8795.5391, -0.1753885];
};

_vehicle_2047 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3820.46, 8781.0947, -0.20330441], [], 0, "CAN_COLLIDE"];
  _vehicle_2047 = _this;
  _this setDir 29.088102;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3820.46, 8781.0947, -0.20330441];
};

_vehicle_2048 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3821.7517, 8788.5713, -0.36404628], [], 0, "CAN_COLLIDE"];
  _vehicle_2048 = _this;
  _this setDir 116.93883;
  _this setPos [3821.7517, 8788.5713, -0.36404628];
};

_vehicle_2052 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3823.9187, 8792.8594, -0.35745394], [], 0, "CAN_COLLIDE"];
  _vehicle_2052 = _this;
  _this setDir 115.77521;
  _this setPos [3823.9187, 8792.8594, -0.35745394];
};

_vehicle_2055 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3819.5837, 8784.3018, -0.24847293], [], 0, "CAN_COLLIDE"];
  _vehicle_2055 = _this;
  _this setDir 116.93883;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3819.5837, 8784.3018, -0.24847293];
};

_vehicle_2058 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3824.7236, 8778.5527, -0.25499821], [], 0, "CAN_COLLIDE"];
  _vehicle_2058 = _this;
  _this setDir -150.70581;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3824.7236, 8778.5527, -0.25499821];
};

_vehicle_2061 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3828.9038, 8776.166, -0.21594499], [], 0, "CAN_COLLIDE"];
  _vehicle_2061 = _this;
  _this setDir 210.56551;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3828.9038, 8776.166, -0.21594499];
};

_vehicle_2064 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3838.2705, 8770.7646, -0.24344929], [], 0, "CAN_COLLIDE"];
  _vehicle_2064 = _this;
  _this setDir 29.727425;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3838.2705, 8770.7646, -0.24344929];
};

_vehicle_2067 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3826.0981, 8797.1563, -0.42792886], [], 0, "CAN_COLLIDE"];
  _vehicle_2067 = _this;
  _this setDir 118.5323;
  _this setPos [3826.0981, 8797.1563, -0.42792886];
};

_vehicle_2070 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3828.446, 8801.502, -0.4486002], [], 0, "CAN_COLLIDE"];
  _vehicle_2070 = _this;
  _this setDir 118.5323;
  _this setPos [3828.446, 8801.502, -0.4486002];
};

_vehicle_2073 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3832.4407, 8806.668, -0.44691309], [], 0, "CAN_COLLIDE"];
  _vehicle_2073 = _this;
  _this setDir -45.263535;
  _this setPos [3832.4407, 8806.668, -0.44691309];
};

_vehicle_2077 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3836.3875, 8809.4424, -0.42057207], [], 0, "CAN_COLLIDE"];
  _vehicle_2077 = _this;
  _this setDir -24.330688;
  _this setPos [3836.3875, 8809.4424, -0.42057207];
};

_vehicle_2080 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3840.9551, 8810.7861, -0.40696597], [], 0, "CAN_COLLIDE"];
  _vehicle_2080 = _this;
  _this setDir -8.8496904;
  _this setPos [3840.9551, 8810.7861, -0.40696597];
};

_vehicle_2083 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3845.8179, 8810.9844, -0.37419149], [], 0, "CAN_COLLIDE"];
  _vehicle_2083 = _this;
  _this setDir 3.7169347;
  _this setPos [3845.8179, 8810.9844, -0.37419149];
};

_vehicle_2086 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3850.2207, 8809.5879, -0.3713192], [], 0, "CAN_COLLIDE"];
  _vehicle_2086 = _this;
  _this setDir 30.406647;
  _this setPos [3850.2207, 8809.5879, -0.3713192];
};

_vehicle_2089 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3854.2917, 8807.0264, -0.3506549], [], 0, "CAN_COLLIDE"];
  _vehicle_2089 = _this;
  _this setDir 33.873432;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3854.2917, 8807.0264, -0.3506549];
};

_vehicle_2092 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3841.6448, 8771.4355, -0.49836951], [], 0, "CAN_COLLIDE"];
  _vehicle_2092 = _this;
  _this setDir -57.230347;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3841.6448, 8771.4355, -0.49836951];
};

_vehicle_2095 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3844.1277, 8775.4932, -0.46665433], [], 0, "CAN_COLLIDE"];
  _vehicle_2095 = _this;
  _this setDir -60.360191;
  _this setPos [3844.1277, 8775.4932, -0.46665433];
};

_vehicle_2098 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3846.5918, 8779.5684, -0.37064171], [], 0, "CAN_COLLIDE"];
  _vehicle_2098 = _this;
  _this setDir -57.230347;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3846.5918, 8779.5684, -0.37064171];
};

_vehicle_2101 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3849.085, 8783.6953, -0.37016624], [], 0, "CAN_COLLIDE"];
  _vehicle_2101 = _this;
  _this setDir -60.360191;
  _this setPos [3849.085, 8783.6953, -0.37016624];
};

_vehicle_2104 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3851.5425, 8787.9658, -0.33117405], [], 0, "CAN_COLLIDE"];
  _vehicle_2104 = _this;
  _this setDir -60.360191;
  _this setPos [3851.5425, 8787.9658, -0.33117405];
};

_vehicle_2107 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3853.9751, 8792.2178, -0.33503348], [], 0, "CAN_COLLIDE"];
  _vehicle_2107 = _this;
  _this setDir -60.360191;
  _this setPos [3853.9751, 8792.2178, -0.33503348];
};

_vehicle_2110 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3856.4109, 8796.4893, -0.32029042], [], 0, "CAN_COLLIDE"];
  _vehicle_2110 = _this;
  _this setDir -60.360191;
  _this setPos [3856.4109, 8796.4893, -0.32029042];
};

_vehicle_2113 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3858.7947, 8800.6699, -0.31857544], [], 0, "CAN_COLLIDE"];
  _vehicle_2113 = _this;
  _this setDir -60.360191;
  _this setPos [3858.7947, 8800.6699, -0.31857544];
};

_vehicle_2116 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3858.2961, 8804.3008, -0.31166273], [], 0, "CAN_COLLIDE"];
  _vehicle_2116 = _this;
  _this setDir -145.69821;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3858.2961, 8804.3008, -0.31166273];
};

_vehicle_2121 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [3790.4414, 8778.0225, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_2121 = _this;
  _this setDir 109.35847;
  _this setPos [3790.4414, 8778.0225, 0.00012207031];
};

_vehicle_2122 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [3796.2771, 8775.8477, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2122 = _this;
  _this setDir 109.72598;
  _this setPos [3796.2771, 8775.8477, -3.0517578e-005];
};

_vehicle_2123 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [3829.5896, 8769.2734, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2123 = _this;
  _this setDir -130.61092;
  _this setPos [3829.5896, 8769.2734, -9.1552734e-005];
};

_vehicle_2125 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [3836.5449, 8777.1982, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2125 = _this;
  _this setDir -141.01492;
  _this setPos [3836.5449, 8777.1982, 9.1552734e-005];
};

_vehicle_2127 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_25", [3832.6201, 8772.3672], [], 0, "CAN_COLLIDE"];
  _vehicle_2127 = _this;
  _this setDir -141.02623;
  _this setPos [3832.6201, 8772.3672];
};

_vehicle_2133 = objNull;
if (true) then
{
  _this = createVehicle ["SKODAWreck", [3835.3164, 8784.6973, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2133 = _this;
  _this setDir 133.89174;
  _this setPos [3835.3164, 8784.6973, 3.0517578e-005];
};

_vehicle_2139 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3835.8931, 8785.4502, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2139 = _this;
  _this setPos [3835.8931, 8785.4502, 6.1035156e-005];
};

_vehicle_2142 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [3835.9043, 8783.0537, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2142 = _this;
  _this setPos [3835.9043, 8783.0537, 0];
};

_vehicle_2144 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [3834.0513, 8784.8945, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2144 = _this;
  _this setPos [3834.0513, 8784.8945, 0];
};

_vehicle_2146 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_weed3", [3833.8599, 8785.2178, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2146 = _this;
  _this setPos [3833.8599, 8785.2178, 0];
};

_vehicle_2163 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV2_04_interier", [3924.0708, 8729.7139, -0.2102717], [], 0, "CAN_COLLIDE"];
  _vehicle_2163 = _this;
  _this setDir -220.01012;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3924.0708, 8729.7139, -0.2102717];
};

_vehicle_2165 = objNull;
if (true) then
{
  _this = createVehicle ["Land_sara_stodola", [3949.7302, 8725.1963, -0.29700494], [], 0, "CAN_COLLIDE"];
  _vehicle_2165 = _this;
  _this setDir -220.21802;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3949.7302, 8725.1963, -0.29700494];
};

_vehicle_2169 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3908.6902, 8753.3057, -0.36857623], [], 0, "CAN_COLLIDE"];
  _vehicle_2169 = _this;
  _this setDir -37.53989;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3908.6902, 8753.3057, -0.36857623];
};

_vehicle_2170 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3899.1492, 8746.0605, -0.33457252], [], 0, "CAN_COLLIDE"];
  _vehicle_2170 = _this;
  _this setDir -218.03539;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3899.1492, 8746.0605, -0.33457252];
};

_vehicle_2174 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3895.3779, 8743.1338, -0.29234535], [], 0, "CAN_COLLIDE"];
  _vehicle_2174 = _this;
  _this setDir -218.03539;
  _this setPos [3895.3779, 8743.1338, -0.29234535];
};

_vehicle_2177 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3904.7339, 8750.332, -0.46782917], [], 0, "CAN_COLLIDE"];
  _vehicle_2177 = _this;
  _this setDir -397.12543;
  _this setPos [3904.7339, 8750.332, -0.46782917];
};

_vehicle_2180 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3924.01, 8756.1836, -0.41610163], [], 0, "CAN_COLLIDE"];
  _vehicle_2180 = _this;
  _this setDir -316.65363;
  _this setPos [3924.01, 8756.1836, -0.41610163];
};

_vehicle_2183 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3910.3418, 8754.5059, -0.33121276], [], 0, "CAN_COLLIDE"];
  _vehicle_2183 = _this;
  _this setDir -217.64941;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3910.3418, 8754.5059, -0.33121276];
};

_vehicle_2186 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3920.5032, 8759.6465, -0.40895081], [], 0, "CAN_COLLIDE"];
  _vehicle_2186 = _this;
  _this setDir -312.55255;
  _this setPos [3920.5032, 8759.6465, -0.40895081];
};

_vehicle_2190 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_R", [3915.3645, 8758.833, -0.65544128], [], 0, "CAN_COLLIDE"];
  _vehicle_2190 = _this;
  _this setDir -221.58461;
  _this setPos [3915.3645, 8758.833, -0.65544128];
};

_vehicle_2191 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_L", [3915.4072, 8758.8496, -0.65541077], [], 0, "CAN_COLLIDE"];
  _vehicle_2191 = _this;
  _this setDir -215.327;
  _this setPos [3915.4072, 8758.8496, -0.65541077];
};

_vehicle_2195 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3935.7585, 8747.0107, -0.43414122], [], 0, "CAN_COLLIDE"];
  _vehicle_2195 = _this;
  _this setDir -326.41959;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3935.7585, 8747.0107, -0.43414122];
};

_vehicle_2198 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3939.1689, 8744.459, -0.44291744], [], 0, "CAN_COLLIDE"];
  _vehicle_2198 = _this;
  _this setDir -506.17493;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3939.1689, 8744.459, -0.44291744];
};

_vehicle_2201 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3927.7461, 8752.9023, -0.42178106], [], 0, "CAN_COLLIDE"];
  _vehicle_2201 = _this;
  _this setDir -681.01483;
  _this setPos [3927.7461, 8752.9023, -0.42178106];
};

_vehicle_2204 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3931.6831, 8749.8682, -0.43646765], [], 0, "CAN_COLLIDE"];
  _vehicle_2204 = _this;
  _this setDir -683.93311;
  _this setPos [3931.6831, 8749.8682, -0.43646765];
};

_vehicle_2208 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3943.3364, 8741.7822, -0.4591682], [], 0, "CAN_COLLIDE"];
  _vehicle_2208 = _this;
  _this setDir -508.43051;
  _this setPos [3943.3364, 8741.7822, -0.4591682];
};

_vehicle_2209 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3947.571, 8739.2451, -0.47108746], [], 0, "CAN_COLLIDE"];
  _vehicle_2209 = _this;
  _this setDir -510.04138;
  _this setPos [3947.571, 8739.2451, -0.47108746];
};

_vehicle_2212 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3951.8533, 8736.6963, -0.46479696], [], 0, "CAN_COLLIDE"];
  _vehicle_2212 = _this;
  _this setDir -508.43051;
  _this setPos [3951.8533, 8736.6963, -0.46479696];
};

_vehicle_2213 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3955.7175, 8733.6357, -0.44209981], [], 0, "CAN_COLLIDE"];
  _vehicle_2213 = _this;
  _this setDir -494.2327;
  _this setPos [3955.7175, 8733.6357, -0.44209981];
};

_vehicle_2217 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3950.6699, 8715.957, -0.35382694], [], 0, "CAN_COLLIDE"];
  _vehicle_2217 = _this;
  _this setDir -583.04156;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3950.6699, 8715.957, -0.35382694];
};

_vehicle_2220 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3948.947, 8714.4756, -0.30761114], [], 0, "CAN_COLLIDE"];
  _vehicle_2220 = _this;
  _this setDir -760.90417;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3948.947, 8714.4756, -0.30761114];
};

_vehicle_2223 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3894.6729, 8739.6924, -0.36837637], [], 0, "CAN_COLLIDE"];
  _vehicle_2223 = _this;
  _this setDir -308.16217;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3894.6729, 8739.6924, -0.36837637];
};

_vehicle_2226 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3897.6667, 8735.9023, -0.22917239], [], 0, "CAN_COLLIDE"];
  _vehicle_2226 = _this;
  _this setDir -308.16217;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3897.6667, 8735.9023, -0.22917239];
};

_vehicle_2230 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Village", [3901.8293, 8748.333, 0.094621167], [], 0, "CAN_COLLIDE"];
  _vehicle_2230 = _this;
  _this setDir -217.31267;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3901.8293, 8748.333, 0.094621167];
};

_vehicle_2232 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3900.7324, 8732.0684, -0.21690756], [], 0, "CAN_COLLIDE"];
  _vehicle_2232 = _this;
  _this setDir -308.16217;
  _this setPos [3900.7324, 8732.0684, -0.21690756];
};

_vehicle_2235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3903.79, 8728.1406, -0.29244861], [], 0, "CAN_COLLIDE"];
  _vehicle_2235 = _this;
  _this setDir -308.16217;
  _this setPos [3903.79, 8728.1406, -0.29244861];
};

_vehicle_2238 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3906.8467, 8724.2139, -0.35549459], [], 0, "CAN_COLLIDE"];
  _vehicle_2238 = _this;
  _this setDir -308.16217;
  _this setPos [3906.8467, 8724.2139, -0.35549459];
};

_vehicle_2241 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3945.1238, 8711.3975, -0.35181445], [], 0, "CAN_COLLIDE"];
  _vehicle_2241 = _this;
  _this setDir -397.12543;
  _this setPos [3945.1238, 8711.3975, -0.35181445];
};

_vehicle_2244 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3941.1755, 8708.4141, -0.37583047], [], 0, "CAN_COLLIDE"];
  _vehicle_2244 = _this;
  _this setDir -397.12543;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3941.1755, 8708.4141, -0.37583047];
};

_vehicle_2247 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3937.2798, 8705.4727, -0.42831582], [], 0, "CAN_COLLIDE"];
  _vehicle_2247 = _this;
  _this setDir -397.12543;
  _this setPos [3937.2798, 8705.4727, -0.42831582];
};

_vehicle_2250 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3933.345, 8702.4824, -0.4593972], [], 0, "CAN_COLLIDE"];
  _vehicle_2250 = _this;
  _this setDir -397.12543;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3933.345, 8702.4824, -0.4593972];
};

_vehicle_2253 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3929.4331, 8699.5264, -0.50971031], [], 0, "CAN_COLLIDE"];
  _vehicle_2253 = _this;
  _this setDir -397.12543;
  _this setPos [3929.4331, 8699.5264, -0.50971031];
};

_vehicle_2256 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3909.895, 8720.335, -0.28902122], [], 0, "CAN_COLLIDE"];
  _vehicle_2256 = _this;
  _this setDir -308.55331;
  _this setPos [3909.895, 8720.335, -0.28902122];
};

_vehicle_2259 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5_D", [3911.531, 8718.2744, -0.38813514], [], 0, "CAN_COLLIDE"];
  _vehicle_2259 = _this;
  _this setDir -489.58463;
  _this setPos [3911.531, 8718.2744, -0.38813514];
};

_vehicle_2262 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3914.7026, 8714.4111, -0.45451796], [], 0, "CAN_COLLIDE"];
  _vehicle_2262 = _this;
  _this setDir -489.17981;
  _this setPos [3914.7026, 8714.4111, -0.45451796];
};

_vehicle_2265 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3917.7937, 8710.5869, -0.50409555], [], 0, "CAN_COLLIDE"];
  _vehicle_2265 = _this;
  _this setDir -489.17981;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3917.7937, 8710.5869, -0.50409555];
};

_vehicle_2268 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar2_5", [3920.8894, 8706.7656, -0.46878791], [], 0, "CAN_COLLIDE"];
  _vehicle_2268 = _this;
  _this setDir -489.17981;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3920.8894, 8706.7656, -0.46878791];
};

_vehicle_2272 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_R", [3924.7371, 8701.4033, -0.66058671], [], 0, "CAN_COLLIDE"];
  _vehicle_2272 = _this;
  _this setDir -312.04849;
  _this setPos [3924.7371, 8701.4033, -0.66058671];
};

_vehicle_2273 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_L", [3924.7219, 8701.4473, -0.71796513], [], 0, "CAN_COLLIDE"];
  _vehicle_2273 = _this;
  _this setDir -305.79086;
  _this setPos [3924.7219, 8701.4473, -0.71796513];
};

_vehicle_2277 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [3905.6113, 8753.3525, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2277 = _this;
  _this setDir 187.92719;
  _this setPos [3905.6113, 8753.3525, -3.0517578e-005];
};

_vehicle_2280 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [3896.7126, 8746.5703, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2280 = _this;
  _this setDir -47.038261;
  _this setPos [3896.7126, 8746.5703, -3.0517578e-005];
};

_vehicle_2283 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [3951.3301, 8741.8867, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2283 = _this;
  _this setDir 279.42154;
  _this setPos [3951.3301, 8741.8867, -3.0517578e-005];
};

_vehicle_2285 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [3902.7437, 8727.1016, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2285 = _this;
  _this setDir 187.92719;
  _this setPos [3902.7437, 8727.1016, 3.0517578e-005];
};

_vehicle_2287 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [3913.3149, 8713.3301, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2287 = _this;
  _this setDir 70.799622;
  _this setPos [3913.3149, 8713.3301, -3.0517578e-005];
};

_vehicle_2291 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [3944.2217, 8843.3262, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_2291 = _this;
  _this setDir 278.05753;
  _this setPos [3944.2217, 8843.3262, -0.00015258789];
};

_vehicle_2293 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [3903.0146, 8803.9971, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2293 = _this;
  _this setDir 245.03975;
  _this setPos [3903.0146, 8803.9971, -3.0517578e-005];
};

_vehicle_2300 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [3900.4978, 8773.748], [], 0, "CAN_COLLIDE"];
  _vehicle_2300 = _this;
  _this setDir 134.06358;
  _this setPos [3900.4978, 8773.748];
};

_vehicle_2301 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [3904.9614, 8769.4297, 0.047607422], [], 0, "CAN_COLLIDE"];
  _vehicle_2301 = _this;
  _this setDir 135.6911;
  _this setPos [3904.9614, 8769.4297, 0.047607422];
};

_vehicle_2305 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [3942.969, 8736.1367, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2305 = _this;
  _this setDir -58.27327;
  _this setPos [3942.969, 8736.1367, 6.1035156e-005];
};

_vehicle_2307 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_15_75", [3937.793, 8739.4482, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2307 = _this;
  _this setDir -59.264099;
  _this setPos [3937.793, 8739.4482, -3.0517578e-005];
};

_vehicle_2309 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ural_wrecked", [3898.1941, 8762.2871, -0.038931143], [], 0, "CAN_COLLIDE"];
  _vehicle_2309 = _this;
  _this setDir 38.811718;
  _this setVehicleInit "this setvectorup [0.1,0,0]";
  _this setPos [3898.1941, 8762.2871, -0.038931143];
};

_vehicle_2311 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel4", [3900.5703, 8764.1875, -0.28499326], [], 0, "CAN_COLLIDE"];
  _vehicle_2311 = _this;
  _this setVehicleInit "this setvectorup [0.1,0.1,0]";
  _this setPos [3900.5703, 8764.1875, -0.28499326];
};

_vehicle_2312 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel5", [3901.6189, 8763.4482, -0.29392236], [], 0, "CAN_COLLIDE"];
  _vehicle_2312 = _this;
  _this setVehicleInit "this setvectorup [0.1,0.5,0]";
  _this setPos [3901.6189, 8763.4482, -0.29392236];
};

_vehicle_2313 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel1", [3900.9097, 8762.5762, -0.28103659], [], 0, "CAN_COLLIDE"];
  _vehicle_2313 = _this;
  _this setVehicleInit "this setvectorup [0.1,0,0]";
  _this setPos [3900.9097, 8762.5762, -0.28103659];
};

_vehicle_2315 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel4", [3901.0935, 8765.7539, -0.289121], [], 0, "CAN_COLLIDE"];
  _vehicle_2315 = _this;
  _this setVehicleInit "this setvectorup [0.1,0.2,0]";
  _this setPos [3901.0935, 8765.7539, -0.289121];
};

_vehicle_2318 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel5", [3903.021, 8764.1357, -0.27997085], [], 0, "CAN_COLLIDE"];
  _vehicle_2318 = _this;
  _this setVehicleInit "this setvectorup [0.1,0.2,0]";
  _this setPos [3903.021, 8764.1357, -0.27997085];
};

_vehicle_2322 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [3958.55, 8737.75], [], 0, "CAN_COLLIDE"];
  _vehicle_2322 = _this;
  _this setDir -199.90735;
  _this setPos [3958.55, 8737.75];
};

_vehicle_2323 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [3951.4102, 8735.9453, 0.043151855], [], 0, "CAN_COLLIDE"];
  _vehicle_2323 = _this;
  _this setDir -318.59918;
  _this setPos [3951.4102, 8735.9453, 0.043151855];
};

_vehicle_2327 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [3955.7593, 8718.6943], [], 0, "CAN_COLLIDE"];
  _vehicle_2327 = _this;
  _this setDir -199.90735;
  _this setPos [3955.7593, 8718.6943];
};

_vehicle_2333 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [3893.5981, 8743.8037], [], 0, "CAN_COLLIDE"];
  _vehicle_2333 = _this;
  _this setDir -318.59918;
  _this setPos [3893.5981, 8743.8037];
};

_vehicle_2335 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [3921.4648, 8708.0107], [], 0, "CAN_COLLIDE"];
  _vehicle_2335 = _this;
  _this setDir -279.69748;
  _this setPos [3921.4648, 8708.0107];
};

_vehicle_2378 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1I4", [3877.1541, 8734.8203, 0.37258857], [], 0, "CAN_COLLIDE"];
  _vehicle_2378 = _this;
  _this setDir -52.776527;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3877.1541, 8734.8203, 0.37258857];
};

_vehicle_2388 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [3866.2981, 8726.9131, -0.037116099], [], 0, "CAN_COLLIDE"];
  _vehicle_2388 = _this;
  _this setDir 54.996716;
  _this setPos [3866.2981, 8726.9131, -0.037116099];
};

_vehicle_2390 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [3869.6868, 8723.3379, -0.15613347], [], 0, "CAN_COLLIDE"];
  _vehicle_2390 = _this;
  _this setDir 38.047119;
  _this setPos [3869.6868, 8723.3379, -0.15613347];
};

_vehicle_2393 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [3873.6016, 8720.2471, -0.16321301], [], 0, "CAN_COLLIDE"];
  _vehicle_2393 = _this;
  _this setDir 38.047119;
  _this setPos [3873.6016, 8720.2471, -0.16321301];
};

_vehicle_2396 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [3863.0117, 8730.5977, 0.036858164], [], 0, "CAN_COLLIDE"];
  _vehicle_2396 = _this;
  _this setDir 41.583969;
  _this setPos [3863.0117, 8730.5977, 0.036858164];
};

_vehicle_2399 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [3859.1555, 8733.7568, 0.052907057], [], 0, "CAN_COLLIDE"];
  _vehicle_2399 = _this;
  _this setDir 37.068611;
  _this setPos [3859.1555, 8733.7568, 0.052907057];
};

_vehicle_2404 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [3887.5964, 8742.667, -0.13920045], [], 0, "CAN_COLLIDE"];
  _vehicle_2404 = _this;
  _this setDir -139.38113;
  _this setPos [3887.5964, 8742.667, -0.13920045];
};

_vehicle_2407 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [3883.8284, 8745.9043, -0.060441829], [], 0, "CAN_COLLIDE"];
  _vehicle_2407 = _this;
  _this setDir -139.38113;
  _this setPos [3883.8284, 8745.9043, -0.060441829];
};

_vehicle_2410 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [3878.3071, 8749.7646, -0.037338257], [], 0, "CAN_COLLIDE"];
  _vehicle_2410 = _this;
  _this setDir -151.72417;
  _this setPos [3878.3071, 8749.7646, -0.037338257];
};

_vehicle_2413 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [3867.4502, 8755.3877, -0.059568483], [], 0, "CAN_COLLIDE"];
  _vehicle_2413 = _this;
  _this setDir -152.87268;
  _this setPos [3867.4502, 8755.3877, -0.059568483];
};

_vehicle_2418 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4", [3864.3901, 8754.7979, -0.17174758], [], 0, "CAN_COLLIDE"];
  _vehicle_2418 = _this;
  _this setDir 115.97698;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3864.3901, 8754.7979, -0.17174758];
};

_vehicle_2420 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4", [3862.6963, 8751.3789, -0.16063046], [], 0, "CAN_COLLIDE"];
  _vehicle_2420 = _this;
  _this setDir 115.97698;
  _this setPos [3862.6963, 8751.3789, -0.16063046];
};

_vehicle_2423 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4", [3860.9119, 8747.7627, -0.15137428], [], 0, "CAN_COLLIDE"];
  _vehicle_2423 = _this;
  _this setDir 115.97698;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3860.9119, 8747.7627, -0.15137428];
};

_vehicle_2426 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4", [3859.2178, 8744.2988, -0.13358285], [], 0, "CAN_COLLIDE"];
  _vehicle_2426 = _this;
  _this setDir 115.97698;
  _this setPos [3859.2178, 8744.2988, -0.13358285];
};

_vehicle_2428 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_craet2", [3862.1479, 8753.2959], [], 0, "CAN_COLLIDE"];
  _vehicle_2428 = _this;
  _this setDir 511.90741;
  _this setPos [3862.1479, 8753.2959];
};

_vehicle_2430 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [3857.45, 8743.3779, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2430 = _this;
  _this setDir -179.90622;
  _this setPos [3857.45, 8743.3779, -6.1035156e-005];
};

_vehicle_2433 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [3856.0549, 8735.1416, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2433 = _this;
  _this setDir -199.90735;
  _this setPos [3856.0549, 8735.1416, -9.1552734e-005];
};

_vehicle_2436 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3884.0093, 8746.4727, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2436 = _this;
  _this setPos [3884.0093, 8746.4727, -6.1035156e-005];
};

_vehicle_2438 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3883.0408, 8747.3359, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2438 = _this;
  _this setPos [3883.0408, 8747.3359, -3.0517578e-005];
};

_vehicle_2440 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3863.9546, 8752.7939, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2440 = _this;
  _this setPos [3863.9546, 8752.7939, 0];
};

_vehicle_2442 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3864.2344, 8753.4893, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2442 = _this;
  _this setPos [3864.2344, 8753.4893, 3.0517578e-005];
};

_vehicle_2444 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3862.0864, 8749.4551, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2444 = _this;
  _this setPos [3862.0864, 8749.4551, -9.1552734e-005];
};

_vehicle_2446 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3861.0813, 8749.5273, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2446 = _this;
  _this setPos [3861.0813, 8749.5273, -3.0517578e-005];
};

_vehicle_2448 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3874.811, 8741.1211, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_2448 = _this;
  _this setPos [3874.811, 8741.1211, -0.00012207031];
};

_vehicle_2450 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3885.4785, 8732.916, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2450 = _this;
  _this setPos [3885.4785, 8732.916, -6.1035156e-005];
};

_vehicle_2452 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3893.0852, 8742.0313, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2452 = _this;
  _this setPos [3893.0852, 8742.0313, 0];
};

_vehicle_2454 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3918.5396, 8761.6445, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2454 = _this;
  _this setPos [3918.5396, 8761.6445, 3.0517578e-005];
};

_vehicle_2456 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3905.9817, 8726.0576, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2456 = _this;
  _this setPos [3905.9817, 8726.0576, 0];
};

_vehicle_2458 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3905.3572, 8726.7227, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2458 = _this;
  _this setPos [3905.3572, 8726.7227, -6.1035156e-005];
};

_vehicle_2460 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3904.4646, 8726.6221, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2460 = _this;
  _this setPos [3904.4646, 8726.6221, 3.0517578e-005];
};

_vehicle_2462 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3904.9863, 8725.8105, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2462 = _this;
  _this setPos [3904.9863, 8725.8105, -3.0517578e-005];
};

_vehicle_2464 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3862.521, 8739.7051, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2464 = _this;
  _this setPos [3862.521, 8739.7051, 0];
};

_vehicle_2466 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3859.137, 8744.1611, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2466 = _this;
  _this setPos [3859.137, 8744.1611, 9.1552734e-005];
};

_vehicle_2468 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3859.3818, 8744.6719, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2468 = _this;
  _this setPos [3859.3818, 8744.6719, -3.0517578e-005];
};

_vehicle_2470 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3873.5933, 8779.9023, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2470 = _this;
  _this setPos [3873.5933, 8779.9023, -3.0517578e-005];
};

_vehicle_2472 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3873.3525, 8779.1982, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2472 = _this;
  _this setPos [3873.3525, 8779.1982, 3.0517578e-005];
};

_vehicle_2474 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3874.6106, 8789.6035, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2474 = _this;
  _this setPos [3874.6106, 8789.6035, -6.1035156e-005];
};

_vehicle_2476 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3874.1633, 8788.835, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2476 = _this;
  _this setPos [3874.1633, 8788.835, 0];
};

_vehicle_2478 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3903.1438, 8802.9941, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2478 = _this;
  _this setPos [3903.1438, 8802.9941, -6.1035156e-005];
};

_vehicle_2480 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3903.458, 8803.5498, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2480 = _this;
  _this setPos [3903.458, 8803.5498, 3.0517578e-005];
};

_vehicle_2484 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3951.8691, 8718.0498, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2484 = _this;
  _this setPos [3951.8691, 8718.0498, 3.0517578e-005];
};

_vehicle_2486 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3951.6384, 8717.3535, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2486 = _this;
  _this setPos [3951.6384, 8717.3535, 6.1035156e-005];
};

_vehicle_2488 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3952.3833, 8716.6055, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2488 = _this;
  _this setPos [3952.3833, 8716.6055, -3.0517578e-005];
};

_vehicle_2490 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3927.4199, 8697.6123, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2490 = _this;
  _this setPos [3927.4199, 8697.6123, 0];
};

_vehicle_2492 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3928.3538, 8698.1523, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2492 = _this;
  _this setPos [3928.3538, 8698.1523, 0];
};

_vehicle_2494 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3921.5542, 8705.1436, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2494 = _this;
  _this setPos [3921.5542, 8705.1436, -6.1035156e-005];
};

_vehicle_2501 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [3894.5439, 8765.5303, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2501 = _this;
  _this setDir 206.65343;
  _this setPos [3894.5439, 8765.5303, -3.0517578e-005];
};

_vehicle_2504 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [3893.3804, 8763.0947, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2504 = _this;
  _this setDir -333.43204;
  _this setPos [3893.3804, 8763.0947, 6.1035156e-005];
};

_vehicle_2508 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [3891.791, 8760.0195, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2508 = _this;
  _this setDir -155.04698;
  _this setPos [3891.791, 8760.0195, -6.1035156e-005];
};

_vehicle_2510 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [3890.751, 8772.4199, -1.7294687], [], 0, "CAN_COLLIDE"];
  _vehicle_2510 = _this;
  _this setDir 117.41772;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3890.751, 8772.4199, -1.7294687];
};

_vehicle_2515 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [3886.7944, 8764.8037, -1.7701132], [], 0, "CAN_COLLIDE"];
  _vehicle_2515 = _this;
  _this setDir 117.41772;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3886.7944, 8764.8037, -1.7701132];
};

_vehicle_2518 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [3870.4626, 8750.0342, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2518 = _this;
  _this setDir -326.18033;
  _this setPos [3870.4626, 8750.0342, -6.1035156e-005];
};

_vehicle_2520 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_25", [3883.6387, 8754.3213, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2520 = _this;
  _this setDir -93.900673;
  _this setPos [3883.6387, 8754.3213, -3.0517578e-005];
};

_vehicle_2522 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [3866.8506, 8745.0527, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2522 = _this;
  _this setDir 35.876511;
  _this setPos [3866.8506, 8745.0527, -9.1552734e-005];
};

_vehicle_2525 = objNull;
if (true) then
{
  _this = createVehicle ["hiluxWreck", [3867.4705, 8742.5313, -0.024659015], [], 0, "CAN_COLLIDE"];
  _vehicle_2525 = _this;
  _this setDir 18.300697;
  _this setPos [3867.4705, 8742.5313, -0.024659015];
};

_vehicle_2528 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3855.4702, 8739.1104, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2528 = _this;
  _this setPos [3855.4702, 8739.1104, 0];
};

_vehicle_2530 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3859.364, 8733.6533, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2530 = _this;
  _this setPos [3859.364, 8733.6533, 0];
};

_vehicle_2532 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3867.6714, 8724.916, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2532 = _this;
  _this setPos [3867.6714, 8724.916, 0];
};

_vehicle_2534 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3868.2761, 8724.4678, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2534 = _this;
  _this setPos [3868.2761, 8724.4678, 3.0517578e-005];
};

_vehicle_2536 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3869.5586, 8723.4023, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2536 = _this;
  _this setPos [3869.5586, 8723.4023, 3.0517578e-005];
};

_vehicle_2538 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3857.1123, 8735.2588, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2538 = _this;
  _this setPos [3857.1123, 8735.2588, -3.0517578e-005];
};

_vehicle_2544 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [3890.9744, 8739.0527, -0.26687351], [], 0, "CAN_COLLIDE"];
  _vehicle_2544 = _this;
  _this setDir -126.75028;
  _this setPos [3890.9744, 8739.0527, -0.26687351];
};

_vehicle_2547 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [3893.9146, 8735.0576, -0.25254443], [], 0, "CAN_COLLIDE"];
  _vehicle_2547 = _this;
  _this setDir -126.86217;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3893.9146, 8735.0576, -0.25254443];
};

_vehicle_2550 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [3843.7407, 8773.0322, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2550 = _this;
  _this setDir -381.57401;
  _this setPos [3843.7407, 8773.0322, -3.0517578e-005];
};

_vehicle_2553 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3889.2417, 8741.0771, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2553 = _this;
  _this setPos [3889.2417, 8741.0771, -6.1035156e-005];
};

_vehicle_2555 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3889.8474, 8740.3945, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2555 = _this;
  _this setPos [3889.8474, 8740.3945, 0];
};

_vehicle_2557 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3885.4434, 8729.5703, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2557 = _this;
  _this setPos [3885.4434, 8729.5703, -6.1035156e-005];
};

_vehicle_2559 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3885.9924, 8730.3418, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2559 = _this;
  _this setPos [3885.9924, 8730.3418, 0];
};

_vehicle_2561 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3884.2756, 8760.8037, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2561 = _this;
  _this setPos [3884.2756, 8760.8037, 3.0517578e-005];
};

_vehicle_2563 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3883.9021, 8760.3262, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2563 = _this;
  _this setPos [3883.9021, 8760.3262, -3.0517578e-005];
};

_vehicle_2565 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3871.0437, 8766.7129, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2565 = _this;
  _this setPos [3871.0437, 8766.7129, 0];
};

_vehicle_2567 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3870.2656, 8767.2393, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2567 = _this;
  _this setPos [3870.2656, 8767.2393, 9.1552734e-005];
};

_vehicle_2573 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3873.9397, 8765.2158, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2573 = _this;
  _this setPos [3873.9397, 8765.2158, 3.0517578e-005];
};

_vehicle_2575 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3874.5674, 8764.8926, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2575 = _this;
  _this setPos [3874.5674, 8764.8926, 0];
};

_vehicle_2577 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3850.4602, 8785.584, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2577 = _this;
  _this setPos [3850.4602, 8785.584, 3.0517578e-005];
};

_vehicle_2579 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3851.054, 8786.6045, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2579 = _this;
  _this setPos [3851.054, 8786.6045, 3.0517578e-005];
};

_vehicle_2581 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3849.9109, 8810.0518, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2581 = _this;
  _this setPos [3849.9109, 8810.0518, 0];
};

_vehicle_2583 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3848.9551, 8810.6719, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2583 = _this;
  _this setPos [3848.9551, 8810.6719, 0];
};

_vehicle_2585 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3836.7939, 8771.8828, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2585 = _this;
  _this setPos [3836.7939, 8771.8828, 0];
};

_vehicle_2587 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stodola_old_open", [3596.5317, 8595.4355, 0.36841536], [], 0, "CAN_COLLIDE"];
  _vehicle_2587 = _this;
  _this setDir 20.173279;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3596.5317, 8595.4355, 0.36841536];
};

_vehicle_2589 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3589.2275, 8591.8672, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2589 = _this;
  _this setPos [3589.2275, 8591.8672, 0];
};

_vehicle_2591 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3589.0308, 8590.999, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2591 = _this;
  _this setPos [3589.0308, 8590.999, 6.1035156e-005];
};

_vehicle_2593 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3588.3118, 8588.7139, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2593 = _this;
  _this setPos [3588.3118, 8588.7139, 6.1035156e-005];
};

_vehicle_2595 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3587.9724, 8587.6787, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2595 = _this;
  _this setPos [3587.9724, 8587.6787, 0];
};

_vehicle_2597 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3588.0037, 8585.1631, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2597 = _this;
  _this setPos [3588.0037, 8585.1631, 3.0517578e-005];
};

_vehicle_2599 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3591.2068, 8583.8115, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2599 = _this;
  _this setPos [3591.2068, 8583.8115, 9.1552734e-005];
};

_vehicle_2601 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3592.1416, 8583.3115, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2601 = _this;
  _this setPos [3592.1416, 8583.3115, 6.1035156e-005];
};

_vehicle_2603 = objNull;
if (true) then
{
  _this = createVehicle ["Land_sara_stodola", [3611.0786, 8574.4189, -0.19905928], [], 0, "CAN_COLLIDE"];
  _vehicle_2603 = _this;
  _this setDir -137.02737;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3611.0786, 8574.4189, -0.19905928];
};

_vehicle_2605 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [3614.9141, 8561.7715, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2605 = _this;
  _this setDir -434.00241;
  _this setPos [3614.9141, 8561.7715, -3.0517578e-005];
};

_vehicle_2608 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [3617.3022, 8564.1436], [], 0, "CAN_COLLIDE"];
  _vehicle_2608 = _this;
  _this setDir -171.55525;
  _this setPos [3617.3022, 8564.1436];
};

_vehicle_2610 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [3623.3203, 8591.5879, -0.019511346], [], 0, "CAN_COLLIDE"];
  _vehicle_2610 = _this;
  _this setDir -155.17661;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3623.3203, 8591.5879, -0.019511346];
};

_vehicle_2617 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_Boulder1", [3595.1826, 8485.9277, -0.55145109], [], 0, "CAN_COLLIDE"];
  _vehicle_2617 = _this;
  _this setDir -31.721172;
  _this setPos [3595.1826, 8485.9277, -0.55145109];
};

_vehicle_2618 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pond_small_27", [3590.5786, 8497.7656, 0.1591319], [], 0, "CAN_COLLIDE"];
  _vehicle_2618 = _this;
  _this setPos [3590.5786, 8497.7656, 0.1591319];
};

_vehicle_2620 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3618.7334, 8566.2861, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2620 = _this;
  _this setPos [3618.7334, 8566.2861, -6.1035156e-005];
};

_vehicle_2622 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3618.8135, 8567.1865, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2622 = _this;
  _this setPos [3618.8135, 8567.1865, -3.0517578e-005];
};

_vehicle_2624 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3621.0264, 8585.2402, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2624 = _this;
  _this setPos [3621.0264, 8585.2402, 0];
};

_vehicle_2626 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [3609.8147, 8576.0342, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2626 = _this;
  _this setPos [3609.8147, 8576.0342, -3.0517578e-005];
};

_vehicle_2631 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV2_04_interier", [3716.9988, 8978.5566, -0.12085891], [], 0, "CAN_COLLIDE"];
  _vehicle_2631 = _this;
  _this setDir -396.1004;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3716.9988, 8978.5566, -0.12085891];
};

_vehicle_2633 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [3653.6802, 8963.8379, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2633 = _this;
  _this setPos [3653.6802, 8963.8379, 6.1035156e-005];
};

_vehicle_2635 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [3657.6865, 8971.7031, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2635 = _this;
  _this setPos [3657.6865, 8971.7031, -3.0517578e-005];
};

_vehicle_2637 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [3646.553, 8978.4268, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2637 = _this;
  _this setPos [3646.553, 8978.4268, 6.1035156e-005];
};

_vehicle_2639 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [3635.9707, 8980.2305, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2639 = _this;
  _this setPos [3635.9707, 8980.2305, 0];
};

_vehicle_2641 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [3645.1221, 8972.0713, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2641 = _this;
  _this setPos [3645.1221, 8972.0713, -6.1035156e-005];
};

_vehicle_2646 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1I4", [4074.2117, 8976.9932, 0.27290246], [], 0, "CAN_COLLIDE"];
  _vehicle_2646 = _this;
  _this setDir -360.66479;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4074.2117, 8976.9932, 0.27290246];
};

_vehicle_2655 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [4069.7783, 8954.7285], [], 0, "CAN_COLLIDE"];
  _vehicle_2655 = _this;
  _this setDir -358.97946;
  _this setPos [4069.7783, 8954.7285];
};

_vehicle_2657 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [4074.6072, 8954.5039, -0.01725525], [], 0, "CAN_COLLIDE"];
  _vehicle_2657 = _this;
  _this setDir -356.18018;
  _this setPos [4074.6072, 8954.5039, -0.01725525];
};

_vehicle_2659 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [4079.4211, 8954.1006, -0.021614842], [], 0, "CAN_COLLIDE"];
  _vehicle_2659 = _this;
  _this setDir -354.43814;
  _this setPos [4079.4211, 8954.1006, -0.021614842];
};

_vehicle_2661 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1I4", [4028.1436, 8969.5566], [], 0, "CAN_COLLIDE"];
  _vehicle_2661 = _this;
  _this setDir 0.79383779;
  _this setPos [4028.1436, 8969.5566];
};

_vehicle_2663 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [4022.5657, 8951.833, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2663 = _this;
  _this setDir -360.79559;
  _this setPos [4022.5657, 8951.833, 3.0517578e-005];
};

_vehicle_2665 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [4040.5068, 8952.9492, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2665 = _this;
  _this setDir -382.10406;
  _this setPos [4040.5068, 8952.9492, 3.0517578e-005];
};

_vehicle_2668 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5", [4035.709, 8951.957, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2668 = _this;
  _this setDir -361.14365;
  _this setPos [4035.709, 8951.957, 6.1035156e-005];
};

_vehicle_2671 = objNull;
if (true) then
{
  _this = createVehicle ["datsun02Wreck", [4035.9949, 8961.3242], [], 0, "CAN_COLLIDE"];
  _vehicle_2671 = _this;
  _this setDir -397.38538;
  _this setPos [4035.9949, 8961.3242];
};

_vehicle_2673 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [3893.7373, 8881.0928, -0.076898016], [], 0, "CAN_COLLIDE"];
  _vehicle_2673 = _this;
  _this setDir -604.05377;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3893.7373, 8881.0928, -0.076898016];
};

_vehicle_2676 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [4054.3413, 8907.3037], [], 0, "CAN_COLLIDE"];
  _vehicle_2676 = _this;
  _this setDir -631.2757;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4054.3413, 8907.3037];
};

_vehicle_2680 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [3774.9844, 9012.083, -0.038931146], [], 0, "CAN_COLLIDE"];
  _vehicle_2680 = _this;
  _this setDir -720.85669;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3774.9844, 9012.083, -0.038931146];
};

_vehicle_2682 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [3871.9077, 8801.4365, -0.099147275], [], 0, "CAN_COLLIDE"];
  _vehicle_2682 = _this;
  _this setDir -512.96155;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3871.9077, 8801.4365, -0.099147275];
};

_vehicle_2684 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [4124.46, 8838.7813, -0.055845078], [], 0, "CAN_COLLIDE"];
  _vehicle_2684 = _this;
  _this setDir -440.99762;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4124.46, 8838.7813, -0.055845078];
};

_vehicle_2687 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4119.1143, 8843.8369, -1.7958962], [], 0, "CAN_COLLIDE"];
  _vehicle_2687 = _this;
  _this setDir 8.6751642;
  _this setPos [4119.1143, 8843.8369, -1.7958962];
};

_vehicle_2689 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4127.2881, 8842.5459, -1.8012668], [], 0, "CAN_COLLIDE"];
  _vehicle_2689 = _this;
  _this setDir 9.2853193;
  _this setPos [4127.2881, 8842.5459, -1.8012668];
};

_vehicle_2691 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [4130.0923, 8841.084, -1.7958353], [], 0, "CAN_COLLIDE"];
  _vehicle_2691 = _this;
  _this setDir 99.074158;
  _this setPos [4130.0923, 8841.084, -1.7958353];
};

_vehicle_2699 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4130.27, 8845.833, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2699 = _this;
  _this setPos [4130.27, 8845.833, 0];
};

_vehicle_2701 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4125.8882, 8846.4873, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2701 = _this;
  _this setPos [4125.8882, 8846.4873, -3.0517578e-005];
};

_vehicle_2703 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [4012.2231, 8983.2363, -0.058411967], [], 0, "CAN_COLLIDE"];
  _vehicle_2703 = _this;
  _this setDir -808.24243;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4012.2231, 8983.2363, -0.058411967];
};

_vehicle_2707 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_rust_draty", [4020.4893, 8976.6025], [], 0, "CAN_COLLIDE"];
  _vehicle_2707 = _this;
  _this setDir -89.43515;
  _this setPos [4020.4893, 8976.6025];
};

_vehicle_2709 = objNull;
if (true) then
{
  _this = createVehicle ["hiluxWreck", [4012.8953, 8991.5947, -0.018511765], [], 0, "CAN_COLLIDE"];
  _vehicle_2709 = _this;
  _this setDir -161.88695;
  _this setPos [4012.8953, 8991.5947, -0.018511765];
};

_vehicle_2712 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_rust_draty", [4020.5068, 8979.1377, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2712 = _this;
  _this setDir -89.43515;
  _this setPos [4020.5068, 8979.1377, 6.1035156e-005];
};

_vehicle_2716 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [3977.5552, 8889.0566, 0.0007567741], [], 0, "CAN_COLLIDE"];
  _vehicle_2716 = _this;
  _this setDir -903.25964;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3977.5552, 8889.0566, 0.0007567741];
};

_vehicle_2718 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [3641.1462, 8993.0801, 0.079724818], [], 0, "CAN_COLLIDE"];
  _vehicle_2718 = _this;
  _this setDir -797.69769;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3641.1462, 8993.0801, 0.079724818];
};

_vehicle_2723 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_02", [3832.7856, 8950.2207, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2723 = _this;
  _this setDir -155.44405;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [3832.7856, 8950.2207, 6.1035156e-005];
};

_vehicle_2725 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [3809.4453, 8848.332, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2725 = _this;
  _this setPos [3809.4453, 8848.332, 0];
};

_vehicle_2727 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [3797.2847, 8854.3486, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2727 = _this;
  _this setPos [3797.2847, 8854.3486, -3.0517578e-005];
};

_vehicle_2729 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [3803.5364, 8842.5137, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2729 = _this;
  _this setPos [3803.5364, 8842.5137, -3.0517578e-005];
};

_vehicle_2731 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [3807.248, 8855.7979, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2731 = _this;
  _this setPos [3807.248, 8855.7979, 0];
};

_vehicle_2733 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [3825.3335, 8830.9414, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_2733 = _this;
  _this setPos [3825.3335, 8830.9414, 0];
};

_vehicle_2735 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [3784.1106, 8834.873, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2735 = _this;
  _this setPos [3784.1106, 8834.873, 6.1035156e-005];
};

_vehicle_2737 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [3785.5918, 8860.3086, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2737 = _this;
  _this setPos [3785.5918, 8860.3086, -3.0517578e-005];
};

_vehicle_2739 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [3838.9971, 8883.1777, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2739 = _this;
  _this setPos [3838.9971, 8883.1777, 6.1035156e-005];
};

_vehicle_2741 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [3835.5557, 8947.7402, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2741 = _this;
  _this setPos [3835.5557, 8947.7402, -3.0517578e-005];
};

_vehicle_2743 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [3826.5981, 8950.9814, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2743 = _this;
  _this setPos [3826.5981, 8950.9814, 3.0517578e-005];
};

};
