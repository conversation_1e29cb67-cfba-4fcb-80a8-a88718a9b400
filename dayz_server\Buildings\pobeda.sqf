if (isserver) then {
_vehicle_0 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9704.4111, 13561.508, 0.00045776367], [], 0, "CAN_COLLIDE"];
  _vehicle_0 = _this;
  _this setDir -24.879169;
  _this setPos [9704.4111, 13561.508, 0.00045776367];
};

_vehicle_1 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_spoj_2", [9593.3301, 13755.949, -0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_1 = _this;
  _this setDir 90.381706;
  _this setPos [9593.3301, 13755.949, -0.00019836426];
};

_vehicle_3 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [9702.5059, 13565.574, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3 = _this;
  _this setDir -74.079048;
  _this setPos [9702.5059, 13565.574, 3.8146973e-005];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [9697.1875, 13577.078, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setDir -25.845177;
  _this setPos [9697.1875, 13577.078, 0.00019836426];
};

_vehicle_14 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [9609.0703, 13796.697, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_14 = _this;
  _this setDir 5.9349685;
  _this setPos [9609.0703, 13796.697, 9.1552734e-005];
};

_vehicle_15 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [9613.5801, 13815.718, -3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_15 = _this;
  _this setDir 21.801962;
  _this setPos [9613.5801, 13815.718, -3.8146973e-005];
};

_vehicle_17 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9605.6279, 13763.521, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_17 = _this;
  _this setDir 5.1362119;
  _this setPos [9605.6279, 13763.521, 0.00016784668];
};

_vehicle_18 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9607.1152, 13779.409, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_18 = _this;
  _this setDir 6.3200755;
  _this setPos [9607.1152, 13779.409, -0.00018310547];
};

_vehicle_20 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [9623.0332, 13832.61, 0.00022125244], [], 0, "CAN_COLLIDE"];
  _vehicle_20 = _this;
  _this setDir 36.264824;
  _this setPos [9623.0332, 13832.61, 0.00022125244];
};

_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9634.3789, 13845.581, -0.00044250488], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir 45.170956;
  _this setPos [9634.3789, 13845.581, -0.00044250488];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9646.6709, 13857.774, 0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir 46.737324;
  _this setPos [9646.6709, 13857.774, 0.00028991699];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9659.2744, 13869.464, 0.99775696], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir 50.112137;
  _this setPos [9659.2744, 13869.464, 0.99775696];
};

_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9672.5645, 13880.458, -0.10290527], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setDir 50.867458;
  _this setPos [9672.5645, 13880.458, -0.10290527];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9686.043, 13891.383, 3.1494217], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setDir 51.587372;
  _this setPos [9686.043, 13891.383, 3.1494217];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9699.54, 13902.07, -0.31761169], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir 52.806168;
  _this setPos [9699.54, 13902.07, -0.31761169];
};

_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9713.1719, 13912.422, 4.9348602], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setDir 55.431137;
  _this setPos [9713.1719, 13912.422, 4.9348602];
};

_vehicle_40 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9727.5029, 13922.231, -0.40705109], [], 0, "CAN_COLLIDE"];
  _vehicle_40 = _this;
  _this setDir 54.913063;
  _this setPos [9727.5029, 13922.231, -0.40705109];
};

_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [9651.2031, 13860.262, -0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setDir -45.517548;
  _this setPos [9651.2031, 13860.262, -0.00020599365];
};

_vehicle_46 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9741.584, 13932.164, 5.2455597], [], 0, "CAN_COLLIDE"];
  _vehicle_46 = _this;
  _this setDir 57.460964;
  _this setPos [9741.584, 13932.164, 5.2455597];
};

_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9756.082, 13941.429, -0.46198273], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setDir 57.998543;
  _this setPos [9756.082, 13941.429, -0.46198273];
};

_vehicle_52 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9770.7773, 13950.539, 3.76651], [], 0, "CAN_COLLIDE"];
  _vehicle_52 = _this;
  _this setDir 56.307507;
  _this setPos [9770.7773, 13950.539, 3.76651];
};

_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9785.2461, 13960.02, -0.34965515], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setDir 57.247597;
  _this setPos [9785.2461, 13960.02, -0.34965515];
};

_vehicle_58 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9799.5293, 13969.248, 3.31427], [], 0, "CAN_COLLIDE"];
  _vehicle_58 = _this;
  _this setDir 57.332924;
  _this setPos [9799.5293, 13969.248, 3.31427];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9814.1104, 13978.72, -0.37916565], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir 57.3335;
  _this setPos [9814.1104, 13978.72, -0.37916565];
};

_vehicle_64 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9828.4541, 13987.784, 1.9757614], [], 0, "CAN_COLLIDE"];
  _vehicle_64 = _this;
  _this setDir 53.715515;
  _this setPos [9828.4541, 13987.784, 1.9757614];
};

_vehicle_65 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [9842.3506, 13997.959, -0.23471832], [], 0, "CAN_COLLIDE"];
  _vehicle_65 = _this;
  _this setDir 53.207737;
  _this setPos [9842.3506, 13997.959, -0.23471832];
};

_vehicle_87 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [9613.2891, 13810.225, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_87 = _this;
  _this setDir -74.991676;
  _this setPos [9613.2891, 13810.225, 0.0001373291];
};

_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_6konec", [9609.7324, 13800.66, -8.392334e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir 118.70003;
  _this setPos [9609.7324, 13800.66, -8.392334e-005];
};

_vehicle_106 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_0_2000", [9630.5781, 13789.528, -0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_106 = _this;
  _this setDir -62.372639;
  _this setPos [9630.5781, 13789.528, -0.0002746582];
};

_vehicle_107 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_0_2000", [9645.3896, 13780.732, 0.00016021729], [], 0, "CAN_COLLIDE"];
  _vehicle_107 = _this;
  _this setDir -60.036499;
  _this setPos [9645.3896, 13780.732, 0.00016021729];
};

_vehicle_110 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_75", [9645.0088, 13780.587, -0.00023651123], [], 0, "CAN_COLLIDE"];
  _vehicle_110 = _this;
  _this setDir 119.72282;
  _this setPos [9645.0088, 13780.587, -0.00023651123];
};

_vehicle_112 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [9669.7842, 13763.15, -0.00025177002], [], 0, "CAN_COLLIDE"];
  _vehicle_112 = _this;
  _this setDir -59.47617;
  _this setPos [9669.7842, 13763.15, -0.00025177002];
};

_vehicle_113 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_0_2000", [9669.6846, 13763.242, 0.00012588501], [], 0, "CAN_COLLIDE"];
  _vehicle_113 = _this;
  _this setDir 121.56758;
  _this setPos [9669.6846, 13763.242, 0.00012588501];
};

_vehicle_115 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_30_25", [9684.4639, 13754.104, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_115 = _this;
  _this setDir 123.16515;
  _this setPos [9684.4639, 13754.104, 5.3405762e-005];
};

_vehicle_116 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_30_25", [9700.0107, 13728.946, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_116 = _this;
  _this setDir 157.62798;
  _this setPos [9700.0107, 13728.946, 5.3405762e-005];
};

_vehicle_117 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_0_2000", [9700.0977, 13728.803, 0.00011444092], [], 0, "CAN_COLLIDE"];
  _vehicle_117 = _this;
  _this setDir -24.092699;
  _this setPos [9700.0977, 13728.803, 0.00011444092];
};

_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_0_2000", [9699.7998, 13698.917, 9.5367432e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setDir 6.0703249;
  _this setPos [9699.7998, 13698.917, 9.5367432e-005];
};

_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_22_50", [9702.7754, 13679.765, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setDir -20.03581;
  _this setPos [9702.7754, 13679.765, 3.8146973e-005];
};

_vehicle_121 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_0_2000", [9708.1543, 13663.339, 0.00018692017], [], 0, "CAN_COLLIDE"];
  _vehicle_121 = _this;
  _this setDir -18.958414;
  _this setPos [9708.1543, 13663.339, 0.00018692017];
};

_vehicle_122 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_0_2000", [9713.8965, 13647.068, 0.00032424927], [], 0, "CAN_COLLIDE"];
  _vehicle_122 = _this;
  _this setDir -20.056875;
  _this setPos [9713.8965, 13647.068, 0.00032424927];
};

_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [9721, 13631.347, 0.0001449585], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setDir 330.82336;
  _this setPos [9721, 13631.347, 0.0001449585];
};

_vehicle_126 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_22_50", [9720.875, 13631.508, 0.00022125244], [], 0, "CAN_COLLIDE"];
  _vehicle_126 = _this;
  _this setDir 151.02466;
  _this setPos [9720.875, 13631.508, 0.00022125244];
};

_vehicle_127 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_30_25", [9724.6279, 13593.861, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_127 = _this;
  _this setDir 197.37958;
  _this setPos [9724.6279, 13593.861, 0.00018310547];
};

_vehicle_130 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_22_50", [9726.7256, 13613.136, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_130 = _this;
  _this setDir 174.82172;
  _this setPos [9726.7256, 13613.136, -7.6293945e-005];
};

_vehicle_138 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_30_25", [9711.1729, 13571.859, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_138 = _this;
  _this setDir 16.047808;
  _this setPos [9711.1729, 13571.859, 7.6293945e-006];
};

_vehicle_139 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_12", [9709.3838, 13560.629, 0.00025177002], [], 0, "CAN_COLLIDE"];
  _vehicle_139 = _this;
  _this setDir 9.4943829;
  _this setPos [9709.3838, 13560.629, 0.00025177002];
};

_vehicle_140 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_6konec", [9708.6299, 13554.871, 9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_140 = _this;
  _this setDir 6.8283281;
  _this setPos [9708.6299, 13554.871, 9.9182129e-005];
};

_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [9524.5469, 13741.678, 8.392334e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setDir -67.663544;
  _this setPos [9524.5469, 13741.678, 8.392334e-005];
};

_vehicle_152 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_0_2000", [9509.209, 13749.657, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_152 = _this;
  _this setDir -59.59494;
  _this setPos [9509.209, 13749.657, -0.00012207031];
};

_vehicle_153 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_0_2000", [9494.5, 13758.737, -5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_153 = _this;
  _this setDir -57.032429;
  _this setPos [9494.5, 13758.737, -5.3405762e-005];
};

_vehicle_154 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_30_25", [9480.2295, 13768.233, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_154 = _this;
  _this setDir -56.443249;
  _this setPos [9480.2295, 13768.233, 3.0517578e-005];
};

_vehicle_160 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9471.6914, 13777.863, -0.0004196167], [], 0, "CAN_COLLIDE"];
  _vehicle_160 = _this;
  _this setDir -26.961788;
  _this setPos [9471.6914, 13777.863, -0.0004196167];
};

_vehicle_161 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [9448.375, 13815.27, -0.00031280518], [], 0, "CAN_COLLIDE"];
  _vehicle_161 = _this;
  _this setDir 130.60934;
  _this setPos [9448.375, 13815.27, -0.00031280518];
};

_vehicle_163 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [9434.3018, 13825.365, 0.00042724609], [], 0, "CAN_COLLIDE"];
  _vehicle_163 = _this;
  _this setDir 120.8359;
  _this setPos [9434.3018, 13825.365, 0.00042724609];
};

_vehicle_164 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9434.5273, 13825.285, -0.00034332275], [], 0, "CAN_COLLIDE"];
  _vehicle_164 = _this;
  _this setDir -59.632214;
  _this setPos [9434.5273, 13825.285, -0.00034332275];
};

_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9413.2471, 13837.659, -0.00061798096], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setDir -59.672245;
  _this setPos [9413.2471, 13837.659, -0.00061798096];
};

_vehicle_166 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [9376.1299, 13857.793, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_166 = _this;
  _this setDir 111.14247;
  _this setPos [9376.1299, 13857.793, 3.0517578e-005];
};

_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [9359.4541, 13862.28, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setDir 100.45335;
  _this setPos [9359.4541, 13862.28, -6.1035156e-005];
};

_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [9342.2979, 13864.219, 0.00037384033], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setDir 91.717155;
  _this setPos [9342.2979, 13864.219, 0.00037384033];
};

_vehicle_169 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [9629.5879, 13882.249, 0.00011444092], [], 0, "CAN_COLLIDE"];
  _vehicle_169 = _this;
  _this setDir -44.282211;
  _this setPos [9629.5879, 13882.249, 0.00011444092];
};

_vehicle_170 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [9646.8994, 13864.556, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_170 = _this;
  _this setDir -44.282211;
  _this setPos [9646.8994, 13864.556, 1.5258789e-005];
};

_vehicle_174 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [9590.46, 13820.837, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_174 = _this;
  _this setDir -49.457581;
  _this setPos [9590.46, 13820.837, 4.5776367e-005];
};

_vehicle_177 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [9578.4463, 13833.113, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_177 = _this;
  _this setDir -38.601738;
  _this setPos [9578.4463, 13833.113, 4.5776367e-005];
};

_vehicle_178 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [9569.5859, 13850.234, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_178 = _this;
  _this setDir -14.931728;
  _this setPos [9569.5859, 13850.234, 0.00012207031];
};

_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [9607.5479, 13811.75, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setDir -73.076363;
  _this setPos [9607.5479, 13811.75, -2.2888184e-005];
};

_vehicle_180 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [9568.3301, 13869.385, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_180 = _this;
  _this setDir 6.4133463;
  _this setPos [9568.3301, 13869.385, 1.5258789e-005];
};

_vehicle_181 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [9576.9102, 13892.575, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_181 = _this;
  _this setDir 24.940744;
  _this setPos [9576.9102, 13892.575, -1.5258789e-005];
};

_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [9588.085, 13908.047, 0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setDir 46.202873;
  _this setPos [9588.085, 13908.047, 0.00028991699];
};

_vehicle_192 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [9597.4072, 13910.479, 0.00011444092], [], 0, "CAN_COLLIDE"];
  _vehicle_192 = _this;
  _this setDir 105.9768;
  _this setPos [9597.4072, 13910.479, 0.00011444092];
};

_vehicle_193 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [9612.2188, 13899.984, 0.00067138672], [], 0, "CAN_COLLIDE"];
  _vehicle_193 = _this;
  _this setDir -43.861469;
  _this setPos [9612.2188, 13899.984, 0.00067138672];
};

_vehicle_194 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [9574.2734, 13887.819, 0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_194 = _this;
  _this setDir 27.58102;
  _this setPos [9574.2734, 13887.819, 0.00020599365];
};

_vehicle_195 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [9577.0596, 13837.756, -6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_195 = _this;
  _this setDir -128.00743;
  _this setPos [9577.0596, 13837.756, -6.8664551e-005];
};

_vehicle_196 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [9561.1465, 13821.691, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_196 = _this;
  _this setDir 37.141243;
  _this setPos [9561.1465, 13821.691, 4.5776367e-005];
};

_vehicle_197 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [9551.6406, 13808.181, -6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_197 = _this;
  _this setDir 30.418955;
  _this setPos [9551.6406, 13808.181, -6.8664551e-005];
};

_vehicle_200 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [9528.3867, 13747.144, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_200 = _this;
  _this setDir 16.971497;
  _this setPos [9528.3867, 13747.144, 0.00016784668];
};

_vehicle_201 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [9544.0791, 13792.572, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_201 = _this;
  _this setDir 21.043194;
  _this setPos [9544.0791, 13792.572, 0.00012207031];
};

_vehicle_202 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [9535.0518, 13769.433, -0.00017547607], [], 0, "CAN_COLLIDE"];
  _vehicle_202 = _this;
  _this setDir 21.246027;
  _this setPos [9535.0518, 13769.433, -0.00017547607];
};

_vehicle_203 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9317.5459, 13865.288, -0.00079345703], [], 0, "CAN_COLLIDE"];
  _vehicle_203 = _this;
  _this setDir 92.587402;
  _this setPos [9317.5459, 13865.288, -0.00079345703];
};

_vehicle_204 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9293.165, 13865.983, -0.00044250488], [], 0, "CAN_COLLIDE"];
  _vehicle_204 = _this;
  _this setDir 91.37101;
  _this setPos [9293.165, 13865.983, -0.00044250488];
};

_vehicle_209 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9268.3145, 13866.564, 0.056053162], [], 0, "CAN_COLLIDE"];
  _vehicle_209 = _this;
  _this setDir 91.37101;
  _this setPos [9268.3145, 13866.564, 0.056053162];
};

_vehicle_210 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9243.4512, 13867.085, -0.50750732], [], 0, "CAN_COLLIDE"];
  _vehicle_210 = _this;
  _this setDir 91.37101;
  _this setPos [9243.4512, 13867.085, -0.50750732];
};

_vehicle_215 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9218.6982, 13867.706, 0.6306076], [], 0, "CAN_COLLIDE"];
  _vehicle_215 = _this;
  _this setDir 91.37101;
  _this setPos [9218.6982, 13867.706, 0.6306076];
};

_vehicle_216 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9193.8486, 13868.233, -0.080024719], [], 0, "CAN_COLLIDE"];
  _vehicle_216 = _this;
  _this setDir 91.37101;
  _this setPos [9193.8486, 13868.233, -0.080024719];
};

_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9169.2939, 13869.056, -0.3099823], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setDir 91.891685;
  _this setPos [9169.2939, 13869.056, -0.3099823];
};

_vehicle_222 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9144.417, 13869.785, -0.84512329], [], 0, "CAN_COLLIDE"];
  _vehicle_222 = _this;
  _this setDir 91.891685;
  _this setPos [9144.417, 13869.785, -0.84512329];
};

_vehicle_227 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9119.6934, 13870.878, -0.96349335], [], 0, "CAN_COLLIDE"];
  _vehicle_227 = _this;
  _this setDir 92.935486;
  _this setPos [9119.6934, 13870.878, -0.96349335];
};

_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9050.9629, 13901.403, -2.9539871], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setDir 126.10274;
  _this setPos [9050.9629, 13901.403, -2.9539871];
};

_vehicle_234 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9030.8223, 13915.966, -1.0567551], [], 0, "CAN_COLLIDE"];
  _vehicle_234 = _this;
  _this setDir 126.10274;
  _this setPos [9030.8223, 13915.966, -1.0567551];
};

_vehicle_239 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9010.7207, 13930.14, -0.18661499], [], 0, "CAN_COLLIDE"];
  _vehicle_239 = _this;
  _this setDir 125.26354;
  _this setPos [9010.7207, 13930.14, -0.18661499];
};

_vehicle_240 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8990.2197, 13944.368, -0.38391876], [], 0, "CAN_COLLIDE"];
  _vehicle_240 = _this;
  _this setDir 124.93159;
  _this setPos [8990.2197, 13944.368, -0.38391876];
};

_vehicle_244 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [9086.2109, 13878.453, -0.00049591064], [], 0, "CAN_COLLIDE"];
  _vehicle_244 = _this;
  _this setDir -66.22937;
  _this setPos [9086.2109, 13878.453, -0.00049591064];
};

_vehicle_245 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [9102.7588, 13873.036, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_245 = _this;
  _this setDir -76.90889;
  _this setPos [9102.7588, 13873.036, 0.00021362305];
};

_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [9119.9678, 13870.864, -0.00023651123], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setDir -87.432648;
  _this setPos [9119.9678, 13870.864, -0.00023651123];
};

_vehicle_248 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8970.0166, 13958.386, -0.8080368], [], 0, "CAN_COLLIDE"];
  _vehicle_248 = _this;
  _this setDir 124.80949;
  _this setPos [8970.0166, 13958.386, -0.8080368];
};

_vehicle_249 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8949.541, 13972.511, -1.3661957], [], 0, "CAN_COLLIDE"];
  _vehicle_249 = _this;
  _this setDir 124.80949;
  _this setPos [8949.541, 13972.511, -1.3661957];
};

_vehicle_254 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8929.5703, 13987.147, -0.082580566], [], 0, "CAN_COLLIDE"];
  _vehicle_254 = _this;
  _this setDir 126.20473;
  _this setPos [8929.5703, 13987.147, -0.082580566];
};

_vehicle_255 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8909.4512, 14001.777, -1.837677], [], 0, "CAN_COLLIDE"];
  _vehicle_255 = _this;
  _this setDir 126.20473;
  _this setPos [8909.4512, 14001.777, -1.837677];
};

_vehicle_260 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8889.1318, 14016.045, -1.2959671], [], 0, "CAN_COLLIDE"];
  _vehicle_260 = _this;
  _this setDir 125.05692;
  _this setPos [8889.1318, 14016.045, -1.2959671];
};

_vehicle_261 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8868.7783, 14030.317, -2.1252136], [], 0, "CAN_COLLIDE"];
  _vehicle_261 = _this;
  _this setDir 125.05692;
  _this setPos [8868.7783, 14030.317, -2.1252136];
};

_vehicle_265 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [8848.8955, 14045.085, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_265 = _this;
  _this setDir -51.713078;
  _this setPos [8848.8955, 14045.085, 3.0517578e-005];
};

_vehicle_266 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8868.8848, 14030.168, -9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_266 = _this;
  _this setDir -53.304035;
  _this setPos [8868.8848, 14030.168, -9.9182129e-005];
};

_vehicle_268 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [8815.4521, 14089.812, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_268 = _this;
  _this setDir 128.60672;
  _this setPos [8815.4521, 14089.812, -7.6293945e-006];
};

_vehicle_269 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_0_2000", [8836.332, 14059.791, 9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_269 = _this;
  _this setDir -29.018171;
  _this setPos [8836.332, 14059.791, 9.9182129e-005];
};

_vehicle_270 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [8798.5635, 14098.931, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_270 = _this;
  _this setDir 107.13486;
  _this setPos [8798.5635, 14098.931, -7.6293945e-005];
};

_vehicle_272 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8774.9639, 14106.876, -2.1135406], [], 0, "CAN_COLLIDE"];
  _vehicle_272 = _this;
  _this setDir 108.47972;
  _this setPos [8774.9639, 14106.876, -2.1135406];
};

_vehicle_273 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8751.3467, 14114.681, -2.1182632], [], 0, "CAN_COLLIDE"];
  _vehicle_273 = _this;
  _this setDir 108.47972;
  _this setPos [8751.3467, 14114.681, -2.1182632];
};

_vehicle_278 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8727.8457, 14122.507, -2.2863235], [], 0, "CAN_COLLIDE"];
  _vehicle_278 = _this;
  _this setDir 108.47236;
  _this setPos [8727.8457, 14122.507, -2.2863235];
};

_vehicle_279 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8704.2354, 14130.299, -2.7389374], [], 0, "CAN_COLLIDE"];
  _vehicle_279 = _this;
  _this setDir 108.47236;
  _this setPos [8704.2354, 14130.299, -2.7389374];
};

_vehicle_284 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8680.6172, 14138.021, -2.1520691], [], 0, "CAN_COLLIDE"];
  _vehicle_284 = _this;
  _this setDir 107.70193;
  _this setPos [8680.6172, 14138.021, -2.1520691];
};

_vehicle_285 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8656.9033, 14145.492, -2.9159012], [], 0, "CAN_COLLIDE"];
  _vehicle_285 = _this;
  _this setDir 107.70193;
  _this setPos [8656.9033, 14145.492, -2.9159012];
};

_vehicle_291 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8633.2061, 14152.926, -2.0826721], [], 0, "CAN_COLLIDE"];
  _vehicle_291 = _this;
  _this setDir 107.38667;
  _this setPos [8633.2061, 14152.926, -2.0826721];
};

_vehicle_292 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8609.457, 14160.258, -3.1536636], [], 0, "CAN_COLLIDE"];
  _vehicle_292 = _this;
  _this setDir 107.38667;
  _this setPos [8609.457, 14160.258, -3.1536636];
};

_vehicle_297 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8571.8867, 14179.278, -1.5387192], [], 0, "CAN_COLLIDE"];
  _vehicle_297 = _this;
  _this setDir 118.92734;
  _this setPos [8571.8867, 14179.278, -1.5387192];
};

_vehicle_298 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8550.0928, 14191.238, -3.5744019], [], 0, "CAN_COLLIDE"];
  _vehicle_298 = _this;
  _this setDir 118.92734;
  _this setPos [8550.0928, 14191.238, -3.5744019];
};

_vehicle_302 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8609.5381, 14160.318, -3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_302 = _this;
  _this setDir -71.327019;
  _this setPos [8609.5381, 14160.318, -3.8146973e-005];
};

_vehicle_304 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8528.041, 14202.328, -1.1240082], [], 0, "CAN_COLLIDE"];
  _vehicle_304 = _this;
  _this setDir 116.83546;
  _this setPos [8528.041, 14202.328, -1.1240082];
};

_vehicle_305 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8505.7314, 14213.446, -3.5765152], [], 0, "CAN_COLLIDE"];
  _vehicle_305 = _this;
  _this setDir 116.83546;
  _this setPos [8505.7314, 14213.446, -3.5765152];
};

_vehicle_309 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8505.9082, 14213.22, 0.00026702881], [], 0, "CAN_COLLIDE"];
  _vehicle_309 = _this;
  _this setDir -59.487774;
  _this setPos [8505.9082, 14213.22, 0.00026702881];
};

_vehicle_311 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8472.3691, 14239.177, 1.8064957], [], 0, "CAN_COLLIDE"];
  _vehicle_311 = _this;
  _this setDir 129.35739;
  _this setPos [8472.3691, 14239.177, 1.8064957];
};

_vehicle_312 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8453.0859, 14254.887, -4.2489777], [], 0, "CAN_COLLIDE"];
  _vehicle_312 = _this;
  _this setDir 129.35739;
  _this setPos [8453.0859, 14254.887, -4.2489777];
};

_vehicle_318 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8453.2031, 14254.711, 0.00039672852], [], 0, "CAN_COLLIDE"];
  _vehicle_318 = _this;
  _this setDir -50.472664;
  _this setPos [8453.2031, 14254.711, 0.00039672852];
};

_vehicle_323 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8414.7012, 14286.318, -0.00012969971], [], 0, "CAN_COLLIDE"];
  _vehicle_323 = _this;
  _this setDir 129.35739;
  _this setPos [8414.7012, 14286.318, -0.00012969971];
};

_vehicle_324 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8395.5996, 14302.049, -4.9517517], [], 0, "CAN_COLLIDE"];
  _vehicle_324 = _this;
  _this setDir 129.35739;
  _this setPos [8395.5996, 14302.049, -4.9517517];
};

_vehicle_326 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8395.7021, 14301.877, -0.43965912], [], 0, "CAN_COLLIDE"];
  _vehicle_326 = _this;
  _this setDir -52.385387;
  _this setPos [8395.7021, 14301.877, -0.43965912];
};

_vehicle_334 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8355.8467, 14331.868, 0.00034332275], [], 0, "CAN_COLLIDE"];
  _vehicle_334 = _this;
  _this setDir 126.67675;
  _this setPos [8355.8467, 14331.868, 0.00034332275];
};

_vehicle_335 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8335.8457, 14346.628, -2.694809], [], 0, "CAN_COLLIDE"];
  _vehicle_335 = _this;
  _this setDir 126.67675;
  _this setPos [8335.8457, 14346.628, -2.694809];
};

_vehicle_340 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8316.0244, 14361.354, 0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_340 = _this;
  _this setDir 126.44434;
  _this setPos [8316.0244, 14361.354, 0.00025939941];
};

_vehicle_341 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8295.9316, 14376.066, -2.4215851], [], 0, "CAN_COLLIDE"];
  _vehicle_341 = _this;
  _this setDir 126.44434;
  _this setPos [8295.9316, 14376.066, -2.4215851];
};

_vehicle_346 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8295.9785, 14375.981, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_346 = _this;
  _this setDir -53.340439;
  _this setPos [8295.9785, 14375.981, 5.3405762e-005];
};

_vehicle_347 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8282.9121, 14387.271, 0.0002822876], [], 0, "CAN_COLLIDE"];
  _vehicle_347 = _this;
  _this setDir -41.951344;
  _this setPos [8282.9121, 14387.271, 0.0002822876];
};

_vehicle_348 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8272.3115, 14401.002, 0.00017547607], [], 0, "CAN_COLLIDE"];
  _vehicle_348 = _this;
  _this setDir -30.706322;
  _this setPos [8272.3115, 14401.002, 0.00017547607];
};

_vehicle_349 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8257.1631, 14432.192, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_349 = _this;
  _this setDir 149.30875;
  _this setPos [8257.1631, 14432.192, -1.5258789e-005];
};

_vehicle_350 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8246.9238, 14446.121, -3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_350 = _this;
  _this setDir 139.17351;
  _this setPos [8246.9238, 14446.121, -3.8146973e-005];
};

_vehicle_352 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8230.5576, 14464.671, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_352 = _this;
  _this setDir 138.56654;
  _this setPos [8230.5576, 14464.671, 9.1552734e-005];
};

_vehicle_353 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8213.9961, 14483.222, -4.1806412], [], 0, "CAN_COLLIDE"];
  _vehicle_353 = _this;
  _this setDir 138.56654;
  _this setPos [8213.9961, 14483.222, -4.1806412];
};

_vehicle_358 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8197.7725, 14501.854, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_358 = _this;
  _this setDir 139.47818;
  _this setPos [8197.7725, 14501.854, -6.1035156e-005];
};

_vehicle_359 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8181.5605, 14520.686, -3.974144], [], 0, "CAN_COLLIDE"];
  _vehicle_359 = _this;
  _this setDir 139.47818;
  _this setPos [8181.5605, 14520.686, -3.974144];
};

_vehicle_364 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8169.3647, 14532.751, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_364 = _this;
  _this setDir 129.59077;
  _this setPos [8169.3647, 14532.751, -6.1035156e-005];
};

_vehicle_365 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8169.4912, 14532.646, 0.00025177002], [], 0, "CAN_COLLIDE"];
  _vehicle_365 = _this;
  _this setDir -51.028019;
  _this setPos [8169.4912, 14532.646, 0.00025177002];
};

_vehicle_366 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8150.1401, 14548.23, -0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_366 = _this;
  _this setDir -50.066952;
  _this setPos [8150.1401, 14548.23, -0.00016784668];
};

_vehicle_367 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8137.8706, 14560.386, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_367 = _this;
  _this setDir -39.681847;
  _this setPos [8137.8706, 14560.386, 3.0517578e-005];
};

_vehicle_368 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8105.5239, 14598.162, 0.00057983398], [], 0, "CAN_COLLIDE"];
  _vehicle_368 = _this;
  _this setDir 138.80077;
  _this setPos [8105.5239, 14598.162, 0.00057983398];
};

_vehicle_369 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8089.0806, 14616.833, -4.085556], [], 0, "CAN_COLLIDE"];
  _vehicle_369 = _this;
  _this setDir 138.80077;
  _this setPos [8089.0806, 14616.833, -4.085556];
};

_vehicle_374 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8089.1353, 14616.612, 0.00051879883], [], 0, "CAN_COLLIDE"];
  _vehicle_374 = _this;
  _this setDir -40.228958;
  _this setPos [8089.1353, 14616.612, 0.00051879883];
};

_vehicle_375 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8079.1348, 14630.775], [], 0, "CAN_COLLIDE"];
  _vehicle_375 = _this;
  _this setDir -29.138525;
  _this setPos [8079.1348, 14630.775];
};

_vehicle_376 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8063.6914, 14669.817, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_376 = _this;
  _this setDir 160.27835;
  _this setPos [8063.6914, 14669.817, 0.00021362305];
};

_vehicle_377 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8055.2119, 14693.175, -5.0672302], [], 0, "CAN_COLLIDE"];
  _vehicle_377 = _this;
  _this setDir 160.27835;
  _this setPos [8055.2119, 14693.175, -5.0672302];
};

_vehicle_382 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [8045.7305, 14716.006, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_382 = _this;
  _this setDir 157.67313;
  _this setPos [8045.7305, 14716.006, 6.1035156e-005];
};

_vehicle_388 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [8034.9849, 14731.884, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_388 = _this;
  _this setDir 134.97549;
  _this setPos [8034.9849, 14731.884, 0.0001373291];
};

_vehicle_389 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [8018.7485, 14742.393, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_389 = _this;
  _this setDir 111.44865;
  _this setPos [8018.7485, 14742.393, 0.00010681152];
};

_vehicle_390 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [8002.2764, 14747.293, 0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_390 = _this;
  _this setDir 101.42585;
  _this setPos [8002.2764, 14747.293, 0.00033569336];
};

_vehicle_391 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7978.0283, 14752.285, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_391 = _this;
  _this setDir 101.81942;
  _this setPos [7978.0283, 14752.285, 0.00015258789];
};

_vehicle_395 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_0_2000", [9614.3105, 13722.519, 0.00019073486], [], 0, "CAN_COLLIDE"];
  _vehicle_395 = _this;
  _this setDir -30.149656;
  _this setPos [9614.3105, 13722.519, 0.00019073486];
};

_vehicle_397 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_60_10", [9597.1133, 13742.703, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_397 = _this;
  _this setDir 90.995461;
  _this setPos [9597.1133, 13742.703, 0.00021362305];
};

_vehicle_398 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [9605.4043, 13739.123, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_398 = _this;
  _this setDir 3.4834995;
  _this setPos [9605.4043, 13739.123, 9.1552734e-005];
};

_vehicle_400 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [9527.5498, 13741.591, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_400 = _this;
  _this setDir 9.4795799;
  _this setPos [9527.5498, 13741.591, 0.00010681152];
};

_vehicle_403 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_30_25", [9537.3691, 13740.393, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_403 = _this;
  _this setDir -98.874542;
  _this setPos [9537.3691, 13740.393, -3.0517578e-005];
};

_vehicle_404 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [9537.3086, 13740.398, -0.00039672852], [], 0, "CAN_COLLIDE"];
  _vehicle_404 = _this;
  _this setDir 81.131119;
  _this setPos [9537.3086, 13740.398, -0.00039672852];
};

_vehicle_408 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9550.1387, 13741.572, -0.00046539307], [], 0, "CAN_COLLIDE"];
  _vehicle_408 = _this;
  _this setDir 89.935837;
  _this setPos [9550.1387, 13741.572, -0.00046539307];
};

_vehicle_409 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [9574.1572, 13741.658, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_409 = _this;
  _this setDir 86.938553;
  _this setPos [9574.1572, 13741.658, -3.0517578e-005];
};

_vehicle_411 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Nav_Boathouse", [9508.2002, 13711.423, 0.94386381], [], 0, "CAN_COLLIDE"];
  _vehicle_411 = _this;
  _this setDir 60.020023;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9508.2002, 13711.423, 0.94386381];
};

_vehicle_416 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Nav_Boathouse_PierL", [9521.709, 13715.722, -0.51211447], [], 0, "CAN_COLLIDE"];
  _vehicle_416 = _this;
  _this setDir 149.63948;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9521.709, 13715.722, -0.51211447];
};

_vehicle_424 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dum_m2", [9626.4414, 13812.816, 0.5640626], [], 0, "CAN_COLLIDE"];
  _vehicle_424 = _this;
  _this setDir 202.09708;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9626.4414, 13812.816, 0.5640626];
};

_vehicle_426 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV2_01B", [9675.0059, 13916.97, -0.40963396], [], 0, "CAN_COLLIDE"];
  _vehicle_426 = _this;
  _this setDir -35.598919;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9675.0059, 13916.97, -0.40963396];
};

_vehicle_428 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV2_05", [9571.8047, 13726.771, 0.66817558], [], 0, "CAN_COLLIDE"];
  _vehicle_428 = _this;
  _this setDir -269.24194;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9571.8047, 13726.771, 0.66817558];
};

_vehicle_429 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1I1", [9633.1553, 13859.57, 0.31271142], [], 0, "CAN_COLLIDE"];
  _vehicle_429 = _this;
  _this setDir -45.240997;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9633.1553, 13859.57, 0.31271142];
};

_vehicle_430 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1I2", [9619.6992, 13844.968, 0.18784311], [], 0, "CAN_COLLIDE"];
  _vehicle_430 = _this;
  _this setDir -52.087337;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9619.6992, 13844.968, 0.18784311];
};

_vehicle_432 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1I4", [8822.2949, 14051.722, 0.19728354], [], 0, "CAN_COLLIDE"];
  _vehicle_432 = _this;
  _this setDir -35.079559;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8822.2949, 14051.722, 0.19728354];
};

_vehicle_433 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1L1", [9557.2568, 13836.663, -0.46104816], [], 0, "CAN_COLLIDE"];
  _vehicle_433 = _this;
  _this setDir -137.50822;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9557.2568, 13836.663, -0.46104816];
};

_vehicle_434 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1L2", [9556.6563, 13862.735, -0.40000573], [], 0, "CAN_COLLIDE"];
  _vehicle_434 = _this;
  _this setDir 180.82011;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9556.6563, 13862.735, -0.40000573];
};

_vehicle_437 = objNull;
if (true) then
{
  _this = createVehicle ["Land_sara_Domek_sedy", [9578.623, 13862.527, 0.16685295], [], 0, "CAN_COLLIDE"];
  _vehicle_437 = _this;
  _this setDir -263.33893;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9578.623, 13862.527, 0.16685295];
};

_vehicle_439 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_stanice", [9606.7617, 13828.103, -0.36197692], [], 0, "CAN_COLLIDE"];
  _vehicle_439 = _this;
  _this setDir 119.35983;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9606.7617, 13828.103, -0.36197692];
};

_vehicle_440 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_statek_hl_bud", [9709.1787, 13928.662, -0.033169184], [], 0, "CAN_COLLIDE"];
  _vehicle_440 = _this;
  _this setDir 412.22806;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9709.1787, 13928.662, -0.033169184];
};

_vehicle_443 = objNull;
if (true) then
{
  _this = createVehicle ["Land_sara_Domek_sedy", [9586.877, 13887.558, 0.31999752], [], 0, "CAN_COLLIDE"];
  _vehicle_443 = _this;
  _this setDir -246.07368;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9586.877, 13887.558, 0.31999752];
};

_vehicle_446 = objNull;
if (true) then
{
  _this = createVehicle ["Land_sara_domek_zluty", [9571.8477, 13817.074, 0.5069415], [], 0, "CAN_COLLIDE"];
  _vehicle_446 = _this;
  _this setDir -202.07829;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9571.8477, 13817.074, 0.5069415];
};

_vehicle_450 = objNull;
if (true) then
{
  _this = createVehicle ["hruzdum", [9542.4854, 13818.654, 0.39778316], [], 0, "CAN_COLLIDE"];
  _vehicle_450 = _this;
  _this setDir -64.874352;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9542.4854, 13818.654, 0.39778316];
};

_vehicle_452 = objNull;
if (true) then
{
  _this = createVehicle ["hruzdum", [9534.2988, 13802.08, 0.52293235], [], 0, "CAN_COLLIDE"];
  _vehicle_452 = _this;
  _this setDir -67.317825;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9534.2988, 13802.08, 0.52293235];
};

_vehicle_453 = objNull;
if (true) then
{
  _this = createVehicle ["hruzdum", [9615.0371, 13876.596, 0.62725312], [], 0, "CAN_COLLIDE"];
  _vehicle_453 = _this;
  _this setDir -155.58281;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9615.0371, 13876.596, 0.62725312];
};

_vehicle_454 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sara_domek_ruina", [9529.9551, 13782.961, 0.36570746], [], 0, "CAN_COLLIDE"];
  _vehicle_454 = _this;
  _this setDir -69.941208;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9529.9551, 13782.961, 0.36570746];
};

_vehicle_455 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_3I3", [9557.3535, 13793.785, 0.60263091], [], 0, "CAN_COLLIDE"];
  _vehicle_455 = _this;
  _this setDir -58.267925;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9557.3535, 13793.785, 0.60263091];
};

_vehicle_457 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_2T2", [9597.9854, 13798.341, -0.23052038], [], 0, "CAN_COLLIDE"];
  _vehicle_457 = _this;
  _this setDir -266.75565;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9597.9854, 13798.341, -0.23052038];
};

_vehicle_458 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_2T1", [9642.6045, 13890.867, 0.39827815], [], 0, "CAN_COLLIDE"];
  _vehicle_458 = _this;
  _this setDir 44.466515;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9642.6045, 13890.867, 0.39827815];
};

_vehicle_461 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV2_04_interier", [9823.04, 13962.392, 0.64926279], [], 0, "CAN_COLLIDE"];
  _vehicle_461 = _this;
  _this setDir -125.96149;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9823.04, 13962.392, 0.64926279];
};

_vehicle_464 = objNull;
if (true) then
{
  _this = createVehicle ["dum_rasovna", [9637.3096, 13827.243, 0.76347524], [], 0, "CAN_COLLIDE"];
  _vehicle_464 = _this;
  _this setDir 396.20831;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9637.3096, 13827.243, 0.76347524];
};

_vehicle_471 = objNull;
if (true) then
{
  _this = createVehicle ["Land_sara_stodola", [9515.5225, 13813.423, 0.45050845], [], 0, "CAN_COLLIDE"];
  _vehicle_471 = _this;
  _this setDir -158.86259;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9515.5225, 13813.423, 0.45050845];
};

_vehicle_473 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stodola_old_open", [9864.79, 14031.484, 0.84714758], [], 0, "CAN_COLLIDE"];
  _vehicle_473 = _this;
  _this setDir 165.06068;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9864.79, 14031.484, 0.84714758];
};

_vehicle_500 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_02", [9578.8975, 13752.994, 0.22742373], [], 0, "CAN_COLLIDE"];
  _vehicle_500 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9578.8975, 13752.994, 0.22742373];
};

_vehicle_522 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Build", [9595.6904, 13763.225, 0.27318278], [], 0, "CAN_COLLIDE"];
  _vehicle_522 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9595.6904, 13763.225, 0.27318278];
};

_vehicle_524 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Feed", [9592.2188, 13754.843, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_524 = _this;
  _this setPos [9592.2188, 13754.843, 0.00024414063];
};

_vehicle_525 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Feed", [9596.2783, 13754.972, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_525 = _this;
  _this setDir 0.34791028;
  _this setPos [9596.2783, 13754.972, 3.0517578e-005];
};

_vehicle_526 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Sign", [9601.1055, 13761.664, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_526 = _this;
  _this setDir -82.716812;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9601.1055, 13761.664, 0.00015258789];
};

_vehicle_541 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_TankSmall2", [9587.502, 13763.22, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_541 = _this;
  _this setPos [9587.502, 13763.22, -6.1035156e-005];
};

_vehicle_542 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7961.0059, 14754.521, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_542 = _this;
  _this setDir 92.57428;
  _this setPos [7961.0059, 14754.521, 0.00021362305];
};

_vehicle_547 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_12", [7948.853, 14754.875, 0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_547 = _this;
  _this setDir 91.485817;
  _this setPos [7948.853, 14754.875, 0.00033569336];
};

_vehicle_550 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [9592.9873, 13751.837, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_550 = _this;
  _this setPos [9592.9873, 13751.837, -7.6293945e-006];
};

_vehicle_552 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [9593.2832, 13757.413, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_552 = _this;
  _this setPos [9593.2832, 13757.413, -6.1035156e-005];
};

_vehicle_553 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [9638.9961, 13831.009, -1.2174318], [], 0, "CAN_COLLIDE"];
  _vehicle_553 = _this;
  _this setDir 214.53062;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9638.9961, 13831.009, -1.2174318];
};

_vehicle_564 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [9570.9053, 13729.718, -0.87839359], [], 0, "CAN_COLLIDE"];
  _vehicle_564 = _this;
  _this setDir 89.797562;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9570.9053, 13729.718, -0.87839359];
};

_vehicle_565 = objNull;
if (true) then
{
  _this = createVehicle ["RampConcrete", [9874.1895, 14033.778, -0.4414297], [], 0, "CAN_COLLIDE"];
  _vehicle_565 = _this;
  _this setDir 74.027649;
  _this setPos [9874.1895, 14033.778, -0.4414297];
};

_vehicle_566 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [9566.8604, 13857.968, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_566 = _this;
  _this setDir -87.358902;
  _this setPos [9566.8604, 13857.968, 2.2888184e-005];
};

_vehicle_570 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [9554.4434, 13858.491, -0.00022125244], [], 0, "CAN_COLLIDE"];
  _vehicle_570 = _this;
  _this setDir 92.060875;
  _this setPos [9554.4434, 13858.491, -0.00022125244];
};

_vehicle_591 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [9697.8203, 13900.121, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_591 = _this;
  _this setDir -36.849312;
  _this setPos [9697.8203, 13900.121, 0.0002746582];
};

_vehicle_593 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [9681.2305, 13887.399, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_593 = _this;
  _this setDir -29.015112;
  _this setPos [9681.2305, 13887.399, 3.8146973e-005];
};

_vehicle_595 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [9677.1465, 13902.368, 0.00029754639], [], 0, "CAN_COLLIDE"];
  _vehicle_595 = _this;
  _this setDir 20.872831;
  _this setPos [9677.1465, 13902.368, 0.00029754639];
};

_vehicle_596 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [9678.335, 13892.699, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_596 = _this;
  _this setDir -36.697159;
  _this setPos [9678.335, 13892.699, 0.00036621094];
};

_vehicle_597 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [9684.8496, 13908.475, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_597 = _this;
  _this setDir 80.071632;
  _this setPos [9684.8496, 13908.475, -1.5258789e-005];
};

_vehicle_644 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2W", [9685.9512, 13899.905, 0.00019073486], [], 0, "CAN_COLLIDE"];
  _vehicle_644 = _this;
  _this setPos [9685.9512, 13899.905, 0.00019073486];
};

_vehicle_645 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9673.9053, 13909.453, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_645 = _this;
  _this setPos [9673.9053, 13909.453, 7.6293945e-006];
};

_vehicle_646 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9678.583, 13912.795, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_646 = _this;
  _this setDir 128.17964;
  _this setPos [9678.583, 13912.795, 3.8146973e-005];
};

_vehicle_652 = objNull;
if (true) then
{
  _this = createVehicle ["WoodStairs_DZ", [9604.0557, 13793.101, -1.7629606], [], 0, "CAN_COLLIDE"];
  _vehicle_652 = _this;
  _this setDir 183.46678;
  _this setPos [9604.0557, 13793.101, -1.7629606];
};

_vehicle_653 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9638.1553, 13855.031, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_653 = _this;
  _this setPos [9638.1553, 13855.031, 0.00030517578];
};

_vehicle_654 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9624.7412, 13841.308, 0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_654 = _this;
  _this setPos [9624.7412, 13841.308, 0.00028991699];
};

_vehicle_655 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9612.8623, 13825.43, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_655 = _this;
  _this setPos [9612.8623, 13825.43, 7.6293945e-006];
};

_vehicle_656 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9552.8203, 13799.355, 0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_656 = _this;
  _this setPos [9552.8203, 13799.355, 0.00020599365];
};

_vehicle_657 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9556.3916, 13799.34, -0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_657 = _this;
  _this setPos [9556.3916, 13799.34, -0.00020599365];
};

_vehicle_658 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9533.1191, 13778.851, -0.00017547607], [], 0, "CAN_COLLIDE"];
  _vehicle_658 = _this;
  _this setPos [9533.1191, 13778.851, -0.00017547607];
};

_vehicle_660 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9529.4551, 13779.344, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_660 = _this;
  _this setPos [9529.4551, 13779.344, -7.6293945e-005];
};

_vehicle_662 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9539.874, 13800.622, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_662 = _this;
  _this setPos [9539.874, 13800.622, -0.00015258789];
};

_vehicle_663 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9542.4805, 13799.512, -0.00035095215], [], 0, "CAN_COLLIDE"];
  _vehicle_663 = _this;
  _this setPos [9542.4805, 13799.512, -0.00035095215];
};

_vehicle_664 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9548.167, 13817.024, -0.0001449585], [], 0, "CAN_COLLIDE"];
  _vehicle_664 = _this;
  _this setPos [9548.167, 13817.024, -0.0001449585];
};

_vehicle_665 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9550.626, 13815.667, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_665 = _this;
  _this setPos [9550.626, 13815.667, -7.6293945e-006];
};

_vehicle_666 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9558.0381, 13829.662, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_666 = _this;
  _this setPos [9558.0381, 13829.662, -0.00018310547];
};

_vehicle_667 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9559.8477, 13826.53, -0.00011444092], [], 0, "CAN_COLLIDE"];
  _vehicle_667 = _this;
  _this setPos [9559.8477, 13826.53, -0.00011444092];
};

_vehicle_669 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9576.2607, 13864.803, 8.392334e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_669 = _this;
  _this setPos [9576.2607, 13864.803, 8.392334e-005];
};

_vehicle_670 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9572.6699, 13864.648, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_670 = _this;
  _this setPos [9572.6699, 13864.648, 0.00012207031];
};

_vehicle_671 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9585.8066, 13890.313, 0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_671 = _this;
  _this setPos [9585.8066, 13890.313, 0.00020599365];
};

_vehicle_672 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9582.1318, 13891.796, 8.392334e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_672 = _this;
  _this setPos [9582.1318, 13891.796, 8.392334e-005];
};

_vehicle_673 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9639.7617, 13884.542, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_673 = _this;
  _this setPos [9639.7617, 13884.542, 1.5258789e-005];
};

_vehicle_675 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9643.6348, 13882.856, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_675 = _this;
  _this setPos [9643.6348, 13882.856, -9.1552734e-005];
};

_vehicle_676 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9640.0215, 13880.638, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_676 = _this;
  _this setPos [9640.0215, 13880.638, 1.5258789e-005];
};

_vehicle_681 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9633.9033, 13833.688, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_681 = _this;
  _this setPos [9633.9033, 13833.688, -7.6293945e-006];
};

_vehicle_682 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9630.6289, 13835.735, 0.00011444092], [], 0, "CAN_COLLIDE"];
  _vehicle_682 = _this;
  _this setPos [9630.6289, 13835.735, 0.00011444092];
};

_vehicle_683 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9620.9854, 13814.677, -3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_683 = _this;
  _this setPos [9620.9854, 13814.677, -3.8146973e-005];
};

_vehicle_684 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9618.0693, 13815.769, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_684 = _this;
  _this setPos [9618.0693, 13815.769, -9.1552734e-005];
};

_vehicle_685 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9605.4688, 13792.704, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_685 = _this;
  _this setPos [9605.4688, 13792.704, 5.3405762e-005];
};

_vehicle_686 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9571.3916, 13735.659, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_686 = _this;
  _this setPos [9571.3916, 13735.659, -9.1552734e-005];
};

_vehicle_689 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9627.9307, 13893.611, 0.00038909912], [], 0, "CAN_COLLIDE"];
  _vehicle_689 = _this;
  _this setPos [9627.9307, 13893.611, 0.00038909912];
};

_vehicle_692 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [9560.4756, 13739.273, -0.0002822876], [], 0, "CAN_COLLIDE"];
  _vehicle_692 = _this;
  _this setDir 180.35493;
  _this setPos [9560.4756, 13739.273, -0.0002822876];
};

_vehicle_693 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [9560.4707, 13720.692, 0.00016021729], [], 0, "CAN_COLLIDE"];
  _vehicle_693 = _this;
  _this setPos [9560.4707, 13720.692, 0.00016021729];
};

_vehicle_694 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9564.7881, 13817.439, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_694 = _this;
  _this setPos [9564.7881, 13817.439, -0.0001373291];
};

_vehicle_697 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [9560.3584, 13733.087, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_697 = _this;
  _this setDir -180.63477;
  _this setPos [9560.3584, 13733.087, 2.2888184e-005];
};

_vehicle_703 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta2", [9599.6357, 13760.015, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_703 = _this;
  _this setPos [9599.6357, 13760.015, -9.1552734e-005];
};

_vehicle_705 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Crates_stack_EP1", [9591.918, 13761.841, -0.056814142], [], 0, "CAN_COLLIDE"];
  _vehicle_705 = _this;
  _this setPos [9591.918, 13761.841, -0.056814142];
};

_vehicle_706 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_TyreHeapEP1", [9582.9072, 13757.512, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_706 = _this;
  _this setDir -74.187569;
  _this setPos [9582.9072, 13757.512, -1.5258789e-005];
};

_vehicle_707 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [9591.4834, 13763.178, -0.045606073], [], 0, "CAN_COLLIDE"];
  _vehicle_707 = _this;
  _this setPos [9591.4834, 13763.178, -0.045606073];
};

_vehicle_709 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Garb_Heap_EP1", [9599.8164, 13763.232, 0.00011444092], [], 0, "CAN_COLLIDE"];
  _vehicle_709 = _this;
  _this setPos [9599.8164, 13763.232, 0.00011444092];
};

_vehicle_714 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [9591.8633, 13747.938, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_714 = _this;
  _this setPos [9591.8633, 13747.938, 2.2888184e-005];
};

_vehicle_715 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_long", [9589.4375, 13755.056, -8.392334e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_715 = _this;
  _this setPos [9589.4375, 13755.056, -8.392334e-005];
};

_vehicle_716 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel5", [9575.9512, 13750.939, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_716 = _this;
  _this setPos [9575.9512, 13750.939, 2.2888184e-005];
};

_vehicle_717 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel3", [9575.5752, 13751.958, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_717 = _this;
  _this setPos [9575.5752, 13751.958, 0.0001373291];
};

_vehicle_719 = objNull;
if (true) then
{
  _this = createVehicle ["SKODAWreck", [9588.9902, 13750.879, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_719 = _this;
  _this setDir -58.687572;
  _this setPos [9588.9902, 13750.879, 6.1035156e-005];
};

_vehicle_720 = objNull;
if (true) then
{
  _this = createVehicle ["Axe_woodblock", [9555.4688, 13787.59, -0.00016021729], [], 0, "CAN_COLLIDE"];
  _vehicle_720 = _this;
  _this setPos [9555.4688, 13787.59, -0.00016021729];
};

_vehicle_728 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [9559.8916, 13841.522, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_728 = _this;
  _this setDir -57.795277;
  _this setPos [9559.8916, 13841.522, -0.00015258789];
};

_vehicle_729 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [9553.0107, 13790.411, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_729 = _this;
  _this setDir -61.443016;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9553.0107, 13790.411, 5.3405762e-005];
};

_vehicle_730 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Barrel_water", [9572.3711, 13816.722, -0.00011444092], [], 0, "CAN_COLLIDE"];
  _vehicle_730 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9572.3711, 13816.722, -0.00011444092];
};

_vehicle_735 = objNull;
if (true) then
{
  _this = createVehicle ["Land_bags_stack_EP1", [9561.0156, 13842.742, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_735 = _this;
  _this setPos [9561.0156, 13842.742, 1.5258789e-005];
};

_vehicle_738 = objNull;
if (true) then
{
  _this = createVehicle ["Fence_corrugated_plate", [9596.5029, 13767.557, 0.12511866], [], 0, "CAN_COLLIDE"];
  _vehicle_738 = _this;
  _this setDir 187.42274;
  _this setPos [9596.5029, 13767.557, 0.12511866];
};

_vehicle_739 = objNull;
if (true) then
{
  _this = createVehicle ["Fence_corrugated_plate", [9600.4482, 13766.991, 0.2890828], [], 0, "CAN_COLLIDE"];
  _vehicle_739 = _this;
  _this setDir -171.94453;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9600.4482, 13766.991, 0.2890828];
};

_vehicle_740 = objNull;
if (true) then
{
  _this = createVehicle ["Fence_corrugated_plate", [9591.5742, 13767.812, 0.17031865], [], 0, "CAN_COLLIDE"];
  _vehicle_740 = _this;
  _this setDir 176.25833;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9591.5742, 13767.812, 0.17031865];
};

_vehicle_741 = objNull;
if (true) then
{
  _this = createVehicle ["Fence_corrugated_plate", [9587.6719, 13767.699, -0.022899529], [], 0, "CAN_COLLIDE"];
  _vehicle_741 = _this;
  _this setDir 179.74477;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9587.6719, 13767.699, -0.022899529];
};

_vehicle_744 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9633.0439, 13871.538, 0.011312434], [], 0, "CAN_COLLIDE"];
  _vehicle_744 = _this;
  _this setDir 137.82939;
  _this setPos [9633.0439, 13871.538, 0.011312434];
};

_vehicle_745 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9631.2246, 13869.847, 0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_745 = _this;
  _this setDir 137.36703;
  _this setPos [9631.2246, 13869.847, 0.00033569336];
};

_vehicle_747 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9627.6963, 13866.425, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_747 = _this;
  _this setDir -223.67265;
  _this setPos [9627.6963, 13866.425, 4.5776367e-005];
};

_vehicle_748 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9626.0156, 13864.725, 0.0001449585], [], 0, "CAN_COLLIDE"];
  _vehicle_748 = _this;
  _this setDir 134.18956;
  _this setPos [9626.0156, 13864.725, 0.0001449585];
};

_vehicle_749 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9624.2393, 13862.899, 0.0001449585], [], 0, "CAN_COLLIDE"];
  _vehicle_749 = _this;
  _this setDir 134.64534;
  _this setPos [9624.2393, 13862.899, 0.0001449585];
};

_vehicle_750 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9622.5, 13861.128, 0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_750 = _this;
  _this setDir 133.82166;
  _this setPos [9622.5, 13861.128, 0.00020599365];
};

_vehicle_751 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9620.8682, 13859.316, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_751 = _this;
  _this setDir 132.15891;
  _this setPos [9620.8682, 13859.316, 1.5258789e-005];
};

_vehicle_752 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9620.9063, 13857.483, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_752 = _this;
  _this setDir 43.266914;
  _this setPos [9620.9063, 13857.483, -4.5776367e-005];
};

_vehicle_753 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9622.7422, 13855.806, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_753 = _this;
  _this setDir 42.324398;
  _this setPos [9622.7422, 13855.806, 0.00012207031];
};

_vehicle_755 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9626.5313, 13852.551, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_755 = _this;
  _this setDir 41.836845;
  _this setPos [9626.5313, 13852.551, 3.0517578e-005];
};

_vehicle_756 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9628.3799, 13850.872, 0.00017547607], [], 0, "CAN_COLLIDE"];
  _vehicle_756 = _this;
  _this setDir 42.837936;
  _this setPos [9628.3799, 13850.872, 0.00017547607];
};

_vehicle_757 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1", [9630.2139, 13849.179, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_757 = _this;
  _this setDir 42.856289;
  _this setPos [9630.2139, 13849.179, 0.0001373291];
};

_vehicle_760 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1b", [9624.6533, 13854.149, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_760 = _this;
  _this setDir 42.399891;
  _this setPos [9624.6533, 13854.149, 0.00015258789];
};

_vehicle_761 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood_sloupek", [9631.1406, 13848.35, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_761 = _this;
  _this setDir -43.077625;
  _this setPos [9631.1406, 13848.35, -0.00012207031];
};

_vehicle_763 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Plot_Wood1b", [9629.4922, 13868.142, 0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_763 = _this;
  _this setDir 135.27089;
  _this setPos [9629.4922, 13868.142, 0.00028991699];
};

_vehicle_772 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4", [9591.0781, 13812.866, -0.34178343], [], 0, "CAN_COLLIDE"];
  _vehicle_772 = _this;
  _this setDir -56.42638;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9591.0781, 13812.866, -0.34178343];
};

_vehicle_773 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4_D", [9588.8828, 13809.512, -0.2053607], [], 0, "CAN_COLLIDE"];
  _vehicle_773 = _this;
  _this setDir -57.503307;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9588.8828, 13809.512, -0.2053607];
};

_vehicle_776 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_Pole", [9592.3408, 13814.591, -0.15941392], [], 0, "CAN_COLLIDE"];
  _vehicle_776 = _this;
  _this setDir -57.560646;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9592.3408, 13814.591, -0.15941392];
};

_vehicle_777 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4_2", [9586.7285, 13806.175, -0.12537628], [], 0, "CAN_COLLIDE"];
  _vehicle_777 = _this;
  _this setDir -57.084053;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9586.7285, 13806.175, -0.12537628];
};

_vehicle_779 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4", [9584.5693, 13802.823, -0.12532964], [], 0, "CAN_COLLIDE"];
  _vehicle_779 = _this;
  _this setDir -56.747494;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9584.5693, 13802.823, -0.12532964];
};

_vehicle_780 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4", [9582.4023, 13799.496, -0.14815702], [], 0, "CAN_COLLIDE"];
  _vehicle_780 = _this;
  _this setDir -57.142944;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9582.4023, 13799.496, -0.14815702];
};

_vehicle_781 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4", [9580.3359, 13796.193, -0.22954483], [], 0, "CAN_COLLIDE"];
  _vehicle_781 = _this;
  _this setDir -57.675304;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9580.3359, 13796.193, -0.22954483];
};

_vehicle_784 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9597.627, 13851.837, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_784 = _this;
  _this setPos [9597.627, 13851.837, 0.00016784668];
};

_vehicle_786 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [9602.9033, 13849.387, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_786 = _this;
  _this setPos [9602.9033, 13849.387, 6.1035156e-005];
};

_vehicle_787 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9608.2549, 13855.222, -0.00035858154], [], 0, "CAN_COLLIDE"];
  _vehicle_787 = _this;
  _this setPos [9608.2549, 13855.222, -0.00035858154];
};

_vehicle_789 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9604.0928, 13865.707, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_789 = _this;
  _this setPos [9604.0928, 13865.707, 3.0517578e-005];
};

_vehicle_792 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9597.1748, 13843.622, 0.24429378], [], 0, "CAN_COLLIDE"];
  _vehicle_792 = _this;
  _this setPos [9597.1748, 13843.622, 0.24429378];
};

_vehicle_793 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [9600.0654, 13859.182, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_793 = _this;
  _this setPos [9600.0654, 13859.182, -3.0517578e-005];
};

_vehicle_797 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Coil_EP1", [9596.6279, 13783.133, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_797 = _this;
  _this setPos [9596.6279, 13783.133, -7.6293945e-006];
};

_vehicle_798 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_GContainer_Big", [9591.1182, 13780.623, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_798 = _this;
  _this setDir -36.532887;
  _this setPos [9591.1182, 13780.623, -9.1552734e-005];
};

_vehicle_800 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta2", [9595.5615, 13779.185, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_800 = _this;
  _this setPos [9595.5615, 13779.185, -7.6293945e-006];
};

_vehicle_801 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Barrel_empty", [9594.54, 13780.45, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_801 = _this;
  _this setPos [9594.54, 13780.45, -7.6293945e-005];
};

_vehicle_804 = objNull;
if (true) then
{
  _this = createVehicle ["Land_tires_EP1", [9593.6143, 13784.372, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_804 = _this;
  _this setPos [9593.6143, 13784.372, -7.6293945e-006];
};

_vehicle_805 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_crates_EP1", [9592.3623, 13778.578, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_805 = _this;
  _this setDir -37.20916;
  _this setPos [9592.3623, 13778.578, 2.2888184e-005];
};

_vehicle_810 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_ConcOutlet_EP1", [9599.4404, 13779.924, -6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_810 = _this;
  _this setDir -16.645386;
  _this setPos [9599.4404, 13779.924, -6.8664551e-005];
};

_vehicle_811 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassDryLong", [9594.3672, 13779.446, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_811 = _this;
  _this setPos [9594.3672, 13779.446, 1.5258789e-005];
};

_vehicle_812 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassDryLong", [9595.0215, 13781.663, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_812 = _this;
  _this setPos [9595.0215, 13781.663, -4.5776367e-005];
};

_vehicle_813 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [9596.5029, 13781.365, -0.00016021729], [], 0, "CAN_COLLIDE"];
  _vehicle_813 = _this;
  _this setPos [9596.5029, 13781.365, -0.00016021729];
};

_vehicle_814 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [9593.3916, 13780.036, -0.00023651123], [], 0, "CAN_COLLIDE"];
  _vehicle_814 = _this;
  _this setPos [9593.3916, 13780.036, -0.00023651123];
};

_vehicle_815 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [9590.6289, 13778.218, -6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_815 = _this;
  _this setPos [9590.6289, 13778.218, -6.8664551e-005];
};

_vehicle_816 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [9594.8584, 13784.53, -5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_816 = _this;
  _this setPos [9594.8584, 13784.53, -5.3405762e-005];
};

_vehicle_817 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassDryLong", [9594.1729, 13783.682, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_817 = _this;
  _this setPos [9594.1729, 13783.682, 6.8664551e-005];
};

_vehicle_818 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassDryLong", [9594.5264, 13781.785, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_818 = _this;
  _this setPos [9594.5264, 13781.785, 2.2888184e-005];
};

_vehicle_823 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_WellPump", [9617.4238, 13837.659, -9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_823 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9617.4238, 13837.659, -9.9182129e-005];
};

_vehicle_824 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_AirCond_big", [9564.5283, 13810.196, 0.18198206], [], 0, "CAN_COLLIDE"];
  _vehicle_824 = _this;
  _this setDir 68.125168;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9564.5283, 13810.196, 0.18198206];
};

_vehicle_825 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [9567.9033, 13812.017, -1.8325013], [], 0, "CAN_COLLIDE"];
  _vehicle_825 = _this;
  _this setDir -23.78846;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9567.9033, 13812.017, -1.8325013];
};

_vehicle_827 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_psi_bouda", [9578.5576, 13809.065, -0.056750894], [], 0, "CAN_COLLIDE"];
  _vehicle_827 = _this;
  _this setDir 150.94351;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9578.5576, 13809.065, -0.056750894];
};

_vehicle_829 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_2_EP1", [9578.2568, 13808.898, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_829 = _this;
  _this setPos [9578.2568, 13808.898, -7.6293945e-005];
};

_vehicle_836 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Church_01", [9791.0459, 13952.278, 0.30960509], [], 0, "CAN_COLLIDE"];
  _vehicle_836 = _this;
  _this setDir 150.38293;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9791.0459, 13952.278, 0.30960509];
};

_vehicle_837 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [9861.1172, 14012.153, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_837 = _this;
  _this setDir -127.93756;
  _this setPos [9861.1172, 14012.153, 0.00022888184];
};

_vehicle_838 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [9785.376, 13954.804, -6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_838 = _this;
  _this setPos [9785.376, 13954.804, -6.8664551e-005];
};

_vehicle_839 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9586.3047, 13887.632, 0.00016021729], [], 0, "CAN_COLLIDE"];
  _vehicle_839 = _this;
  _this setPos [9586.3047, 13887.632, 0.00016021729];
};

_vehicle_840 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9588.3398, 13892.009, 0.00025177002], [], 0, "CAN_COLLIDE"];
  _vehicle_840 = _this;
  _this setPos [9588.3398, 13892.009, 0.00025177002];
};

_vehicle_841 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9557.1621, 13808.806, -0.00038146973], [], 0, "CAN_COLLIDE"];
  _vehicle_841 = _this;
  _this setPos [9557.1621, 13808.806, -0.00038146973];
};

_vehicle_842 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9558.8076, 13807.465, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_842 = _this;
  _this setDir -41.477501;
  _this setPos [9558.8076, 13807.465, -7.6293945e-006];
};

_vehicle_843 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9561.9268, 13805.093, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_843 = _this;
  _this setDir -18.303698;
  _this setPos [9561.9268, 13805.093, 6.1035156e-005];
};

_vehicle_844 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9560.292, 13806.552, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_844 = _this;
  _this setDir 49.396423;
  _this setPos [9560.292, 13806.552, 0.00018310547];
};

_vehicle_845 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9563.6387, 13803.732, -0.00023651123], [], 0, "CAN_COLLIDE"];
  _vehicle_845 = _this;
  _this setDir -142.7505;
  _this setPos [9563.6387, 13803.732, -0.00023651123];
};

_vehicle_846 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9564.7217, 13802.547, -0.00029754639], [], 0, "CAN_COLLIDE"];
  _vehicle_846 = _this;
  _this setPos [9564.7217, 13802.547, -0.00029754639];
};

_vehicle_847 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9566.1045, 13801.398, -0.00026702881], [], 0, "CAN_COLLIDE"];
  _vehicle_847 = _this;
  _this setDir 38.712009;
  _this setPos [9566.1045, 13801.398, -0.00026702881];
};

_vehicle_848 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9567.6035, 13799.627, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_848 = _this;
  _this setDir -15.644153;
  _this setPos [9567.6035, 13799.627, -6.1035156e-005];
};

_vehicle_849 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [9569.4961, 13797.974, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_849 = _this;
  _this setDir -109.20473;
  _this setPos [9569.4961, 13797.974, -4.5776367e-005];
};

_vehicle_852 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [9565.792, 13815.022, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_852 = _this;
  _this setPos [9565.792, 13815.022, -7.6293945e-005];
};

_vehicle_853 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [9570.1211, 13816.625, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_853 = _this;
  _this setPos [9570.1211, 13816.625, -0.00015258789];
};

_vehicle_854 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [9571.2646, 13817.172, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_854 = _this;
  _this setDir -97.286011;
  _this setPos [9571.2646, 13817.172, -0.00024414063];
};

_vehicle_858 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [9564.2666, 13814.244, -0.00017547607], [], 0, "CAN_COLLIDE"];
  _vehicle_858 = _this;
  _this setDir 187.35411;
  _this setPos [9564.2666, 13814.244, -0.00017547607];
};

_vehicle_864 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Garb_Heap_EP1", [9599.248, 13826.605, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_864 = _this;
  _this setPos [9599.248, 13826.605, -6.1035156e-005];
};

_vehicle_868 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [9548.2178, 13802.271, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_868 = _this;
  _this setDir -65.832382;
  _this setPos [9548.2178, 13802.271, -1.5258789e-005];
};

_vehicle_869 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [9521.3945, 13815.212, -0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_869 = _this;
  _this setDir 113.22013;
  _this setPos [9521.3945, 13815.212, -0.00022888184];
};

_vehicle_874 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_0_2000", [9542.6602, 13804.871, -0.00031280518], [], 0, "CAN_COLLIDE"];
  _vehicle_874 = _this;
  _this setDir -63.609409;
  _this setPos [9542.6602, 13804.871, -0.00031280518];
};

_vehicle_875 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [9342.377, 13797.464, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_875 = _this;
  _this setPos [9342.377, 13797.464, 3.0517578e-005];
};

_vehicle_876 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9318.8789, 13799.892, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_876 = _this;
  _this setPos [9318.8789, 13799.892, 0.00012207031];
};

_vehicle_877 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9357.5576, 13761.769, 0.0001449585], [], 0, "CAN_COLLIDE"];
  _vehicle_877 = _this;
  _this setPos [9357.5576, 13761.769, 0.0001449585];
};

_vehicle_878 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9291.3379, 13807.972, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_878 = _this;
  _this setDir -25.651312;
  _this setPos [9291.3379, 13807.972, -2.2888184e-005];
};

_vehicle_879 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [9256.0508, 13785.189, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_879 = _this;
  _this setPos [9256.0508, 13785.189, 0.00018310547];
};

_vehicle_880 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [9272.7891, 13809.821, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_880 = _this;
  _this setPos [9272.7891, 13809.821, 5.3405762e-005];
};

_vehicle_881 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [9229.5938, 13812.759, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_881 = _this;
  _this setPos [9229.5938, 13812.759, 3.8146973e-005];
};

_vehicle_882 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9215.3105, 13810.778, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_882 = _this;
  _this setPos [9215.3105, 13810.778, -2.2888184e-005];
};

_vehicle_883 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9146.5205, 13797.305, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_883 = _this;
  _this setPos [9146.5205, 13797.305, 3.8146973e-005];
};

_vehicle_884 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9043.6523, 13861.242, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_884 = _this;
  _this setPos [9043.6523, 13861.242, -0.00015258789];
};

_vehicle_885 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [8974.3066, 13938.044, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_885 = _this;
  _this setPos [8974.3066, 13938.044, 0.00019836426];
};

_vehicle_886 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [8939.9297, 13955.496, -0.00012969971], [], 0, "CAN_COLLIDE"];
  _vehicle_886 = _this;
  _this setPos [8939.9297, 13955.496, -0.00012969971];
};

_vehicle_887 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [8900.2334, 13986.736, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_887 = _this;
  _this setPos [8900.2334, 13986.736, 3.0517578e-005];
};

_vehicle_888 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9171.9912, 13819.152, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_888 = _this;
  _this setPos [9171.9912, 13819.152, -0.00010681152];
};

_vehicle_889 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [8798.291, 14043.215, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_889 = _this;
  _this setPos [8798.291, 14043.215, 6.8664551e-005];
};

_vehicle_890 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [8806.3574, 14033.105, 0.00011444092], [], 0, "CAN_COLLIDE"];
  _vehicle_890 = _this;
  _this setPos [8806.3574, 14033.105, 0.00011444092];
};

_vehicle_891 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [8816.3076, 14026.543, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_891 = _this;
  _this setPos [8816.3076, 14026.543, 9.1552734e-005];
};

_vehicle_892 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [8826.5244, 14107.431, -0.00022125244], [], 0, "CAN_COLLIDE"];
  _vehicle_892 = _this;
  _this setPos [8826.5244, 14107.431, -0.00022125244];
};

_vehicle_893 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9227.4561, 13769.049, 0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_893 = _this;
  _this setPos [9227.4561, 13769.049, 0.00020599365];
};

_vehicle_894 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9124.7236, 13771.819, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_894 = _this;
  _this setPos [9124.7236, 13771.819, 0.00015258789];
};

_vehicle_895 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9067.3398, 13789.543, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_895 = _this;
  _this setPos [9067.3398, 13789.543, 6.8664551e-005];
};

_vehicle_896 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9188.9932, 13788.644, 8.392334e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_896 = _this;
  _this setPos [9188.9932, 13788.644, 8.392334e-005];
};

_vehicle_897 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [9447.3037, 13850.425, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_897 = _this;
  _this setPos [9447.3037, 13850.425, -0.00024414063];
};

_vehicle_906 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2W", [9199.2969, 13815.056, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_906 = _this;
  _this setPos [9199.2969, 13815.056, 3.8146973e-005];
};

_vehicle_912 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_FallenTree1", [9078.1172, 13813.586, 0.00016021729], [], 0, "CAN_COLLIDE"];
  _vehicle_912 = _this;
  _this setDir -37.768589;
  _this setPos [9078.1172, 13813.586, 0.00016021729];
};

_vehicle_914 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula1f", [9026.8105, 13877.953, 9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_914 = _this;
  _this setPos [9026.8105, 13877.953, 9.9182129e-005];
};

_vehicle_916 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2s", [8835.3857, 14042.766, 0.00022125244], [], 0, "CAN_COLLIDE"];
  _vehicle_916 = _this;
  _this setPos [8835.3857, 14042.766, 0.00022125244];
};

_vehicle_920 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [9582.0928, 13794.7, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_920 = _this;
  _this setPos [9582.0928, 13794.7, 1.5258789e-005];
};

_vehicle_921 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin2", [9583.9619, 13796.827, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_921 = _this;
  _this setPos [9583.9619, 13796.827, 0.00015258789];
};

_vehicle_922 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin2", [9583.9297, 13794.977, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_922 = _this;
  _this setPos [9583.9297, 13794.977, 3.8146973e-005];
};

_vehicle_923 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [9583.2549, 13793.771, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_923 = _this;
  _this setPos [9583.2549, 13793.771, -6.1035156e-005];
};

_vehicle_924 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [9584.959, 13796.448, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_924 = _this;
  _this setPos [9584.959, 13796.448, 0.00010681152];
};

_vehicle_925 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [9585.4131, 13795.163, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_925 = _this;
  _this setPos [9585.4131, 13795.163, -1.5258789e-005];
};

_vehicle_926 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpkin", [9583.9121, 13793.671, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_926 = _this;
  _this setPos [9583.9121, 13793.671, 7.6293945e-006];
};

_vehicle_928 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_sorbus2s", [9581.3408, 13878.837, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_928 = _this;
  _this setPos [9581.3408, 13878.837, -2.2888184e-005];
};

_vehicle_929 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_salix2s", [9579.374, 13850.365, 9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_929 = _this;
  _this setPos [9579.374, 13850.365, 9.9182129e-005];
};

_vehicle_932 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9584.7363, 13792.119, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_932 = _this;
  _this setDir 77.479538;
  _this setPos [9584.7363, 13792.119, -0.00010681152];
};

_vehicle_933 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9584.9111, 13793.177, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_933 = _this;
  _this setDir -81.678123;
  _this setPos [9584.9111, 13793.177, 5.3405762e-005];
};

_vehicle_934 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9585.8926, 13793.974, -0.0001449585], [], 0, "CAN_COLLIDE"];
  _vehicle_934 = _this;
  _this setDir -125.1969;
  _this setPos [9585.8926, 13793.974, -0.0001449585];
};

_vehicle_935 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9585.9658, 13795.571, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_935 = _this;
  _this setDir -164.00813;
  _this setPos [9585.9658, 13795.571, 0.00012207031];
};

_vehicle_936 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9585.707, 13791.715, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_936 = _this;
  _this setDir -59.815945;
  _this setPos [9585.707, 13791.715, 5.3405762e-005];
};

_vehicle_937 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9586.2773, 13793.007, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_937 = _this;
  _this setPos [9586.2773, 13793.007, 7.6293945e-005];
};

_vehicle_938 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9587.3496, 13794.55, -6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_938 = _this;
  _this setDir -34.844196;
  _this setPos [9587.3496, 13794.55, -6.8664551e-005];
};

_vehicle_939 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9587.0996, 13796.031, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_939 = _this;
  _this setDir 29.868591;
  _this setPos [9587.0996, 13796.031, -2.2888184e-005];
};

_vehicle_940 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9587.167, 13792.346, 0.01093279], [], 0, "CAN_COLLIDE"];
  _vehicle_940 = _this;
  _this setDir -144.97575;
  _this setPos [9587.167, 13792.346, 0.01093279];
};

_vehicle_941 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9587.9346, 13794.02, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_941 = _this;
  _this setPos [9587.9346, 13794.02, -1.5258789e-005];
};

_vehicle_942 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9588.3926, 13795.518, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_942 = _this;
  _this setDir -59.461979;
  _this setPos [9588.3926, 13795.518, -0.00012207031];
};

_vehicle_943 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9586.7998, 13793.892, -9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_943 = _this;
  _this setDir -140.83408;
  _this setPos [9586.7998, 13793.892, -9.9182129e-005];
};

_vehicle_944 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_p_Helianthus", [9586.7471, 13795.528, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_944 = _this;
  _this setDir -24.970625;
  _this setPos [9586.7471, 13795.528, 3.0517578e-005];
};

_vehicle_945 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_0_2000", [8732.8955, 14363.018, -0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_945 = _this;
  _this setDir 42.638954;
  _this setPos [8732.8955, 14363.018, -0.00022888184];
};

_vehicle_949 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_25", [8643.0742, 14278.642, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_949 = _this;
  _this setDir 52.282776;
  _this setPos [8643.0742, 14278.642, -4.5776367e-005];
};

_vehicle_951 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8722.543, 14349.173, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_951 = _this;
  _this setDir 31.943605;
  _this setPos [8722.543, 14349.173, -3.0517578e-005];
};

_vehicle_952 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8722.7813, 14349.42, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_952 = _this;
  _this setDir 209.54872;
  _this setPos [8722.7813, 14349.42, 0.00024414063];
};

_vehicle_953 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8703.0557, 14321.128, -0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_953 = _this;
  _this setDir 29.315315;
  _this setPos [8703.0557, 14321.128, -0.00022888184];
};

_vehicle_954 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8703.2754, 14321.277, -0.00035095215], [], 0, "CAN_COLLIDE"];
  _vehicle_954 = _this;
  _this setDir 210.17384;
  _this setPos [8703.2754, 14321.277, -0.00035095215];
};

_vehicle_959 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_30_25", [8693.4873, 14307.199, 0.0006942749], [], 0, "CAN_COLLIDE"];
  _vehicle_959 = _this;
  _this setDir -137.29562;
  _this setPos [8693.4873, 14307.199, 0.0006942749];
};

_vehicle_960 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_30_25", [8662.832, 14293.533, -0.00094604492], [], 0, "CAN_COLLIDE"];
  _vehicle_960 = _this;
  _this setDir -305.94125;
  _this setPos [8662.832, 14293.533, -0.00094604492];
};

_vehicle_961 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_50", [8682.8047, 14300.426, -0.00049591064], [], 0, "CAN_COLLIDE"];
  _vehicle_961 = _this;
  _this setDir -110.20333;
  _this setPos [8682.8047, 14300.426, -0.00049591064];
};

_vehicle_962 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_25", [8643.4736, 14278.646, -0.00025177002], [], 0, "CAN_COLLIDE"];
  _vehicle_962 = _this;
  _this setDir -125.52352;
  _this setPos [8643.4736, 14278.646, -0.00025177002];
};

_vehicle_963 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8610.3945, 14252.604, -0.00029754639], [], 0, "CAN_COLLIDE"];
  _vehicle_963 = _this;
  _this setDir 43.318638;
  _this setPos [8610.3945, 14252.604, -0.00029754639];
};

_vehicle_965 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_12", [8601.708, 14244.289, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_965 = _this;
  _this setDir 46.93782;
  _this setPos [8601.708, 14244.289, -1.5258789e-005];
};

_vehicle_970 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_30_25", [8595.0889, 14233.854, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_970 = _this;
  _this setDir 19.257294;
  _this setPos [8595.0889, 14233.854, -1.5258789e-005];
};

_vehicle_972 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_30_25", [8595.2119, 14234.257, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_972 = _this;
  _this setDir -161.70116;
  _this setPos [8595.2119, 14234.257, -7.6293945e-005];
};

_vehicle_973 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8576.6123, 14210.715, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_973 = _this;
  _this setDir -322.78992;
  _this setPos [8576.6123, 14210.715, -0.00010681152];
};

_vehicle_975 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_6konec", [8558.9639, 14187.52, 8.392334e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_975 = _this;
  _this setDir 39.761971;
  _this setPos [8558.9639, 14187.52, 8.392334e-005];
};

_vehicle_976 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_25", [8561.832, 14190.853, 0.0002822876], [], 0, "CAN_COLLIDE"];
  _vehicle_976 = _this;
  _this setDir 36.373806;
  _this setPos [8561.832, 14190.853, 0.0002822876];
};

_vehicle_977 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8744.6211, 14375.637, -0.00071716309], [], 0, "CAN_COLLIDE"];
  _vehicle_977 = _this;
  _this setDir 43.405994;
  _this setPos [8744.6211, 14375.637, -0.00071716309];
};

_vehicle_978 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8757.3662, 14387.076, -0.00076293945], [], 0, "CAN_COLLIDE"];
  _vehicle_978 = _this;
  _this setDir 53.656105;
  _this setPos [8757.3662, 14387.076, -0.00076293945];
};

_vehicle_979 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8772.0117, 14396.094, -0.00035095215], [], 0, "CAN_COLLIDE"];
  _vehicle_979 = _this;
  _this setDir 65.106979;
  _this setPos [8772.0117, 14396.094, -0.00035095215];
};

_vehicle_980 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8788.1426, 14401.979, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_980 = _this;
  _this setDir 75.683823;
  _this setPos [8788.1426, 14401.979, -0.00012207031];
};

_vehicle_981 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8805.1748, 14404.796, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_981 = _this;
  _this setDir 85.93306;
  _this setPos [8805.1748, 14404.796, -6.1035156e-005];
};

_vehicle_982 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_25", [8822.457, 14404.486, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_982 = _this;
  _this setDir 94.22744;
  _this setPos [8822.457, 14404.486, -1.5258789e-005];
};

_vehicle_983 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [9641.3496, 13831.672, -0.80789655], [], 0, "CAN_COLLIDE"];
  _vehicle_983 = _this;
  _this setDir 125.62467;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9641.3496, 13831.672, -0.80789655];
};

_vehicle_987 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_25", [8847.1689, 14402.691, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_987 = _this;
  _this setDir 95.063416;
  _this setPos [8847.1689, 14402.691, -0.00018310547];
};

_vehicle_988 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_25", [8871.8633, 14400.534, 0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_988 = _this;
  _this setDir 94.57534;
  _this setPos [8871.8633, 14400.534, 0.00025939941];
};

_vehicle_989 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8914.1006, 14398.454, -0.00073242188], [], 0, "CAN_COLLIDE"];
  _vehicle_989 = _this;
  _this setDir -94.456177;
  _this setPos [8914.1006, 14398.454, -0.00073242188];
};

_vehicle_990 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8931.0361, 14401.284, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_990 = _this;
  _this setDir -104.69434;
  _this setPos [8931.0361, 14401.284, 9.1552734e-005];
};

_vehicle_991 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8930.7256, 14401.15, -0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_991 = _this;
  _this setDir 75.724197;
  _this setPos [8930.7256, 14401.15, -0.00025939941];
};

_vehicle_992 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_100", [8947.7275, 14404.021, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_992 = _this;
  _this setDir 86.363548;
  _this setPos [8947.7275, 14404.021, -0.0001373291];
};

_vehicle_994 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_60_10", [8964.9941, 14403.529, -0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_994 = _this;
  _this setDir 95.94735;
  _this setPos [8964.9941, 14403.529, -0.00019836426];
};

_vehicle_995 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_0_2000", [8980.5664, 14382.146, -0.00038146973], [], 0, "CAN_COLLIDE"];
  _vehicle_995 = _this;
  _this setDir -25.618574;
  _this setPos [8980.5664, 14382.146, -0.00038146973];
};

_vehicle_996 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_6konec", [8983.1436, 14376.567, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_996 = _this;
  _this setDir -24.70063;
  _this setPos [8983.1436, 14376.567, -0.00015258789];
};

_vehicle_999 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_telek1", [8991.666, 14384.53, -0.3648954], [], 0, "CAN_COLLIDE"];
  _vehicle_999 = _this;
  _this setDir -26.269855;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8991.666, 14384.53, -0.3648954];
};

_vehicle_1014 = objNull;
if (true) then
{
  _this = createVehicle ["PowerGenerator_EP1", [8994.6152, 14384.8, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1014 = _this;
  _this setDir -24.358788;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8994.6152, 14384.8, -3.0517578e-005];
};

_vehicle_1022 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5", [8997.7227, 14382.851, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1022 = _this;
  _this setDir 68.038864;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8997.7227, 14382.851, -4.5776367e-005];
};

_vehicle_1023 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5", [8995.875, 14387.466, -0.11342188], [], 0, "CAN_COLLIDE"];
  _vehicle_1023 = _this;
  _this setDir 68.992561;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8995.875, 14387.466, -0.11342188];
};

_vehicle_1024 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5", [8992.6299, 14389.057, -0.44495106], [], 0, "CAN_COLLIDE"];
  _vehicle_1024 = _this;
  _this setDir -18.320219;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8992.6299, 14389.057, -0.44495106];
};

_vehicle_1025 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5", [8988.0098, 14387.325, -0.53480911], [], 0, "CAN_COLLIDE"];
  _vehicle_1025 = _this;
  _this setDir -21.139292;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8988.0098, 14387.325, -0.53480911];
};

_vehicle_1027 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5", [8986.6807, 14384.068, -0.54628825], [], 0, "CAN_COLLIDE"];
  _vehicle_1027 = _this;
  _this setDir -111.63661;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8986.6807, 14384.068, -0.54628825];
};

_vehicle_1028 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5", [8988.457, 14379.442, -0.22379978], [], 0, "CAN_COLLIDE"];
  _vehicle_1028 = _this;
  _this setDir -108.57123;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8988.457, 14379.442, -0.22379978];
};

_vehicle_1029 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5", [8991.5479, 14378.107, 0.13606396], [], 0, "CAN_COLLIDE"];
  _vehicle_1029 = _this;
  _this setDir 155.52989;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8991.5479, 14378.107, 0.13606396];
};

_vehicle_1032 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5", [8996.2305, 14379.889, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1032 = _this;
  _this setDir -195.21703;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8996.2305, 14379.889, -7.6293945e-005];
};

_vehicle_1056 = objNull;
if (true) then
{
  _this = createVehicle ["dum_rasovna", [8327.4395, 14382.175, 0.31758359], [], 0, "CAN_COLLIDE"];
  _vehicle_1056 = _this;
  _this setDir -54.698761;
  _this setPos [8327.4395, 14382.175, 0.31758359];
};

_vehicle_1068 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_carpinus2s", [8314.1543, 14384.133, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_1068 = _this;
  _this setDir -54.211544;
  _this setPos [8314.1543, 14384.133, -0.00018310547];
};

_vehicle_1069 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2s", [8335.6797, 14381.302, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1069 = _this;
  _this setPos [8335.6797, 14381.302, 6.8664551e-005];
};

_vehicle_1077 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [9605.1572, 13897.291, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1077 = _this;
  _this setPos [9605.1572, 13897.291, -2.2888184e-005];
};

_vehicle_1078 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [9602.2168, 13902.118, -0.13669868], [], 0, "CAN_COLLIDE"];
  _vehicle_1078 = _this;
  _this setDir -127.8009;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [9602.2168, 13902.118, -0.13669868];
};

_vehicle_1081 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [9605.5498, 13744.027, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1081 = _this;
  _this setDir 0.43326989;
  _this setPos [9605.5498, 13744.027, -7.6293945e-005];
};

};

