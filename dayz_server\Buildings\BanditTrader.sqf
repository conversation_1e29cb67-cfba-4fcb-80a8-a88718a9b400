if (isServer) then {

_vehicle_0 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Wall_Gate_Ind1_L", [1626.9729, 7783.7725], [], 0, "CAN_COLLIDE"];
  _vehicle_0 = _this;
  _this setDir -57.960144;
  _this setPos [1626.9729, 7783.7725];
};

_vehicle_2 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Wall_Gate_Ind1_R", [1626.9581, 7783.8027, 0.037100784], [], 0, "CAN_COLLIDE"];
  _vehicle_2 = _this;
  _this setDir -58.162891;
  _this setPos [1626.9581, 7783.8027, 0.037100784];
};

_vehicle_3 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1629.7881, 7788.4727], [], 0, "CAN_COLLIDE"];
  _vehicle_3 = _this;
  _this setDir -53.380955;
  _this setPos [1629.7881, 7788.4727];
};

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1631.1312, 7790.5889], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir -58.066414;
  _this setPos [1631.1312, 7790.5889];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1632.4697, 7792.6182, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setDir 123.62859;
  _this setPos [1632.4697, 7792.6182, 3.0517578e-005];
};

_vehicle_6 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1633.3752, 7794.8364, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_6 = _this;
  _this setDir 99.659836;
  _this setPos [1633.3752, 7794.8364, -3.0517578e-005];
};

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1633.3723, 7797.249], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setDir -97.828522;
  _this setPos [1633.3723, 7797.249];
};

_vehicle_8 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1632.8053, 7799.6318, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_8 = _this;
  _this setDir -110.86166;
  _this setPos [1632.8053, 7799.6318, -3.0517578e-005];
};

_vehicle_9 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1631.7693, 7801.6587, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_9 = _this;
  _this setDir 56.57835;
  _this setPos [1631.7693, 7801.6587, -3.0517578e-005];
};

_vehicle_10 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1630.3252, 7803.5913, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_10 = _this;
  _this setDir 50.859711;
  _this setPos [1630.3252, 7803.5913, -3.0517578e-005];
};

_vehicle_11 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1628.6788, 7805.2642, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_11 = _this;
  _this setDir 40.373013;
  _this setPos [1628.6788, 7805.2642, -3.0517578e-005];
};

_vehicle_12 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1626.7871, 7806.5981, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_12 = _this;
  _this setDir 28.883881;
  _this setPos [1626.7871, 7806.5981, -3.0517578e-005];
};

_vehicle_13 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1624.613, 7807.7065], [], 0, "CAN_COLLIDE"];
  _vehicle_13 = _this;
  _this setDir 27.463621;
  _this setPos [1624.613, 7807.7065];
};

_vehicle_14 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1615.8949, 7810.9639], [], 0, "CAN_COLLIDE"];
  _vehicle_14 = _this;
  _this setDir -3.3118048;
  _this setPos [1615.8949, 7810.9639];
};

_vehicle_15 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1618.2295, 7810.5146], [], 0, "CAN_COLLIDE"];
  _vehicle_15 = _this;
  _this setDir 22.527002;
  _this setPos [1618.2295, 7810.5146];
};

_vehicle_16 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1620.4335, 7809.6392, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_16 = _this;
  _this setDir 21.134714;
  _this setPos [1620.4335, 7809.6392, 6.1035156e-005];
};

_vehicle_17 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1622.5638, 7808.7334], [], 0, "CAN_COLLIDE"];
  _vehicle_17 = _this;
  _this setDir 24.02422;
  _this setPos [1622.5638, 7808.7334];
};

_vehicle_18 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1596.7488, 7791.8174], [], 0, "CAN_COLLIDE"];
  _vehicle_18 = _this;
  _this setDir 93.310349;
  _this setPos [1596.7488, 7791.8174];
};

_vehicle_19 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1596.9606, 7794.2036, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_19 = _this;
  _this setDir -81.782494;
  _this setPos [1596.9606, 7794.2036, -3.0517578e-005];
};

_vehicle_20 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1597.2817, 7796.6016, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_20 = _this;
  _this setDir -83.68222;
  _this setPos [1597.2817, 7796.6016, -6.1035156e-005];
};

_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1597.5394, 7798.9795, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir -84.999908;
  _this setPos [1597.5394, 7798.9795, 3.0517578e-005];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1597.8138, 7801.2637, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir -79.312096;
  _this setPos [1597.8138, 7801.2637, -3.0517578e-005];
};

_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1598.3944, 7803.3457], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir -67.604782;
  _this setPos [1598.3944, 7803.3457];
};

_vehicle_24 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1624.0422, 7779.2588, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_24 = _this;
  _this setDir -52.605072;
  _this setPos [1624.0422, 7779.2588, 3.0517578e-005];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1622.4526, 7777.4766, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir -42.391735;
  _this setPos [1622.4526, 7777.4766, 3.0517578e-005];
};

_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1620.7291, 7775.9141, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setDir -37.308521;
  _this setPos [1620.7291, 7775.9141, 3.0517578e-005];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1618.7869, 7774.6362], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir -29.394289;
  _this setPos [1618.7869, 7774.6362];
};

_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1616.6309, 7773.438, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setDir -29.404501;
  _this setPos [1616.6309, 7773.438, -3.0517578e-005];
};

_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1614.543, 7772.3428, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setDir -25.82172;
  _this setPos [1614.543, 7772.3428, -6.1035156e-005];
};

_vehicle_30 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1612.3496, 7771.6709, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_30 = _this;
  _this setDir -7.8599453;
  _this setPos [1612.3496, 7771.6709, 3.0517578e-005];
};

_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1609.9077, 7771.3101], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir -8.2095509;
  _this setPos [1609.9077, 7771.3101];
};

_vehicle_32 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1607.4419, 7770.9155, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_32 = _this;
  _this setDir -8.5199318;
  _this setPos [1607.4419, 7770.9155, -6.1035156e-005];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1605.161, 7770.5332], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setDir -11.663867;
  _this setPos [1605.161, 7770.5332];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1602.704, 7770.2139], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir -2.1790507;
  _this setPos [1602.704, 7770.2139];
};

_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1598.0117, 7770.4233, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir 0.38137239;
  _this setPos [1598.0117, 7770.4233, 6.1035156e-005];
};

_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [1600.3898, 7770.2627], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir 7.3182831;
  _this setPos [1600.3898, 7770.2627];
};

_vehicle_42 = objNull;
if (true) then
{
  _this = createVehicle ["MASH", [1603.1797, 7799.0156], [], 0, "CAN_COLLIDE"];
  _vehicle_42 = _this;
  _this setDir 86.285904;
  _this setPos [1603.1797, 7799.0156];
};

_vehicle_43 = objNull;
if (true) then
{
  _this = createVehicle ["MASH", [1603.3827, 7792.7686, 0.02160514], [], 0, "CAN_COLLIDE"];
  _vehicle_43 = _this;
  _this setDir 87.143814;
  _this setPos [1603.3827, 7792.7686, 0.02160514];
};

_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [1633.8741, 7785.8101], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setDir 38.764328;
  _this setPos [1633.8741, 7785.8101];
};

_vehicle_48 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [1627.6777, 7776.5728, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_48 = _this;
  _this setDir 35.979343;
  _this setPos [1627.6777, 7776.5728, 3.0517578e-005];
};

_vehicle_49 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_TankSmall2", [1620.9851, 7779.6704], [], 0, "CAN_COLLIDE"];
  _vehicle_49 = _this;
  _this setDir -36.926064;
  _this setPos [1620.9851, 7779.6704];
};

};
