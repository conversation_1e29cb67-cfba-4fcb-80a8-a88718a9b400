// Written by Sigma for Stomping Grounds PvE
sched_sqlnotifier = {
	private ["_query", "_notifs", "_puid", "_player", "_notif_uid"];

	diag_log("Running sqlnotifier");

	if (count playableUnits != 0) then {
		_query = format ["SELECT player_uid, message FROM player_notifications"];
		_notifs = [_query, 2, true] call fn_asyncCall;
		if (count _notifs != 0) then {
			{
				_puid = getPlayerUID _x;
				_player = _x;
				{
					_notif_uid = _x select 0;
					if (_notif_uid == _puid) then {
						diag_log format ["Notifying %1", _puid];
						RemoteMessage = ["systemChat", _x select 1];
						(owner _player) publicVariableClient "RemoteMessage";
						_resetQ = format ["DELETE FROM player_notifications WHERE player_uid = '%1'", _puid];
						_result = [_resetQ, 1, true] call fn_asyncCall;
					}
				} count _notifs;
			} forEach playableUnits;
		};
	};
    
objNull
};