private ["_item","_player","_price","_type","_clientID","_playerUID","_query","_name"];

//PVDZE_sellItem = [_item, player, userprice, _type,_name];
_item =	_this select 0;
_player =_this select 1;
_price = _this select 2;
_type =	_this select 3;
_name =	name _player;
_clientID = owner _player;
_playerUID = getPlayerUID _player;

if (_item == "") then {
	diag_log "[O9 Custom Trading]: No item to sell selected";
	PVDZE_sellItemResult = false;
} else {
	_query = format["INSERT INTO customTrade (PlayerUID, PlayerName, Classname, Cost, Type) VALUES ('%1', '%2', '%3', '%4' ,'%5')",_playerUID,_name,_item,_price,_type];
	[_query, 1, true] call fn_asyncCall;

	PVDZE_sellItemResult = true;

	diag_log format ["[O9 Custom Trading]: Player %4 :(%1) sold %2 for %3 on the marketplace.",_playerUID,_item,[_price] call BIS_fnc_numberText,_name];
};

if (!isNull _player) then {
	_clientID publicVariableClient "PVDZE_sellItemResult";
};
