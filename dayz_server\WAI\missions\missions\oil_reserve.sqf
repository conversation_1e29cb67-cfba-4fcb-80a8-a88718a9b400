local _mission = count WAI_MissionData -1;
local _aiType = _this select 0; // "Bandit" or "Hero"
local _position = [80] call WAI_FindPos;
local _name = "Oil Reserve";
local _startTime = diag_tickTime;
local _difficulty = "Hard";
local _localized = ["STR_CL_MISSION_BANDIT", "STR_CL_MISSION_HERO"] select (_aiType == "Hero");
local _localName = "STR_CUST_WAI_OIL_RESERVE_TITLE";

diag_log format["[WAI]: %1 %2 started at %3.",_aiType,_name,_position];

local _messages = if (_aiType == "Hero") then {
	["STR_CUST_WAI_HERO_OIL_RESERVE_START","STR_CUST_WAI_HERO_OIL_RESERVE_WIN","STR_CUST_WAI_HERO_OIL_RESERVE_FAIL"];
} else {
	["STR_CUST_WAI_BANDIT_OIL_RESERVE_START","STR_CUST_WAI_BANDIT_OIL_RESERVE_WIN","STR_CUST_WAI_BANDIT_OIL_RESERVE_FAIL"];
};

////////////////////// Do not edit this section ///////////////////////////
local _markers = [1,1,1,1];
//[position,createMarker,setMarkerColor,setMarkerType,setMarkerShape,setMarkerBrush,setMarkerSize,setMarkerText,setMarkerAlpha]
_markers set [0, [_position, "WAI" + str(_mission), "ColorYellow", "", "ELLIPSE", "SolidBorder", [300,300], [], 0]];
_markers set [1, [_position, "WAI" + str(_mission) + "dot", "ColorBlue", "mil_dot", "", "", [], [_localName], 0]];
if (WAI_AutoClaim) then {_markers set [2, [_position, "WAI" + str(_mission) + "auto", "ColorBlue", "", "ELLIPSE", "Border", [WAI_AcAlertDistance,WAI_AcAlertDistance], [], 0]];};
DZE_ServerMarkerArray set [count DZE_ServerMarkerArray, _markers]; // Markers added to global array for JIP player requests.
_markerIndex = count DZE_ServerMarkerArray - 1;
PVDZ_ServerMarkerSend = ["start",_markers];
publicVariable "PVDZ_ServerMarkerSend";

WAI_MarkerReady = true;

// Add the mission's position to the global array so that other missions do not spawn near it.
DZE_MissionPositions set [count DZE_MissionPositions, _position];
local _posIndex = count DZE_MissionPositions - 1;

// Send announcement
[_difficulty,(_messages select 0)] call WAI_Message;

// Wait until a player is within range or timeout is reached.
local _timeout = false;
local _claimPlayer = objNull;

while {WAI_WaitForPlayer && !_timeout && {isNull _claimPlayer}} do {
	_claimPlayer = [_position, WAI_TimeoutDist] call isClosestPlayer;
	
	if (diag_tickTime - _startTime >= (WAI_Timeout * 60)) then {
		_timeout = true;
	};
	uiSleep 1;
};

if (_timeout) exitWith {
	[_mission, _aiType, _markerIndex, _posIndex] call WAI_AbortMission;
	[_difficulty,(_messages select 2)] call WAI_Message;
	diag_log format["WAI: %1 %2 aborted.",_aiType,_name,_position];
};
//////////////////////////////// End //////////////////////////////////////

// Spawn Crates
// Th3-Hunter333 Custom Loot Tables
local _loot = if (_aiType == "Hero") then {Loot_OilReserve select 0;} else {Loot_OilReserve select 1;};
// local _loot = if (_aiType == "Hero") then {Loot_AbandonedTrader select 0;} else {Loot_AbandonedTrader select 1;};
[[
	[_loot,WAI_CrateLg,[-0.01,-0.01]]
],_position,_mission] call WAI_SpawnCrate;

// Spawn Objects
[[
	//Buildings 
	["MAP_Ind_TankBig",[-9.5,7,-0.15],0],
	["MAP_Ind_TankBig",[11,4,-0.15],0],
	["MAP_Ind_TankBig",[8,-6,-0.15],0],
	["MAP_Ind_TankBig",[-11,-12,-0.15],0],
	["MAP_Ind_TankBig",[-28.5,-1.2,-0.15],0],
	["MAP_Ind_TankBig",[-26.2,20,-0.15],0],
	["MAP_Ind_TankBig",[-6,28,-0.015],0],
	["GUE_WarfareBVehicleServicePoint",[-30,-15,-0.015],-176.24],
	["GUE_WarfareBVehicleServicePoint",[21,3,-0.015],122.11],
	["GUE_WarfareBVehicleServicePoint",[-7,43,-0.015],-0.11],
	["Barrels",[0.1,6,-0.015],82.81],
	["Barrels",[- 17,15,-0.015],-0.5],
	["Barrels",[18,-2,-0.015],-48],
	["Barrel1",[-16,-2,-0.015],0],
	["Barrel1",[3,6,-0.015],0],
	["Barrel1",[-11.6,18,-0.015],0]
	
],_position,_mission] call WAI_SpawnObjects;

// Group Spawn Examples
// Parameters:	0: Position
//				1: Unit Count
//				2: Unit Skill ("easy","medium","hard","extreme" or "random")
//				3: Primary gun - "Random", "Unarmed", "Classname", Array
//				4: Launcher - "AT", "AA", or "" for no launcher
//				5: Backpack - "Random", "Classname", "none" or Array
//				6: Skin (_aiType, "Random","classname", array)
//				7: Gearset - 0,1,2, or "Random"
//				8: AI Type (_aiType, or ["type", #] format to overwrite default gain amount) ***Used to determine humanity gain or loss***
//				9: Mission variable from line 5 (_mission)

//AI Spawns
local _num = round (random 3) + 4; // You can use this to get a random number of AI between 4 and 7.   
[[(_position select 0) + 9, (_position select 1) + 40, 0],_num,_difficulty,WAI_M16,"AT",WAI_Packs,WAI_NacSoldier,"Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) - 25, (_position select 1) - 21, 0],2,_difficulty,WAI_Sniper,"AT",WAI_PacksAll,WAI_HeroSkin,"Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) + 32, (_position select 1) + 2, 0],3,_difficulty,WAI_M4,"AA",WAI_Packs,WAI_NacSoldier,"Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) + 9, (_position select 1) + 40, 0],3,_difficulty,WAI_M16,"AA",WAI_Packs,WAI_HeroSkin,"Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) - 41, (_position select 1) + 6, 0],3,_difficulty,WAI_LMG,"",WAI_PacksLg,WAI_HeroSkin,"Random",_aiType,_mission] call WAI_SpawnGroup;

//Static Guns
[[
	[(_position select 0) + 6.2, (_position select 1) - 28, 0],
	[(_position select 0) - 26, (_position select 1) + 47, 0]
],"M2StaticMG",_difficulty,_aiType,_aiType,"Random","Random","Random",_mission] call WAI_SpawnStatic;

// Vehicle Patrol
[[(_position select 0) + 100, _position select 1, 0],[(_position select 0) + 100, _position select 1, 0],50,2,"HMMWV_Armored",_difficulty,_aiType,_aiType,_mission] call WAI_VehPatrol;

//Heli Paradrop
[_position,400,"UH1H_DZ","East",[3000,4000],150,1.0,200,10,_difficulty,"Random","","Random",_aiType,"Random",_aiType,true,_mission] spawn WAI_HeliPara;

[
	_mission, // Mission number
	_position, // Position of mission
	_difficulty, // Difficulty
	_name, // Name of Mission
	_localName, // localized marker text
	_aiType, // "Bandit" or "Hero"
	_markerIndex,
	_posIndex,
	_claimPlayer,
	true, // show mission marker?
	true, // make minefields available for this mission
	["crate"], // Completion type: ["crate"], ["kill"], or ["assassinate", _unitGroup],
	_messages
] spawn WAI_MissionMonitor;
