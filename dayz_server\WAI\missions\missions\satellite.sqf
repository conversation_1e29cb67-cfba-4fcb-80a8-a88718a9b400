local _mission = count WAI_MissionData -1;
local _aiType = _this select 0; // "Bandit" or "Hero"
local _position = [80] call WAI_FindPos;
local _name = "Fallen Satellite";
local _startTime = diag_tickTime;
local _difficulty = "Hard";
local _localized = ["STR_CL_MISSION_BANDIT", "STR_CL_MISSION_HERO"] select (_aiType == "Hero");
local _localName = "STR_CUST_WAI_FALLEN_SAT_TITLE";

diag_log format["[WAI]: %1 %2 started at %3.",_aiType,_name,_position];

local _messages = if (_aiType == "Hero") then {
	["STR_CUST_WAI_HERO_FALLEN_SAT_START","STR_CUST_WAI_HERO_FALLEN_SAT_WIN","STR_CUST_WAI_HERO_FALLEN_SAT_FAIL"];
} else {
	["STR_CUST_WAI_BANDIT_FALLEN_SAT_START","STR_CUST_WAI_BANDIT_FALLEN_SAT_WIN","STR_CUST_WAI_BANDIT_FALLEN_SAT_FAIL"];
};

////////////////////// Do not edit this section ///////////////////////////
local _markers = [1,1,1,1];
//[position,createMarker,setMarkerColor,setMarkerType,setMarkerShape,setMarkerBrush,setMarkerSize,setMarkerText,setMarkerAlpha]
_markers set [0, [_position, "WAI" + str(_mission), "ColorRed", "", "ELLIPSE", "Solid", [300,300], [], 0]];
_markers set [1, [_position, "WAI" + str(_mission) + "dot", "ColorBlue", "mil_dot", "", "", [], [_localized,_localName], 0]];
if (WAI_AutoClaim) then {_markers set [2, [_position, "WAI" + str(_mission) + "auto", "ColorBlue", "", "ELLIPSE", "Border", [WAI_AcAlertDistance,WAI_AcAlertDistance], [], 0]];};
DZE_ServerMarkerArray set [count DZE_ServerMarkerArray, _markers]; // Markers added to global array for JIP player requests.
_markerIndex = count DZE_ServerMarkerArray - 1;
PVDZ_ServerMarkerSend = ["start",_markers];
publicVariable "PVDZ_ServerMarkerSend";

WAI_MarkerReady = true;

// Add the mission's position to the global array so that other missions do not spawn near it.
DZE_MissionPositions set [count DZE_MissionPositions, _position];
local _posIndex = count DZE_MissionPositions - 1;

// Send announcement
[_difficulty,(_messages select 0)] call WAI_Message;

// Wait until a player is within range or timeout is reached.
local _timeout = false;
local _claimPlayer = objNull;

while {WAI_WaitForPlayer && !_timeout && {isNull _claimPlayer}} do {
	_claimPlayer = [_position, WAI_TimeoutDist] call isClosestPlayer;
	
	if (diag_tickTime - _startTime >= (WAI_Timeout * 60)) then {
		_timeout = true;
	};
	uiSleep 1;
};

if (_timeout) exitWith {
	[_mission, _aiType, _markerIndex, _posIndex] call WAI_AbortMission;
	[_difficulty,(_messages select 2)] call WAI_Message;
	diag_log format["WAI: %1 %2 aborted.",_aiType,_name,_position];
};
//////////////////////////////// End //////////////////////////////////////

//Spawn Crates
local _loot = if (_aiType == "Hero") then {Loot_TankColumn select 0;} else {Loot_TankColumn select 1;};
[[
	[_loot,WAI_CrateLg,[-0.01,-0.01]]
],_position,_mission] call WAI_SpawnCrate;

// Spawn Objects
[[
	//Buildings 
	["Satelit",[-8.41,3.37,-0.013],0],
	["MAP_rubble_metal_plates_03",[-8.1,- 3.8,0.01],0],
	["MAP_rubble_metal_plates_01",[-6.8,-1.6,-0.015],61.76],
	["Land_Dirthump01",[-7.4,-2.8,-1.96],-2.706],
	["Land_Dirthump01",[-6.6,-2.8,-1.99],-66.12],
	["Land_Fire_barrel_burning",[-6.1-3.8,-0.85],0]
	
	],_position,_mission] call WAI_SpawnObjects;

// Group Spawn Examples
// Parameters:	0: Position
//				1: Unit Count
//				2: Unit Skill ("easy","medium","hard","extreme" or "random")
//				3: Primary gun - "Random", "Unarmed", "Classname", Array
//				4: Launcher - "AT", "AA", or "" for no launcher
//				5: Backpack - "Random", "Classname", "none" or Array
//				6: Skin (_aiType, "Random","classname", array)
//				7: Gearset - 0,1,2, or "Random"
//				8: AI Type (_aiType, or ["type", #] format to overwrite default gain amount) ***Used to determine humanity gain or loss***
//				9: Mission variable from line 5 (_mission)

//AI Spawns
local _num = round (random 3) + 4; // You can use this to get a random number of AI between 4 and 7.   
[[(_position select 0) + 9, (_position select 1) + 40, 0],_num,_difficulty,"Random","AT","Random",WAI_NacSoldier,"Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) - 25, (_position select 1) - 21, 0],3,_difficulty,"Random","AA","Random",WAI_HeroSkin,"Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) + 32, (_position select 1) + 2, 0],4,_difficulty,"Random","","Random",WAI_NacSoldier,"Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) + 9, (_position select 1) + 40, 0],3,_difficulty,"Random","AA","Random",WAI_HeroSkin,"Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) - 41, (_position select 1) + 6, 0],3,_difficulty,"Random","AT","Random",WAI_HeroSkin,"Random",_aiType,_mission] call WAI_SpawnGroup;

//Static Guns
[[
	[(_position select 0) + 6.2, (_position select 1) - 28, 0],
	[(_position select 0) - 26, (_position select 1) + 47, 0]
],"M2StaticMG",_difficulty,_aiType,_aiType,"Random","Random","Random",_mission] call WAI_SpawnStatic;

[
	_mission, // Mission number
	_position, // Position of mission
	_difficulty, // Difficulty
	_name, // Name of Mission
	_localName, // localized marker text
	_aiType, // "Bandit" or "Hero"
	_markerIndex,
	_posIndex,
	_claimPlayer,
	true, // show mission marker?
	true, // make minefields available for this mission
	["crate"], // Completion type: ["crate"], ["kill"], or ["assassinate", _unitGroup],
	_messages
] spawn WAI_MissionMonitor;
