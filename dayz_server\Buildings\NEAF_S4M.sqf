if (isServer) then {

_vehicle_0 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11454.295, 12230.232, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_0 = _this;
  _this setDir -24.234381;
  _this setPos [11454.295, 12230.232, -0.00012207031];
};

_vehicle_2 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11447.252, 12245.924, 0.00035095215], [], 0, "CAN_COLLIDE"];
  _vehicle_2 = _this;
  _this setDir -24.234381;
  _this setPos [11447.252, 12245.924, 0.00035095215];
};

_vehicle_3 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11440.236, 12261.573, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3 = _this;
  _this setDir -24.234381;
  _this setPos [11440.236, 12261.573, 6.1035156e-005];
};

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [11456.9, 12224.573, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir -24.224983;
  _this setPos [11456.9, 12224.573, 0.00018310547];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [11433.094, 12277.49, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setDir -23.392973;
  _this setPos [11433.094, 12277.49, -6.1035156e-005];
};

_vehicle_9 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [11427.7, 12296.287, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_9 = _this;
  _this setDir -8.5496807;
  _this setPos [11427.7, 12296.287, 1.5258789e-005];
};

_vehicle_16 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [11428.724, 12315.696, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_16 = _this;
  _this setDir 14.2953;
  _this setPos [11428.724, 12315.696, 3.0517578e-005];
};

_vehicle_17 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [11434.411, 12332.151, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_17 = _this;
  _this setDir 23.97102;
  _this setPos [11434.411, 12332.151, 0.00010681152];
};

_vehicle_18 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11442.762, 12347.396, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_18 = _this;
  _this setDir 34.643333;
  _this setPos [11442.762, 12347.396, 0.00015258789];
};

_vehicle_20 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_dirt_1", [11828.008, 12788.619, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_20 = _this;
  _this setDir 109.02111;
  _this setPos [11828.008, 12788.619, -3.0517578e-005];
};

_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_dirt_2", [11903.051, 12762.869, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir 109.02111;
  _this setPos [11903.051, 12762.869, 0.00022888184];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_dirt_3", [11978.403, 12737.063, 0.00059509277], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir 109.02111;
  _this setPos [11978.403, 12737.063, 0.00059509277];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton_end1", [11787.255, 12761.912, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir 18.953274;
  _this setPos [11787.255, 12761.912, 4.5776367e-005];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_L_2", [11791.454, 12682.313, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setDir 18.945122;
  _this setPos [11791.454, 12682.313, 3.0517578e-005];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_L_2", [11850.538, 12730.928], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir -160.87627;
  _this setPos [11850.538, 12730.928];
};

_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_L_2_end", [11831.651, 12676.674], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir -160.47411;
  _this setPos [11831.651, 12676.674];
};

_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_L_2_end", [11810.11, 12736.628, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir -340.96005;
  _this setPos [11810.11, 12736.628, 4.5776367e-005];
};

_vehicle_37 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_end33", [11809.199, 12795.147, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_37 = _this;
  _this setDir -70.807915;
  _this setPos [11809.199, 12795.147, -4.5776367e-005];
};

_vehicle_38 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton", [11870.896, 12668.069, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_38 = _this;
  _this setDir 19.089352;
  _this setPos [11870.896, 12668.069, -3.0517578e-005];
};

_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton", [11832.526, 12617.976, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setDir -70.80162;
  _this setPos [11832.526, 12617.976, -1.5258789e-005];
};

_vehicle_40 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton", [11844.597, 12592.473, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_40 = _this;
  _this setDir 19.127775;
  _this setPos [11844.597, 12592.473, 1.5258789e-005];
};

_vehicle_41 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hangar_2", [11867.751, 12648.273, -0.10467908], [], 0, "CAN_COLLIDE"];
  _vehicle_41 = _this;
  _this setDir -250.50026;
  _this setPos [11867.751, 12648.273, -0.10467908];
};

_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hangar_2", [11854.512, 12610.866, -0.30669004], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setDir 108.65707;
  _this setPos [11854.512, 12610.866, -0.30669004];
};

_vehicle_45 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hangar_2", [11893.728, 12723.477, -0.20650396], [], 0, "CAN_COLLIDE"];
  _vehicle_45 = _this;
  _this setDir 108.47755;
  _this setPos [11893.728, 12723.477, -0.20650396];
};

_vehicle_46 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hangar_2", [11880.733, 12686.082, -0.19982216], [], 0, "CAN_COLLIDE"];
  _vehicle_46 = _this;
  _this setDir 109.08341;
  _this setPos [11880.733, 12686.082, -0.19982216];
};

_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["Land_repair_center", [11783.639, 12730.271, -0.18666834], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setDir 343.07758;
  _this setPos [11783.639, 12730.271, -0.18666834];
};

_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_Vysypka", [12041.074, 12793.854, 0.062138889], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setDir -251.13838;
  _this setPos [12041.074, 12793.854, 0.062138889];
};

_vehicle_58 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_Stack_Big", [12021.296, 12819.635, -0.570081], [], 0, "CAN_COLLIDE"];
  _vehicle_58 = _this;
  _this setDir -70.659081;
  _this setPos [12021.296, 12819.635, -0.570081];
};

_vehicle_63 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_GeneralStore_01a_dam", [11782.537, 12757.155, 0.2006668], [], 0, "CAN_COLLIDE"];
  _vehicle_63 = _this;
  _this setDir 74.723259;
  _this setPos [11782.537, 12757.155, 0.2006668];
};

_vehicle_64 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_Office01", [12450.645, 12533.542, -0.24144828], [], 0, "CAN_COLLIDE"];
  _vehicle_64 = _this;
  _this setDir -555.15936;
  _this setPos [12450.645, 12533.542, -0.24144828];
};

_vehicle_66 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_a_stationhouse", [11820.115, 12840.488, 0.17830744], [], 0, "CAN_COLLIDE"];
  _vehicle_66 = _this;
  _this setDir -430.74246;
  _this setPos [11820.115, 12840.488, 0.17830744];
};

_vehicle_72 = objNull;
if (true) then
{
  _this = createVehicle ["Land_SS_hangar", [12334.169, 12655.206, 0.19385341], [], 0, "CAN_COLLIDE"];
  _vehicle_72 = _this;
  _this setDir -250.49821;
  _this setPos [12334.169, 12655.206, 0.19385341];
};

_vehicle_73 = objNull;
if (true) then
{
  _this = createVehicle ["Land_SS_hangar", [12242.912, 12598.474, 0.31050378], [], 0, "CAN_COLLIDE"];
  _vehicle_73 = _this;
  _this setDir -71.547089;
  _this setPos [12242.912, 12598.474, 0.31050378];
};

_vehicle_75 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_ControlTower", [11811.101, 12690.712, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_75 = _this;
  _this setDir 19.084133;
  _this setPos [11811.101, 12690.712, -4.5776367e-005];
};

_vehicle_76 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Mil_Guardhouse", [11832.935, 12733.113, 0.10370234], [], 0, "CAN_COLLIDE"];
  _vehicle_76 = _this;
  _this setDir -69.997963;
  _this setPos [11832.935, 12733.113, 0.10370234];
};

_vehicle_77 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_House", [11772.026, 12708.694, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_77 = _this;
  _this setDir -250.91646;
  _this setPos [11772.026, 12708.694, 0.00015258789];
};

_vehicle_79 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [11823.69, 12716.677, 0.10214376], [], 0, "CAN_COLLIDE"];
  _vehicle_79 = _this;
  _this setDir -71.31031;
  _this setPos [11823.69, 12716.677, 0.10214376];
};

_vehicle_80 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [11829.339, 12631.049, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_80 = _this;
  _this setDir -520.03583;
  _this setPos [11829.339, 12631.049, 0.00015258789];
};

_vehicle_81 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [11781.145, 12621.933, 0.068480819], [], 0, "CAN_COLLIDE"];
  _vehicle_81 = _this;
  _this setDir -70.684845;
  _this setPos [11781.145, 12621.933, 0.068480819];
};

_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [11820.657, 12607.997, 0.023520449], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setDir -70.542671;
  _this setPos [11820.657, 12607.997, 0.023520449];
};

_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [11801.121, 12614.945, 0.1099462], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setDir -71.072571;
  _this setPos [11801.121, 12614.945, 0.1099462];
};

_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [11760.776, 12628.945, 0.232227], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setDir -70.583809;
  _this setPos [11760.776, 12628.945, 0.232227];
};

_vehicle_85 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [11766.486, 12650.505, 0.010785072], [], 0, "CAN_COLLIDE"];
  _vehicle_85 = _this;
  _this setDir 18.876339;
  _this setPos [11766.486, 12650.505, 0.010785072];
};

_vehicle_92 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Mil_House", [12329.203, 12569.594, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_92 = _this;
  _this setDir -71.048454;
  _this setPos [12329.203, 12569.594, 0.00030517578];
};

_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [11813.984, 12720.15, 0.22243296], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setDir -71.126526;
  _this setPos [11813.984, 12720.15, 0.22243296];
};

_vehicle_94 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [11833.339, 12713.323, 0.15725639], [], 0, "CAN_COLLIDE"];
  _vehicle_94 = _this;
  _this setDir -70.813454;
  _this setPos [11833.339, 12713.323, 0.15725639];
};

_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11820.051, 12721.946, 0.021681448], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setDir 19.00351;
  _this setPos [11820.051, 12721.946, 0.021681448];
};

_vehicle_99 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCrossingT", [11822.2, 12728.065, -0.079668075], [], 0, "CAN_COLLIDE"];
  _vehicle_99 = _this;
  _this setDir 19.708967;
  _this setPos [11822.2, 12728.065, -0.079668075];
};

_vehicle_101 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11829.78, 12718.65, -0.045510259], [], 0, "CAN_COLLIDE"];
  _vehicle_101 = _this;
  _this setDir 19.136557;
  _this setPos [11829.78, 12718.65, -0.045510259];
};

_vehicle_104 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCrossingT", [11831.854, 12724.672, -0.079167053], [], 0, "CAN_COLLIDE"];
  _vehicle_104 = _this;
  _this setDir 19.577648;
  _this setPos [11831.854, 12724.672, -0.079167053];
};

_vehicle_105 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCorner", [11841.419, 12721.386, -0.010804966], [], 0, "CAN_COLLIDE"];
  _vehicle_105 = _this;
  _this setDir 18.459385;
  _this setPos [11841.419, 12721.386, -0.010804966];
};

_vehicle_106 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCorner", [11812.477, 12731.591, -0.012241242], [], 0, "CAN_COLLIDE"];
  _vehicle_106 = _this;
  _this setDir -70.676178;
  _this setPos [11812.477, 12731.591, -0.012241242];
};

_vehicle_107 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearMiddle", [11825.882, 12726.769, -0.068736203], [], 0, "CAN_COLLIDE"];
  _vehicle_107 = _this;
  _this setDir -70.995636;
  _this setPos [11825.882, 12726.769, -0.068736203];
};

_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearMiddle", [11835.641, 12723.378, -0.091050588], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setDir 108.28654;
  _this setPos [11835.641, 12723.378, -0.091050588];
};

_vehicle_109 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearMiddle", [11818.441, 12729.419, -0.091251135], [], 0, "CAN_COLLIDE"];
  _vehicle_109 = _this;
  _this setDir -70.300331;
  _this setPos [11818.441, 12729.419, -0.091251135];
};

_vehicle_112 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearMiddle", [11828.259, 12725.932, -0.068103455], [], 0, "CAN_COLLIDE"];
  _vehicle_112 = _this;
  _this setDir -70.325691;
  _this setPos [11828.259, 12725.932, -0.068103455];
};

_vehicle_113 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearMiddle", [11837.651, 12722.656, 0.00059368648], [], 0, "CAN_COLLIDE"];
  _vehicle_113 = _this;
  _this setDir -70.655746;
  _this setPos [11837.651, 12722.656, 0.00059368648];
};

_vehicle_114 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11839.331, 12715.247, 0.045590807], [], 0, "CAN_COLLIDE"];
  _vehicle_114 = _this;
  _this setDir 18.932642;
  _this setPos [11839.331, 12715.247, 0.045590807];
};

_vehicle_116 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11836.818, 12707.91, 0.093775161], [], 0, "CAN_COLLIDE"];
  _vehicle_116 = _this;
  _this setDir 18.594021;
  _this setPos [11836.818, 12707.91, 0.093775161];
};

_vehicle_117 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCorner", [11834.79, 12701.76, -0.10611616], [], 0, "CAN_COLLIDE"];
  _vehicle_117 = _this;
  _this setDir 108.25794;
  _this setPos [11834.79, 12701.76, -0.10611616];
};

_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCrossingT", [11825.283, 12705.081, 0.033107843], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setDir -160.19521;
  _this setPos [11825.283, 12705.081, 0.033107843];
};

_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCrossingT", [11815.45, 12708.481, 0.021298142], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setDir -161.57979;
  _this setPos [11815.45, 12708.481, 0.021298142];
};

_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCrossingT", [11805.648, 12711.785, 0.034846243], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir -70.996201;
  _this setPos [11805.648, 12711.785, 0.034846243];
};

_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11810.357, 12725.455, -0.011255471], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setDir 19.055079;
  _this setPos [11810.357, 12725.455, -0.011255471];
};

_vehicle_124 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11816.851, 12712.595, 0.011851083], [], 0, "CAN_COLLIDE"];
  _vehicle_124 = _this;
  _this setDir 18.532492;
  _this setPos [11816.851, 12712.595, 0.011851083];
};

_vehicle_125 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11821.584, 12706.39, 0.022446727], [], 0, "CAN_COLLIDE"];
  _vehicle_125 = _this;
  _this setDir -70.801628;
  _this setPos [11821.584, 12706.39, 0.022446727];
};

_vehicle_126 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11827.354, 12711.311, 0.068308182], [], 0, "CAN_COLLIDE"];
  _vehicle_126 = _this;
  _this setDir 18.623621;
  _this setPos [11827.354, 12711.311, 0.068308182];
};

_vehicle_127 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11831.422, 12702.948, 0.011231196], [], 0, "CAN_COLLIDE"];
  _vehicle_127 = _this;
  _this setDir -70.847366;
  _this setPos [11831.422, 12702.948, 0.011231196];
};

_vehicle_128 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11807.771, 12717.944, -0.011098085], [], 0, "CAN_COLLIDE"];
  _vehicle_128 = _this;
  _this setDir 19.153395;
  _this setPos [11807.771, 12717.944, -0.011098085];
};

_vehicle_129 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11811.748, 12709.719, 0.012332406], [], 0, "CAN_COLLIDE"];
  _vehicle_129 = _this;
  _this setDir -71.36245;
  _this setPos [11811.748, 12709.719, 0.012332406];
};

_vehicle_130 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [11803.589, 12705.655, 0.011549216], [], 0, "CAN_COLLIDE"];
  _vehicle_130 = _this;
  _this setDir 18.621336;
  _this setPos [11803.589, 12705.655, 0.011549216];
};

_vehicle_132 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCrossingT", [11800.904, 12697.696, -0.00086931954], [], 0, "CAN_COLLIDE"];
  _vehicle_132 = _this;
  _this setDir -162.10947;
  _this setPos [11800.904, 12697.696, -0.00086931954];
};

_vehicle_133 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearMiddle", [11802.17, 12701.467, -0.010805486], [], 0, "CAN_COLLIDE"];
  _vehicle_133 = _this;
  _this setDir 18.644739;
  _this setPos [11802.17, 12701.467, -0.010805486];
};

_vehicle_134 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearMiddle", [11804.743, 12696.446, -0.021426305], [], 0, "CAN_COLLIDE"];
  _vehicle_134 = _this;
  _this setDir 108.05357;
  _this setPos [11804.743, 12696.446, -0.021426305];
};

_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearMiddle", [11814.61, 12730.834, -0.071350522], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir -69.517342;
  _this setPos [11814.61, 12730.834, -0.071350522];
};

_vehicle_136 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkShortEnd", [11798.046, 12698.648, 0.0023145839], [], 0, "CAN_COLLIDE"];
  _vehicle_136 = _this;
  _this setDir -71.631393;
  _this setPos [11798.046, 12698.648, 0.0023145839];
};

_vehicle_137 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearShort", [11799.173, 12698.265, 0.0035641058], [], 0, "CAN_COLLIDE"];
  _vehicle_137 = _this;
  _this setDir 109.14684;
  _this setPos [11799.173, 12698.265, 0.0035641058];
};

_vehicle_152 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_L_2", [11848.299, 12823.922, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_152 = _this;
  _this setDir -250.83609;
  _this setPos [11848.299, 12823.922, 1.5258789e-005];
};

_vehicle_154 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_draha", [11921.407, 12809.256, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_154 = _this;
  _this setDir -70.859978;
  _this setPos [11921.407, 12809.256, 4.5776367e-005];
};

_vehicle_155 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_draha", [11996.824, 12782.95], [], 0, "CAN_COLLIDE"];
  _vehicle_155 = _this;
  _this setDir -70.820076;
  _this setPos [11996.824, 12782.95];
};

_vehicle_156 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_draha", [12072.452, 12756.774, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_156 = _this;
  _this setDir -71.066597;
  _this setPos [12072.452, 12756.774, 3.0517578e-005];
};

_vehicle_157 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_draha", [12147.939, 12730.671, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_157 = _this;
  _this setDir -70.913574;
  _this setPos [12147.939, 12730.671, 4.5776367e-005];
};

_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_draha", [12223.409, 12704.384, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setDir -70.56131;
  _this setPos [12223.409, 12704.384, -3.0517578e-005];
};

_vehicle_162 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_main_40", [12451.135, 12573.205, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_162 = _this;
  _this setDir -70.817711;
  _this setPos [12451.135, 12573.205, 3.0517578e-005];
};

_vehicle_163 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_main", [12204.708, 12658.738, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_163 = _this;
  _this setDir -70.954193;
  _this setPos [12204.708, 12658.738, 9.1552734e-005];
};

_vehicle_164 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_main", [12129.386, 12684.774, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_164 = _this;
  _this setDir -70.809776;
  _this setPos [12129.386, 12684.774, 4.5776367e-005];
};

_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_dirt_1", [12204.624, 12658.771, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setDir 109.10841;
  _this setPos [12204.624, 12658.771, -3.0517578e-005];
};

_vehicle_166 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_dirt_2", [12280.124, 12632.63], [], 0, "CAN_COLLIDE"];
  _vehicle_166 = _this;
  _this setDir 109.03307;
  _this setPos [12280.124, 12632.63];
};

_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_dirt_3", [12355.73, 12606.716, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setDir 109.26426;
  _this setPos [12355.73, 12606.716, -3.0517578e-005];
};

_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_end15", [12468.977, 12567.249, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setDir -70.957443;
  _this setPos [12468.977, 12567.249, 7.6293945e-005];
};

_vehicle_170 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_L_2_end", [12278.507, 12674.471, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_170 = _this;
  _this setDir 109.23837;
  _this setPos [12278.507, 12674.471, 3.0517578e-005];
};

_vehicle_171 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_L_2_end", [12300.279, 12583.518, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_171 = _this;
  _this setDir -71.151993;
  _this setPos [12300.279, 12583.518, -9.1552734e-005];
};

_vehicle_172 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11756.176, 12623.542, -0.19262698], [], 0, "CAN_COLLIDE"];
  _vehicle_172 = _this;
  _this setDir -160.64572;
  _this setPos [11756.176, 12623.542, -0.19262698];
};

_vehicle_175 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11910.174, 12739.406, 0.0121886], [], 0, "CAN_COLLIDE"];
  _vehicle_175 = _this;
  _this setDir -161.2249;
  _this setPos [11910.174, 12739.406, 0.0121886];
};

_vehicle_176 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11782.844, 12613.94, -0.032725599], [], 0, "CAN_COLLIDE"];
  _vehicle_176 = _this;
  _this setDir 19.770123;
  _this setPos [11782.844, 12613.94, -0.032725599];
};

_vehicle_177 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11788.533, 12611.996, -0.14716074], [], 0, "CAN_COLLIDE"];
  _vehicle_177 = _this;
  _this setDir 19.209669;
  _this setPos [11788.533, 12611.996, -0.14716074];
};

_vehicle_178 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11898.625, 12743.306], [], 0, "CAN_COLLIDE"];
  _vehicle_178 = _this;
  _this setDir -161.55487;
  _this setPos [11898.625, 12743.306];
};

_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11904.452, 12741.384], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setDir -161.40358;
  _this setPos [11904.452, 12741.384];
};

_vehicle_180 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11915.995, 12737.439, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_180 = _this;
  _this setDir -161.61229;
  _this setPos [11915.995, 12737.439, -1.5258789e-005];
};

_vehicle_181 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11892.895, 12745.232, -0.11547012], [], 0, "CAN_COLLIDE"];
  _vehicle_181 = _this;
  _this setDir -161.11897;
  _this setPos [11892.895, 12745.232, -0.11547012];
};

_vehicle_182 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11914.178, 12731.521], [], 0, "CAN_COLLIDE"];
  _vehicle_182 = _this;
  _this setDir -71.338554;
  _this setPos [11914.178, 12731.521];
};

_vehicle_183 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11912.207, 12725.808, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_183 = _this;
  _this setDir -70.144516;
  _this setPos [11912.207, 12725.808, 7.6293945e-005];
};

_vehicle_184 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11908.16, 12714.295, -0.022920059], [], 0, "CAN_COLLIDE"];
  _vehicle_184 = _this;
  _this setDir -70.699707;
  _this setPos [11908.16, 12714.295, -0.022920059];
};

_vehicle_185 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11904.145, 12702.772, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_185 = _this;
  _this setDir -70.832069;
  _this setPos [11904.145, 12702.772, 3.0517578e-005];
};

_vehicle_186 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11910.177, 12720.072, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_186 = _this;
  _this setDir -70.805397;
  _this setPos [11910.177, 12720.072, 1.5258789e-005];
};

_vehicle_187 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11906.13, 12708.532, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_187 = _this;
  _this setDir -70.289711;
  _this setPos [11906.13, 12708.532, 1.5258789e-005];
};

_vehicle_188 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11902.138, 12696.984], [], 0, "CAN_COLLIDE"];
  _vehicle_188 = _this;
  _this setDir -70.851501;
  _this setPos [11902.138, 12696.984];
};

_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11900.144, 12691.159, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setDir -70.74527;
  _this setPos [11900.144, 12691.159, -9.1552734e-005];
};

_vehicle_190 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11898.104, 12685.443, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_190 = _this;
  _this setDir -70.356148;
  _this setPos [11898.104, 12685.443, 0.00010681152];
};

_vehicle_191 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11896.138, 12679.661, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_191 = _this;
  _this setDir -70.834534;
  _this setPos [11896.138, 12679.661, 0.0001373291];
};

_vehicle_192 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11894.215, 12673.896, -0.044857703], [], 0, "CAN_COLLIDE"];
  _vehicle_192 = _this;
  _this setDir -71.28405;
  _this setPos [11894.215, 12673.896, -0.044857703];
};

_vehicle_193 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11892.191, 12668.188, -0.02264707], [], 0, "CAN_COLLIDE"];
  _vehicle_193 = _this;
  _this setDir -70.887947;
  _this setPos [11892.191, 12668.188, -0.02264707];
};

_vehicle_194 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11890.184, 12662.45, -0.057265244], [], 0, "CAN_COLLIDE"];
  _vehicle_194 = _this;
  _this setDir -70.806488;
  _this setPos [11890.184, 12662.45, -0.057265244];
};

_vehicle_195 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11888.194, 12656.666, -0.057938807], [], 0, "CAN_COLLIDE"];
  _vehicle_195 = _this;
  _this setDir -70.763489;
  _this setPos [11888.194, 12656.666, -0.057938807];
};

_vehicle_196 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11886.179, 12650.937, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_196 = _this;
  _this setDir -70.752357;
  _this setPos [11886.179, 12650.937, 7.6293945e-005];
};

_vehicle_197 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11884.179, 12645.131, -0.034472514], [], 0, "CAN_COLLIDE"];
  _vehicle_197 = _this;
  _this setDir -70.921303;
  _this setPos [11884.179, 12645.131, -0.034472514];
};

_vehicle_198 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11882.103, 12639.373, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_198 = _this;
  _this setDir -70.574486;
  _this setPos [11882.103, 12639.373, 1.5258789e-005];
};

_vehicle_199 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11880.037, 12633.695, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_199 = _this;
  _this setDir -70.15686;
  _this setPos [11880.037, 12633.695, 0.00010681152];
};

_vehicle_200 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11878.11, 12627.979, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_200 = _this;
  _this setDir -70.902687;
  _this setPos [11878.11, 12627.979, 0.00024414063];
};

_vehicle_201 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11876.145, 12622.205, 0.029507803], [], 0, "CAN_COLLIDE"];
  _vehicle_201 = _this;
  _this setDir -71.006531;
  _this setPos [11876.145, 12622.205, 0.029507803];
};

_vehicle_202 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11874.143, 12616.47, -0.024677105], [], 0, "CAN_COLLIDE"];
  _vehicle_202 = _this;
  _this setDir -70.705238;
  _this setPos [11874.143, 12616.47, -0.024677105];
};

_vehicle_203 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11872.24, 12610.642, -0.11318775], [], 0, "CAN_COLLIDE"];
  _vehicle_203 = _this;
  _this setDir -71.195061;
  _this setPos [11872.24, 12610.642, -0.11318775];
};

_vehicle_204 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11870.203, 12604.833, -0.33465576], [], 0, "CAN_COLLIDE"];
  _vehicle_204 = _this;
  _this setDir -70.534416;
  _this setPos [11870.203, 12604.833, -0.33465576];
};

_vehicle_205 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11868.177, 12599.03, -0.60326844], [], 0, "CAN_COLLIDE"];
  _vehicle_205 = _this;
  _this setDir -70.801353;
  _this setPos [11868.177, 12599.03, -0.60326844];
};

_vehicle_206 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11866.291, 12593.255, -0.52630109], [], 0, "CAN_COLLIDE"];
  _vehicle_206 = _this;
  _this setDir -71.025002;
  _this setPos [11866.291, 12593.255, -0.52630109];
};

_vehicle_207 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11864.259, 12587.482, -0.39463595], [], 0, "CAN_COLLIDE"];
  _vehicle_207 = _this;
  _this setDir -70.406609;
  _this setPos [11864.259, 12587.482, -0.39463595];
};

_vehicle_208 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11856.887, 12588.029, -0.32930472], [], 0, "CAN_COLLIDE"];
  _vehicle_208 = _this;
  _this setDir 18.520729;
  _this setPos [11856.887, 12588.029, -0.32930472];
};

_vehicle_209 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11750.408, 12625.362, 0.053571563], [], 0, "CAN_COLLIDE"];
  _vehicle_209 = _this;
  _this setDir -69.294312;
  _this setPos [11750.408, 12625.362, 0.053571563];
};

_vehicle_210 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11762.47, 12621.046, -0.092896186], [], 0, "CAN_COLLIDE"];
  _vehicle_210 = _this;
  _this setDir 18.129101;
  _this setPos [11762.47, 12621.046, -0.092896186];
};

_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11768.34, 12619.044, -0.14873396], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setDir 18.50868;
  _this setPos [11768.34, 12619.044, -0.14873396];
};

_vehicle_212 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11828.238, 12598.038, -0.10294963], [], 0, "CAN_COLLIDE"];
  _vehicle_212 = _this;
  _this setDir 20.175596;
  _this setPos [11828.238, 12598.038, -0.10294963];
};

_vehicle_213 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11851.097, 12590.103, -0.10505453], [], 0, "CAN_COLLIDE"];
  _vehicle_213 = _this;
  _this setDir 19.600857;
  _this setPos [11851.097, 12590.103, -0.10505453];
};

_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11845.527, 12592.161, 0.080275796], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setDir 19.776066;
  _this setPos [11845.527, 12592.161, 0.080275796];
};

_vehicle_215 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11839.816, 12594.06, -0.17221078], [], 0, "CAN_COLLIDE"];
  _vehicle_215 = _this;
  _this setDir 19.200359;
  _this setPos [11839.816, 12594.06, -0.17221078];
};

_vehicle_216 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11834.036, 12596.046, -0.21620035], [], 0, "CAN_COLLIDE"];
  _vehicle_216 = _this;
  _this setDir 19.797127;
  _this setPos [11834.036, 12596.046, -0.21620035];
};

_vehicle_217 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11822.483, 12600.202, -0.12462547], [], 0, "CAN_COLLIDE"];
  _vehicle_217 = _this;
  _this setDir 20.578798;
  _this setPos [11822.483, 12600.202, -0.12462547];
};

_vehicle_218 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11808.11, 12605.237, -0.15912956], [], 0, "CAN_COLLIDE"];
  _vehicle_218 = _this;
  _this setDir 19.48509;
  _this setPos [11808.11, 12605.237, -0.15912956];
};

_vehicle_219 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11802.538, 12607.232, -0.056701176], [], 0, "CAN_COLLIDE"];
  _vehicle_219 = _this;
  _this setDir 19.151367;
  _this setPos [11802.538, 12607.232, -0.056701176];
};

_vehicle_220 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11833.583, 12622.074, -0.13337871], [], 0, "CAN_COLLIDE"];
  _vehicle_220 = _this;
  _this setDir -71.143883;
  _this setPos [11833.583, 12622.074, -0.13337871];
};

_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11838.927, 12636.799, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setDir -159.46783;
  _this setPos [11838.927, 12636.799, 4.5776367e-005];
};

_vehicle_222 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11833.169, 12638.95, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_222 = _this;
  _this setDir -161.03687;
  _this setPos [11833.169, 12638.95, 0.00016784668];
};

_vehicle_223 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11827.449, 12640.864], [], 0, "CAN_COLLIDE"];
  _vehicle_223 = _this;
  _this setDir -160.91661;
  _this setPos [11827.449, 12640.864];
};

_vehicle_224 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11829.68, 12610.458, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_224 = _this;
  _this setDir -71.340919;
  _this setPos [11829.68, 12610.458, 7.6293945e-005];
};

_vehicle_225 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11831.58, 12616.209, -0.12552929], [], 0, "CAN_COLLIDE"];
  _vehicle_225 = _this;
  _this setDir -71.575348;
  _this setPos [11831.58, 12616.209, -0.12552929];
};

_vehicle_226 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11825.675, 12598.911, -0.033826806], [], 0, "CAN_COLLIDE"];
  _vehicle_226 = _this;
  _this setDir -71.086105;
  _this setPos [11825.675, 12598.911, -0.033826806];
};

_vehicle_227 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11827.673, 12604.647, -0.078956224], [], 0, "CAN_COLLIDE"];
  _vehicle_227 = _this;
  _this setDir -70.949966;
  _this setPos [11827.673, 12604.647, -0.078956224];
};

_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11821.768, 12642.833, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setDir -160.26138;
  _this setPos [11821.768, 12642.833, 3.0517578e-005];
};

_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11816.02, 12644.891], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setDir -159.58305;
  _this setPos [11816.02, 12644.891];
};

_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11752.548, 12631.181, -0.080673717], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setDir -70.182549;
  _this setPos [11752.548, 12631.181, -0.080673717];
};

_vehicle_231 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11754.527, 12636.977, -0.057038348], [], 0, "CAN_COLLIDE"];
  _vehicle_231 = _this;
  _this setDir -70.102364;
  _this setPos [11754.527, 12636.977, -0.057038348];
};

_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11756.563, 12642.686, -0.15061942], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setDir -70.207695;
  _this setPos [11756.563, 12642.686, -0.15061942];
};

_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11763.596, 12663.147, -0.16938536], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setDir 18.983681;
  _this setPos [11763.596, 12663.147, -0.16938536];
};

_vehicle_234 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11761.65, 12657.438, -0.0465311], [], 0, "CAN_COLLIDE"];
  _vehicle_234 = _this;
  _this setDir -70.999992;
  _this setPos [11761.65, 12657.438, -0.0465311];
};

_vehicle_235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11769.344, 12661.164, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_235 = _this;
  _this setDir 19.579718;
  _this setPos [11769.344, 12661.164, 9.1552734e-005];
};

_vehicle_236 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11775.097, 12659.127, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_236 = _this;
  _this setDir 19.336296;
  _this setPos [11775.097, 12659.127, 7.6293945e-005];
};

_vehicle_237 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11780.923, 12657.128, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_237 = _this;
  _this setDir 19.722073;
  _this setPos [11780.923, 12657.128, 9.1552734e-005];
};

_vehicle_238 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11786.71, 12655.076, -0.14854804], [], 0, "CAN_COLLIDE"];
  _vehicle_238 = _this;
  _this setDir 19.309458;
  _this setPos [11786.71, 12655.076, -0.14854804];
};

_vehicle_239 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [11836.865, 12630.945, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_239 = _this;
  _this setDir -69.766045;
  _this setPos [11836.865, 12630.945, 3.0517578e-005];
};

_vehicle_241 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11781.041, 12671.201, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_241 = _this;
  _this setPos [11781.041, 12671.201, 1.5258789e-005];
};

_vehicle_242 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11786.063, 12697.681, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_242 = _this;
  _this setPos [11786.063, 12697.681, 0];
};

_vehicle_243 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11782.343, 12685.199, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_243 = _this;
  _this setPos [11782.343, 12685.199, -1.5258789e-005];
};

_vehicle_244 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11801.221, 12650.11, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_244 = _this;
  _this setPos [11801.221, 12650.11, -6.1035156e-005];
};

_vehicle_245 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11795.014, 12632.272, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_245 = _this;
  _this setPos [11795.014, 12632.272, -6.1035156e-005];
};

_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11798.466, 12641.552, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setPos [11798.466, 12641.552, 0];
};

_vehicle_247 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11780.527, 12643.5, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_247 = _this;
  _this setPos [11780.527, 12643.5, 0];
};

_vehicle_248 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11772.079, 12628.171, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_248 = _this;
  _this setPos [11772.079, 12628.171, -1.5258789e-005];
};

_vehicle_249 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11762.005, 12639.175, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_249 = _this;
  _this setPos [11762.005, 12639.175, 6.1035156e-005];
};

_vehicle_250 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11769.633, 12663.473, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_250 = _this;
  _this setPos [11769.633, 12663.473, -6.1035156e-005];
};

_vehicle_251 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11785.095, 12657.938, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_251 = _this;
  _this setPos [11785.095, 12657.938, -6.1035156e-005];
};

_vehicle_252 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11794.419, 12655.573, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_252 = _this;
  _this setPos [11794.419, 12655.573, -1.5258789e-005];
};

_vehicle_253 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11830.442, 12606.694, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_253 = _this;
  _this setPos [11830.442, 12606.694, 4.5776367e-005];
};

_vehicle_254 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11814.676, 12614.303, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_254 = _this;
  _this setPos [11814.676, 12614.303, -1.5258789e-005];
};

_vehicle_255 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11826.145, 12621.18, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_255 = _this;
  _this setPos [11826.145, 12621.18, -1.5258789e-005];
};

_vehicle_256 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11811.654, 12628.897, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_256 = _this;
  _this setPos [11811.654, 12628.897, -3.0517578e-005];
};

_vehicle_257 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11790.483, 12615.717, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_257 = _this;
  _this setPos [11790.483, 12615.717, 1.5258789e-005];
};

_vehicle_258 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11834.496, 12637.237, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_258 = _this;
  _this setPos [11834.496, 12637.237, -3.0517578e-005];
};

_vehicle_259 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11818.75, 12642.017, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_259 = _this;
  _this setPos [11818.75, 12642.017, 6.1035156e-005];
};

_vehicle_260 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11810.168, 12646.002, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_260 = _this;
  _this setPos [11810.168, 12646.002, 1.5258789e-005];
};

_vehicle_261 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11795.919, 12670.368, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_261 = _this;
  _this setPos [11795.919, 12670.368, 0];
};

_vehicle_262 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11825.374, 12658.709, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_262 = _this;
  _this setPos [11825.374, 12658.709, 9.1552734e-005];
};

_vehicle_263 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11842.215, 12667.439, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_263 = _this;
  _this setPos [11842.215, 12667.439, 3.0517578e-005];
};

_vehicle_264 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11843.409, 12650.034, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_264 = _this;
  _this setPos [11843.409, 12650.034, 4.5776367e-005];
};

_vehicle_265 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11809.679, 12662.513, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_265 = _this;
  _this setPos [11809.679, 12662.513, 0];
};

_vehicle_266 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11848.509, 12679.177, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_266 = _this;
  _this setPos [11848.509, 12679.177, -3.0517578e-005];
};

_vehicle_267 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11872.982, 12625.169, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_267 = _this;
  _this setPos [11872.982, 12625.169, 0];
};

_vehicle_268 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11856.567, 12630.876, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_268 = _this;
  _this setPos [11856.567, 12630.876, 3.0517578e-005];
};

_vehicle_269 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11845.083, 12635.001, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_269 = _this;
  _this setPos [11845.083, 12635.001, 1.5258789e-005];
};

_vehicle_270 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11840.979, 12619.472, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_270 = _this;
  _this setPos [11840.979, 12619.472, 3.0517578e-005];
};

_vehicle_271 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11847.678, 12594.498, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_271 = _this;
  _this setPos [11847.678, 12594.498, 4.5776367e-005];
};

_vehicle_272 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11839.551, 12601.847, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_272 = _this;
  _this setPos [11839.551, 12601.847, -3.0517578e-005];
};

_vehicle_273 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11862.974, 12593.709, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_273 = _this;
  _this setPos [11862.974, 12593.709, -1.5258789e-005];
};

_vehicle_274 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11867.18, 12609.017, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_274 = _this;
  _this setPos [11867.18, 12609.017, 0];
};

_vehicle_275 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11880.374, 12641.78, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_275 = _this;
  _this setPos [11880.374, 12641.78, 1.5258789e-005];
};

_vehicle_276 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11884.804, 12663.339, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_276 = _this;
  _this setPos [11884.804, 12663.339, -1.5258789e-005];
};

_vehicle_277 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11864.585, 12670.444, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_277 = _this;
  _this setPos [11864.585, 12670.444, 0];
};

_vehicle_278 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11875.147, 12666.462, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_278 = _this;
  _this setPos [11875.147, 12666.462, 1.5258789e-005];
};

_vehicle_279 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11855.692, 12653.74, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_279 = _this;
  _this setPos [11855.692, 12653.74, 3.0517578e-005];
};

_vehicle_280 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11853.356, 12669.235, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_280 = _this;
  _this setPos [11853.356, 12669.235, 0];
};

_vehicle_281 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11831.726, 12655.521, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_281 = _this;
  _this setPos [11831.726, 12655.521, 3.0517578e-005];
};

_vehicle_282 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11827.813, 12764.205, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_282 = _this;
  _this setPos [11827.813, 12764.205, 0];
};

_vehicle_283 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11804.793, 12768.608, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_283 = _this;
  _this setPos [11804.793, 12768.608, 0];
};

_vehicle_284 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11821.716, 12748.531, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_284 = _this;
  _this setPos [11821.716, 12748.531, 3.0517578e-005];
};

_vehicle_285 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11843.665, 12749.004, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_285 = _this;
  _this setPos [11843.665, 12749.004, 3.0517578e-005];
};

_vehicle_286 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11862.469, 12734.579, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_286 = _this;
  _this setPos [11862.469, 12734.579, 4.5776367e-005];
};

_vehicle_287 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11867.143, 12751.841, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_287 = _this;
  _this setPos [11867.143, 12751.841, 0];
};

_vehicle_288 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11859.116, 12724.235, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_288 = _this;
  _this setPos [11859.116, 12724.235, 1.5258789e-005];
};

_vehicle_289 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11854.192, 12708.366, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_289 = _this;
  _this setPos [11854.192, 12708.366, 4.5776367e-005];
};

_vehicle_290 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11849.563, 12693.986, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_290 = _this;
  _this setPos [11849.563, 12693.986, 0];
};

_vehicle_291 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11877.504, 12708.044, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_291 = _this;
  _this setPos [11877.504, 12708.044, 0];
};

_vehicle_292 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11878.648, 12750.12, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_292 = _this;
  _this setPos [11878.648, 12750.12, 0];
};

_vehicle_293 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11883.265, 12730.033, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_293 = _this;
  _this setPos [11883.265, 12730.033, 0];
};

_vehicle_294 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11872.339, 12733.44, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_294 = _this;
  _this setPos [11872.339, 12733.44, -6.1035156e-005];
};

_vehicle_295 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11889.925, 12743.196, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_295 = _this;
  _this setPos [11889.925, 12743.196, -3.0517578e-005];
};

_vehicle_296 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11870.304, 12716.831, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_296 = _this;
  _this setPos [11870.304, 12716.831, 0];
};

_vehicle_297 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11852.567, 12740.634, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_297 = _this;
  _this setPos [11852.567, 12740.634, 3.0517578e-005];
};

_vehicle_298 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11846.71, 12726.728, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_298 = _this;
  _this setPos [11846.71, 12726.728, 0];
};

_vehicle_299 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11833.623, 12735.61, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_299 = _this;
  _this setPos [11833.623, 12735.61, -3.0517578e-005];
};

_vehicle_300 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11840.346, 12760.989, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_300 = _this;
  _this setPos [11840.346, 12760.989, 0];
};

_vehicle_301 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11832.835, 12752.694, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_301 = _this;
  _this setPos [11832.835, 12752.694, -4.5776367e-005];
};

_vehicle_302 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11810.222, 12734.35, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_302 = _this;
  _this setPos [11810.222, 12734.35, -1.5258789e-005];
};

_vehicle_303 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11821.705, 12727.353, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_303 = _this;
  _this setPos [11821.705, 12727.353, 0];
};

_vehicle_304 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11829.099, 12672.534, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_304 = _this;
  _this setPos [11829.099, 12672.534, -3.0517578e-005];
};

_vehicle_305 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11841.861, 12694.852, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_305 = _this;
  _this setPos [11841.861, 12694.852, 0];
};

_vehicle_306 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11840.102, 12680.83], [], 0, "CAN_COLLIDE"];
  _vehicle_306 = _this;
  _this setPos [11840.102, 12680.83];
};

_vehicle_307 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11850.166, 12709.307, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_307 = _this;
  _this setPos [11850.166, 12709.307, -0.00010681152];
};

_vehicle_308 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11781.339, 12740.069, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_308 = _this;
  _this setPos [11781.339, 12740.069, 1.5258789e-005];
};

_vehicle_309 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11796.154, 12736.229, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_309 = _this;
  _this setPos [11796.154, 12736.229, -1.5258789e-005];
};

_vehicle_310 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11795.96, 12750.925, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_310 = _this;
  _this setPos [11795.96, 12750.925, -7.6293945e-005];
};

_vehicle_311 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11810.393, 12753.891, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_311 = _this;
  _this setPos [11810.393, 12753.891, 1.5258789e-005];
};

_vehicle_312 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11817.12, 12771.006, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_312 = _this;
  _this setPos [11817.12, 12771.006, -1.5258789e-005];
};

_vehicle_313 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11806.912, 12745.466, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_313 = _this;
  _this setPos [11806.912, 12745.466, 1.5258789e-005];
};

_vehicle_314 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11786.428, 12772.816, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_314 = _this;
  _this setPos [11786.428, 12772.816, -0.00010681152];
};

_vehicle_315 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11793.198, 12762.236, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_315 = _this;
  _this setPos [11793.198, 12762.236, 4.5776367e-005];
};

_vehicle_316 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11771.691, 12783.592, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_316 = _this;
  _this setPos [11771.691, 12783.592, 3.0517578e-005];
};

_vehicle_317 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11771.827, 12767.19, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_317 = _this;
  _this setPos [11771.827, 12767.19, -1.5258789e-005];
};

_vehicle_318 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11786.44, 12724.738, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_318 = _this;
  _this setPos [11786.44, 12724.738, 3.0517578e-005];
};

_vehicle_319 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11790.465, 12709.537, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_319 = _this;
  _this setPos [11790.465, 12709.537, -1.5258789e-005];
};

_vehicle_320 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11799.336, 12720.645, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_320 = _this;
  _this setPos [11799.336, 12720.645, 4.5776367e-005];
};

_vehicle_321 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11781.57, 12703.358, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_321 = _this;
  _this setPos [11781.57, 12703.358, 0];
};

_vehicle_322 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11773.089, 12680.494, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_322 = _this;
  _this setPos [11773.089, 12680.494, -1.5258789e-005];
};

_vehicle_323 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11794.023, 12681.914, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_323 = _this;
  _this setPos [11794.023, 12681.914, 4.5776367e-005];
};

_vehicle_324 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11818.593, 12666.381, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_324 = _this;
  _this setPos [11818.593, 12666.381, 4.5776367e-005];
};

_vehicle_325 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11806.628, 12677.353, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_325 = _this;
  _this setPos [11806.628, 12677.353, 1.5258789e-005];
};

_vehicle_326 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11793.322, 12784.27, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_326 = _this;
  _this setPos [11793.322, 12784.27, -1.5258789e-005];
};

_vehicle_327 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11781.169, 12790.494, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_327 = _this;
  _this setPos [11781.169, 12790.494, -7.6293945e-005];
};

_vehicle_328 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11771.69, 12796.753, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_328 = _this;
  _this setPos [11771.69, 12796.753, 4.5776367e-005];
};

_vehicle_329 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11775.399, 12813.791, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_329 = _this;
  _this setPos [11775.399, 12813.791, 1.5258789e-005];
};

_vehicle_330 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11787.465, 12822.12, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_330 = _this;
  _this setPos [11787.465, 12822.12, 6.1035156e-005];
};

_vehicle_331 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11778.247, 12826.471, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_331 = _this;
  _this setPos [11778.247, 12826.471, -6.1035156e-005];
};

_vehicle_332 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11793.149, 12807.952, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_332 = _this;
  _this setPos [11793.149, 12807.952, 3.0517578e-005];
};

_vehicle_333 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11791.415, 12798.149, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_333 = _this;
  _this setPos [11791.415, 12798.149, 1.5258789e-005];
};

_vehicle_334 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11805.625, 12800.735, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_334 = _this;
  _this setPos [11805.625, 12800.735, 1.5258789e-005];
};

_vehicle_335 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11808.258, 12784.559, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_335 = _this;
  _this setPos [11808.258, 12784.559, 3.0517578e-005];
};

_vehicle_336 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11815.397, 12811.775, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_336 = _this;
  _this setPos [11815.397, 12811.775, 3.0517578e-005];
};

_vehicle_337 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11801.87, 12814.56, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_337 = _this;
  _this setPos [11801.87, 12814.56, 6.1035156e-005];
};

_vehicle_338 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11825.606, 12810.688, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_338 = _this;
  _this setPos [11825.606, 12810.688, 0];
};

_vehicle_339 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11830.109, 12794.746, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_339 = _this;
  _this setPos [11830.109, 12794.746, 0];
};

_vehicle_340 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11817.68, 12795.781, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_340 = _this;
  _this setPos [11817.68, 12795.781, 7.6293945e-005];
};

_vehicle_341 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11836.155, 12841.953, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_341 = _this;
  _this setPos [11836.155, 12841.953, 3.0517578e-005];
};

_vehicle_342 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11829.874, 12823.23, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_342 = _this;
  _this setPos [11829.874, 12823.23, 9.1552734e-005];
};

_vehicle_343 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11832.816, 12832.805, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_343 = _this;
  _this setPos [11832.816, 12832.805, 1.5258789e-005];
};

_vehicle_344 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11836.711, 12806.731, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_344 = _this;
  _this setPos [11836.711, 12806.731, 9.1552734e-005];
};

_vehicle_345 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11845.38, 12789.747, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_345 = _this;
  _this setPos [11845.38, 12789.747, 1.5258789e-005];
};

_vehicle_346 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11855.418, 12798.575, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_346 = _this;
  _this setPos [11855.418, 12798.575, 4.5776367e-005];
};

_vehicle_347 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11845.596, 12801.225, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_347 = _this;
  _this setPos [11845.596, 12801.225, 4.5776367e-005];
};

_vehicle_348 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11846.688, 12776.354, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_348 = _this;
  _this setPos [11846.688, 12776.354, 7.6293945e-005];
};

_vehicle_349 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11831.273, 12775.287, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_349 = _this;
  _this setPos [11831.273, 12775.287, 9.1552734e-005];
};

_vehicle_350 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11844.833, 12835.667, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_350 = _this;
  _this setPos [11844.833, 12835.667, 4.5776367e-005];
};

_vehicle_351 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11847.733, 12820.902, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_351 = _this;
  _this setPos [11847.733, 12820.902, 0.00015258789];
};

_vehicle_352 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11861.768, 12832.505, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_352 = _this;
  _this setPos [11861.768, 12832.505, 0];
};

_vehicle_353 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11854.391, 12836.688, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_353 = _this;
  _this setPos [11854.391, 12836.688, 6.1035156e-005];
};

_vehicle_354 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11878.049, 12824.187, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_354 = _this;
  _this setPos [11878.049, 12824.187, 6.1035156e-005];
};

_vehicle_355 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11865.256, 12822.391, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_355 = _this;
  _this setPos [11865.256, 12822.391, 0];
};

_vehicle_356 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11897.533, 12818.49, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_356 = _this;
  _this setPos [11897.533, 12818.49, 6.1035156e-005];
};

_vehicle_357 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11919.554, 12809.685, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_357 = _this;
  _this setPos [11919.554, 12809.685, 3.0517578e-005];
};

_vehicle_358 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11934.826, 12803.989, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_358 = _this;
  _this setPos [11934.826, 12803.989, -4.5776367e-005];
};

_vehicle_359 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11908.095, 12813.949, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_359 = _this;
  _this setPos [11908.095, 12813.949, 9.1552734e-005];
};

_vehicle_360 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11887.29, 12820.964, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_360 = _this;
  _this setPos [11887.29, 12820.964, 7.6293945e-005];
};

_vehicle_361 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11900.611, 12815.699, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_361 = _this;
  _this setPos [11900.611, 12815.699, 7.6293945e-005];
};

_vehicle_362 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11912.299, 12812.094, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_362 = _this;
  _this setPos [11912.299, 12812.094, 4.5776367e-005];
};

_vehicle_363 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11873.766, 12832.526, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_363 = _this;
  _this setPos [11873.766, 12832.526, 0];
};

_vehicle_364 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11966.858, 12793.196, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_364 = _this;
  _this setPos [11966.858, 12793.196, 3.0517578e-005];
};

_vehicle_365 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11958.001, 12796.524, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_365 = _this;
  _this setPos [11958.001, 12796.524, 4.5776367e-005];
};

_vehicle_366 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11976.237, 12789.743, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_366 = _this;
  _this setPos [11976.237, 12789.743, 4.5776367e-005];
};

_vehicle_367 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11986.644, 12786.358, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_367 = _this;
  _this setPos [11986.644, 12786.358, -1.5258789e-005];
};

_vehicle_368 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11945.761, 12800.643, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_368 = _this;
  _this setPos [11945.761, 12800.643, 0.00015258789];
};

_vehicle_369 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11940.525, 12806.923, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_369 = _this;
  _this setPos [11940.525, 12806.923, 1.5258789e-005];
};

_vehicle_370 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11930.071, 12811.228, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_370 = _this;
  _this setPos [11930.071, 12811.228, 3.0517578e-005];
};

_vehicle_371 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11950.817, 12802.587, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_371 = _this;
  _this setPos [11950.817, 12802.587, 1.5258789e-005];
};

_vehicle_372 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11951.797, 12793.676, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_372 = _this;
  _this setPos [11951.797, 12793.676, 0.00016784668];
};

_vehicle_373 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11925.044, 12803.066, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_373 = _this;
  _this setPos [11925.044, 12803.066, 1.5258789e-005];
};

_vehicle_374 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11855.771, 12759.998, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_374 = _this;
  _this setPos [11855.771, 12759.998, 7.6293945e-005];
};

_vehicle_375 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11864.538, 12776.244, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_375 = _this;
  _this setPos [11864.538, 12776.244, 3.0517578e-005];
};

_vehicle_376 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11867.583, 12788.752, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_376 = _this;
  _this setPos [11867.583, 12788.752, 0.00010681152];
};

_vehicle_377 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11856.763, 12784.795, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_377 = _this;
  _this setPos [11856.763, 12784.795, 0];
};

_vehicle_378 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12005.063, 12779.792, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_378 = _this;
  _this setPos [12005.063, 12779.792, 6.1035156e-005];
};

_vehicle_379 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11995.962, 12783.971, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_379 = _this;
  _this setPos [11995.962, 12783.971, -1.5258789e-005];
};

_vehicle_380 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11872.921, 12765.737, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_380 = _this;
  _this setPos [11872.921, 12765.737, 1.5258789e-005];
};

_vehicle_381 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11883.191, 12782.7, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_381 = _this;
  _this setPos [11883.191, 12782.7, 6.1035156e-005];
};

_vehicle_382 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11886.297, 12770.429, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_382 = _this;
  _this setPos [11886.297, 12770.429, 0.0001373291];
};

_vehicle_383 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11892.133, 12759.216, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_383 = _this;
  _this setPos [11892.133, 12759.216, 0.00010681152];
};

_vehicle_384 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11916.166, 12739.763, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_384 = _this;
  _this setPos [11916.166, 12739.763, 0.00012207031];
};

_vehicle_385 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11903.497, 12743.724, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_385 = _this;
  _this setPos [11903.497, 12743.724, 7.6293945e-005];
};

_vehicle_386 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11909.415, 12756.333, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_386 = _this;
  _this setPos [11909.415, 12756.333, 1.5258789e-005];
};

_vehicle_387 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11900.95, 12774.8, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_387 = _this;
  _this setPos [11900.95, 12774.8, 0.00024414063];
};

_vehicle_388 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11913.124, 12767.16, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_388 = _this;
  _this setPos [11913.124, 12767.16, 9.1552734e-005];
};

_vehicle_389 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11901.894, 12764.535, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_389 = _this;
  _this setPos [11901.894, 12764.535, 7.6293945e-005];
};

_vehicle_390 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11921.559, 12754.327, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_390 = _this;
  _this setPos [11921.559, 12754.327, 6.1035156e-005];
};

_vehicle_391 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11933.862, 12752.543, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_391 = _this;
  _this setPos [11933.862, 12752.543, 3.0517578e-005];
};

_vehicle_392 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11944.898, 12749.269, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_392 = _this;
  _this setPos [11944.898, 12749.269, 6.1035156e-005];
};

_vehicle_393 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11959.347, 12744.063, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_393 = _this;
  _this setPos [11959.347, 12744.063, 7.6293945e-005];
};

_vehicle_394 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11975.443, 12738.682, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_394 = _this;
  _this setPos [11975.443, 12738.682, 0.00016784668];
};

_vehicle_395 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11964.324, 12727.517, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_395 = _this;
  _this setPos [11964.324, 12727.517, -7.6293945e-005];
};

_vehicle_396 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11948.049, 12733.223, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_396 = _this;
  _this setPos [11948.049, 12733.223, 0];
};

_vehicle_397 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11931.261, 12738.623, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_397 = _this;
  _this setPos [11931.261, 12738.623, 0.00016784668];
};

_vehicle_398 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11918.792, 12776.49, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_398 = _this;
  _this setPos [11918.792, 12776.49, 3.0517578e-005];
};

_vehicle_399 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11933.727, 12767.152, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_399 = _this;
  _this setPos [11933.727, 12767.152, 3.0517578e-005];
};

_vehicle_400 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11923.941, 12766.017, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_400 = _this;
  _this setPos [11923.941, 12766.017, 0.00019836426];
};

_vehicle_401 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11949.484, 12763.525, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_401 = _this;
  _this setPos [11949.484, 12763.525, 4.5776367e-005];
};

_vehicle_402 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11963.413, 12757.477, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_402 = _this;
  _this setPos [11963.413, 12757.477, -1.5258789e-005];
};

_vehicle_403 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11977.099, 12752.12, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_403 = _this;
  _this setPos [11977.099, 12752.12, 0.00016784668];
};

_vehicle_404 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11994.39, 12745.832, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_404 = _this;
  _this setPos [11994.39, 12745.832, 3.0517578e-005];
};

_vehicle_405 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11980.166, 12720.897, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_405 = _this;
  _this setPos [11980.166, 12720.897, 3.0517578e-005];
};

_vehicle_406 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11970.571, 12718.366, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_406 = _this;
  _this setPos [11970.571, 12718.366, 0];
};

_vehicle_407 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11892.928, 12702.799, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_407 = _this;
  _this setPos [11892.928, 12702.799, 0.00010681152];
};

_vehicle_408 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11907.403, 12721.045, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_408 = _this;
  _this setPos [11907.403, 12721.045, 3.0517578e-005];
};

_vehicle_409 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11904.014, 12709.424, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_409 = _this;
  _this setPos [11904.014, 12709.424, 0.00010681152];
};

_vehicle_410 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11898.227, 12693.702, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_410 = _this;
  _this setPos [11898.227, 12693.702, 7.6293945e-005];
};

_vehicle_411 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11892.55, 12676.773, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_411 = _this;
  _this setPos [11892.55, 12676.773, 6.1035156e-005];
};

_vehicle_412 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11884.044, 12652.775, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_412 = _this;
  _this setPos [11884.044, 12652.775, 1.5258789e-005];
};

_vehicle_413 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11911.057, 12731.491, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_413 = _this;
  _this setPos [11911.057, 12731.491, 0.00010681152];
};

_vehicle_414 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11792.876, 12701.004, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_414 = _this;
  _this setPos [11792.876, 12701.004, 0.00010681152];
};

_vehicle_415 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11834.871, 12726.046, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_415 = _this;
  _this setPos [11834.871, 12726.046, 1.5258789e-005];
};

_vehicle_416 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11991.141, 12731.992, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_416 = _this;
  _this setPos [11991.141, 12731.992, 7.6293945e-005];
};

_vehicle_417 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11996.321, 12715.892, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_417 = _this;
  _this setPos [11996.321, 12715.892, 7.6293945e-005];
};

_vehicle_418 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11986.695, 12711.632, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_418 = _this;
  _this setPos [11986.695, 12711.632, 3.0517578e-005];
};

_vehicle_419 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12009.276, 12733.816, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_419 = _this;
  _this setPos [12009.276, 12733.816, 7.6293945e-005];
};

_vehicle_420 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12013.06, 12744.402, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_420 = _this;
  _this setPos [12013.06, 12744.402, 0.00021362305];
};

_vehicle_421 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12009.038, 12719.789, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_421 = _this;
  _this setPos [12009.038, 12719.789, 1.5258789e-005];
};

_vehicle_422 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12001.438, 12727.106, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_422 = _this;
  _this setPos [12001.438, 12727.106, 0];
};

_vehicle_423 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12011.271, 12706.311, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_423 = _this;
  _this setPos [12011.271, 12706.311, 6.1035156e-005];
};

_vehicle_424 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12000.844, 12706.643, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_424 = _this;
  _this setPos [12000.844, 12706.643, 0.00010681152];
};

_vehicle_425 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12024.788, 12728.642, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_425 = _this;
  _this setPos [12024.788, 12728.642, 7.6293945e-005];
};

_vehicle_426 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12027.043, 12713.265, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_426 = _this;
  _this setPos [12027.043, 12713.265, 0.00012207031];
};

_vehicle_427 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12034.093, 12698.898, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_427 = _this;
  _this setPos [12034.093, 12698.898, 0.0001373291];
};

_vehicle_428 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12023.927, 12701.119, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_428 = _this;
  _this setPos [12023.927, 12701.119, 6.1035156e-005];
};

_vehicle_429 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12046.451, 12720.64, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_429 = _this;
  _this setPos [12046.451, 12720.64, 3.0517578e-005];
};

_vehicle_430 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12037.018, 12725.404, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_430 = _this;
  _this setPos [12037.018, 12725.404, 0.00019836426];
};

_vehicle_431 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12035.87, 12735.015, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_431 = _this;
  _this setPos [12035.87, 12735.015, 0.00015258789];
};

_vehicle_432 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12043.917, 12707.762, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_432 = _this;
  _this setPos [12043.917, 12707.762, 0.00019836426];
};

_vehicle_433 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12058.783, 12699.92, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_433 = _this;
  _this setPos [12058.783, 12699.92, 0.00016784668];
};

_vehicle_434 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12049.799, 12691.292, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_434 = _this;
  _this setPos [12049.799, 12691.292, 0.00012207031];
};

_vehicle_435 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12072.142, 12689.203, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_435 = _this;
  _this setPos [12072.142, 12689.203, 0.00010681152];
};

_vehicle_436 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12071.005, 12718.993, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_436 = _this;
  _this setPos [12071.005, 12718.993, 4.5776367e-005];
};

_vehicle_437 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12056.604, 12718.865, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_437 = _this;
  _this setPos [12056.604, 12718.865, 4.5776367e-005];
};

_vehicle_438 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12055.751, 12728.541, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_438 = _this;
  _this setPos [12055.751, 12728.541, 7.6293945e-005];
};

_vehicle_439 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12025.447, 12740.544, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_439 = _this;
  _this setPos [12025.447, 12740.544, 7.6293945e-005];
};

_vehicle_440 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12072.537, 12704.761, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_440 = _this;
  _this setPos [12072.537, 12704.761, 1.5258789e-005];
};

_vehicle_441 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12089.226, 12691.559, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_441 = _this;
  _this setPos [12089.226, 12691.559, 0.00019836426];
};

_vehicle_442 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12090.971, 12681.137, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_442 = _this;
  _this setPos [12090.971, 12681.137, 3.0517578e-005];
};

_vehicle_443 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12085.391, 12710.604, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_443 = _this;
  _this setPos [12085.391, 12710.604, 3.0517578e-005];
};

_vehicle_444 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12089.729, 12701.313, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_444 = _this;
  _this setPos [12089.729, 12701.313, 0.00015258789];
};

_vehicle_445 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12103.951, 12706.64, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_445 = _this;
  _this setPos [12103.951, 12706.64, 0.00019836426];
};

_vehicle_446 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12096.971, 12715.78, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_446 = _this;
  _this setPos [12096.971, 12715.78, 0.00024414063];
};

_vehicle_447 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12105.078, 12691.084, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_447 = _this;
  _this setPos [12105.078, 12691.084, 0];
};

_vehicle_448 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12108.335, 12677.693, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_448 = _this;
  _this setPos [12108.335, 12677.693, 0.00018310547];
};

_vehicle_449 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12119.18, 12701.906, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_449 = _this;
  _this setPos [12119.18, 12701.906, 0.0002746582];
};

_vehicle_450 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12121.623, 12686.598, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_450 = _this;
  _this setPos [12121.623, 12686.598, 7.6293945e-005];
};

_vehicle_451 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12126.893, 12674.408, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_451 = _this;
  _this setPos [12126.893, 12674.408, 0];
};

_vehicle_452 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12119.202, 12668.771, 0.00032043457], [], 0, "CAN_COLLIDE"];
  _vehicle_452 = _this;
  _this setPos [12119.202, 12668.771, 0.00032043457];
};

_vehicle_453 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12140.098, 12668.658, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_453 = _this;
  _this setPos [12140.098, 12668.658, 0.00012207031];
};

_vehicle_454 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12155.53, 12664.829, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_454 = _this;
  _this setPos [12155.53, 12664.829, 0.00015258789];
};

_vehicle_455 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12149.476, 12657.678, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_455 = _this;
  _this setPos [12149.476, 12657.678, 0.00022888184];
};

_vehicle_456 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12134, 12695.715, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_456 = _this;
  _this setPos [12134, 12695.715, 0.00016784668];
};

_vehicle_457 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12140.142, 12681.36, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_457 = _this;
  _this setPos [12140.142, 12681.36, 0.00010681152];
};

_vehicle_458 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12149.602, 12693.393, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_458 = _this;
  _this setPos [12149.602, 12693.393, 0.00016784668];
};

_vehicle_459 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12158.364, 12680.522, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_459 = _this;
  _this setPos [12158.364, 12680.522, 3.0517578e-005];
};

_vehicle_460 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12149.682, 12674.392, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_460 = _this;
  _this setPos [12149.682, 12674.392, 4.5776367e-005];
};

_vehicle_461 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11864.349, 12690.013, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_461 = _this;
  _this setPos [11864.349, 12690.013, 1.5258789e-005];
};

_vehicle_463 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11867.705, 12700.115, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_463 = _this;
  _this setPos [11867.705, 12700.115, 1.5258789e-005];
};

_vehicle_464 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [11859.024, 12679.771, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_464 = _this;
  _this setPos [11859.024, 12679.771, 6.1035156e-005];
};

_vehicle_465 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12282.733, 12610.484, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_465 = _this;
  _this setPos [12282.733, 12610.484, 4.5776367e-005];
};

_vehicle_466 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12278.712, 12597.984, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_466 = _this;
  _this setPos [12278.712, 12597.984, 3.0517578e-005];
};

_vehicle_467 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12288.393, 12594.676, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_467 = _this;
  _this setPos [12288.393, 12594.676, 3.0517578e-005];
};

_vehicle_468 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12294.967, 12612.774, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_468 = _this;
  _this setPos [12294.967, 12612.774, 1.5258789e-005];
};

_vehicle_469 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12272.684, 12604.626, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_469 = _this;
  _this setPos [12272.684, 12604.626, -3.0517578e-005];
};

_vehicle_470 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12273.182, 12619.314, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_470 = _this;
  _this setPos [12273.182, 12619.314, -4.5776367e-005];
};

_vehicle_471 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12272.345, 12579.321, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_471 = _this;
  _this setPos [12272.345, 12579.321, -6.1035156e-005];
};

_vehicle_472 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12276.068, 12589.079, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_472 = _this;
  _this setPos [12276.068, 12589.079, -1.5258789e-005];
};

_vehicle_473 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12315.065, 12567.732, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_473 = _this;
  _this setPos [12315.065, 12567.732, 1.5258789e-005];
};

_vehicle_474 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12301.841, 12572.477, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_474 = _this;
  _this setPos [12301.841, 12572.477, 1.5258789e-005];
};

_vehicle_475 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12289.075, 12577.182, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_475 = _this;
  _this setPos [12289.075, 12577.182, 0];
};

_vehicle_476 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12304.273, 12580.771, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_476 = _this;
  _this setPos [12304.273, 12580.771, 0];
};

_vehicle_477 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12295.354, 12586.434, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_477 = _this;
  _this setPos [12295.354, 12586.434, -1.5258789e-005];
};

_vehicle_478 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12286.114, 12567.551, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_478 = _this;
  _this setPos [12286.114, 12567.551, -3.0517578e-005];
};

_vehicle_479 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12306.812, 12559.918, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_479 = _this;
  _this setPos [12306.812, 12559.918, 7.6293945e-005];
};

_vehicle_480 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12295.994, 12564.041, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_480 = _this;
  _this setPos [12295.994, 12564.041, 7.6293945e-005];
};

_vehicle_481 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12295.697, 12648.519, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_481 = _this;
  _this setPos [12295.697, 12648.519, 3.0517578e-005];
};

_vehicle_482 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12279.008, 12645.068, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_482 = _this;
  _this setPos [12279.008, 12645.068, 0.00010681152];
};

_vehicle_483 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12300.014, 12659.524, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_483 = _this;
  _this setPos [12300.014, 12659.524, 0];
};

_vehicle_484 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12306.385, 12678.329, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_484 = _this;
  _this setPos [12306.385, 12678.329, 3.0517578e-005];
};

_vehicle_485 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12296.457, 12670.88, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_485 = _this;
  _this setPos [12296.457, 12670.88, -1.5258789e-005];
};

_vehicle_486 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12288.254, 12680.679, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_486 = _this;
  _this setPos [12288.254, 12680.679, 3.0517578e-005];
};

_vehicle_487 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12287.607, 12665.485, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_487 = _this;
  _this setPos [12287.607, 12665.485, 3.0517578e-005];
};

_vehicle_488 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12310.453, 12673.407, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_488 = _this;
  _this setPos [12310.453, 12673.407, 0];
};

_vehicle_489 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12288.752, 12646.31, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_489 = _this;
  _this setPos [12288.752, 12646.31, 3.0517578e-005];
};

_vehicle_490 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12302.154, 12634.345, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_490 = _this;
  _this setPos [12302.154, 12634.345, 1.5258789e-005];
};

_vehicle_491 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12288.242, 12627.709, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_491 = _this;
  _this setPos [12288.242, 12627.709, 3.0517578e-005];
};

_vehicle_492 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12265.42, 12633.962, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_492 = _this;
  _this setPos [12265.42, 12633.962, 3.0517578e-005];
};

_vehicle_493 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12249.821, 12646.238, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_493 = _this;
  _this setPos [12249.821, 12646.238, 1.5258789e-005];
};

_vehicle_494 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12221.283, 12651.481, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_494 = _this;
  _this setPos [12221.283, 12651.481, 1.5258789e-005];
};

_vehicle_495 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12187.844, 12661.434, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_495 = _this;
  _this setPos [12187.844, 12661.434, 0];
};

_vehicle_496 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12171.393, 12666.951, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_496 = _this;
  _this setPos [12171.393, 12666.951, 3.0517578e-005];
};

_vehicle_497 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12174.695, 12653.402, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_497 = _this;
  _this setPos [12174.695, 12653.402, 1.5258789e-005];
};

_vehicle_498 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12162.455, 12654.26, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_498 = _this;
  _this setPos [12162.455, 12654.26, 0];
};

_vehicle_500 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12166.05, 12689.953, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_500 = _this;
  _this setPos [12166.05, 12689.953, -3.0517578e-005];
};

_vehicle_501 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12173.844, 12680.185, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_501 = _this;
  _this setPos [12173.844, 12680.185, 0];
};

_vehicle_502 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12186.215, 12677.46, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_502 = _this;
  _this setPos [12186.215, 12677.46, 7.6293945e-005];
};

_vehicle_503 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12198.816, 12672.917, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_503 = _this;
  _this setPos [12198.816, 12672.917, 1.5258789e-005];
};

_vehicle_504 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12203.384, 12659.754, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_504 = _this;
  _this setPos [12203.384, 12659.754, 0];
};

_vehicle_505 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12189.693, 12646.856, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_505 = _this;
  _this setPos [12189.693, 12646.856, 1.5258789e-005];
};

_vehicle_506 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12206.34, 12643.356, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_506 = _this;
  _this setPos [12206.34, 12643.356, 1.5258789e-005];
};

_vehicle_507 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12221.335, 12637.545, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_507 = _this;
  _this setPos [12221.335, 12637.545, 0];
};

_vehicle_508 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12214.964, 12670.33, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_508 = _this;
  _this setPos [12214.964, 12670.33, 0];
};

_vehicle_509 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12223.557, 12662.141, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_509 = _this;
  _this setPos [12223.557, 12662.141, 4.5776367e-005];
};

_vehicle_510 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12236.721, 12652.478, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_510 = _this;
  _this setPos [12236.721, 12652.478, 0];
};

_vehicle_511 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12233.427, 12637.745, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_511 = _this;
  _this setPos [12233.427, 12637.745, 1.5258789e-005];
};

_vehicle_512 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12245.502, 12627.206, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_512 = _this;
  _this setPos [12245.502, 12627.206, 4.5776367e-005];
};

_vehicle_513 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12248.267, 12637.069, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_513 = _this;
  _this setPos [12248.267, 12637.069, 0];
};

_vehicle_514 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12237.882, 12665.976, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_514 = _this;
  _this setPos [12237.882, 12665.976, 1.5258789e-005];
};

_vehicle_515 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12253.271, 12657.035, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_515 = _this;
  _this setPos [12253.271, 12657.035, 1.5258789e-005];
};

_vehicle_517 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12265.167, 12650.005, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_517 = _this;
  _this setPos [12265.167, 12650.005, 1.5258789e-005];
};

_vehicle_518 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12260.145, 12625.201, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_518 = _this;
  _this setPos [12260.145, 12625.201, 1.5258789e-005];
};

_vehicle_519 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12277.177, 12632.151, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_519 = _this;
  _this setPos [12277.177, 12632.151, 0];
};

_vehicle_520 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12015.479, 12776.344, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_520 = _this;
  _this setPos [12015.479, 12776.344, 0];
};

_vehicle_521 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12029.663, 12773.835, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_521 = _this;
  _this setPos [12029.663, 12773.835, 3.0517578e-005];
};

_vehicle_522 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12042.406, 12766.783, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_522 = _this;
  _this setPos [12042.406, 12766.783, 4.5776367e-005];
};

_vehicle_523 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12057.748, 12762.253, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_523 = _this;
  _this setPos [12057.748, 12762.253, 1.5258789e-005];
};

_vehicle_524 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12072.329, 12756.455, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_524 = _this;
  _this setPos [12072.329, 12756.455, 3.0517578e-005];
};

_vehicle_525 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12086.161, 12753.32, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_525 = _this;
  _this setPos [12086.161, 12753.32, 6.1035156e-005];
};

_vehicle_526 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12098.658, 12747.762, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_526 = _this;
  _this setPos [12098.658, 12747.762, -1.5258789e-005];
};

_vehicle_527 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12110.167, 12744.197, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_527 = _this;
  _this setPos [12110.167, 12744.197, 4.5776367e-005];
};

_vehicle_528 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12122.527, 12739.151, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_528 = _this;
  _this setPos [12122.527, 12739.151, 3.0517578e-005];
};

_vehicle_529 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12136.929, 12734.514, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_529 = _this;
  _this setPos [12136.929, 12734.514, -3.0517578e-005];
};

_vehicle_530 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12147.828, 12730.648, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_530 = _this;
  _this setPos [12147.828, 12730.648, 7.6293945e-005];
};

_vehicle_531 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12159.142, 12727.031, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_531 = _this;
  _this setPos [12159.142, 12727.031, 4.5776367e-005];
};

_vehicle_532 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12169.512, 12723.042, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_532 = _this;
  _this setPos [12169.512, 12723.042, 3.0517578e-005];
};

_vehicle_533 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12180.746, 12719.065, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_533 = _this;
  _this setPos [12180.746, 12719.065, 1.5258789e-005];
};

_vehicle_534 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12190.902, 12715.937, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_534 = _this;
  _this setPos [12190.902, 12715.937, 4.5776367e-005];
};

_vehicle_535 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12201.853, 12711.883, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_535 = _this;
  _this setPos [12201.853, 12711.883, 6.1035156e-005];
};

_vehicle_536 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12211.858, 12708.125, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_536 = _this;
  _this setPos [12211.858, 12708.125, 1.5258789e-005];
};

_vehicle_537 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12222.428, 12704.36, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_537 = _this;
  _this setPos [12222.428, 12704.36, 4.5776367e-005];
};

_vehicle_538 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12232.416, 12701.004, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_538 = _this;
  _this setPos [12232.416, 12701.004, 6.1035156e-005];
};

_vehicle_539 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12242.198, 12697.502, -0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_539 = _this;
  _this setPos [12242.198, 12697.502, -0.00019836426];
};

_vehicle_540 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12251.909, 12694.251, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_540 = _this;
  _this setPos [12251.909, 12694.251, 4.5776367e-005];
};

_vehicle_541 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12261.857, 12690.441, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_541 = _this;
  _this setPos [12261.857, 12690.441, 7.6293945e-005];
};

_vehicle_542 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12273.046, 12687.207, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_542 = _this;
  _this setPos [12273.046, 12687.207, 3.0517578e-005];
};

_vehicle_543 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12132.203, 12743.408, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_543 = _this;
  _this setPos [12132.203, 12743.408, 4.5776367e-005];
};

_vehicle_544 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12130.619, 12746.375, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_544 = _this;
  _this setPos [12130.619, 12746.375, 3.0517578e-005];
};

_vehicle_545 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12127.937, 12747.136, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_545 = _this;
  _this setPos [12127.937, 12747.136, -1.5258789e-005];
};

_vehicle_546 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12187.466, 12726.559, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_546 = _this;
  _this setPos [12187.466, 12726.559, 4.5776367e-005];
};

_vehicle_547 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12177.93, 12729.978, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_547 = _this;
  _this setPos [12177.93, 12729.978, 0.00010681152];
};

_vehicle_548 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12210.293, 12718.613, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_548 = _this;
  _this setPos [12210.293, 12718.613, 0];
};

_vehicle_549 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12220.512, 12714.996, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_549 = _this;
  _this setPos [12220.512, 12714.996, 6.1035156e-005];
};

_vehicle_550 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12284.087, 12690.846, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_550 = _this;
  _this setPos [12284.087, 12690.846, 6.1035156e-005];
};

_vehicle_551 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12296.935, 12696.535, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_551 = _this;
  _this setDir -0.55224586;
  _this setPos [12296.935, 12696.535, 4.5776367e-005];
};

_vehicle_552 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12281.867, 12693.831, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_552 = _this;
  _this setPos [12281.867, 12693.831, 7.6293945e-005];
};

_vehicle_553 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12299.175, 12685.835, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_553 = _this;
  _this setPos [12299.175, 12685.835, 7.6293945e-005];
};

_vehicle_554 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12296.62, 12688.619, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_554 = _this;
  _this setPos [12296.62, 12688.619, 6.1035156e-005];
};

_vehicle_555 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12293.421, 12690, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_555 = _this;
  _this setPos [12293.421, 12690, -1.5258789e-005];
};

_vehicle_556 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12270.481, 12697.526, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_556 = _this;
  _this setPos [12270.481, 12697.526, 4.5776367e-005];
};

_vehicle_557 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12020.73, 12765.831, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_557 = _this;
  _this setPos [12020.73, 12765.831, 0];
};

_vehicle_558 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12030.203, 12762.688, 0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_558 = _this;
  _this setPos [12030.203, 12762.688, 0.00028991699];
};

_vehicle_559 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12034.148, 12760.607, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_559 = _this;
  _this setPos [12034.148, 12760.607, 0.00015258789];
};

_vehicle_560 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12025.739, 12763.338, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_560 = _this;
  _this setPos [12025.739, 12763.338, 0.00019836426];
};

_vehicle_561 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12017.184, 12766.201, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_561 = _this;
  _this setPos [12017.184, 12766.201, 9.1552734e-005];
};

_vehicle_562 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [12007.676, 12769.513, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_562 = _this;
  _this setPos [12007.676, 12769.513, 3.0517578e-005];
};

_vehicle_575 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_Hospital", [12290.103, 12562.68], [], 0, "CAN_COLLIDE"];
  _vehicle_575 = _this;
  _this setDir 198.77;
  _this setPos [12290.103, 12562.68];
};

_vehicle_579 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_BuildingWIP", [11992.816, 12820.54, -0.084935442], [], 0, "CAN_COLLIDE"];
  _vehicle_579 = _this;
  _this setDir -160.57185;
  _this setPos [11992.816, 12820.54, -0.084935442];
};

_vehicle_580 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton", [12408.874, 12546.162, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_580 = _this;
  _this setDir -70.597488;
  _this setPos [12408.874, 12546.162, 0.00012207031];
};

_vehicle_582 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [12403.579, 12541.974, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_582 = _this;
  _this setDir -70.467926;
  _this setPos [12403.579, 12541.974, 3.0517578e-005];
};

_vehicle_583 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [12392.269, 12545.831, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_583 = _this;
  _this setDir -71.208344;
  _this setPos [12392.269, 12545.831, 1.5258789e-005];
};

_vehicle_584 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [12380.774, 12549.82, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_584 = _this;
  _this setDir -70.971649;
  _this setPos [12380.774, 12549.82, -3.0517578e-005];
};

_vehicle_585 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [12349.929, 12565.921, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_585 = _this;
  _this setDir 18.986847;
  _this setPos [12349.929, 12565.921, -3.0517578e-005];
};

_vehicle_586 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [12353.542, 12576.43, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_586 = _this;
  _this setDir 18.597763;
  _this setPos [12353.542, 12576.43, 1.5258789e-005];
};

_vehicle_587 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton_end1", [12429.646, 12549.282, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_587 = _this;
  _this setDir -70.581955;
  _this setPos [12429.646, 12549.282, 1.5258789e-005];
};

_vehicle_588 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12389.777, 12532.118, -0.11380719], [], 0, "CAN_COLLIDE"];
  _vehicle_588 = _this;
  _this setDir 20.931683;
  _this setPos [12389.777, 12532.118, -0.11380719];
};

_vehicle_595 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12334.88, 12551.655, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_595 = _this;
  _this setDir 21.097929;
  _this setPos [12334.88, 12551.655, -3.0517578e-005];
};

_vehicle_596 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12342.782, 12548.985, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_596 = _this;
  _this setDir 20.048683;
  _this setPos [12342.782, 12548.985, -1.5258789e-005];
};

_vehicle_597 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12350.673, 12546.197, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_597 = _this;
  _this setDir 23.012659;
  _this setPos [12350.673, 12546.197, 1.5258789e-005];
};

_vehicle_598 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12358.466, 12543.366, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_598 = _this;
  _this setDir 20.827003;
  _this setPos [12358.466, 12543.366, -1.5258789e-005];
};

_vehicle_599 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12397.697, 12529.588, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_599 = _this;
  _this setDir 17.972862;
  _this setPos [12397.697, 12529.588, -1.5258789e-005];
};

_vehicle_600 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12366.339, 12540.664], [], 0, "CAN_COLLIDE"];
  _vehicle_600 = _this;
  _this setDir 21.665548;
  _this setPos [12366.339, 12540.664];
};

_vehicle_601 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12374.163, 12537.724], [], 0, "CAN_COLLIDE"];
  _vehicle_601 = _this;
  _this setDir 23.247587;
  _this setPos [12374.163, 12537.724];
};

_vehicle_602 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12381.953, 12534.835, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_602 = _this;
  _this setDir 22.110668;
  _this setPos [12381.953, 12534.835, -1.5258789e-005];
};

_vehicle_603 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12405.538, 12529.113, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_603 = _this;
  _this setDir -7.9248195;
  _this setPos [12405.538, 12529.113, -3.0517578e-005];
};

_vehicle_604 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12413.673, 12530.805, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_604 = _this;
  _this setDir -11.903347;
  _this setPos [12413.673, 12530.805, -1.5258789e-005];
};

_vehicle_605 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12433.104, 12552.122], [], 0, "CAN_COLLIDE"];
  _vehicle_605 = _this;
  _this setDir -68.282356;
  _this setPos [12433.104, 12552.122];
};

_vehicle_606 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12415.957, 12563.608, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_606 = _this;
  _this setDir 21.326698;
  _this setPos [12415.957, 12563.608, 6.1035156e-005];
};

_vehicle_607 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12352.938, 12585.434, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_607 = _this;
  _this setDir 20.927032;
  _this setPos [12352.938, 12585.434, 7.6293945e-005];
};

_vehicle_608 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12345.104, 12588.201, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_608 = _this;
  _this setDir 21.864315;
  _this setPos [12345.104, 12588.201, 4.5776367e-005];
};

_vehicle_609 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12339.215, 12586.759, -0.028958863], [], 0, "CAN_COLLIDE"];
  _vehicle_609 = _this;
  _this setDir -69.179771;
  _this setPos [12339.215, 12586.759, -0.028958863];
};

_vehicle_610 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12360.743, 12582.67, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_610 = _this;
  _this setDir 20.881117;
  _this setPos [12360.743, 12582.67, 4.5776367e-005];
};

_vehicle_611 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12368.601, 12579.849, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_611 = _this;
  _this setDir 20.718679;
  _this setPos [12368.601, 12579.849, 4.5776367e-005];
};

_vehicle_612 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12376.521, 12577.135], [], 0, "CAN_COLLIDE"];
  _vehicle_612 = _this;
  _this setDir 20.415949;
  _this setPos [12376.521, 12577.135];
};

_vehicle_613 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12384.389, 12574.417, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_613 = _this;
  _this setDir 21.605556;
  _this setPos [12384.389, 12574.417, 9.1552734e-005];
};

_vehicle_614 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12392.182, 12571.65, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_614 = _this;
  _this setDir 21.235949;
  _this setPos [12392.182, 12571.65, -9.1552734e-005];
};

_vehicle_615 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12400.044, 12568.922, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_615 = _this;
  _this setDir 20.608904;
  _this setPos [12400.044, 12568.922, 9.1552734e-005];
};

_vehicle_616 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12408.037, 12566.286, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_616 = _this;
  _this setDir 20.416199;
  _this setPos [12408.037, 12566.286, -3.0517578e-005];
};

_vehicle_617 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12423.811, 12560.881, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_617 = _this;
  _this setDir 20.671167;
  _this setPos [12423.811, 12560.881, 3.0517578e-005];
};

_vehicle_618 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12431.665, 12558.063, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_618 = _this;
  _this setDir 21.100286;
  _this setPos [12431.665, 12558.063, 0.00016784668];
};

_vehicle_619 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12422.047, 12532.964, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_619 = _this;
  _this setDir -12.626069;
  _this setPos [12422.047, 12532.964, 9.1552734e-005];
};

_vehicle_620 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [12427.754, 12537.244, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_620 = _this;
  _this setDir -67.943237;
  _this setPos [12427.754, 12537.244, 3.0517578e-005];
};

_vehicle_621 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [12346.324, 12555.657], [], 0, "CAN_COLLIDE"];
  _vehicle_621 = _this;
  _this setDir 19.296562;
  _this setPos [12346.324, 12555.657];
};

_vehicle_680 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12272.045, 12665.515, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_680 = _this;
  _this setPos [12272.045, 12665.515, 6.1035156e-005];
};

_vehicle_681 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12276.015, 12662.775, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_681 = _this;
  _this setPos [12276.015, 12662.775, 3.0517578e-005];
};

_vehicle_682 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12269.501, 12666.563, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_682 = _this;
  _this setPos [12269.501, 12666.563, 4.5776367e-005];
};

_vehicle_685 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12260.94, 12668.738, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_685 = _this;
  _this setPos [12260.94, 12668.738, 3.0517578e-005];
};

_vehicle_686 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12237.62, 12678.15, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_686 = _this;
  _this setPos [12237.62, 12678.15, -7.6293945e-005];
};

_vehicle_687 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12242.805, 12675.548, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_687 = _this;
  _this setPos [12242.805, 12675.548, 4.5776367e-005];
};

_vehicle_688 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12267.27, 12669.313, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_688 = _this;
  _this setPos [12267.27, 12669.313, -6.1035156e-005];
};

_vehicle_689 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12248.766, 12674.659, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_689 = _this;
  _this setPos [12248.766, 12674.659, 4.5776367e-005];
};

_vehicle_690 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12235.357, 12680.108, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_690 = _this;
  _this setPos [12235.357, 12680.108, -7.6293945e-005];
};

_vehicle_691 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12238.945, 12677.881, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_691 = _this;
  _this setPos [12238.945, 12677.881, -3.0517578e-005];
};

_vehicle_692 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12236.041, 12678.406, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_692 = _this;
  _this setPos [12236.041, 12678.406, 4.5776367e-005];
};

_vehicle_693 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12032.929, 12749.28, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_693 = _this;
  _this setDir -0.27565628;
  _this setPos [12032.929, 12749.28, -9.1552734e-005];
};

_vehicle_694 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12000.87, 12760.847, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_694 = _this;
  _this setDir -6.423945;
  _this setPos [12000.87, 12760.847, -6.1035156e-005];
};

_vehicle_695 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12011.113, 12753.015, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_695 = _this;
  _this setDir -7.2770958;
  _this setPos [12011.113, 12753.015, -9.1552734e-005];
};

_vehicle_696 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12044.099, 12748.466, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_696 = _this;
  _this setDir -12.490563;
  _this setPos [12044.099, 12748.466, -1.5258789e-005];
};

_vehicle_697 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11989.254, 12760.646, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_697 = _this;
  _this setPos [11989.254, 12760.646, 6.1035156e-005];
};

_vehicle_698 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12264.41, 12611.746, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_698 = _this;
  _this setPos [12264.41, 12611.746, 3.0517578e-005];
};

_vehicle_699 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12228.665, 12623.841, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_699 = _this;
  _this setPos [12228.665, 12623.841, 6.1035156e-005];
};

_vehicle_700 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12257.619, 12613.466, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_700 = _this;
  _this setPos [12257.619, 12613.466, 1.5258789e-005];
};

_vehicle_701 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12269.087, 12611.904], [], 0, "CAN_COLLIDE"];
  _vehicle_701 = _this;
  _this setPos [12269.087, 12611.904];
};

_vehicle_702 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12238.239, 12622.145], [], 0, "CAN_COLLIDE"];
  _vehicle_702 = _this;
  _this setPos [12238.239, 12622.145];
};

_vehicle_703 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12224.327, 12627.089], [], 0, "CAN_COLLIDE"];
  _vehicle_703 = _this;
  _this setPos [12224.327, 12627.089];
};

_vehicle_704 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12219.873, 12623.175, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_704 = _this;
  _this setPos [12219.873, 12623.175, 3.0517578e-005];
};

_vehicle_705 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12233.347, 12621.627, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_705 = _this;
  _this setPos [12233.347, 12621.627, 1.5258789e-005];
};

_vehicle_706 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12254.215, 12616.966, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_706 = _this;
  _this setPos [12254.215, 12616.966, -0.00012207031];
};

_vehicle_707 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12261.001, 12615.144, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_707 = _this;
  _this setPos [12261.001, 12615.144, 3.0517578e-005];
};

_vehicle_709 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12249.885, 12618.098, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_709 = _this;
  _this setPos [12249.885, 12618.098, 3.0517578e-005];
};

_vehicle_710 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12208.183, 12606.27], [], 0, "CAN_COLLIDE"];
  _vehicle_710 = _this;
  _this setPos [12208.183, 12606.27];
};

_vehicle_711 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12193.069, 12609.17, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_711 = _this;
  _this setPos [12193.069, 12609.17, 3.0517578e-005];
};

_vehicle_712 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12182.177, 12601.473], [], 0, "CAN_COLLIDE"];
  _vehicle_712 = _this;
  _this setPos [12182.177, 12601.473];
};

_vehicle_713 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12186.086, 12608.186, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_713 = _this;
  _this setPos [12186.086, 12608.186, 4.5776367e-005];
};

_vehicle_714 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12195.675, 12608.199, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_714 = _this;
  _this setPos [12195.675, 12608.199, -1.5258789e-005];
};

_vehicle_715 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12182.414, 12605.41], [], 0, "CAN_COLLIDE"];
  _vehicle_715 = _this;
  _this setPos [12182.414, 12605.41];
};

_vehicle_716 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12332.748, 12589.096, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_716 = _this;
  _this setPos [12332.748, 12589.096, -1.5258789e-005];
};

_vehicle_717 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12309.639, 12598.558, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_717 = _this;
  _this setPos [12309.639, 12598.558, 3.0517578e-005];
};

_vehicle_718 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12300.587, 12596.339, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_718 = _this;
  _this setPos [12300.587, 12596.339, 4.5776367e-005];
};

_vehicle_719 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12297.244, 12599.516, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_719 = _this;
  _this setPos [12297.244, 12599.516, -4.5776367e-005];
};

_vehicle_720 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12300.911, 12601.858, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_720 = _this;
  _this setPos [12300.911, 12601.858, 1.5258789e-005];
};

_vehicle_721 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12323.907, 12587.946, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_721 = _this;
  _this setPos [12323.907, 12587.946, 1.5258789e-005];
};

_vehicle_722 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12324.986, 12594.568, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_722 = _this;
  _this setPos [12324.986, 12594.568, 6.1035156e-005];
};

_vehicle_723 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12321.954, 12584.49, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_723 = _this;
  _this setPos [12321.954, 12584.49, 6.1035156e-005];
};

_vehicle_724 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12327.531, 12590.242, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_724 = _this;
  _this setPos [12327.531, 12590.242, 3.0517578e-005];
};

_vehicle_725 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12316.43, 12594.606, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_725 = _this;
  _this setPos [12316.43, 12594.606, 1.5258789e-005];
};

_vehicle_726 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12303.782, 12597.515, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_726 = _this;
  _this setPos [12303.782, 12597.515, 3.0517578e-005];
};

_vehicle_727 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12264.8, 12569.747], [], 0, "CAN_COLLIDE"];
  _vehicle_727 = _this;
  _this setPos [12264.8, 12569.747];
};

_vehicle_728 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12260.547, 12573.671, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_728 = _this;
  _this setPos [12260.547, 12573.671, 0.00012207031];
};

_vehicle_729 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12357.336, 12536.596, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_729 = _this;
  _this setPos [12357.336, 12536.596, 4.5776367e-005];
};

_vehicle_730 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12318.608, 12550.507, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_730 = _this;
  _this setPos [12318.608, 12550.507, 3.0517578e-005];
};

_vehicle_731 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12323.052, 12548.159], [], 0, "CAN_COLLIDE"];
  _vehicle_731 = _this;
  _this setPos [12323.052, 12548.159];
};

_vehicle_732 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12326.115, 12550.923, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_732 = _this;
  _this setPos [12326.115, 12550.923, 1.5258789e-005];
};

_vehicle_733 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12328.856, 12546.642], [], 0, "CAN_COLLIDE"];
  _vehicle_733 = _this;
  _this setPos [12328.856, 12546.642];
};

_vehicle_734 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12345.505, 12541.072, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_734 = _this;
  _this setPos [12345.505, 12541.072, 3.0517578e-005];
};

_vehicle_735 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12363.394, 12534.126, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_735 = _this;
  _this setPos [12363.394, 12534.126, -4.5776367e-005];
};

_vehicle_736 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12427.998, 12525.944, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_736 = _this;
  _this setPos [12427.998, 12525.944, 3.0517578e-005];
};

_vehicle_737 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12424.759, 12525.425], [], 0, "CAN_COLLIDE"];
  _vehicle_737 = _this;
  _this setPos [12424.759, 12525.425];
};

_vehicle_738 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12424.278, 12528.864, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_738 = _this;
  _this setPos [12424.278, 12528.864, 1.5258789e-005];
};

_vehicle_739 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12421.604, 12526.481, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_739 = _this;
  _this setPos [12421.604, 12526.481, -1.5258789e-005];
};

_vehicle_740 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12406.664, 12524.514, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_740 = _this;
  _this setPos [12406.664, 12524.514, 3.0517578e-005];
};

_vehicle_741 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12366.037, 12533.142, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_741 = _this;
  _this setPos [12366.037, 12533.142, -1.5258789e-005];
};

_vehicle_742 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12407.937, 12523.771, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_742 = _this;
  _this setPos [12407.937, 12523.771, -1.5258789e-005];
};

_vehicle_743 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12318.559, 12684.756, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_743 = _this;
  _this setPos [12318.559, 12684.756, -6.1035156e-005];
};

_vehicle_744 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12309.71, 12687.866, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_744 = _this;
  _this setPos [12309.71, 12687.866, -1.5258789e-005];
};

_vehicle_745 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12299.767, 12691.204, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_745 = _this;
  _this setPos [12299.767, 12691.204, -1.5258789e-005];
};

_vehicle_746 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12302.84, 12691.826, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_746 = _this;
  _this setPos [12302.84, 12691.826, -1.5258789e-005];
};

_vehicle_747 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12298.162, 12691.326, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_747 = _this;
  _this setPos [12298.162, 12691.326, -1.5258789e-005];
};

_vehicle_748 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11841.654, 12710.551], [], 0, "CAN_COLLIDE"];
  _vehicle_748 = _this;
  _this setPos [11841.654, 12710.551];
};

_vehicle_749 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11839.99, 12705.615, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_749 = _this;
  _this setPos [11839.99, 12705.615, 1.5258789e-005];
};

_vehicle_750 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11838.246, 12700.631, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_750 = _this;
  _this setPos [11838.246, 12700.631, 1.5258789e-005];
};

_vehicle_751 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11818.943, 12698.985, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_751 = _this;
  _this setPos [11818.943, 12698.985, 3.0517578e-005];
};

_vehicle_752 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11804.929, 12699.908, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_752 = _this;
  _this setPos [11804.929, 12699.908, 1.5258789e-005];
};

_vehicle_753 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11799.866, 12702.775], [], 0, "CAN_COLLIDE"];
  _vehicle_753 = _this;
  _this setPos [11799.866, 12702.775];
};

_vehicle_754 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11804.323, 12716.44], [], 0, "CAN_COLLIDE"];
  _vehicle_754 = _this;
  _this setPos [11804.323, 12716.44];
};

_vehicle_755 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11809.666, 12730.78, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_755 = _this;
  _this setPos [11809.666, 12730.78, 4.5776367e-005];
};

_vehicle_756 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11809.943, 12706.009, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_756 = _this;
  _this setPos [11809.943, 12706.009, 1.5258789e-005];
};

_vehicle_757 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11806.612, 12722.667, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_757 = _this;
  _this setPos [11806.612, 12722.667, 1.5258789e-005];
};

_vehicle_758 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11807.453, 12678.974, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_758 = _this;
  _this setPos [11807.453, 12678.974, 3.0517578e-005];
};

_vehicle_762 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11813.642, 12700.945, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_762 = _this;
  _this setPos [11813.642, 12700.945, 1.5258789e-005];
};

_vehicle_763 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [11803.599, 12715.265, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_763 = _this;
  _this setPos [11803.599, 12715.265, 3.0517578e-005];
};

_vehicle_765 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [11824.221, 12735.876, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_765 = _this;
  _this setPos [11824.221, 12735.876, -3.0517578e-005];
};

_vehicle_766 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [11800.929, 12684.552, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_766 = _this;
  _this setDir 0.041598521;
  _this setPos [11800.929, 12684.552, 3.0517578e-005];
};

_vehicle_767 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11844.866, 12719.793, 0.056610294], [], 0, "CAN_COLLIDE"];
  _vehicle_767 = _this;
  _this setPos [11844.866, 12719.793, 0.056610294];
};

_vehicle_768 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11843.339, 12715.121, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_768 = _this;
  _this setPos [11843.339, 12715.121, 1.5258789e-005];
};

_vehicle_769 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11820.615, 12737.392, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_769 = _this;
  _this setPos [11820.615, 12737.392, 4.5776367e-005];
};

_vehicle_770 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11826.223, 12737.692, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_770 = _this;
  _this setDir -1.4268359;
  _this setPos [11826.223, 12737.692, 4.5776367e-005];
};

_vehicle_771 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11839.707, 12727.313], [], 0, "CAN_COLLIDE"];
  _vehicle_771 = _this;
  _this setPos [11839.707, 12727.313];
};

_vehicle_772 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [11814.046, 12816.501, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_772 = _this;
  _this setPos [11814.046, 12816.501, 0.0001373291];
};

_vehicle_773 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [11796.509, 12826.645, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_773 = _this;
  _this setPos [11796.509, 12826.645, 3.0517578e-005];
};

_vehicle_774 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11807.69, 12818.504], [], 0, "CAN_COLLIDE"];
  _vehicle_774 = _this;
  _this setPos [11807.69, 12818.504];
};

_vehicle_775 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11797.559, 12823.833, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_775 = _this;
  _this setPos [11797.559, 12823.833, 6.1035156e-005];
};

_vehicle_776 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11793.608, 12827.463, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_776 = _this;
  _this setPos [11793.608, 12827.463, 1.5258789e-005];
};

_vehicle_777 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11836.49, 12854.131, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_777 = _this;
  _this setPos [11836.49, 12854.131, 0.0001373291];
};

_vehicle_778 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11842.257, 12851.546, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_778 = _this;
  _this setPos [11842.257, 12851.546, 0.00015258789];
};

_vehicle_779 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11846.956, 12848.443, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_779 = _this;
  _this setPos [11846.956, 12848.443, 0.00012207031];
};

_vehicle_780 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11848.128, 12852.033, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_780 = _this;
  _this setPos [11848.128, 12852.033, 9.1552734e-005];
};

_vehicle_781 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11853.769, 12848.179, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_781 = _this;
  _this setPos [11853.769, 12848.179, 0.0001373291];
};

_vehicle_782 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11864.362, 12843.893, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_782 = _this;
  _this setPos [11864.362, 12843.893, 9.1552734e-005];
};

_vehicle_783 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11878.519, 12838.78, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_783 = _this;
  _this setPos [11878.519, 12838.78, 7.6293945e-005];
};

_vehicle_784 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11883.411, 12835.259, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_784 = _this;
  _this setPos [11883.411, 12835.259, 4.5776367e-005];
};

_vehicle_785 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11887.251, 12838.314, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_785 = _this;
  _this setPos [11887.251, 12838.314, 1.5258789e-005];
};

_vehicle_786 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11890.964, 12834.703, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_786 = _this;
  _this setPos [11890.964, 12834.703, 0.00012207031];
};

_vehicle_787 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11907.015, 12828.61, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_787 = _this;
  _this setPos [11907.015, 12828.61, 9.1552734e-005];
};

_vehicle_788 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11935.533, 12819.3, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_788 = _this;
  _this setPos [11935.533, 12819.3, 4.5776367e-005];
};

_vehicle_789 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11952.513, 12828.566, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_789 = _this;
  _this setPos [11952.513, 12828.566, 3.0517578e-005];
};

_vehicle_790 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11950.099, 12813.44, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_790 = _this;
  _this setPos [11950.099, 12813.44, 1.5258789e-005];
};

_vehicle_791 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11944.22, 12816.522, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_791 = _this;
  _this setPos [11944.22, 12816.522, 4.5776367e-005];
};

_vehicle_792 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11943.803, 12827.45, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_792 = _this;
  _this setPos [11943.803, 12827.45, 6.1035156e-005];
};

_vehicle_793 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11958.196, 12821.444, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_793 = _this;
  _this setPos [11958.196, 12821.444, 6.1035156e-005];
};

_vehicle_794 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11949.313, 12822.857, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_794 = _this;
  _this setPos [11949.313, 12822.857, 7.6293945e-005];
};

_vehicle_795 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11953.699, 12813.595, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_795 = _this;
  _this setPos [11953.699, 12813.595, 1.5258789e-005];
};

_vehicle_796 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12008.005, 12796.311, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_796 = _this;
  _this setPos [12008.005, 12796.311, -1.5258789e-005];
};

_vehicle_797 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12117.845, 12759.927, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_797 = _this;
  _this setPos [12117.845, 12759.927, 3.0517578e-005];
};

_vehicle_798 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12133.021, 12759.303, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_798 = _this;
  _this setPos [12133.021, 12759.303, 0.00010681152];
};

_vehicle_799 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12128.225, 12756.171, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_799 = _this;
  _this setPos [12128.225, 12756.171, 0.00010681152];
};

_vehicle_800 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12147.852, 12746.769, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_800 = _this;
  _this setPos [12147.852, 12746.769, 7.6293945e-005];
};

_vehicle_801 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12184.265, 12735.798, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_801 = _this;
  _this setPos [12184.265, 12735.798, 0];
};

_vehicle_802 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12136.681, 12754.995, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_802 = _this;
  _this setPos [12136.681, 12754.995, 7.6293945e-005];
};

_vehicle_803 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12137.869, 12750.881, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_803 = _this;
  _this setPos [12137.869, 12750.881, 0];
};

_vehicle_804 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12156.371, 12746.715, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_804 = _this;
  _this setPos [12156.371, 12746.715, 7.6293945e-005];
};

_vehicle_805 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12153.052, 12743.621, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_805 = _this;
  _this setPos [12153.052, 12743.621, 1.5258789e-005];
};

_vehicle_806 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12150.361, 12754.099, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_806 = _this;
  _this setPos [12150.361, 12754.099, -9.1552734e-005];
};

_vehicle_807 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12165.236, 12740.187, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_807 = _this;
  _this setPos [12165.236, 12740.187, 0.0001373291];
};

_vehicle_808 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12178.128, 12739.402, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_808 = _this;
  _this setPos [12178.128, 12739.402, 7.6293945e-005];
};

_vehicle_809 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12188.825, 12732.098, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_809 = _this;
  _this setPos [12188.825, 12732.098, 6.1035156e-005];
};

_vehicle_810 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12191.196, 12735.316, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_810 = _this;
  _this setPos [12191.196, 12735.316, 0];
};

_vehicle_811 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12192.401, 12730.188, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_811 = _this;
  _this setPos [12192.401, 12730.188, 7.6293945e-005];
};

_vehicle_812 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12200.006, 12726.648, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_812 = _this;
  _this setPos [12200.006, 12726.648, 7.6293945e-005];
};

_vehicle_813 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12204.225, 12726.297, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_813 = _this;
  _this setPos [12204.225, 12726.297, 9.1552734e-005];
};

_vehicle_814 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [12208.536, 12725.647, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_814 = _this;
  _this setPos [12208.536, 12725.647, 3.0517578e-005];
};

_vehicle_815 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12054.06, 12740.917, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_815 = _this;
  _this setPos [12054.06, 12740.917, 9.1552734e-005];
};

_vehicle_816 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12025.574, 12749.659, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_816 = _this;
  _this setPos [12025.574, 12749.659, 9.1552734e-005];
};

_vehicle_817 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12006.285, 12756.392, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_817 = _this;
  _this setPos [12006.285, 12756.392, 0.00015258789];
};

_vehicle_818 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11980.128, 12765.914, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_818 = _this;
  _this setPos [11980.128, 12765.914, 3.0517578e-005];
};

_vehicle_819 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11936.57, 12782.323, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_819 = _this;
  _this setPos [11936.57, 12782.323, 6.1035156e-005];
};

_vehicle_820 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11955.332, 12775.495, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_820 = _this;
  _this setPos [11955.332, 12775.495, 9.1552734e-005];
};

_vehicle_821 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [12073.566, 12734.614, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_821 = _this;
  _this setPos [12073.566, 12734.614, 7.6293945e-005];
};

_vehicle_822 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11923.708, 12786.941, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_822 = _this;
  _this setDir -2.3747926;
  _this setPos [11923.708, 12786.941, 7.6293945e-005];
};

_vehicle_823 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11930.472, 12785.502, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_823 = _this;
  _this setPos [11930.472, 12785.502, 0.00015258789];
};

_vehicle_824 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11931.472, 12782.893, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_824 = _this;
  _this setPos [11931.472, 12782.893, 0.00021362305];
};

_vehicle_825 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11927.83, 12784.332, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_825 = _this;
  _this setPos [11927.83, 12784.332, 9.1552734e-005];
};

_vehicle_826 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11902.634, 12793.591, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_826 = _this;
  _this setPos [11902.634, 12793.591, 7.6293945e-005];
};

_vehicle_827 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11913.861, 12789.534, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_827 = _this;
  _this setPos [11913.861, 12789.534, 4.5776367e-005];
};

_vehicle_828 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11945.764, 12779.886, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_828 = _this;
  _this setPos [11945.764, 12779.886, 0.00018310547];
};

_vehicle_829 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11951.002, 12776.531, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_829 = _this;
  _this setPos [11951.002, 12776.531, 0.0001373291];
};

_vehicle_830 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11951.127, 12772.155, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_830 = _this;
  _this setPos [11951.127, 12772.155, 0.00015258789];
};

_vehicle_831 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11961.967, 12776.856, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_831 = _this;
  _this setPos [11961.967, 12776.856, -3.0517578e-005];
};

_vehicle_832 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11965.509, 12769.965, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_832 = _this;
  _this setPos [11965.509, 12769.965, 0.00016784668];
};

_vehicle_833 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11969.147, 12772.068, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_833 = _this;
  _this setPos [11969.147, 12772.068, 9.1552734e-005];
};

_vehicle_834 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11967.332, 12766.102, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_834 = _this;
  _this setPos [11967.332, 12766.102, 4.5776367e-005];
};

_vehicle_835 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11962.236, 12773.236, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_835 = _this;
  _this setPos [11962.236, 12773.236, 1.5258789e-005];
};

_vehicle_836 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11975.337, 12766.938, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_836 = _this;
  _this setPos [11975.337, 12766.938, 3.0517578e-005];
};

_vehicle_837 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11984.602, 12761.876, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_837 = _this;
  _this setPos [11984.602, 12761.876, -4.5776367e-005];
};

_vehicle_838 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11993.919, 12764.126, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_838 = _this;
  _this setPos [11993.919, 12764.126, 9.1552734e-005];
};

_vehicle_839 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12012.58, 12756.699], [], 0, "CAN_COLLIDE"];
  _vehicle_839 = _this;
  _this setPos [12012.58, 12756.699];
};

_vehicle_840 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12022.427, 12750.267, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_840 = _this;
  _this setPos [12022.427, 12750.267, 4.5776367e-005];
};

_vehicle_841 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11758.516, 12691.019, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_841 = _this;
  _this setPos [11758.516, 12691.019, 1.5258789e-005];
};

_vehicle_842 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11756.434, 12678.248, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_842 = _this;
  _this setPos [11756.434, 12678.248, 7.6293945e-005];
};

_vehicle_843 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11759.253, 12674.467, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_843 = _this;
  _this setPos [11759.253, 12674.467, 7.6293945e-005];
};

_vehicle_844 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11759.995, 12685.612, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_844 = _this;
  _this setDir -10.916196;
  _this setPos [11759.995, 12685.612, 0.00018310547];
};

_vehicle_845 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11767.412, 12683.143, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_845 = _this;
  _this setDir 0.10687801;
  _this setPos [11767.412, 12683.143, -1.5258789e-005];
};

_vehicle_846 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11761.241, 12711.639, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_846 = _this;
  _this setDir 0.94518417;
  _this setPos [11761.241, 12711.639, 0.00016784668];
};

_vehicle_847 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11761.578, 12698.003], [], 0, "CAN_COLLIDE"];
  _vehicle_847 = _this;
  _this setPos [11761.578, 12698.003];
};

_vehicle_848 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11752.19, 12658.451, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_848 = _this;
  _this setPos [11752.19, 12658.451, 9.1552734e-005];
};

_vehicle_849 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11748.283, 12630.049, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_849 = _this;
  _this setPos [11748.283, 12630.049, 0.00012207031];
};

_vehicle_850 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11744.133, 12628.508, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_850 = _this;
  _this setPos [11744.133, 12628.508, 0.00021362305];
};

_vehicle_851 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11748.208, 12636.246, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_851 = _this;
  _this setPos [11748.208, 12636.246, 6.1035156e-005];
};

_vehicle_852 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11743.309, 12620.921, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_852 = _this;
  _this setPos [11743.309, 12620.921, 0.00015258789];
};

_vehicle_853 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11750.402, 12620.452, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_853 = _this;
  _this setPos [11750.402, 12620.452, 4.5776367e-005];
};

_vehicle_854 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11754.922, 12619.889, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_854 = _this;
  _this setPos [11754.922, 12619.889, -0.0001373291];
};

_vehicle_855 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11745.587, 12623.846, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_855 = _this;
  _this setPos [11745.587, 12623.846, 7.6293945e-005];
};

_vehicle_856 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11752.018, 12652.901, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_856 = _this;
  _this setPos [11752.018, 12652.901, 0.00015258789];
};

_vehicle_857 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11755.454, 12664.478, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_857 = _this;
  _this setPos [11755.454, 12664.478, -3.0517578e-005];
};

_vehicle_858 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11764.015, 12688.8, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_858 = _this;
  _this setPos [11764.015, 12688.8, 4.5776367e-005];
};

_vehicle_859 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11761.137, 12680.504, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_859 = _this;
  _this setPos [11761.137, 12680.504, 6.1035156e-005];
};

_vehicle_860 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [11771.376, 12733.112, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_860 = _this;
  _this setPos [11771.376, 12733.112, -3.0517578e-005];
};

_vehicle_861 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12081.068, 12733.444, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_861 = _this;
  _this setPos [12081.068, 12733.444, 6.1035156e-005];
};

_vehicle_862 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12062.471, 12739.771, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_862 = _this;
  _this setPos [12062.471, 12739.771, 0.00010681152];
};

_vehicle_863 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12079.02, 12736.711, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_863 = _this;
  _this setDir 51.187977;
  _this setPos [12079.02, 12736.711, 9.1552734e-005];
};

_vehicle_864 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [12045.81, 12744.362, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_864 = _this;
  _this setPos [12045.81, 12744.362, 1.5258789e-005];
};

_vehicle_865 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_a_stationhouse", [12232.37, 12717.122, 0.1823587], [], 0, "CAN_COLLIDE"];
  _vehicle_865 = _this;
  _this setDir 19.424667;
  _this setPos [12232.37, 12717.122, 0.1823587];
};

_vehicle_866 = objNull;
if (true) then
{
  _this = createVehicle ["HeliH", [11826.112, 12686.838, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_866 = _this;
  _this setDir -40.450752;
  _this setPos [11826.112, 12686.838, -1.5258789e-005];
};

_vehicle_871 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_CraneCon", [11955.985, 12835.626, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_871 = _this;
  _this setDir 109.68633;
  _this setPos [11955.985, 12835.626, 7.6293945e-005];
};

_vehicle_877 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo2A", [11950.069, 12819.615, 15.894426], [], 0, "CAN_COLLIDE"];
  _vehicle_877 = _this;
  _this setPos [11950.069, 12819.615, 15.894426];
};

_vehicle_878 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo2A", [11937.14, 12826.691, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_878 = _this;
  _this setDir -70.140549;
  _this setPos [11937.14, 12826.691, 3.0517578e-005];
};

_vehicle_879 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo2A", [11934.35, 12833.281, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_879 = _this;
  _this setDir 20.920361;
  _this setPos [11934.35, 12833.281, -1.5258789e-005];
};

_vehicle_880 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12442.271, 12552.189, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_880 = _this;
  _this setPos [12442.271, 12552.189, 0];
};

_vehicle_881 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12430.478, 12544.043, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_881 = _this;
  _this setPos [12430.478, 12544.043, 4.5776367e-005];
};

_vehicle_882 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12449.432, 12540.645, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_882 = _this;
  _this setPos [12449.432, 12540.645, -1.5258789e-005];
};

_vehicle_883 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12465.492, 12545.214, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_883 = _this;
  _this setPos [12465.492, 12545.214, 6.1035156e-005];
};

_vehicle_884 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12425.261, 12535.525, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_884 = _this;
  _this setPos [12425.261, 12535.525, 1.5258789e-005];
};

_vehicle_885 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12456.226, 12558.739, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_885 = _this;
  _this setPos [12456.226, 12558.739, -1.5258789e-005];
};

_vehicle_886 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12438.813, 12566.919, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_886 = _this;
  _this setPos [12438.813, 12566.919, 1.5258789e-005];
};

_vehicle_887 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12450.732, 12576.606, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_887 = _this;
  _this setPos [12450.732, 12576.606, 1.5258789e-005];
};

_vehicle_888 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12457.761, 12567.789, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_888 = _this;
  _this setPos [12457.761, 12567.789, 0];
};

_vehicle_889 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12395.757, 12538.813, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_889 = _this;
  _this setPos [12395.757, 12538.813, -1.5258789e-005];
};

_vehicle_890 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12400.62, 12552.679, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_890 = _this;
  _this setPos [12400.62, 12552.679, 3.0517578e-005];
};

_vehicle_891 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12408.666, 12536.087, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_891 = _this;
  _this setPos [12408.666, 12536.087, -1.5258789e-005];
};

_vehicle_892 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12416.289, 12550.595, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_892 = _this;
  _this setPos [12416.289, 12550.595, -3.0517578e-005];
};

_vehicle_893 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12388.49, 12552.346, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_893 = _this;
  _this setPos [12388.49, 12552.346, 3.0517578e-005];
};

_vehicle_894 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12384, 12538.841, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_894 = _this;
  _this setPos [12384, 12538.841, 0];
};

_vehicle_895 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12426.479, 12557.555, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_895 = _this;
  _this setPos [12426.479, 12557.555, 1.5258789e-005];
};

_vehicle_896 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12408.701, 12563.592, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_896 = _this;
  _this setPos [12408.701, 12563.592, -1.5258789e-005];
};

_vehicle_897 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12394.56, 12568.194, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_897 = _this;
  _this setPos [12394.56, 12568.194, 1.5258789e-005];
};

_vehicle_898 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12380.634, 12564.559, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_898 = _this;
  _this setPos [12380.634, 12564.559, 4.5776367e-005];
};

_vehicle_899 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12373.369, 12551.427, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_899 = _this;
  _this setPos [12373.369, 12551.427, 0.00010681152];
};

_vehicle_900 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12366.266, 12544.295, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_900 = _this;
  _this setPos [12366.266, 12544.295, 1.5258789e-005];
};

_vehicle_901 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12334.169, 12554.722, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_901 = _this;
  _this setPos [12334.169, 12554.722, -6.1035156e-005];
};

_vehicle_902 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12351.031, 12548.924, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_902 = _this;
  _this setPos [12351.031, 12548.924, 0];
};

_vehicle_903 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12350.245, 12559.918, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_903 = _this;
  _this setPos [12350.245, 12559.918, 0];
};

_vehicle_904 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12363.748, 12559.989, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_904 = _this;
  _this setPos [12363.748, 12559.989, -4.5776367e-005];
};

_vehicle_905 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12370.119, 12573.593, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_905 = _this;
  _this setPos [12370.119, 12573.593, -4.5776367e-005];
};

_vehicle_906 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12352.177, 12570.781, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_906 = _this;
  _this setPos [12352.177, 12570.781, 1.5258789e-005];
};

_vehicle_907 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12339.235, 12569.342, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_907 = _this;
  _this setPos [12339.235, 12569.342, 4.5776367e-005];
};

_vehicle_908 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12341.75, 12581.712, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_908 = _this;
  _this setPos [12341.75, 12581.712, 3.0517578e-005];
};

_vehicle_909 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12356.782, 12581.574, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_909 = _this;
  _this setPos [12356.782, 12581.574, 6.1035156e-005];
};

_vehicle_910 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12421.073, 12570.282, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_910 = _this;
  _this setPos [12421.073, 12570.282, 0];
};

_vehicle_911 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12432.096, 12582.285, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_911 = _this;
  _this setPos [12432.096, 12582.285, 6.1035156e-005];
};

_vehicle_912 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12445.685, 12589.345, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_912 = _this;
  _this setPos [12445.685, 12589.345, 0];
};

_vehicle_913 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12463.488, 12584.573, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_913 = _this;
  _this setPos [12463.488, 12584.573, 0.0001373291];
};

_vehicle_914 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12471.046, 12573.526, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_914 = _this;
  _this setPos [12471.046, 12573.526, 4.5776367e-005];
};

_vehicle_915 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12472.326, 12560.66, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_915 = _this;
  _this setPos [12472.326, 12560.66, 7.6293945e-005];
};

_vehicle_916 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12483.193, 12547.455, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_916 = _this;
  _this setPos [12483.193, 12547.455, -0.00010681152];
};

_vehicle_917 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12480.576, 12582.136, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_917 = _this;
  _this setPos [12480.576, 12582.136, -1.5258789e-005];
};

_vehicle_918 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12493.433, 12574.153, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_918 = _this;
  _this setPos [12493.433, 12574.153, 9.1552734e-005];
};

_vehicle_919 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12483.951, 12567.021, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_919 = _this;
  _this setPos [12483.951, 12567.021, 4.5776367e-005];
};

_vehicle_920 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12493.922, 12555.471, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_920 = _this;
  _this setPos [12493.922, 12555.471, 0.00021362305];
};

_vehicle_921 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12505.946, 12567.163, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_921 = _this;
  _this setPos [12505.946, 12567.163, 1.5258789e-005];
};

_vehicle_922 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12521.453, 12565.87, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_922 = _this;
  _this setPos [12521.453, 12565.87, 6.1035156e-005];
};

_vehicle_923 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12508.19, 12551.933, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_923 = _this;
  _this setPos [12508.19, 12551.933, 0];
};

_vehicle_924 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12500.205, 12540.32, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_924 = _this;
  _this setPos [12500.205, 12540.32, -9.1552734e-005];
};

_vehicle_925 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12514.83, 12535.696, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_925 = _this;
  _this setPos [12514.83, 12535.696, 0];
};

_vehicle_926 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12522.354, 12550.548, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_926 = _this;
  _this setPos [12522.354, 12550.548, 3.0517578e-005];
};

_vehicle_927 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12429.923, 12595.854, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_927 = _this;
  _this setPos [12429.923, 12595.854, 4.5776367e-005];
};

_vehicle_928 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12419.021, 12585.822, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_928 = _this;
  _this setPos [12419.021, 12585.822, -4.5776367e-005];
};

_vehicle_929 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12413.174, 12600.412, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_929 = _this;
  _this setPos [12413.174, 12600.412, 4.5776367e-005];
};

_vehicle_930 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12406.385, 12578.617, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_930 = _this;
  _this setPos [12406.385, 12578.617, 0.00010681152];
};

_vehicle_931 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12402.657, 12591.602, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_931 = _this;
  _this setPos [12402.657, 12591.602, 1.5258789e-005];
};

_vehicle_932 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12399.559, 12605.799, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_932 = _this;
  _this setPos [12399.559, 12605.799, -1.5258789e-005];
};

_vehicle_933 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12389.27, 12581.629, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_933 = _this;
  _this setPos [12389.27, 12581.629, 1.5258789e-005];
};

_vehicle_934 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12387.352, 12597.922, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_934 = _this;
  _this setPos [12387.352, 12597.922, 3.0517578e-005];
};

_vehicle_935 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12386.011, 12612.169, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_935 = _this;
  _this setPos [12386.011, 12612.169, 7.6293945e-005];
};

_vehicle_936 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12377.904, 12586.219, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_936 = _this;
  _this setPos [12377.904, 12586.219, 4.5776367e-005];
};

_vehicle_937 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12367.424, 12592.359, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_937 = _this;
  _this setPos [12367.424, 12592.359, 0];
};

_vehicle_938 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12373.454, 12604.828, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_938 = _this;
  _this setPos [12373.454, 12604.828, 1.5258789e-005];
};

_vehicle_939 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12370.631, 12615.927, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_939 = _this;
  _this setPos [12370.631, 12615.927, 0.00010681152];
};

_vehicle_940 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12355.406, 12617.514, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_940 = _this;
  _this setPos [12355.406, 12617.514, 7.6293945e-005];
};

_vehicle_941 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12358.88, 12601.826, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_941 = _this;
  _this setPos [12358.88, 12601.826, 0];
};

_vehicle_942 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12349.838, 12592.343, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_942 = _this;
  _this setPos [12349.838, 12592.343, 1.5258789e-005];
};

_vehicle_943 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12342.66, 12623.02, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_943 = _this;
  _this setPos [12342.66, 12623.02, 6.1035156e-005];
};

_vehicle_944 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12343.703, 12607.529, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_944 = _this;
  _this setPos [12343.703, 12607.529, 0.00018310547];
};

_vehicle_945 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12335.102, 12598.135, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_945 = _this;
  _this setPos [12335.102, 12598.135, 4.5776367e-005];
};

_vehicle_946 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12329.567, 12613.764, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_946 = _this;
  _this setPos [12329.567, 12613.764, 1.5258789e-005];
};

_vehicle_947 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12331.512, 12628.779, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_947 = _this;
  _this setPos [12331.512, 12628.779, 0];
};

_vehicle_948 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12320.063, 12603.49, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_948 = _this;
  _this setPos [12320.063, 12603.49, 4.5776367e-005];
};

_vehicle_949 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12318.122, 12632.779, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_949 = _this;
  _this setPos [12318.122, 12632.779, 6.1035156e-005];
};

_vehicle_950 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12317.383, 12619.231, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_950 = _this;
  _this setPos [12317.383, 12619.231, 6.1035156e-005];
};

_vehicle_951 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12307.5, 12612.077, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_951 = _this;
  _this setPos [12307.5, 12612.077, 0.0001373291];
};

_vehicle_952 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12305.892, 12624.138, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_952 = _this;
  _this setPos [12305.892, 12624.138, 1.5258789e-005];
};

_vehicle_953 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [12309.41, 12640.396, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_953 = _this;
  _this setPos [12309.41, 12640.396, 3.0517578e-005];
};

_vehicle_955 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11452.719, 12361.721, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_955 = _this;
  _this setDir 34.979858;
  _this setPos [11452.719, 12361.721, -0.00012207031];
};

_vehicle_957 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [11462.829, 12375.916, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_957 = _this;
  _this setDir 35.166157;
  _this setPos [11462.829, 12375.916, 7.6293945e-005];
};

_vehicle_961 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [11476.913, 12389.394], [], 0, "CAN_COLLIDE"];
  _vehicle_961 = _this;
  _this setDir 57.591789;
  _this setPos [11476.913, 12389.394];
};

_vehicle_963 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_12", [11492.344, 12397.435, 0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_963 = _this;
  _this setDir 67.918961;
  _this setPos [11492.344, 12397.435, 0.00028991699];
};

_vehicle_964 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [11503.858, 12402.206, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_964 = _this;
  _this setDir 68.242081;
  _this setPos [11503.858, 12402.206, 0.00019836426];
};

_vehicle_966 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_50", [11520.535, 12407.255, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_966 = _this;
  _this setDir -281.65009;
  _this setPos [11520.535, 12407.255, 0.00018310547];
};

_vehicle_967 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [11541.607, 12411.968, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_967 = _this;
  _this setDir -121.23314;
  _this setPos [11541.607, 12411.968, 6.1035156e-005];
};

_vehicle_968 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [11555.626, 12425.41, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_968 = _this;
  _this setDir -145.14151;
  _this setPos [11555.626, 12425.41, 3.0517578e-005];
};

_vehicle_969 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11557.778, 12429.174, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_969 = _this;
  _this setDir -155.17632;
  _this setPos [11557.778, 12429.174, 1.5258789e-005];
};

_vehicle_970 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11557.74, 12429.193, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_970 = _this;
  _this setDir 24.709036;
  _this setPos [11557.74, 12429.193, 1.5258789e-005];
};

_vehicle_972 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_50", [11565.08, 12445.01], [], 0, "CAN_COLLIDE"];
  _vehicle_972 = _this;
  _this setDir 25.540455;
  _this setPos [11565.08, 12445.01];
};

_vehicle_973 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_50", [11572.921, 12460.189, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_973 = _this;
  _this setDir -160.4577;
  _this setPos [11572.921, 12460.189, -3.0517578e-005];
};

_vehicle_975 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11572.876, 12460.176, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_975 = _this;
  _this setDir 20.229797;
  _this setPos [11572.876, 12460.176, -4.5776367e-005];
};

_vehicle_976 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11578.853, 12476.429, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_976 = _this;
  _this setDir 20.229797;
  _this setPos [11578.853, 12476.429, -1.5258789e-005];
};

_vehicle_977 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11584.738, 12492.564, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_977 = _this;
  _this setDir 20.229797;
  _this setPos [11584.738, 12492.564, -4.5776367e-005];
};

_vehicle_978 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11590.516, 12508.706, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_978 = _this;
  _this setDir 20.229797;
  _this setPos [11590.516, 12508.706, 9.1552734e-005];
};

_vehicle_979 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11596.466, 12524.867], [], 0, "CAN_COLLIDE"];
  _vehicle_979 = _this;
  _this setDir 20.229797;
  _this setPos [11596.466, 12524.867];
};

_vehicle_980 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11602.521, 12541.235, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_980 = _this;
  _this setDir 20.609941;
  _this setPos [11602.521, 12541.235, 3.0517578e-005];
};

_vehicle_981 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11606.245, 12549.09], [], 0, "CAN_COLLIDE"];
  _vehicle_981 = _this;
  _this setDir -158.93706;
  _this setPos [11606.245, 12549.09];
};

_vehicle_982 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11609.935, 12561.599, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_982 = _this;
  _this setDir -168.41574;
  _this setPos [11609.935, 12561.599, -1.5258789e-005];
};

_vehicle_983 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [11609.889, 12561.584, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_983 = _this;
  _this setDir 11.37239;
  _this setPos [11609.889, 12561.584, -9.1552734e-005];
};

_vehicle_984 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11613.389, 12578.669, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_984 = _this;
  _this setDir 12.490353;
  _this setPos [11613.389, 12578.669, -7.6293945e-005];
};

_vehicle_985 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11614.708, 12582.824, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_985 = _this;
  _this setDir 22.567684;
  _this setPos [11614.708, 12582.824, -4.5776367e-005];
};

_vehicle_986 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [11629.368, 12617.766, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_986 = _this;
  _this setDir -156.8533;
  _this setPos [11629.368, 12617.766, -1.5258789e-005];
};

_vehicle_998 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [11620.234, 12595.912, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_998 = _this;
  _this setDir 22.039507;
  _this setPos [11620.234, 12595.912, -6.1035156e-005];
};

_vehicle_999 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11631.734, 12623.538, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_999 = _this;
  _this setDir 23.247337;
  _this setPos [11631.734, 12623.538, 7.6293945e-005];
};

_vehicle_1001 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11647.515, 12611.309, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1001 = _this;
  _this setDir -77.336777;
  _this setPos [11647.515, 12611.309, 1.5258789e-005];
};

_vehicle_1002 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11660.428, 12609.48], [], 0, "CAN_COLLIDE"];
  _vehicle_1002 = _this;
  _this setDir -87.274902;
  _this setPos [11660.428, 12609.48];
};

_vehicle_1003 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11682.136, 12608.352, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1003 = _this;
  _this setDir 101.40969;
  _this setPos [11682.136, 12608.352, 9.1552734e-005];
};

_vehicle_1004 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [11677.85, 12608.826, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1004 = _this;
  _this setDir -88.291359;
  _this setPos [11677.85, 12608.826, -7.6293945e-005];
};

_vehicle_1005 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11677.835, 12608.842, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1005 = _this;
  _this setDir 91.703278;
  _this setPos [11677.835, 12608.842, 7.6293945e-005];
};

_vehicle_1006 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11707.242, 12601.081, 0.00038146973], [], 0, "CAN_COLLIDE"];
  _vehicle_1006 = _this;
  _this setDir -78.694984;
  _this setPos [11707.242, 12601.081, 0.00038146973];
};

_vehicle_1007 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11707.215, 12601.073, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_1007 = _this;
  _this setDir 100.87107;
  _this setPos [11707.215, 12601.073, 0.00036621094];
};

_vehicle_1008 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11719.782, 12597.515, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1008 = _this;
  _this setDir 110.50448;
  _this setPos [11719.782, 12597.515, 1.5258789e-005];
};

_vehicle_1009 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11746.673, 12583.146, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1009 = _this;
  _this setDir -60.034969;
  _this setPos [11746.673, 12583.146, 0.00012207031];
};

_vehicle_1010 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11761.941, 12574.764, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1010 = _this;
  _this setDir -61.220242;
  _this setPos [11761.941, 12574.764, 1.5258789e-005];
};

_vehicle_1011 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11784.256, 12561.454, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1011 = _this;
  _this setDir -60.576988;
  _this setPos [11784.256, 12561.454, 1.5258789e-005];
};

_vehicle_1012 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11799.406, 12552.884, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1012 = _this;
  _this setDir -60.594635;
  _this setPos [11799.406, 12552.884, 0.00012207031];
};

_vehicle_1013 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11829.229, 12534.988, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1013 = _this;
  _this setDir -61.810917;
  _this setPos [11829.229, 12534.988, 3.0517578e-005];
};

_vehicle_1014 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11761.913, 12574.8, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1014 = _this;
  _this setDir -241.27159;
  _this setPos [11761.913, 12574.8, 7.6293945e-005];
};

_vehicle_1015 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11769.062, 12570.015, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1015 = _this;
  _this setDir -59.944794;
  _this setPos [11769.062, 12570.015, 1.5258789e-005];
};

_vehicle_1016 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11799.379, 12552.884, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1016 = _this;
  _this setDir 119.09927;
  _this setPos [11799.379, 12552.884, -6.1035156e-005];
};

_vehicle_1017 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11813.885, 12543.239, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1017 = _this;
  _this setDir -61.089008;
  _this setPos [11813.885, 12543.239, 0.00010681152];
};

_vehicle_1018 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [11829.237, 12535.061, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1018 = _this;
  _this setDir 118.26004;
  _this setPos [11829.237, 12535.061, -4.5776367e-005];
};

_vehicle_1020 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [11860.052, 12524.008, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1020 = _this;
  _this setDir 238.74455;
  _this setPos [11860.052, 12524.008, -3.0517578e-005];
};

_vehicle_1021 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [11849.977, 12523.648, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1021 = _this;
  _this setDir -60.91338;
  _this setPos [11849.977, 12523.648, 6.1035156e-005];
};

_vehicle_1022 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [11868.909, 12533.428, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1022 = _this;
  _this setDir -151.49895;
  _this setPos [11868.909, 12533.428, 4.5776367e-005];
};

_vehicle_1023 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11899.109, 12595.881, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1023 = _this;
  _this setDir 25.397573;
  _this setPos [11899.109, 12595.881, 3.0517578e-005];
};

_vehicle_1024 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11884.425, 12564.26, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1024 = _this;
  _this setDir 24.821568;
  _this setPos [11884.425, 12564.26, 4.5776367e-005];
};

_vehicle_1025 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11891.787, 12580.064, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1025 = _this;
  _this setDir 24.821568;
  _this setPos [11891.787, 12580.064, 1.5258789e-005];
};

_vehicle_1026 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11868.896, 12533.417, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1026 = _this;
  _this setDir 28.540073;
  _this setPos [11868.896, 12533.417, 0.00010681152];
};

_vehicle_1027 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11879.107, 12552.644, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1027 = _this;
  _this setDir -160.66373;
  _this setPos [11879.107, 12552.644, 0.00010681152];
};

_vehicle_1028 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11879.089, 12552.635, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1028 = _this;
  _this setDir 19.122307;
  _this setPos [11879.089, 12552.635, -3.0517578e-005];
};

_vehicle_1029 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_50", [11884.514, 12564.377, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1029 = _this;
  _this setDir -158.72308;
  _this setPos [11884.514, 12564.377, 7.6293945e-005];
};

_vehicle_1030 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [11912.763, 12627.926, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1030 = _this;
  _this setDir -164.3732;
  _this setPos [11912.763, 12627.926, 1.5258789e-005];
};

_vehicle_1031 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [11912.752, 12627.93, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1031 = _this;
  _this setDir 15.373545;
  _this setPos [11912.752, 12627.93, -0.00015258789];
};

_vehicle_1032 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [11941.104, 12656.398, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1032 = _this;
  _this setDir 68.367599;
  _this setPos [11941.104, 12656.398, 1.5258789e-005];
};

_vehicle_1033 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11922.104, 12642.39, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1033 = _this;
  _this setDir -144.41342;
  _this setPos [11922.104, 12642.39, -6.1035156e-005];
};

_vehicle_1034 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11922.03, 12642.365, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1034 = _this;
  _this setDir 35.489765;
  _this setPos [11922.03, 12642.365, -7.6293945e-005];
};

_vehicle_1035 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [11924.854, 12645.683, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1035 = _this;
  _this setDir 45.262985;
  _this setPos [11924.854, 12645.683, 4.5776367e-005];
};

_vehicle_1037 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11960.076, 12661.234, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1037 = _this;
  _this setDir 83.571251;
  _this setPos [11960.076, 12661.234, -1.5258789e-005];
};

_vehicle_1038 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11968.766, 12661.461, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1038 = _this;
  _this setDir -95.946777;
  _this setPos [11968.766, 12661.461, 1.5258789e-005];
};

_vehicle_1039 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [11980.977, 12665.234, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1039 = _this;
  _this setDir -122.22685;
  _this setPos [11980.977, 12665.234, 1.5258789e-005];
};

_vehicle_1040 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [11980.913, 12665.286], [], 0, "CAN_COLLIDE"];
  _vehicle_1040 = _this;
  _this setDir 57.560814;
  _this setPos [11980.913, 12665.286];
};

_vehicle_1041 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [12002.152, 12675.723, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1041 = _this;
  _this setDir -112.40295;
  _this setPos [12002.152, 12675.723, 1.5258789e-005];
};

_vehicle_1042 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11633.753, 12627.378, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1042 = _this;
  _this setDir 32.752586;
  _this setPos [11633.753, 12627.378, -0.00015258789];
};

_vehicle_1044 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [11651.703, 12657.13, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1044 = _this;
  _this setDir -155.64281;
  _this setPos [11651.703, 12657.13, -3.0517578e-005];
};

_vehicle_1045 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [11651.64, 12657.114, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1045 = _this;
  _this setDir 24.828156;
  _this setPos [11651.64, 12657.114, 1.5258789e-005];
};

_vehicle_1046 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11660.274, 12672.231, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1046 = _this;
  _this setDir 34.138714;
  _this setPos [11660.274, 12672.231, 0.00010681152];
};

_vehicle_1047 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11670.076, 12686.639, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1047 = _this;
  _this setDir 34.0294;
  _this setPos [11670.076, 12686.639, 6.1035156e-005];
};

_vehicle_1048 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11679.949, 12701.03, -0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_1048 = _this;
  _this setDir 34.40905;
  _this setPos [11679.949, 12701.03, -0.00022888184];
};

_vehicle_1049 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11696.382, 12726.702, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1049 = _this;
  _this setDir 205.00171;
  _this setPos [11696.382, 12726.702, 0.00010681152];
};

_vehicle_1050 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [11696.38, 12726.688, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1050 = _this;
  _this setDir 25.221928;
  _this setPos [11696.38, 12726.688, 1.5258789e-005];
};

_vehicle_1052 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11705.169, 12741.729, 0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_1052 = _this;
  _this setDir -154.77936;
  _this setPos [11705.169, 12741.729, 0.00028991699];
};

_vehicle_1053 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11705.158, 12741.729, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1053 = _this;
  _this setDir 24.810852;
  _this setPos [11705.158, 12741.729, 7.6293945e-005];
};

_vehicle_1054 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11712.484, 12757.541, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_1054 = _this;
  _this setDir 25.567984;
  _this setPos [11712.484, 12757.541, 0.0002746582];
};

_vehicle_1055 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11720.06, 12773.235, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1055 = _this;
  _this setDir 25.816614;
  _this setPos [11720.06, 12773.235, 3.0517578e-005];
};

_vehicle_1056 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11727.632, 12788.947, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1056 = _this;
  _this setDir 25.919949;
  _this setPos [11727.632, 12788.947, -1.5258789e-005];
};

_vehicle_1057 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11735.29, 12804.512, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1057 = _this;
  _this setDir 24.971945;
  _this setPos [11735.29, 12804.512, -7.6293945e-005];
};

_vehicle_1058 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11742.698, 12820.271, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_1058 = _this;
  _this setDir 25.234354;
  _this setPos [11742.698, 12820.271, 0.00024414063];
};

_vehicle_1059 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11750.174, 12836.031, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1059 = _this;
  _this setDir 25.901892;
  _this setPos [11750.174, 12836.031, 0.00012207031];
};

_vehicle_1060 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11757.844, 12851.665, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1060 = _this;
  _this setDir 26.240644;
  _this setPos [11757.844, 12851.665, 3.0517578e-005];
};

_vehicle_1062 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [11765.665, 12867.229, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1062 = _this;
  _this setDir 26.771936;
  _this setPos [11765.665, 12867.229, 6.1035156e-005];
};

_vehicle_1067 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [11777.71, 12882.544, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1067 = _this;
  _this setDir 49.290558;
  _this setPos [11777.71, 12882.544, 0.00012207031];
};

_vehicle_1068 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [11782.409, 12886.641, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1068 = _this;
  _this setDir 49.701405;
  _this setPos [11782.409, 12886.641, 4.5776367e-005];
};

_vehicle_1069 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11794.084, 12892.284, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1069 = _this;
  _this setDir 79.275085;
  _this setPos [11794.084, 12892.284, -1.5258789e-005];
};

_vehicle_1070 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [11828.413, 12898.597, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1070 = _this;
  _this setDir -100.9249;
  _this setPos [11828.413, 12898.597, -7.6293945e-005];
};

_vehicle_1071 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11828.359, 12898.605, 0.00032043457], [], 0, "CAN_COLLIDE"];
  _vehicle_1071 = _this;
  _this setDir 80.222916;
  _this setPos [11828.359, 12898.605, 0.00032043457];
};

_vehicle_1072 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11841.382, 12899.728, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1072 = _this;
  _this setDir 90.406197;
  _this setPos [11841.382, 12899.728, 6.1035156e-005];
};

_vehicle_1073 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11858.828, 12899.528, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1073 = _this;
  _this setDir 91.14357;
  _this setPos [11858.828, 12899.528, 0.00012207031];
};

_vehicle_1074 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [11893.618, 12900.136, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1074 = _this;
  _this setDir -98.415871;
  _this setPos [11893.618, 12900.136, 4.5776367e-005];
};

_vehicle_1075 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11893.58, 12900.147, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1075 = _this;
  _this setDir 81.873001;
  _this setPos [11893.58, 12900.147, 7.6293945e-005];
};

_vehicle_1076 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11910.854, 12902.525, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1076 = _this;
  _this setDir 82.35704;
  _this setPos [11910.854, 12902.525, 0.00010681152];
};

_vehicle_1077 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11945.457, 12906.998, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1077 = _this;
  _this setDir 83.781143;
  _this setPos [11945.457, 12906.998, 1.5258789e-005];
};

_vehicle_1078 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11928.156, 12904.773, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1078 = _this;
  _this setDir 82.888916;
  _this setPos [11928.156, 12904.773, 1.5258789e-005];
};

_vehicle_1079 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11958.526, 12907.445], [], 0, "CAN_COLLIDE"];
  _vehicle_1079 = _this;
  _this setDir 93.850319;
  _this setPos [11958.526, 12907.445];
};

_vehicle_1080 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [11971.441, 12905.525, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1080 = _this;
  _this setDir 104.12585;
  _this setPos [11971.441, 12905.525, -1.5258789e-005];
};

_vehicle_1081 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12005.187, 12896.709], [], 0, "CAN_COLLIDE"];
  _vehicle_1081 = _this;
  _this setDir 284.40738;
  _this setPos [12005.187, 12896.709];
};

_vehicle_1082 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12005.186, 12896.78, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1082 = _this;
  _this setDir 104.38071;
  _this setPos [12005.186, 12896.78, 3.0517578e-005];
};

_vehicle_1083 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [12022.063, 12892.408, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1083 = _this;
  _this setDir 105.23024;
  _this setPos [12022.063, 12892.408, 3.0517578e-005];
};

_vehicle_1084 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [12040.116, 12884.855, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1084 = _this;
  _this setDir 120.37135;
  _this setPos [12040.116, 12884.855, 3.0517578e-005];
};

_vehicle_1085 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12057.013, 12871.199, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1085 = _this;
  _this setDir -49.870731;
  _this setPos [12057.013, 12871.199, -3.0517578e-005];
};

_vehicle_1087 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12071.271, 12861.217, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1087 = _this;
  _this setDir -59.907784;
  _this setPos [12071.271, 12861.217, 4.5776367e-005];
};

_vehicle_1088 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [12083.017, 12855.755, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1088 = _this;
  _this setDir -70.042999;
  _this setPos [12083.017, 12855.755, -4.5776367e-005];
};

_vehicle_1089 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [12087.229, 12854.689, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1089 = _this;
  _this setDir -80.397018;
  _this setPos [12087.229, 12854.689, -4.5776367e-005];
};

_vehicle_1090 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [12105.425, 12847.914, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1090 = _this;
  _this setDir 122.10175;
  _this setPos [12105.425, 12847.914, 1.5258789e-005];
};

_vehicle_1091 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [12141.202, 12811.214, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1091 = _this;
  _this setDir 118.82717;
  _this setPos [12141.202, 12811.214, 3.0517578e-005];
};

_vehicle_1092 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [12087.198, 12854.667, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1092 = _this;
  _this setDir 98.840393;
  _this setPos [12087.198, 12854.667, -4.5776367e-005];
};

_vehicle_1093 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [12125.443, 12822.416, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1093 = _this;
  _this setDir -42.463428;
  _this setPos [12125.443, 12822.416, 0.00012207031];
};

_vehicle_1095 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [12141.168, 12811.117, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1095 = _this;
  _this setDir -61.484833;
  _this setPos [12141.168, 12811.117, 7.6293945e-005];
};

_vehicle_1096 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12156.421, 12802.737, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1096 = _this;
  _this setDir 119.95148;
  _this setPos [12156.421, 12802.737, 1.5258789e-005];
};

_vehicle_1097 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12186.631, 12785.258, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1097 = _this;
  _this setDir -60.245651;
  _this setPos [12186.631, 12785.258, -9.1552734e-005];
};

_vehicle_1098 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12201.783, 12776.646, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1098 = _this;
  _this setDir -60.517723;
  _this setPos [12201.783, 12776.646, -9.1552734e-005];
};

_vehicle_1099 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12216.889, 12767.977, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1099 = _this;
  _this setDir -60.087948;
  _this setPos [12216.889, 12767.977, -3.0517578e-005];
};

_vehicle_1100 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12232.042, 12759.325, -0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_1100 = _this;
  _this setDir -60.382923;
  _this setPos [12232.042, 12759.325, -0.00022888184];
};

_vehicle_1101 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12247.296, 12750.893, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1101 = _this;
  _this setDir -61.12373;
  _this setPos [12247.296, 12750.893, -4.5776367e-005];
};

_vehicle_1102 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [12247.238, 12750.861, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1102 = _this;
  _this setDir 118.62586;
  _this setPos [12247.238, 12750.861, 1.5258789e-005];
};

_vehicle_1103 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [12550.289, 12608.892, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1103 = _this;
  _this setDir 108.73188;
  _this setPos [12550.289, 12608.892, -1.5258789e-005];
};

_vehicle_1104 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12282.082, 12716.066, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1104 = _this;
  _this setDir -48.985386;
  _this setPos [12282.082, 12716.066, 3.0517578e-005];
};

_vehicle_1105 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_12", [12262.188, 12738.343, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1105 = _this;
  _this setDir 141.25319;
  _this setPos [12262.188, 12738.343, 4.5776367e-005];
};

_vehicle_1106 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12296.286, 12706.022], [], 0, "CAN_COLLIDE"];
  _vehicle_1106 = _this;
  _this setDir -59.69767;
  _this setPos [12296.286, 12706.022];
};

_vehicle_1107 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12312.069, 12698.647], [], 0, "CAN_COLLIDE"];
  _vehicle_1107 = _this;
  _this setDir -69.87133;
  _this setPos [12312.069, 12698.647];
};

_vehicle_1108 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12312.097, 12698.747, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1108 = _this;
  _this setDir 110.38444;
  _this setPos [12312.097, 12698.747, -3.0517578e-005];
};

_vehicle_1109 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12328.391, 12692.615, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1109 = _this;
  _this setDir 110.4367;
  _this setPos [12328.391, 12692.615, -3.0517578e-005];
};

_vehicle_1110 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [12347.954, 12683.139, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1110 = _this;
  _this setDir -68.307907;
  _this setPos [12347.954, 12683.139, 3.0517578e-005];
};

_vehicle_1111 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12364.224, 12676.949], [], 0, "CAN_COLLIDE"];
  _vehicle_1111 = _this;
  _this setDir -69.436501;
  _this setPos [12364.224, 12676.949];
};

_vehicle_1112 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12364.219, 12676.982, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1112 = _this;
  _this setDir 110.81908;
  _this setPos [12364.219, 12676.982, 1.5258789e-005];
};

_vehicle_1113 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12395.722, 12662.174, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1113 = _this;
  _this setDir -70.307793;
  _this setPos [12395.722, 12662.174, 1.5258789e-005];
};

_vehicle_1114 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12434.776, 12648.371, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1114 = _this;
  _this setDir -70.822815;
  _this setPos [12434.776, 12648.371, 1.5258789e-005];
};

_vehicle_1115 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [12412.464, 12656.305], [], 0, "CAN_COLLIDE"];
  _vehicle_1115 = _this;
  _this setDir 289.58911;
  _this setPos [12412.464, 12656.305];
};

_vehicle_1118 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [12395.358, 12662.305, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1118 = _this;
  _this setDir 109.88702;
  _this setPos [12395.358, 12662.305, -0.00010681152];
};

_vehicle_1119 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [12406.646, 12658.319, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1119 = _this;
  _this setDir -70.369072;
  _this setPos [12406.646, 12658.319, -4.5776367e-005];
};

_vehicle_1120 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [12412.094, 12640.614, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1120 = _this;
  _this setDir -39.93034;
  _this setPos [12412.094, 12640.614, 1.5258789e-005];
};

_vehicle_1123 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [12445.387, 12644.628, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1123 = _this;
  _this setDir -70.063423;
  _this setPos [12445.387, 12644.628, 1.5258789e-005];
};

_vehicle_1124 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [12443.323, 12638.747], [], 0, "CAN_COLLIDE"];
  _vehicle_1124 = _this;
  _this setDir -160.06444;
  _this setPos [12443.323, 12638.747];
};

_vehicle_1125 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [12435.687, 12632.238, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1125 = _this;
  _this setDir -99.91243;
  _this setPos [12435.687, 12632.238, 9.1552734e-005];
};

_vehicle_1126 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [12422.774, 12633.321], [], 0, "CAN_COLLIDE"];
  _vehicle_1126 = _this;
  _this setDir -70.24205;
  _this setPos [12422.774, 12633.321];
};

_vehicle_1127 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [12439.547, 12646.674, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1127 = _this;
  _this setDir -70.031441;
  _this setPos [12439.547, 12646.674, -1.5258789e-005];
};

_vehicle_1128 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Build", [12433.549, 12638.294, 0.11365201], [], 0, "CAN_COLLIDE"];
  _vehicle_1128 = _this;
  _this setDir 18.123989;
  _this setPos [12433.549, 12638.294, 0.11365201];
};

_vehicle_1129 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Feed", [12420.571, 12630.677, 0.20468497], [], 0, "CAN_COLLIDE"];
  _vehicle_1129 = _this;
  _this setDir 21.980387;
  _this setPos [12420.571, 12630.677, 0.20468497];
};

_vehicle_1130 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Feed", [12422.965, 12637.124, 0.25062081], [], 0, "CAN_COLLIDE"];
  _vehicle_1130 = _this;
  _this setDir 23.409969;
  _this setPos [12422.965, 12637.124, 0.25062081];
};

_vehicle_1131 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Feed", [12417.979, 12624.251, 0.14803851], [], 0, "CAN_COLLIDE"];
  _vehicle_1131 = _this;
  _this setDir 24.034624;
  _this setPos [12417.979, 12624.251, 0.14803851];
};

_vehicle_1132 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Shed", [12420.502, 12630.696, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1132 = _this;
  _this setDir -248.32339;
  _this setPos [12420.502, 12630.696, -4.5776367e-005];
};

_vehicle_1133 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Sign", [12404.071, 12653.156], [], 0, "CAN_COLLIDE"];
  _vehicle_1133 = _this;
  _this setDir 26.844742;
  _this setPos [12404.071, 12653.156];
};

_vehicle_1134 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Sign", [12437.613, 12641.1], [], 0, "CAN_COLLIDE"];
  _vehicle_1134 = _this;
  _this setDir 23.593412;
  _this setPos [12437.613, 12641.1];
};

_vehicle_1136 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_TankBig", [11927.3, 12852.213], [], 0, "CAN_COLLIDE"];
  _vehicle_1136 = _this;
  _this setDir -72.773903;
  _this setPos [11927.3, 12852.213];
};

_vehicle_1137 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_TankBig", [11906.677, 12848.896, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1137 = _this;
  _this setDir -425.30301;
  _this setPos [11906.677, 12848.896, 3.0517578e-005];
};

_vehicle_1139 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_TankSmall2", [12424.15, 12641.467], [], 0, "CAN_COLLIDE"];
  _vehicle_1139 = _this;
  _this setDir 19.268299;
  _this setPos [12424.15, 12641.467];
};

_vehicle_1140 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12467.711, 12636.697, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1140 = _this;
  _this setDir -70.589485;
  _this setPos [12467.711, 12636.697, 0.00010681152];
};

_vehicle_1141 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12484.156, 12630.942], [], 0, "CAN_COLLIDE"];
  _vehicle_1141 = _this;
  _this setDir -70.945717;
  _this setPos [12484.156, 12630.942];
};

_vehicle_1142 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12500.668, 12625.38, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1142 = _this;
  _this setDir -71.536484;
  _this setPos [12500.668, 12625.38, 1.5258789e-005];
};

_vehicle_1143 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12500.644, 12625.374, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1143 = _this;
  _this setDir 108.48458;
  _this setPos [12500.644, 12625.374, 7.6293945e-005];
};

_vehicle_1144 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12533.726, 12614.325, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_1144 = _this;
  _this setDir -71.72731;
  _this setPos [12533.726, 12614.325, -0.00010681152];
};

_vehicle_1145 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12533.739, 12614.413, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1145 = _this;
  _this setDir 108.1659;
  _this setPos [12533.739, 12614.413, -3.0517578e-005];
};

_vehicle_1147 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [12567.194, 12599.192, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1147 = _this;
  _this setDir 131.39369;
  _this setPos [12567.194, 12599.192, -0.0001373291];
};

_vehicle_1148 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [12578.959, 12583.665, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1148 = _this;
  _this setDir 154.2738;
  _this setPos [12578.959, 12583.665, 3.0517578e-005];
};

_vehicle_1149 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12584.948, 12547.391, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1149 = _this;
  _this setDir -3.842886;
  _this setPos [12584.948, 12547.391, -1.5258789e-005];
};

_vehicle_1150 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12586.288, 12529.992, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1150 = _this;
  _this setDir -4.3974257;
  _this setPos [12586.288, 12529.992, 1.5258789e-005];
};

_vehicle_1151 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12587.473, 12512.621, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1151 = _this;
  _this setDir -4.0310698;
  _this setPos [12587.473, 12512.621, -1.5258789e-005];
};

_vehicle_1152 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12590.089, 12456.14, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1152 = _this;
  _this setDir -5.3389459;
  _this setPos [12590.089, 12456.14, -3.0517578e-005];
};

_vehicle_1153 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12588.507, 12473.489, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1153 = _this;
  _this setDir -4.8084798;
  _this setPos [12588.507, 12473.489, 1.5258789e-005];
};

_vehicle_1154 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12587.462, 12512.649], [], 0, "CAN_COLLIDE"];
  _vehicle_1154 = _this;
  _this setDir 176.09966;
  _this setPos [12587.462, 12512.649];
};

_vehicle_1155 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [12587.06, 12490.878, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1155 = _this;
  _this setDir -4.3744235;
  _this setPos [12587.06, 12490.878, 1.5258789e-005];
};

_vehicle_1156 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12593.284, 12439.024, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1156 = _this;
  _this setDir -15.332064;
  _this setPos [12593.284, 12439.024, -1.5258789e-005];
};

_vehicle_1157 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12599.32, 12422.676, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1157 = _this;
  _this setDir -25.318102;
  _this setPos [12599.32, 12422.676, -4.5776367e-005];
};

_vehicle_1158 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12605.528, 12406.54, -0.00032043457], [], 0, "CAN_COLLIDE"];
  _vehicle_1158 = _this;
  _this setDir -21.257433;
  _this setPos [12605.528, 12406.54, -0.00032043457];
};

_vehicle_1161 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12612.042, 12390.367, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_1161 = _this;
  _this setDir -21.997383;
  _this setPos [12612.042, 12390.367, 0.00018310547];
};

_vehicle_1162 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [12655.712, 12334.899, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1162 = _this;
  _this setDir -65.194077;
  _this setPos [12655.712, 12334.899, -7.6293945e-005];
};

_vehicle_1163 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12619.922, 12374.833, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1163 = _this;
  _this setDir -31.843197;
  _this setPos [12619.922, 12374.833, 4.5776367e-005];
};

_vehicle_1164 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12629.241, 12360.113, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1164 = _this;
  _this setDir -32.326004;
  _this setPos [12629.241, 12360.113, -4.5776367e-005];
};

_vehicle_1165 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [12639.962, 12346.395, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1165 = _this;
  _this setDir -42.904015;
  _this setPos [12639.962, 12346.395, 4.5776367e-005];
};

_vehicle_1166 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [12655.622, 12334.958, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1166 = _this;
  _this setDir 112.18005;
  _this setPos [12655.622, 12334.958, -6.1035156e-005];
};

_vehicle_1169 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12671.878, 12324.215, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1169 = _this;
  _this setDir 135.39522;
  _this setPos [12671.878, 12324.215, 7.6293945e-005];
};

_vehicle_1170 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12696.266, 12299.343, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1170 = _this;
  _this setDir -44.008564;
  _this setPos [12696.266, 12299.343, -4.5776367e-005];
};

_vehicle_1171 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12708.418, 12286.955, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1171 = _this;
  _this setDir -44.611382;
  _this setPos [12708.418, 12286.955, 1.5258789e-005];
};

_vehicle_1172 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12720.671, 12274.561, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1172 = _this;
  _this setDir -44.85408;
  _this setPos [12720.671, 12274.561, 0.00012207031];
};

_vehicle_1173 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [12732.931, 12262.212, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_1173 = _this;
  _this setDir -45.042576;
  _this setPos [12732.931, 12262.212, 0.0001373291];
};

_vehicle_1174 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [12741.661, 12253.454, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_1174 = _this;
  _this setDir -44.875084;
  _this setPos [12741.661, 12253.454, 0.00016784668];
};

_vehicle_1175 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [12737.315, 12257.89, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1175 = _this;
  _this setDir -44.903378;
  _this setPos [12737.315, 12257.89, 7.6293945e-005];
};

};
