[
	/******************Drug Empire*******************/
	["RU_Policeman",[13727.1,2887.03,0],245],

	/*****************Th3-Hunter333's Creations*****************/
	// Hacked <PERSON>usk<PERSON>
	["RU_Soldier_SniperH",[9307.44,8883.77,0.721527],235],

	/**************<PERSON>s Trader****************/
	// Bunnys Hacked Vehicles
	["Hooker1",[4613,2517,3.8],78],
	// <PERSON>
	["Hooker2",[4619.55,2526,3.8],179],
	// <PERSON>
	["Hooker3",[4622.96,2524,3.8],240],
	// Bunnys Gardencenter
	["Hooker4",[4616.45,2514,3.8],20],
	
	/**************Super Hero Trader****************/
	// Black Market Trader
	["GUE_Woodlander2",[2159.6,14937.7,0],357.841],		
	// Super Hero Trader			
	["RU_Priest",[2138.53,14932.2,0],300],
	// Car Trader							
	["Villager1",[2098.84,14938.7,0],90], 
	// Ammo Trader						
    ["Profiteer1",[2151.83,14963,0],165], 
	// Tools Traders							
    ["Worker2",[2116.65,14974.6,0],181],
	// Medical Trader 						
    ["Dr_Hladik_EP1",[2128.8,14974.3,0],183],
	// Weapons Trader 						
    ["RU_Citizen4",[2149.23,14959.6,0],93],
	// Food and Drinks 				
    ["Rita_Ensler_EP1",[2119.31,14973.8,0],165],

	/******************Hero Trader*******************/
	// Eastern Hero Vendor
	["FR_AC",[12946.4,12766.8,0],194],
	// Western Hero Vendor
	["GUE_Commander",[1623,7797,0],274],
	
	/******************Stary************************/
	//Black Market
	["GUE_Woodlander2",[6321.04,7781.03,0],10],
	//Weapons
	["RU_Citizen4",[6322.2,7797.8,-0.27],220],
	//Ammunition
	["Profiteer1",[6319,7800.2,-0.27],220],
	//General Store
	["Rita_Ensler_EP1",[6310.9,7794.8,0],245],
	//Medical Supplies
	["Dr_Hladik_EP1",[6314.1,7791.5,0],217],
	//Building/Parts
	["Worker2",[6317.9,7789,0.0],244],
	//Vehicles
	["Villager1",[6300.6,7800.3,0],310],

	/*********Kozlovka Trader Outpost*************/
	//Medical Supplies
	["Dr_Hladik_EP1",[4683.7,4705.05, 0.0],215],
	//Building/Parts
	["Worker2",[4675.7, 4707.5, 3.0],215],
	//General Store
	["Rita_Ensler_EP1",[4687.7, 4698.7,6.0],290],
	//Weapons
	["RU_Citizen4",[4696.9,4707.5],90.0],
	//Ammunition
	["Profiteer1",[4696.7,4701.9],90.0],
	//Vehicles
	["Villager1",[4726.6,4685.5, -3],280],

	/*********Sandford and Son Salvage *************/
	//Weapons
    ["RU_Citizen4", [9910.58,5432.17,0.00143433],106.201],
	//General Store
	["Rita_Ensler_EP1",[9915.88,5434.21,0.001],185],
	//Ammunition
	["Profiteer1",[9911.24,5434.7,0.001],90],
	//Building/Parts		
	["Worker2",[9909.33,5428.35,0.001],63],
	//Medical Supplies
	["Dr_Hladik_EP1",[9912.66,5423.03,0.001],24],

	/******************Bash************************/
	//Weapons
	["RU_Citizen4",[4064,11680,0],231],
	//Ammunition
	["Profiteer1",[4058,11679,0],90],
	//General Store
	["Rita_Ensler_EP1",[4072,11677,0],207],
	//Medical Supplies
	["Dr_Hladik_EP1",[4059,11660,0],24],
	//Building/Parts		
	["Worker2",[4054,11664,0],63],
	//Vehicles
	["Villager1",[4041,11669,0],25],

	/******************Klen************************/
	//Weapons
	["RU_Citizen4",[11465.8,11354.5,0],310],
	//Ammunition
	["Profiteer1",[11462,11366,0],125],
	//General Store
	["Rita_Ensler_EP1",[11464.6,11350.4,0],310],
	//Medical Supplies
	["Dr_Hladik_EP1",[11472,11370,0],217],
	//Building/Parts	
	["Worker2",[11471,11361,0],250],
	//Vehicles
	["Villager1",[11449,11341,0],34],

	/***********************************************/
	//Aircraft Dealer
	["RU_Pilot",[12061,12636.3,0],20],
	//NWA Trader
	["RU_Pilot",[5042.2383,9710.0039,-339],-52.404758],	
	//Boat Vendor
	["RU_Villager3",[7996,2899,0.6],87],
	//Boat Vendor
	["HouseWife1",[13468,5439,2.5],268],	
	//Wholesaler
	["Profiteer2",[13531,6356,0],102],
	//Wholesaler
	["Profiteer3",[4360,2261,0],196]
] call server_spawnTraders;

// Bankers
if (Z_singleCurrency && {Z_globalBanking && Z_globalBankingTraders}) then {
	[
		// Super Hero Trader
		["Functionary1_EP1",[2168,14936,0],357],
		// Stary Trader
		["Functionary1_EP1",[6324,7787,0],64],
		// Bash	Trader
		["Functionary1_EP1",[4043.99,11667.6,0],16],
		// Klen Trader
		["Functionary1_EP1",[11461,11348,0],310],
		// Staroye Trader
		["Functionary1_EP1",[9913.82,5435.3,0],190],
		// Kozlovka Trader
		["Functionary1_EP1",[4687.7,4700.6,6.0],290]
	] call server_spawnTraders;
};
