if (isServer) then {
 
_vehicle_24 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_deerstand", [6938.9043, 2519.9492, -1.8119812e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_24 = _this;
  _this setDir 232.98361;
  _this setPos [6938.9043, 2519.9492, -1.8119812e-005];
};
 
_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["Land_covering_hut_EP1", [6925.7842, 2460.3633, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setDir 142.69261;
  _this setPos [6925.7842, 2460.3633, -1.5258789e-005];
};
 
_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6869.1401, 2471.1709, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setDir -128.57056;
  _this setPos [6869.1401, 2471.1709, 7.6293945e-006];
};
 
_vehicle_30 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6872.1704, 2467.1887, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_30 = _this;
  _this setDir 51.84351;
  _this setPos [6872.1704, 2467.1887, -9.5367432e-007];
};
 
_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6870.6235, 2469.2207, 3.2424927e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir -127.84912;
  _this setPos [6870.6235, 2469.2207, 3.2424927e-005];
};
 
_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6909.9536, 2547.4688, 5.7220459e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir 55.705364;
  _this setPos [6909.9536, 2547.4688, 5.7220459e-005];
};
 
_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6911.3257, 2545.4226, 4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir 55.150051;
  _this setPos [6911.3257, 2545.4226, 4.7683716e-006];
};
 
_vehicle_37 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6951.1836, 2450.2258, 1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_37 = _this;
  _this setDir -38.483284;
  _this setPos [6951.1836, 2450.2258, 1.2397766e-005];
};
 
_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6949.2695, 2448.6672, 2.5749207e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setDir -37.325478;
  _this setPos [6949.2695, 2448.6672, 2.5749207e-005];
};
 
_vehicle_45 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6932.6416, 2531.9436, 1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_45 = _this;
  _this setDir 53.011639;
  _this setPos [6932.6416, 2531.9436, 1.1444092e-005];
};
 
_vehicle_46 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6934.1597, 2529.948, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_46 = _this;
  _this setDir 51.821644;
  _this setPos [6934.1597, 2529.948, -2.8610229e-006];
};
 
_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6935.6763, 2527.9614, -4.9591064e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setDir 53.103577;
  _this setPos [6935.6763, 2527.9614, -4.9591064e-005];
};
 
_vehicle_48 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6937.1641, 2525.9658, 4.2438507e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_48 = _this;
  _this setDir 52.659122;
  _this setPos [6937.1641, 2525.9658, 4.2438507e-005];
};
 
_vehicle_49 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6938.6758, 2523.9792, 1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_49 = _this;
  _this setDir 52.675896;
  _this setPos [6938.6758, 2523.9792, 1.335144e-005];
};
 
_vehicle_50 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6940.1528, 2521.9824, 7.2002411e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_50 = _this;
  _this setDir 52.830757;
  _this setPos [6940.1528, 2521.9824, 7.2002411e-005];
};
 
_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6941.6807, 2520.0635, 7.1525574e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setDir 50.111958;
  _this setPos [6941.6807, 2520.0635, 7.1525574e-006];
};
 
_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small", [6955.4038, 2456.0564, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setDir -36.320347;
  _this setPos [6955.4038, 2456.0564, 7.6293945e-006];
};
 
_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6928.7432, 2433.4263, 0.0001001358], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setDir -35.127045;
  _this setPos [6928.7432, 2433.4263, 0.0001001358];
};
 
_vehicle_56 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6926.7441, 2431.9766, 6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_56 = _this;
  _this setDir -35.431427;
  _this setPos [6926.7441, 2431.9766, 6.6757202e-006];
};
 
_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6924.7563, 2430.5049, 0.0001411438], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setDir -35.593365;
  _this setPos [6924.7563, 2430.5049, 0.0001411438];
};
 
_vehicle_58 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6922.7686, 2428.9961, 6.5803528e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_58 = _this;
  _this setDir -36.715534;
  _this setPos [6922.7686, 2428.9961, 6.5803528e-005];
};
 
_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6920.7671, 2427.4988, 4.0054321e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir -36.637112;
  _this setPos [6920.7671, 2427.4988, 4.0054321e-005];
};
 
_vehicle_60 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6918.7773, 2426.0017, 4.1007996e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_60 = _this;
  _this setDir -37.460854;
  _this setPos [6918.7773, 2426.0017, 4.1007996e-005];
};
 
_vehicle_61 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_RazorWire", [6919.4976, 2426.4414, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_61 = _this;
  _this setDir -36.038803;
  _this setPos [6919.4976, 2426.4414, 5.3405762e-005];
};
 
_vehicle_62 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_RazorWire", [6926.1997, 2431.5129, 4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_62 = _this;
  _this setDir -36.718044;
  _this setPos [6926.1997, 2431.5129, 4.7683716e-006];
};
 
_vehicle_63 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_RazorWire", [6938.998, 2523.4014, 3.6239624e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_63 = _this;
  _this setDir 54.311279;
  _this setPos [6938.998, 2523.4014, 3.6239624e-005];
};
 
_vehicle_64 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_RazorWire", [6933.9272, 2530.2539, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_64 = _this;
  _this setDir 52.196262;
  _this setPos [6933.9272, 2530.2539, -1.9073486e-006];
};
 
_vehicle_66 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6862.3877, 2479.9971, 7.1525574e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_66 = _this;
  _this setDir -130.84302;
  _this setPos [6862.3877, 2479.9971, 7.1525574e-005];
};
 
_vehicle_67 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6873.7153, 2465.2205, 0.0001077652], [], 0, "CAN_COLLIDE"];
  _vehicle_67 = _this;
  _this setDir 51.162563;
  _this setPos [6873.7153, 2465.2205, 0.0001077652];
};
 
_vehicle_68 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_RazorWire", [6868.3857, 2468.1074, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_68 = _this;
  _this setDir -125.47104;
  _this setPos [6868.3857, 2468.1074, 5.7220459e-006];
};
 
_vehicle_69 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6863.9814, 2478.001, 1.8119812e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_69 = _this;
  _this setDir 52.5495;
  _this setPos [6863.9814, 2478.001, 1.8119812e-005];
};
 
_vehicle_70 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6862.6982, 2481.6802, 0.0002040863], [], 0, "CAN_COLLIDE"];
  _vehicle_70 = _this;
  _this setDir -46.528709;
  _this setPos [6862.6982, 2481.6802, 0.0002040863];
};
 
_vehicle_71 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6864.4448, 2483.4146, 0.00020980835], [], 0, "CAN_COLLIDE"];
  _vehicle_71 = _this;
  _this setDir -44.751034;
  _this setPos [6864.4448, 2483.4146, 0.00020980835];
};
 
_vehicle_75 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower", [6865.9209, 2528.0815, 0.1027339], [], 0, "CAN_COLLIDE"];
  _vehicle_75 = _this;
  _this setDir 129.27437;
  _this setPos [6865.9209, 2528.0815, 0.1027339];
};
 
_vehicle_76 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [6849.855, 2519.5, 5.5789948e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_76 = _this;
  _this setDir -47.155548;
  _this setPos [6849.855, 2519.5, 5.5789948e-005];
};
 
_vehicle_77 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [6853.7705, 2523.7297, 4.7683716e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_77 = _this;
  _this setDir -47.233459;
  _this setPos [6853.7705, 2523.7297, 4.7683716e-007];
};
 
_vehicle_78 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_RazorWire", [6851.1338, 2520.5266, 1.5535585], [], 0, "CAN_COLLIDE"];
  _vehicle_78 = _this;
  _this setDir -48.103252;
  _this setPos [6851.1338, 2520.5266, 1.5535585];
};
 
_vehicle_79 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [6857.6641, 2527.9058, 0.00011014938], [], 0, "CAN_COLLIDE"];
  _vehicle_79 = _this;
  _this setDir -45.828079;
  _this setPos [6857.6641, 2527.9058, 0.00011014938];
};
 
_vehicle_81 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_RazorWire", [6856.6465, 2526.2278, 1.6256284], [], 0, "CAN_COLLIDE"];
  _vehicle_81 = _this;
  _this setDir -45.069595;
  _this setPos [6856.6465, 2526.2278, 1.6256284];
};
 
_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small", [6886.4497, 2550.1101, 3.2901764e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setDir 131.37633;
  _this setPos [6886.4497, 2550.1101, 3.2901764e-005];
};
 
_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["MASH", [6870.0708, 2512.3267, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir -137.41786;
  _this setPos [6870.0708, 2512.3267, 0.0002746582];
};
 
_vehicle_90 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [6874.6416, 2507.9878, 0.12557718], [], 0, "CAN_COLLIDE"];
  _vehicle_90 = _this;
  _this setDir -136.76004;
  _this setPos [6874.6416, 2507.9878, 0.12557718];
};
 
_vehicle_91 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [6865.1807, 2516.6965, 6.0081482e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_91 = _this;
  _this setDir -138.18243;
  _this setPos [6865.1807, 2516.6965, 6.0081482e-005];
};
 
_vehicle_92 = objNull;
if (true) then
{
  _this = createVehicle ["MASH", [6860.5801, 2521.1755, 0.00014400482], [], 0, "CAN_COLLIDE"];
  _vehicle_92 = _this;
  _this setDir 222.22516;
  _this setPos [6860.5801, 2521.1755, 0.00014400482];
};
 
_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6878.624, 2498.4016, 0.00018215179], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setDir -47.009193;
  _this setPos [6878.624, 2498.4016, 0.00018215179];
};
 
_vehicle_94 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6880.7412, 2500.6318, 0.00011730194], [], 0, "CAN_COLLIDE"];
  _vehicle_94 = _this;
  _this setDir -46.400913;
  _this setPos [6880.7412, 2500.6318, 0.00011730194];
};
 
_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6882.8838, 2502.8962, 6.6757202e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setDir -46.60083;
  _this setPos [6882.8838, 2502.8962, 6.6757202e-005];
};
 
_vehicle_96 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6883.1543, 2504.9946, 3.2424927e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_96 = _this;
  _this setDir 41.074757;
  _this setPos [6883.1543, 2504.9946, 3.2424927e-005];
};
 
_vehicle_97 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6876.8833, 2510.5415, 7.9154968e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_97 = _this;
  _this setDir 43.859978;
  _this setPos [6876.8833, 2510.5415, 7.9154968e-005];
};
 
_vehicle_98 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6874.583, 2512.6262, 4.7683716e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_98 = _this;
  _this setDir 43.019703;
  _this setPos [6874.583, 2512.6262, 4.7683716e-005];
};
 
_vehicle_99 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6872.3257, 2514.7393, 0.00016498566], [], 0, "CAN_COLLIDE"];
  _vehicle_99 = _this;
  _this setDir 44.202431;
  _this setPos [6872.3257, 2514.7393, 0.00016498566];
};
 
_vehicle_100 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6870.1079, 2516.8315, 7.2479248e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_100 = _this;
  _this setDir 43.549316;
  _this setPos [6870.1079, 2516.8315, 7.2479248e-005];
};
 
_vehicle_101 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6867.8506, 2518.9663, 0.00010490417], [], 0, "CAN_COLLIDE"];
  _vehicle_101 = _this;
  _this setDir 44.456501;
  _this setPos [6867.8506, 2518.9663, 0.00010490417];
};
 
_vehicle_102 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6865.5938, 2521.1172, 0.00030231476], [], 0, "CAN_COLLIDE"];
  _vehicle_102 = _this;
  _this setDir 44.216251;
  _this setPos [6865.5938, 2521.1172, 0.00030231476];
};
 
_vehicle_103 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6863.3721, 2523.2563, 0.00026607513], [], 0, "CAN_COLLIDE"];
  _vehicle_103 = _this;
  _this setDir 43.817951;
  _this setPos [6863.3721, 2523.2563, 0.00026607513];
};
 
_vehicle_104 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6861.085, 2525.4214, 0.0002374649], [], 0, "CAN_COLLIDE"];
  _vehicle_104 = _this;
  _this setDir 42.665215;
  _this setPos [6861.085, 2525.4214, 0.0002374649];
};
 
_vehicle_106 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [6878.3955, 2523.6357, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_106 = _this;
  _this setDir 33.948441;
  _this setPos [6878.3955, 2523.6357, 4.5776367e-005];
};
 
_vehicle_107 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [6876.6406, 2524.8032, 0.00014972687], [], 0, "CAN_COLLIDE"];
  _vehicle_107 = _this;
  _this setDir 33.986759;
  _this setPos [6876.6406, 2524.8032, 0.00014972687];
};
 
_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [6874.7666, 2526.0059, 0.00024318695], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setDir 32.076015;
  _this setPos [6874.7666, 2526.0059, 0.00024318695];
};
 
_vehicle_109 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [6872.897, 2527.2769, 0.00028514862], [], 0, "CAN_COLLIDE"];
  _vehicle_109 = _this;
  _this setDir 32.709988;
  _this setPos [6872.897, 2527.2769, 0.00028514862];
};
 
_vehicle_115 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6859.3662, 2490.4343, 6.2168751], [], 0, "CAN_COLLIDE"];
  _vehicle_115 = _this;
  _this setDir 42.47765;
  _this setPos [6859.3662, 2490.4343, 6.2168751];
};
 
_vehicle_116 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6861.6167, 2488.417, 6.2219691], [], 0, "CAN_COLLIDE"];
  _vehicle_116 = _this;
  _this setDir 42.238712;
  _this setPos [6861.6167, 2488.417, 6.2219691];
};
 
_vehicle_117 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6873.167, 2499.8735, 6.2193704], [], 0, "CAN_COLLIDE"];
  _vehicle_117 = _this;
  _this setDir 44.919907;
  _this setPos [6873.167, 2499.8735, 6.2193704];
};
 
_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6875.3657, 2497.762, 6.2381802], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setDir -136.90157;
  _this setPos [6875.3657, 2497.762, 6.2381802];
};
 
_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6868.3501, 2488.1028, 6.2446103], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setDir -47.792595;
  _this setPos [6868.3501, 2488.1028, 6.2446103];
};
 
_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6870.4067, 2490.3936, 6.2385106], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir -47.701584;
  _this setPos [6870.4067, 2490.3936, 6.2385106];
};
 
_vehicle_121 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6872.4824, 2492.6321, 6.2443752], [], 0, "CAN_COLLIDE"];
  _vehicle_121 = _this;
  _this setDir -47.468933;
  _this setPos [6872.4824, 2492.6321, 6.2443752];
};
 
_vehicle_122 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6874.5054, 2494.9507, 6.2340589], [], 0, "CAN_COLLIDE"];
  _vehicle_122 = _this;
  _this setDir -47.835972;
  _this setPos [6874.5054, 2494.9507, 6.2340589];
};
 
_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small", [6864.8672, 2486.9663, 6.1918659], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setDir 41.533424;
  _this setPos [6864.8672, 2486.9663, 6.1918659];
};
 
_vehicle_125 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6870.9761, 2502.001, 6.2717814], [], 0, "CAN_COLLIDE"];
  _vehicle_125 = _this;
  _this setDir 43.18652;
  _this setPos [6870.9761, 2502.001, 6.2717814];
};
 
_vehicle_126 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [6869.0605, 2503.7261, 6.2615752], [], 0, "CAN_COLLIDE"];
  _vehicle_126 = _this;
  _this setDir 43.208214;
  _this setPos [6869.0605, 2503.7261, 6.2615752];
};
 
_vehicle_128 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concrete", [6864.668, 2472.865, 0.00012683868], [], 0, "CAN_COLLIDE"];
  _vehicle_128 = _this;
  _this setDir 53.462677;
  _this setPos [6864.668, 2472.865, 0.00012683868];
};
 
_vehicle_130 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small", [6879.5522, 2445.3894, 0.00025367737], [], 0, "CAN_COLLIDE"];
  _vehicle_130 = _this;
  _this setDir 51.826626;
  _this setPos [6879.5522, 2445.3894, 0.00025367737];
};
 
_vehicle_131 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [6838.9365, 2501.0857, -0.061884444], [], 0, "CAN_COLLIDE"];
  _vehicle_131 = _this;
  _this setDir 27.752316;
  _this setPos [6838.9365, 2501.0857, -0.061884444];
};
 
_vehicle_132 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [6862.6201, 2470.7742, 3.528595e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_132 = _this;
  _this setDir -165.57999;
  _this setPos [6862.6201, 2470.7742, 3.528595e-005];
};
 
_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [6872.4126, 2549.0864, -0.067603394], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir 91.9039;
  _this setPos [6872.4126, 2549.0864, -0.067603394];
};
 
_vehicle_136 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [6861.7896, 2473.6147, 5.2452087e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_136 = _this;
  _this setPos [6861.7896, 2473.6147, 5.2452087e-006];
};
 
_vehicle_138 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [6860.9702, 2471.47, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_138 = _this;
  _this setPos [6860.9702, 2471.47, 3.0517578e-005];
};
 
_vehicle_152 = objNull;
if (true) then
{
  _this = createVehicle ["Mass_grave", [6870.3008, 2534.2031, -0.23382521], [], 0, "CAN_COLLIDE"];
  _vehicle_152 = _this;
  _this setDir -6.0893621;
  _this setPos [6870.3008, 2534.2031, -0.23382521];
};
 
_vehicle_153 = objNull;
if (true) then
{
  _this = createVehicle ["Body", [6877.2246, 2546.0464, 1.0967255e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_153 = _this;
  _this setDir -46.969749;
  _this setPos [6877.2246, 2546.0464, 1.0967255e-005];
};
 
_vehicle_154 = objNull;
if (true) then
{
  _this = createVehicle ["Body", [6876.751, 2545.4988, 2.4795532e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_154 = _this;
  _this setDir -50.363758;
  _this setPos [6876.751, 2545.4988, 2.4795532e-005];
};
 
_vehicle_155 = objNull;
if (true) then
{
  _this = createVehicle ["Body", [6876.2212, 2544.905, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_155 = _this;
  _this setDir -46.180222;
  _this setPos [6876.2212, 2544.905, 1.9073486e-006];
};
 
_vehicle_156 = objNull;
if (true) then
{
  _this = createVehicle ["Body", [6875.6001, 2544.3125, 4.2438507e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_156 = _this;
  _this setDir -50.796089;
  _this setPos [6875.6001, 2544.3125, 4.2438507e-005];
};
 
_vehicle_157 = objNull;
if (true) then
{
  _this = createVehicle ["Body", [6875.041, 2543.4543, 2.8610229e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_157 = _this;
  _this setDir -37.578129;
  _this setPos [6875.041, 2543.4543, 2.8610229e-005];
};
 
_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["Body", [6873.9663, 2542.3623, 2.4795532e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setPos [6873.9663, 2542.3623, 2.4795532e-005];
};
 
_vehicle_161 = objNull;
if (true) then
{
  _this = createVehicle ["Hanged", [6903.6025, 2518.4893, 0.85053778], [], 0, "CAN_COLLIDE"];
  _vehicle_161 = _this;
  _this setDir -141.35666;
  _this setPos [6903.6025, 2518.4893, 0.85053778];
};
 
_vehicle_162 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Campfire_burning", [6915.7646, 2541.3398, 2.0980835e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_162 = _this;
  _this setPos [6915.7646, 2541.3398, 2.0980835e-005];
};
 
_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["Mi8Wreck", [6891.1011, 2508.9695, 4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setDir -32.002037;
  _this setPos [6891.1011, 2508.9695, 4.7683716e-006];
};
 
_vehicle_174 = objNull;
if (true) then
{
  _this = createVehicle ["Land_radar_EP1", [6896.7188, 2553.8279, 5.9127808e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_174 = _this;
  _this setDir -46.941559;
  _this setPos [6896.7188, 2553.8279, 5.9127808e-005];
};
 
_vehicle_176 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Rubble_EP1", [6898.4766, 2567.645, 0.31336632], [], 0, "CAN_COLLIDE"];
  _vehicle_176 = _this;
  _this setDir -14.385149;
  _this setPos [6898.4766, 2567.645, 0.31336632];
};
 
_vehicle_177 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_crates_EP1", [6903.998, 2564.6736, 0.24077302], [], 0, "CAN_COLLIDE"];
  _vehicle_177 = _this;
  _this setDir 136.88528;
  _this setPos [6903.998, 2564.6736, 0.24077302];
};
 
_vehicle_182 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Market_shelter_EP1", [6913.7456, 2486.6375, 0.00016117096], [], 0, "CAN_COLLIDE"];
  _vehicle_182 = _this;
  _this setDir 142.80615;
  _this setPos [6913.7456, 2486.6375, 0.00016117096];
};
 
_vehicle_183 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Market_stalls_01_EP1", [6956.6494, 2479.9063, 0.00025558472], [], 0, "CAN_COLLIDE"];
  _vehicle_183 = _this;
  _this setDir 52.631798;
  _this setPos [6956.6494, 2479.9063, 0.00025558472];
};
 
_vehicle_184 = objNull;
if (true) then
{
  _this = createVehicle ["Haystack", [6968.5605, 2479.6133, 1.6212463e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_184 = _this;
  _this setDir 51.164406;
  _this setPos [6968.5605, 2479.6133, 1.6212463e-005];
};
 
_vehicle_185 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Market_stalls_02_EP1", [6939.0747, 2473.4851, 2.2411346e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_185 = _this;
  _this setDir 52.608376;
  _this setPos [6939.0747, 2473.4851, 2.2411346e-005];
};
 
_vehicle_186 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1Bo_civil", [6947.0332, 2453.4819, 0.09564589], [], 0, "CAN_COLLIDE"];
  _vehicle_186 = _this;
  _this setDir -46.281216;
  _this setPos [6947.0332, 2453.4819, 0.09564589];
};
 
_vehicle_187 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta2", [6946.2007, 2455.9587, 8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_187 = _this;
  _this setPos [6946.2007, 2455.9587, 8.5830688e-006];
};
 
_vehicle_188 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Barrel_empty", [6945.3745, 2457.082, 3.7193298e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_188 = _this;
  _this setPos [6945.3745, 2457.082, 3.7193298e-005];
};
 
_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [6946.9766, 2460.219, 5.197525e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setDir -41.657917;
  _this setPos [6946.9766, 2460.219, 5.197525e-005];
};
 
_vehicle_191 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_kiosk_EP1", [6946.3521, 2462.0403, 8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_191 = _this;
  _this setDir -40.39537;
  _this setPos [6946.3521, 2462.0403, 8.5830688e-006];
};
 
_vehicle_192 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_small_EP1", [6944.9878, 2463.0854, 0.00011730194], [], 0, "CAN_COLLIDE"];
  _vehicle_192 = _this;
  _this setPos [6944.9878, 2463.0854, 0.00011730194];
};
 
_vehicle_194 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Timbers", [6929.9985, 2515.4175, 2.6702881e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_194 = _this;
  _this setDir 63.247765;
  _this setPos [6929.9985, 2515.4175, 2.6702881e-005];
};
 
_vehicle_196 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Wheel_cart_EP1", [6935.2295, 2509.6997, 4.2915344e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_196 = _this;
  _this setPos [6935.2295, 2509.6997, 4.2915344e-005];
};
 
_vehicle_197 = objNull;
if (true) then
{
  _this = createVehicle ["Axe_woodblock", [6934.8359, 2511.0317, 4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_197 = _this;
  _this setPos [6934.8359, 2511.0317, 4.7683716e-006];
};
 
_vehicle_198 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_cart_EP1", [6933.8828, 2505.4961, 5.531311e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_198 = _this;
  _this setDir -19.796349;
  _this setPos [6933.8828, 2505.4961, 5.531311e-005];
};
 
_vehicle_199 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_crates_EP1", [6918.4561, 2502.792], [], 0, "CAN_COLLIDE"];
  _vehicle_199 = _this;
  _this setDir 24.084408;
  _this setPos [6918.4561, 2502.792];
};
 
_vehicle_200 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_Crate_wood", [6919.2803, 2501.1133, 3.2424927e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_200 = _this;
  _this setPos [6919.2803, 2501.1133, 3.2424927e-005];
};
 
_vehicle_201 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_meat_EP1", [6925.9873, 2460.1343, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_201 = _this;
  _this setDir 57.681694;
  _this setPos [6925.9873, 2460.1343, 3.0517578e-005];
};
 
_vehicle_202 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_TankSmall", [6916.9683, 2471.5212, 0.00016689301], [], 0, "CAN_COLLIDE"];
  _vehicle_202 = _this;
  _this setDir 61.620296;
  _this setPos [6916.9683, 2471.5212, 0.00016689301];
};
 
_vehicle_205 = objNull;
if (true) then
{
  _this = createVehicle ["Loudspeakers_EP1", [6871.2661, 2470.0837, 6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_205 = _this;
  _this setPos [6871.2661, 2470.0837, 6.6757202e-006];
};
 
_vehicle_207 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Crates_stack_EP1", [6944.9766, 2498.2976, 4.7683716e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_207 = _this;
  _this setPos [6944.9766, 2498.2976, 4.7683716e-005];
};
 
_vehicle_208 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [6946.5698, 2496.7327, 9.9182129e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_208 = _this;
  _this setDir -38.020123;
  _this setPos [6946.5698, 2496.7327, 9.9182129e-005];
};
 
_vehicle_210 = objNull;
if (true) then
{
  _this = createVehicle ["Land_bags_stack_EP1", [6948.126, 2492.1792, 0.0001244545], [], 0, "CAN_COLLIDE"];
  _vehicle_210 = _this;
  _this setPos [6948.126, 2492.1792, 0.0001244545];
};
 
_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [6935.8496, 2483.1294, 1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setDir -132.93054;
  _this setPos [6935.8496, 2483.1294, 1.335144e-005];
};
 
_vehicle_212 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [6934.1392, 2482.1128, 0.00012969971], [], 0, "CAN_COLLIDE"];
  _vehicle_212 = _this;
  _this setDir -104.88282;
  _this setPos [6934.1392, 2482.1128, 0.00012969971];
};
 
_vehicle_213 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Garb_Heap_EP1", [6931.4023, 2480.6255, 2.1457672e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_213 = _this;
  _this setDir -4.5438523;
  _this setPos [6931.4023, 2480.6255, 2.1457672e-005];
};
 
_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_palletsfoiled_heap", [6859.9365, 2510.3855, 9.727478e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setDir -48.394917;
  _this setPos [6859.9365, 2510.3855, 9.727478e-005];
};
 
_vehicle_215 = objNull;
if (true) then
{
  _this = createVehicle ["Land_psi_bouda", [6933.0713, 2493.4504, 0.00010871887], [], 0, "CAN_COLLIDE"];
  _vehicle_215 = _this;
  _this setDir -32.777939;
  _this setPos [6933.0713, 2493.4504, 0.00010871887];
};
 
_vehicle_216 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6869.7725, 2474.7598, 4.3869019e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_216 = _this;
  _this setDir 52.09782;
  _this setPos [6869.7725, 2474.7598, 4.3869019e-005];
};
 
_vehicle_217 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6869.8501, 2479.2783, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_217 = _this;
  _this setDir 54.118629;
  _this setPos [6869.8501, 2479.2783, 7.6293945e-005];
};
 
_vehicle_218 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6874.1313, 2478.0398, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_218 = _this;
  _this setDir 53.28553;
  _this setPos [6874.1313, 2478.0398, 3.0517578e-005];
};
 
_vehicle_219 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6873.8735, 2482.395, 7.0571899e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_219 = _this;
  _this setDir 52.992397;
  _this setPos [6873.8735, 2482.395, 7.0571899e-005];
};
 
_vehicle_220 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6878.2461, 2481.198, 0.00022220612], [], 0, "CAN_COLLIDE"];
  _vehicle_220 = _this;
  _this setDir 53.226665;
  _this setPos [6878.2461, 2481.198, 0.00022220612];
};
 
_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6869.8149, 2472.4231, 0.00015115738], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setDir -37.769875;
  _this setPos [6869.8149, 2472.4231, 0.00015115738];
};
 
_vehicle_222 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6871.7979, 2473.926, 0.00010871887], [], 0, "CAN_COLLIDE"];
  _vehicle_222 = _this;
  _this setDir -37.007736;
  _this setPos [6871.7979, 2473.926, 0.00010871887];
};
 
_vehicle_223 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6873.8047, 2475.4277, 0.00019264221], [], 0, "CAN_COLLIDE"];
  _vehicle_223 = _this;
  _this setDir -37.610355;
  _this setPos [6873.8047, 2475.4277, 0.00019264221];
};
 
_vehicle_224 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6875.8062, 2476.937, 0.00033760071], [], 0, "CAN_COLLIDE"];
  _vehicle_224 = _this;
  _this setDir -37.015697;
  _this setPos [6875.8062, 2476.937, 0.00033760071];
};
 
_vehicle_225 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6877.7666, 2478.4429, 4.9591064e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_225 = _this;
  _this setDir -38.110882;
  _this setPos [6877.7666, 2478.4429, 4.9591064e-005];
};
 
_vehicle_226 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6879.75, 2479.9897, 2.3841858e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_226 = _this;
  _this setDir -37.826183;
  _this setPos [6879.75, 2479.9897, 2.3841858e-005];
};
 
_vehicle_227 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6865.5122, 2478.3738], [], 0, "CAN_COLLIDE"];
  _vehicle_227 = _this;
  _this setDir -37.982529;
  _this setPos [6865.5122, 2478.3738];
};
 
_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6867.4878, 2479.9055, 0.00015830994], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setDir -38.06353;
  _this setPos [6867.4878, 2479.9055, 0.00015830994];
};
 
_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6869.4727, 2481.4402, 1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setDir -35.668858;
  _this setPos [6869.4727, 2481.4402, 1.9073486e-005];
};
 
_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6871.4458, 2482.9368, 0.00010204315], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setDir -37.643612;
  _this setPos [6871.4458, 2482.9368, 0.00010204315];
};
 
_vehicle_231 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6873.3818, 2484.4363, 0.00013446808], [], 0, "CAN_COLLIDE"];
  _vehicle_231 = _this;
  _this setDir -38.107597;
  _this setPos [6873.3818, 2484.4363, 0.00013446808];
};
 
_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["Concrete_Wall_EP1", [6875.2563, 2485.9363, 4.2915344e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setDir -37.678036;
  _this setPos [6875.2563, 2485.9363, 4.2915344e-006];
};
 
_vehicle_239 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_deerstand", [6874.7788, 2468.4751, 0.00021934509], [], 0, "CAN_COLLIDE"];
  _vehicle_239 = _this;
  _this setDir 52.389187;
  _this setPos [6874.7788, 2468.4751, 0.00021934509];
};
 
_vehicle_240 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_deerstand", [6865.5454, 2480.3318, 5.2452087e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_240 = _this;
  _this setDir 51.405235;
  _this setPos [6865.5454, 2480.3318, 5.2452087e-006];
};
 
_vehicle_242 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net2", [6877.2734, 2500.074, 8.6784363e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_242 = _this;
  _this setDir -3.8909826;
  _this setPos [6877.2734, 2500.074, 8.6784363e-005];
};
 
_vehicle_243 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNetVar_EAST", [6902.5122, 2546.5083, 0.00015640259], [], 0, "CAN_COLLIDE"];
  _vehicle_243 = _this;
  _this setDir 40.875751;
  _this setPos [6902.5122, 2546.5083, 0.00015640259];
};
 
_vehicle_244 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_small", [6910.4702, 2539.2642, 0.0001039505], [], 0, "CAN_COLLIDE"];
  _vehicle_244 = _this;
  _this setPos [6910.4702, 2539.2642, 0.0001039505];
};
 
_vehicle_245 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_small", [6913.0449, 2537.4995, 0.00013494492], [], 0, "CAN_COLLIDE"];
  _vehicle_245 = _this;
  _this setDir 28.758589;
  _this setPos [6913.0449, 2537.4995, 0.00013494492];
};
 
_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_small2", [6914.9106, 2535.2568, 9.7751617e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setDir 64.08918;
  _this setPos [6914.9106, 2535.2568, 9.7751617e-005];
};
 
_vehicle_247 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_tiny", [6908.1274, 2538.998, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_247 = _this;
  _this setDir 27.142601;
  _this setPos [6908.1274, 2538.998, -2.8610229e-006];
};
 
_vehicle_248 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net3", [6923.0996, 2525.4238, 5.6266785e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_248 = _this;
  _this setDir -46.625217;
  _this setPos [6923.0996, 2525.4238, 5.6266785e-005];
};
 
_vehicle_249 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BarGate2", [6879.7422, 2482.2981, 0.00012016296], [], 0, "CAN_COLLIDE"];
  _vehicle_249 = _this;
  _this setDir 52.300526;
  _this setPos [6879.7422, 2482.2981, 0.00012016296];
};
 
_vehicle_250 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BarGate2", [6877.5063, 2485.3188, 7.9154968e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_250 = _this;
  _this setDir 55.129105;
  _this setPos [6877.5063, 2485.3188, 7.9154968e-005];
};
 
_vehicle_252 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [6917.5645, 2528.6128, 0.063793823], [], 0, "CAN_COLLIDE"];
  _vehicle_252 = _this;
  _this setDir -305.80801;
  _this setPos [6917.5645, 2528.6128, 0.063793823];
};
 
_vehicle_253 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast_EP1", [6898.2393, 2519.5862, -1.7166138e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_253 = _this;
  _this setDir -134.76749;
  _this setPos [6898.2393, 2519.5862, -1.7166138e-005];
};
 
_vehicle_254 = objNull;
if (true) then
{
  _this = createVehicle ["Camp", [6907.3735, 2512.5066, 0.15546094], [], 0, "CAN_COLLIDE"];
  _vehicle_254 = _this;
  _this setDir 51.152615;
  _this setPos [6907.3735, 2512.5066, 0.15546094];
};
 
_vehicle_258 = objNull;
if (true) then
{
  _this = createVehicle ["Land_tent_east", [6899.8208, 2437.9019, 3.4332275e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_258 = _this;
  _this setDir 53.963745;
  _this setPos [6899.8208, 2437.9019, 3.4332275e-005];
};
 
_vehicle_259 = objNull;
if (true) then
{
  _this = createVehicle ["Land_tent_east", [6885.0327, 2458.8835, 1.7166138e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_259 = _this;
  _this setDir 53.487518;
  _this setPos [6885.0327, 2458.8835, 1.7166138e-005];
};
 
_vehicle_261 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Backpackheap", [6918.981, 2524.5989, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_261 = _this;
  _this setPos [6918.981, 2524.5989, 5.7220459e-006];
};
 
_vehicle_262 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Blankets_EP1", [6942.3696, 2500.0205, 2.6702881e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_262 = _this;
  _this setPos [6942.3696, 2500.0205, 2.6702881e-005];
};
 
_vehicle_263 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bag_EP1", [6947.4951, 2493.6804, 1.7642975e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_263 = _this;
  _this setPos [6947.4951, 2493.6804, 1.7642975e-005];
};
 
_vehicle_264 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Crates_EP1", [6950.96, 2490.2219, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_264 = _this;
  _this setPos [6950.96, 2490.2219, 9.5367432e-007];
};
 
_vehicle_265 = objNull;
if (true) then
{
  _this = createVehicle ["FoldTable", [6894.5024, 2525.4016, 3.3378601e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_265 = _this;
  _this setDir -42.504704;
  _this setPos [6894.5024, 2525.4016, 3.3378601e-006];
};
 
_vehicle_266 = objNull;
if (true) then
{
  _this = createVehicle ["FoldChair", [6895.8403, 2525.1831, -6.1988831e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_266 = _this;
  _this setDir 92.091583;
  _this setPos [6895.8403, 2525.1831, -6.1988831e-006];
};
 
_vehicle_267 = objNull;
if (true) then
{
  _this = createVehicle ["FoldChair", [6893.5962, 2526.2002, 8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_267 = _this;
  _this setDir -65.140259;
  _this setPos [6893.5962, 2526.2002, 8.5830688e-006];
};
 
_vehicle_268 = objNull;
if (true) then
{
  _this = createVehicle ["FoldChair", [6894.2695, 2524.1318, 1.001358e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_268 = _this;
  _this setDir 151.05209;
  _this setPos [6894.2695, 2524.1318, 1.001358e-005];
};
 
_vehicle_270 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [6953.5068, 2466.3208, 2.1457672e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_270 = _this;
  _this setDir 53.654114;
  _this setPos [6953.5068, 2466.3208, 2.1457672e-005];
};
 
_vehicle_271 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [6955.7534, 2467.8545, 3.2424927e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_271 = _this;
  _this setDir 55.526463;
  _this setPos [6955.7534, 2467.8545, 3.2424927e-005];
};
 
_vehicle_272 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [6957.9199, 2469.1548, 5.1021576e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_272 = _this;
  _this setDir 55.572075;
  _this setPos [6957.9199, 2469.1548, 5.1021576e-005];
};
 
_vehicle_273 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Table_EP1", [6902.9897, 2541.6772, 4.1007996e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_273 = _this;
  _this setDir -44.92181;
  _this setPos [6902.9897, 2541.6772, 4.1007996e-005];
};
 
_vehicle_275 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Table_EP1", [6901.959, 2542.7449, 2.3841858e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_275 = _this;
  _this setDir -44.802879;
  _this setPos [6901.959, 2542.7449, 2.3841858e-006];
};
 
_vehicle_276 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Table_EP1", [6900.9561, 2543.8062, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_276 = _this;
  _this setDir -45.684025;
  _this setPos [6900.9561, 2543.8062, 3.8146973e-006];
};
 
_vehicle_279 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Crates_EP1", [6899.625, 2544.7754, 1.7642975e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_279 = _this;
  _this setPos [6899.625, 2544.7754, 1.7642975e-005];
};
 
_vehicle_280 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bucket_EP1", [6899.835, 2544.1167, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_280 = _this;
  _this setPos [6899.835, 2544.1167, 5.7220459e-006];
};
 
_vehicle_281 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Blankets_EP1", [6904.4204, 2540.8176, -1.4305115e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_281 = _this;
  _this setPos [6904.4204, 2540.8176, -1.4305115e-006];
};
 
_vehicle_282 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Basket_EP1", [6899.0225, 2545.2605, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_282 = _this;
  _this setPos [6899.0225, 2545.2605, 5.7220459e-006];
};
 
_vehicle_301 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Well_C_EP1", [6934.7866, 2453.1904, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_301 = _this;
  _this setDir 1.9954436;
  _this setPos [6934.7866, 2453.1904, -1.9073486e-006];
};
 
_vehicle_302 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Campfire_burning", [6954.2598, 2470.7529, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_302 = _this;
  _this setPos [6954.2598, 2470.7529, 2.8610229e-006];
};
 
_vehicle_303 = objNull;
if (true) then
{
  _this = createVehicle ["T72Wreck", [6923.9805, 2434.9409, -1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_303 = _this;
  _this setPos [6923.9805, 2434.9409, -1.2397766e-005];
};
 
_vehicle_304 = objNull;
if (true) then
{
  _this = createVehicle ["datsun02Wreck", [6920.1772, 2429.1975, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_304 = _this;
  _this setDir 66.04615;
  _this setPos [6920.1772, 2429.1975, -9.5367432e-007];
};
 
_vehicle_305 = objNull;
if (true) then
{
  _this = createVehicle ["datsun01Wreck", [6918.8911, 2433.3762, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_305 = _this;
  _this setPos [6918.8911, 2433.3762, -9.5367432e-007];
};
 
_vehicle_308 = objNull;
if (true) then
{
  _this = createVehicle ["SKODAWreck", [6920.6528, 2436.4963, 4.196167e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_308 = _this;
  _this setDir -47.700592;
  _this setPos [6920.6528, 2436.4963, 4.196167e-005];
};
 
_vehicle_309 = objNull;
if (true) then
{
  _this = createVehicle ["UAZWreck", [6916.7798, 2432.4263, 4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_309 = _this;
  _this setDir 259.65387;
  _this setPos [6916.7798, 2432.4263, 4.7683716e-006];
};
 
_vehicle_318 = objNull;
if (true) then
{
  _this = createVehicle ["C130J_wreck_EP1", [6831.5488, 2271.6995, -0.81505674], [], 0, "CAN_COLLIDE"];
  _vehicle_318 = _this;
  _this setDir 42.649948;
  _this setPos [6831.5488, 2271.6995, -0.81505674];
};
 
_vehicle_327 = objNull;
if (true) then
{
  _this = createVehicle ["USMC_WarfareBFieldhHospital", [6881.1309, 2533.1758, 0.091935642], [], 0, "CAN_COLLIDE"];
  _vehicle_327 = _this;
  _this setDir -57.968998;
  _this setPos [6881.1309, 2533.1758, 0.091935642];
};
 
_vehicle_329 = objNull;
if (true) then
{
  _this = createVehicle ["hiluxWreck", [6915.6758, 2441.5286, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_329 = _this;
  _this setDir -35.24062;
  _this setPos [6915.6758, 2441.5286, -3.8146973e-006];
};
 
_vehicle_330 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [6912.5698, 2448.751, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_330 = _this;
  _this setDir -36.532326;
  _this setPos [6912.5698, 2448.751, -6.1035156e-005];
};
 
_vehicle_331 = objNull;
if (true) then
{
  _this = createVehicle ["UAZWreck", [6908.6455, 2451.3318, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_331 = _this;
  _this setDir -11.071608;
  _this setPos [6908.6455, 2451.3318, 9.5367432e-006];
};
 
_vehicle_332 = objNull;
if (true) then
{
  _this = createVehicle ["SKODAWreck", [6907.0781, 2457.0325, 7.4386597e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_332 = _this;
  _this setDir -29.695831;
  _this setPos [6907.0781, 2457.0325, 7.4386597e-005];
};
 
_vehicle_333 = objNull;
if (true) then
{
  _this = createVehicle ["hiluxWreck", [6900.8315, 2463.3113, 3.2424927e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_333 = _this;
  _this setDir -43.580128;
  _this setPos [6900.8315, 2463.3113, 3.2424927e-005];
};
 
_vehicle_334 = objNull;
if (true) then
{
  _this = createVehicle ["BRDMWreck", [6919.6484, 2441.927, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_334 = _this;
  _this setDir -21.55472;
  _this setPos [6919.6484, 2441.927, 9.5367432e-006];
};
 
_vehicle_335 = objNull;
if (true) then
{
  _this = createVehicle ["BMP2Wreck", [6903.7134, 2456.0576, -3.4332275e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_335 = _this;
  _this setDir -31.843973;
  _this setPos [6903.7134, 2456.0576, -3.4332275e-005];
};
 
_vehicle_336 = objNull;
if (true) then
{
  _this = createVehicle ["T72WreckTurret", [6911.8218, 2442.0967, -9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_336 = _this;
  _this setDir 3.5279195;
  _this setPos [6911.8218, 2442.0967, -9.5367432e-006];
};
 
_vehicle_338 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [6895.5962, 2469.446, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_338 = _this;
  _this setDir -13.752922;
  _this setPos [6895.5962, 2469.446, -1.1444092e-005];
};
 
_vehicle_339 = objNull;
if (true) then
{
  _this = createVehicle ["LADAWreck", [6920.5908, 2474.1663, 1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_339 = _this;
  _this setDir -2.1656337;
  _this setPos [6920.5908, 2474.1663, 1.1444092e-005];
};
 
_vehicle_343 = objNull;
if (true) then
{
  _this = createVehicle ["Land_obihacka", [6907.646, 2468.7014, 1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_343 = _this;
  _this setDir 51.525635;
  _this setPos [6907.646, 2468.7014, 1.1444092e-005];
};
 
_vehicle_344 = objNull;
if (true) then
{
  _this = createVehicle ["Land_prolejzacka", [6914.6289, 2455.2646, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_344 = _this;
  _this setDir 53.221497;
  _this setPos [6914.6289, 2455.2646, 4.5776367e-005];
};
 
_vehicle_346 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Coil_EP1", [6932.689, 2473.1853, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_346 = _this;
  _this setPos [6932.689, 2473.1853, 1.9073486e-006];
};
 
_vehicle_347 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_BoardsPack1", [6930.7056, 2476.6497, 2.3365021e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_347 = _this;
  _this setPos [6930.7056, 2476.6497, 2.3365021e-005];
};
 
_vehicle_356 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6900.416, 2544.8464, -2.0027161e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_356 = _this;
  _this setPos [6900.416, 2544.8464, -2.0027161e-005];
};
};
