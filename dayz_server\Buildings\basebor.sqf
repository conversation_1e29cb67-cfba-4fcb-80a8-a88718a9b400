/*
  CHERNARUS ENHANCEMENTS - Bor Military Base
  ----------------------------------------------------------
    Military Base, Camp Bor by <PERSON>, blackwiddow
    Email: <EMAIL>
    Steam: blackwiddow20
*/


if (isServer) then {

_vehicle_3301 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_House", [2896.6016, 4503.7505, 0.23285694], [], 0, "CAN_COLLIDE"];
  _vehicle_3301 = _this;
  _this setDir -89.442787;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2896.6016, 4503.7505, 0.23285694];
};

_vehicle_3306 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [2898.9043, 4467.2925, 0.11803091], [], 0, "CAN_COLLIDE"];
  _vehicle_3306 = _this;
  _this setDir -89.038475;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2898.9043, 4467.2925, 0.11803091];
};

_vehicle_3308 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [2869.4885, 4501.8965, -0.03989353], [], 0, "CAN_COLLIDE"];
  _vehicle_3308 = _this;
  _this setDir -629.09021;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2869.4885, 4501.8965, -0.03989353];
};

_vehicle_3310 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hlidac_budka", [2887.156, 4449.0405, 0.017982315], [], 0, "CAN_COLLIDE"];
  _vehicle_3310 = _this;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2887.156, 4449.0405, 0.017982315];
};

_vehicle_3311 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Guardhouse", [2869.1665, 4461.7607, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3311 = _this;
  _this setDir -178.86699;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2869.1665, 4461.7607, 3.0517578e-005];
};

_vehicle_3314 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2898.0757, 4450.7192, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3314 = _this;
  _this setDir -181.53957;
  _this setPos [2898.0757, 4450.7192, 3.0517578e-005];
};

_vehicle_3318 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [2890.5342, 4450.5322], [], 0, "CAN_COLLIDE"];
  _vehicle_3318 = _this;
  _this setDir -182.38899;
  _this setPos [2890.5342, 4450.5322];
};

_vehicle_3319 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_L", [2877.7075, 4453.9482, -0.078066565], [], 0, "CAN_COLLIDE"];
  _vehicle_3319 = _this;
  _this setDir 5.1135168;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2877.7075, 4453.9482, -0.078066565];
};

_vehicle_3320 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_R", [2877.7517, 4453.9688, -0.038961664], [], 0, "CAN_COLLIDE"];
  _vehicle_3320 = _this;
  _this setDir -9.5964155;
  _this setPos [2877.7517, 4453.9688, -0.038961664];
};

_vehicle_3321 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2883.825, 4454.6226, 0.15495279], [], 0, "CAN_COLLIDE"];
  _vehicle_3321 = _this;
  _this setDir 0.38438192;
  _this setPos [2883.825, 4454.6226, 0.15495279];
};

_vehicle_3325 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [2895.5779, 4454.6519, 0.1290243], [], 0, "CAN_COLLIDE"];
  _vehicle_3325 = _this;
  _this setDir -361.56604;
  _this setPos [2895.5779, 4454.6519, 0.1290243];
};

_vehicle_3331 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2889.6689, 4454.5898, 0.11695766], [], 0, "CAN_COLLIDE"];
  _vehicle_3331 = _this;
  _this setDir 0.38438192;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2889.6689, 4454.5898, 0.11695766];
};

_vehicle_3333 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2901.5308, 4454.7305, 0.1158902], [], 0, "CAN_COLLIDE"];
  _vehicle_3333 = _this;
  _this setDir 0.38438192;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2901.5308, 4454.7305, 0.1158902];
};

_vehicle_3336 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2905.5217, 4456.5942, -0.038900614], [], 0, "CAN_COLLIDE"];
  _vehicle_3336 = _this;
  _this setDir -88.761261;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2905.5217, 4456.5942, -0.038900614];
};

_vehicle_3339 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2907.115, 4450.8999, -0.038931139], [], 0, "CAN_COLLIDE"];
  _vehicle_3339 = _this;
  _this setDir -181.53957;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2907.115, 4450.8999, -0.038931139];
};

_vehicle_3342 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3_D", [2908.6311, 4458.5449, -0.019145567], [], 0, "CAN_COLLIDE"];
  _vehicle_3342 = _this;
  _this setDir -269.44318;
  _this setPos [2908.6311, 4458.5449, -0.019145567];
};

_vehicle_3345 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2908.7739, 4467.4995, -0.058473002], [], 0, "CAN_COLLIDE"];
  _vehicle_3345 = _this;
  _this setDir -268.90402;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2908.7739, 4467.4995, -0.058473002];
};

_vehicle_3348 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2905.6177, 4462.4766, -0.057113249], [], 0, "CAN_COLLIDE"];
  _vehicle_3348 = _this;
  _this setDir -88.761261;
  _this setPos [2905.6177, 4462.4766, -0.057113249];
};

_vehicle_3351 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2905.8896, 4474.3135, 0.05411341], [], 0, "CAN_COLLIDE"];
  _vehicle_3351 = _this;
  _this setDir -88.761261;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2905.8896, 4474.3135, 0.05411341];
};

_vehicle_3354 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [2905.7429, 4468.4297, 0.077923313], [], 0, "CAN_COLLIDE"];
  _vehicle_3354 = _this;
  _this setDir -448.72064;
  _this setPos [2905.7429, 4468.4297, 0.077923313];
};

_vehicle_3357 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [2908.6274, 4451.0557, -0.038900621], [], 0, "CAN_COLLIDE"];
  _vehicle_3357 = _this;
  _this setDir -270.6304;
  _this setPos [2908.6274, 4451.0557, -0.038900621];
};

_vehicle_3360 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3_Hole", [2908.6516, 4455.5454, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3360 = _this;
  _this setDir -268.78696;
  _this setPos [2908.6516, 4455.5454, 3.0517578e-005];
};

_vehicle_3362 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [2908.6021, 4452.5854, -0.08195506], [], 0, "CAN_COLLIDE"];
  _vehicle_3362 = _this;
  _this setDir -269.96475;
  _this setPos [2908.6021, 4452.5854, -0.08195506];
};

_vehicle_3363 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [2883.2568, 4455.4512, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3363 = _this;
  _this setPos [2883.2568, 4455.4512, 3.0517578e-005];
};

_vehicle_3364 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2908.717, 4450.7925, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3364 = _this;
  _this setPos [2908.717, 4450.7925, -3.0517578e-005];
};

_vehicle_3371 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2905.9673, 4480.2129, 0.077984355], [], 0, "CAN_COLLIDE"];
  _vehicle_3371 = _this;
  _this setDir -88.761261;
  _this setPos [2905.9673, 4480.2129, 0.077984355];
};

_vehicle_3372 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2906.0632, 4486.0952, 0.1072202], [], 0, "CAN_COLLIDE"];
  _vehicle_3372 = _this;
  _this setDir -88.761261;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2906.0632, 4486.0952, 0.1072202];
};

_vehicle_3373 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [2906.1885, 4492.0483, 0.083533347], [], 0, "CAN_COLLIDE"];
  _vehicle_3373 = _this;
  _this setDir -448.72064;
  _this setPos [2906.1885, 4492.0483, 0.083533347];
};

_vehicle_3374 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2906.3352, 4497.9321, 0.13970554], [], 0, "CAN_COLLIDE"];
  _vehicle_3374 = _this;
  _this setDir -88.761261;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2906.3352, 4497.9321, 0.13970554];
};

_vehicle_3382 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2906.4529, 4503.8521, 0.17621294], [], 0, "CAN_COLLIDE"];
  _vehicle_3382 = _this;
  _this setDir -88.761261;
  _this setPos [2906.4529, 4503.8521, 0.17621294];
};

_vehicle_3383 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2906.8477, 4521.832, -0.0063298829], [], 0, "CAN_COLLIDE"];
  _vehicle_3383 = _this;
  _this setDir -268.60162;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2906.8477, 4521.832, -0.0063298829];
};

_vehicle_3387 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [2906.7205, 4515.7524, 0.17275856], [], 0, "CAN_COLLIDE"];
  _vehicle_3387 = _this;
  _this setDir -88.289352;
  _this setPos [2906.7205, 4515.7524, 0.17275856];
};

_vehicle_3390 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [2906.5708, 4509.772, 0.19843146], [], 0, "CAN_COLLIDE"];
  _vehicle_3390 = _this;
  _this setDir -448.72064;
  _this setPos [2906.5708, 4509.772, 0.19843146];
};

_vehicle_3393 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2908.9873, 4476.4521, -0.21525523], [], 0, "CAN_COLLIDE"];
  _vehicle_3393 = _this;
  _this setDir -268.30502;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2908.9873, 4476.4521, -0.21525523];
};

_vehicle_3396 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2909.2551, 4485.4546, -0.23267749], [], 0, "CAN_COLLIDE"];
  _vehicle_3396 = _this;
  _this setDir -268.30502;
  _this setPos [2909.2551, 4485.4546, -0.23267749];
};

_vehicle_3398 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2909.5063, 4494.4316, -0.17418155], [], 0, "CAN_COLLIDE"];
  _vehicle_3398 = _this;
  _this setDir -268.30502;
  _this setPos [2909.5063, 4494.4316, -0.17418155];
};

_vehicle_3401 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2909.7644, 4503.3467, -0.058473013], [], 0, "CAN_COLLIDE"];
  _vehicle_3401 = _this;
  _this setDir -268.30502;
  _this setPos [2909.7644, 4503.3467, -0.058473013];
};

_vehicle_3404 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2871.6003, 4454.2539, -0.077968679], [], 0, "CAN_COLLIDE"];
  _vehicle_3404 = _this;
  _this setDir -181.95563;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2871.6003, 4454.2539, -0.077968679];
};

_vehicle_3407 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [2865.699, 4454.0884, -0.069273978], [], 0, "CAN_COLLIDE"];
  _vehicle_3407 = _this;
  _this setDir -540.17029;
  _this setPos [2865.699, 4454.0884, -0.069273978];
};

_vehicle_3409 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [2861.6748, 4459.4229, -0.01422785], [], 0, "CAN_COLLIDE"];
  _vehicle_3409 = _this;
  _this setDir -449.28665;
  _this setPos [2861.6748, 4459.4229, -0.01422785];
};

_vehicle_3412 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [2861.6868, 4455.9619, 0.21439159], [], 0, "CAN_COLLIDE"];
  _vehicle_3412 = _this;
  _this setDir -89.077805;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2861.6868, 4455.9619, 0.21439159];
};

_vehicle_3415 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2861.7761, 4465.3374, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3415 = _this;
  _this setDir -88.761261;
  _this setPos [2861.7761, 4465.3374, 3.0517578e-005];
};

_vehicle_3418 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2861.897, 4471.2847, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3418 = _this;
  _this setDir -88.761261;
  _this setPos [2861.897, 4471.2847, 3.0517578e-005];
};

_vehicle_3421 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2906.9741, 4527.7036, -0.018455494], [], 0, "CAN_COLLIDE"];
  _vehicle_3421 = _this;
  _this setDir -268.60162;
  _this setPos [2906.9741, 4527.7036, -0.018455494];
};

_vehicle_3423 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2910.0317, 4512.3208, -0.038961656], [], 0, "CAN_COLLIDE"];
  _vehicle_3423 = _this;
  _this setDir -268.30502;
  _this setPos [2910.0317, 4512.3208, -0.038961656];
};

_vehicle_3426 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3_D", [2910.1072, 4515.3081, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3426 = _this;
  _this setDir -269.44318;
  _this setPos [2910.1072, 4515.3081, 3.0517578e-005];
};

_vehicle_3429 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2910.3301, 4524.2842, -0.073748864], [], 0, "CAN_COLLIDE"];
  _vehicle_3429 = _this;
  _this setDir -268.30502;
  _this setPos [2910.3301, 4524.2842, -0.073748864];
};

_vehicle_3432 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [2902.9565, 4529.4795, 0.018537302], [], 0, "CAN_COLLIDE"];
  _vehicle_3432 = _this;
  _this setDir -361.56604;
  _this setPos [2902.9565, 4529.4795, 0.018537302];
};

_vehicle_3435 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2897.0244, 4529.4502, 0.019450311], [], 0, "CAN_COLLIDE"];
  _vehicle_3435 = _this;
  _this setDir 0.38438192;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2897.0244, 4529.4502, 0.019450311];
};

_vehicle_3441 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [2906.7231, 4515.875, 0.038961664], [], 0, "CAN_COLLIDE"];
  _vehicle_3441 = _this;
  _this setDir -268.97504;
  _this setPos [2906.7231, 4515.875, 0.038961664];
};

_vehicle_3444 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3_Hole", [2910.3931, 4527.2598, -0.15584663], [], 0, "CAN_COLLIDE"];
  _vehicle_3444 = _this;
  _this setDir -268.51202;
  _this setPos [2910.3931, 4527.2598, -0.15584663];
};

_vehicle_3447 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3_D", [2910.4539, 4530.2495, -0.15581614], [], 0, "CAN_COLLIDE"];
  _vehicle_3447 = _this;
  _this setDir -269.44318;
  _this setPos [2910.4539, 4530.2495, -0.15581614];
};

_vehicle_3450 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [2909.3506, 4532.6338, -0.17557205], [], 0, "CAN_COLLIDE"];
  _vehicle_3450 = _this;
  _this setDir -321.89932;
  _this setPos [2909.3506, 4532.6338, -0.17557205];
};

_vehicle_3453 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2900.7778, 4533.5562, -0.1763487], [], 0, "CAN_COLLIDE"];
  _vehicle_3453 = _this;
  _this setDir -359.76151;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2900.7778, 4533.5562, -0.1763487];
};

_vehicle_3474 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2862.0305, 4477.2168, -0.019450311], [], 0, "CAN_COLLIDE"];
  _vehicle_3474 = _this;
  _this setDir -88.761261;
  _this setPos [2862.0305, 4477.2168, -0.019450311];
};

_vehicle_3475 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2862.1514, 4483.1641, -0.046305779], [], 0, "CAN_COLLIDE"];
  _vehicle_3475 = _this;
  _this setDir -88.761261;
  _this setPos [2862.1514, 4483.1641, -0.046305779];
};

_vehicle_3483 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [2862.2468, 4489.0986, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3483 = _this;
  _this setDir -449.28665;
  _this setPos [2862.2468, 4489.0986, 6.1035156e-005];
};

_vehicle_3484 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2862.3481, 4495.0132, 0.01763916], [], 0, "CAN_COLLIDE"];
  _vehicle_3484 = _this;
  _this setDir -88.761261;
  _this setPos [2862.3481, 4495.0132, 0.01763916];
};

_vehicle_3485 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2862.469, 4500.9604, 0.054229736], [], 0, "CAN_COLLIDE"];
  _vehicle_3485 = _this;
  _this setDir -88.761261;
  _this setPos [2862.469, 4500.9604, 0.054229736];
};

_vehicle_3486 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2862.6025, 4506.8926, 0.06428609], [], 0, "CAN_COLLIDE"];
  _vehicle_3486 = _this;
  _this setDir -88.761261;
  _this setPos [2862.6025, 4506.8926, 0.06428609];
};

_vehicle_3487 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2862.7234, 4512.8398, 0.048223134], [], 0, "CAN_COLLIDE"];
  _vehicle_3487 = _this;
  _this setDir -88.761261;
  _this setPos [2862.7234, 4512.8398, 0.048223134];
};

_vehicle_3495 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [2862.8237, 4518.7344, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3495 = _this;
  _this setDir -449.28665;
  _this setPos [2862.8237, 4518.7344, 3.0517578e-005];
};

_vehicle_3496 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2862.9443, 4524.6226, 0.084399909], [], 0, "CAN_COLLIDE"];
  _vehicle_3496 = _this;
  _this setDir -88.761261;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2862.9443, 4524.6226, 0.084399909];
};

_vehicle_3500 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2891.0925, 4529.4756, 0.097404137], [], 0, "CAN_COLLIDE"];
  _vehicle_3500 = _this;
  _this setDir 0.38438192;
  _this setPos [2891.0925, 4529.4756, 0.097404137];
};

_vehicle_3503 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2885.1372, 4529.5391, 0.097404137], [], 0, "CAN_COLLIDE"];
  _vehicle_3503 = _this;
  _this setDir 0.38438192;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2885.1372, 4529.5391, 0.097404137];
};

_vehicle_3509 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [2879.1814, 4529.4346, 0.018599551], [], 0, "CAN_COLLIDE"];
  _vehicle_3509 = _this;
  _this setDir -362.1911;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2879.1814, 4529.4346, 0.018599551];
};

_vehicle_3510 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2873.2231, 4529.1997, 0.0029301867], [], 0, "CAN_COLLIDE"];
  _vehicle_3510 = _this;
  _this setDir -1.8032482;
  _this setPos [2873.2231, 4529.1997, 0.0029301867];
};

_vehicle_3511 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [2867.2603, 4528.9321, 0.13907845], [], 0, "CAN_COLLIDE"];
  _vehicle_3511 = _this;
  _this setDir -3.0533226;
  _this setPos [2867.2603, 4528.9321, 0.13907845];
};

_vehicle_3519 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [2864.8928, 4528.8345, 0.097404145], [], 0, "CAN_COLLIDE"];
  _vehicle_3519 = _this;
  _this setDir -7.1166835;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2864.8928, 4528.8345, 0.097404145];
};

_vehicle_3522 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2891.7871, 4533.6055, -0.15581609], [], 0, "CAN_COLLIDE"];
  _vehicle_3522 = _this;
  _this setDir -359.76151;
  _this setPos [2891.7871, 4533.6055, -0.15581609];
};

_vehicle_3524 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2882.7925, 4533.5708, -0.058442485], [], 0, "CAN_COLLIDE"];
  _vehicle_3524 = _this;
  _this setDir -359.76151;
  _this setPos [2882.7925, 4533.5708, -0.058442485];
};

_vehicle_3527 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2871.6392, 4450.4282], [], 0, "CAN_COLLIDE"];
  _vehicle_3527 = _this;
  _this setDir -181.53957;
  _this setPos [2871.6392, 4450.4282];
};

_vehicle_3531 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2873.8718, 4533.5015, -0.058350932], [], 0, "CAN_COLLIDE"];
  _vehicle_3531 = _this;
  _this setDir -361.32416;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2873.8718, 4533.5015, -0.058350932];
};

_vehicle_3534 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2864.8843, 4533.1196, -0.077923313], [], 0, "CAN_COLLIDE"];
  _vehicle_3534 = _this;
  _this setDir -362.57428;
  _this setPos [2864.8843, 4533.1196, -0.077923313];
};

_vehicle_3537 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3_D", [2861.8792, 4532.9111, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3537 = _this;
  _this setDir -361.65723;
  _this setPos [2861.8792, 4532.9111, 3.0517578e-005];
};

_vehicle_3540 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2859.8267, 4525.2139, 0.060442585], [], 0, "CAN_COLLIDE"];
  _vehicle_3540 = _this;
  _this setDir -446.22833;
  _this setPos [2859.8267, 4525.2139, 0.060442585];
};

_vehicle_3543 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [2860.3127, 4532.729, 0.019480828], [], 0, "CAN_COLLIDE"];
  _vehicle_3543 = _this;
  _this setDir -450.49728;
  _this setPos [2860.3127, 4532.729, 0.019480828];
};

_vehicle_3546 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2859.6602, 4516.2524, 0.13544978], [], 0, "CAN_COLLIDE"];
  _vehicle_3546 = _this;
  _this setDir -449.35364;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2859.6602, 4516.2524, 0.13544978];
};

_vehicle_3549 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2859.5422, 4507.3535, 0.039053217], [], 0, "CAN_COLLIDE"];
  _vehicle_3549 = _this;
  _this setDir -448.72858;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2859.5422, 4507.3535, 0.039053217];
};

_vehicle_3552 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2859.3374, 4498.3882, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3552 = _this;
  _this setDir -448.72858;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2859.3374, 4498.3882, -3.0517578e-005];
};

_vehicle_3555 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3_D", [2859.2002, 4495.396, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3555 = _this;
  _this setDir -448.72821;
  _this setPos [2859.2002, 4495.396, 3.0517578e-005];
};

_vehicle_3558 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2859.0447, 4486.4087], [], 0, "CAN_COLLIDE"];
  _vehicle_3558 = _this;
  _this setDir -448.72858;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2859.0447, 4486.4087];
};

_vehicle_3561 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2858.856, 4477.4263], [], 0, "CAN_COLLIDE"];
  _vehicle_3561 = _this;
  _this setDir -448.72858;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2858.856, 4477.4263];
};

_vehicle_3564 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2858.6511, 4468.4375, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3564 = _this;
  _this setDir -448.72858;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2858.6511, 4468.4375, 6.1035156e-005];
};

_vehicle_3567 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [2858.436, 4459.4302], [], 0, "CAN_COLLIDE"];
  _vehicle_3567 = _this;
  _this setDir -448.72858;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2858.436, 4459.4302];
};

_vehicle_3570 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3_D", [2858.2952, 4456.4365], [], 0, "CAN_COLLIDE"];
  _vehicle_3570 = _this;
  _this setDir -448.72821;
  _this setPos [2858.2952, 4456.4365];
};

_vehicle_3573 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [2862.7129, 4450.1772, 0.077250347], [], 0, "CAN_COLLIDE"];
  _vehicle_3573 = _this;
  _this setDir -182.09396;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [2862.7129, 4450.1772, 0.077250347];
};

_vehicle_3576 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [2860.061, 4451.1011, 0.019480828], [], 0, "CAN_COLLIDE"];
  _vehicle_3576 = _this;
  _this setDir -139.39333;
  _this setPos [2860.061, 4451.1011, 0.019480828];
};

_vehicle_3579 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [2858.5991, 4453.5166, 0.077923313], [], 0, "CAN_COLLIDE"];
  _vehicle_3579 = _this;
  _this setDir -104.06274;
  _this setPos [2858.5991, 4453.5166, 0.077923313];
};

_vehicle_3581 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [2870.1921, 4449.478, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3581 = _this;
  _this setPos [2870.1921, 4449.478, -3.0517578e-005];
};

_vehicle_3582 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_pmugo", [2858.7434, 4510.8276, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3582 = _this;
  _this setPos [2858.7434, 4510.8276, 3.0517578e-005];
};

_vehicle_3583 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_craet2", [2910.7615, 4493.9888, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_3583 = _this;
  _this setDir -48.453613;
  _this setPos [2910.7615, 4493.9888, 0.00012207031];
};

_vehicle_3585 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2872.8293, 4450.3525, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3585 = _this;
  _this setPos [2872.8293, 4450.3525, 0];
};

_vehicle_3587 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2858.748, 4481.8828, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3587 = _this;
  _this setPos [2858.748, 4481.8828, 6.1035156e-005];
};

_vehicle_3589 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2893.5056, 4450.4619, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3589 = _this;
  _this setPos [2893.5056, 4450.4619, 0];
};

_vehicle_3591 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2894.2253, 4450.7021, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3591 = _this;
  _this setPos [2894.2253, 4450.7021, 0];
};

_vehicle_3593 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2896.5259, 4450.6904, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3593 = _this;
  _this setPos [2896.5259, 4450.6904, 0];
};

_vehicle_3595 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2908.9944, 4472.1143, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3595 = _this;
  _this setPos [2908.9944, 4472.1143, 0];
};

_vehicle_3597 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2909.0037, 4474.6143, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3597 = _this;
  _this setPos [2909.0037, 4474.6143, 3.0517578e-005];
};

_vehicle_3599 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2909.0193, 4473.8306, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3599 = _this;
  _this setPos [2909.0193, 4473.8306, 0];
};

_vehicle_3601 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2862.7302, 4478.2915, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3601 = _this;
  _this setPos [2862.7302, 4478.2915, 0];
};

_vehicle_3603 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2896.0212, 4487.4004, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3603 = _this;
  _this setPos [2896.0212, 4487.4004, 3.0517578e-005];
};

_vehicle_3605 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2897.1018, 4487.4385, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3605 = _this;
  _this setPos [2897.1018, 4487.4385, 0];
};

_vehicle_3607 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2890.8171, 4487.5898, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3607 = _this;
  _this setPos [2890.8171, 4487.5898, 3.0517578e-005];
};

_vehicle_3610 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [2870.259, 4524.7612, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3610 = _this;
  _this setDir -39.562504;
  _this setPos [2870.259, 4524.7612, 9.1552734e-005];
};

_vehicle_3651 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_6konec", [2905.2712, 4382.6851], [], 0, "CAN_COLLIDE"];
  _vehicle_3651 = _this;
  _this setDir -18.699928;
  _this setPos [2905.2712, 4382.6851];
};

_vehicle_3653 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_60_10", [2895.8005, 4395.1973, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3653 = _this;
  _this setDir 101.51058;
  _this setPos [2895.8005, 4395.1973, -6.1035156e-005];
};

_vehicle_3657 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_60_10", [2895.8352, 4395.2759, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3657 = _this;
  _this setDir 282.41492;
  _this setPos [2895.8352, 4395.2759, -9.1552734e-005];
};

_vehicle_3659 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_15_75", [2881.5701, 4425.9292], [], 0, "CAN_COLLIDE"];
  _vehicle_3659 = _this;
  _this setDir -16.928057;
  _this setPos [2881.5701, 4425.9292];
};

_vehicle_3660 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_25", [2888.4856, 4401.9556, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3660 = _this;
  _this setDir -16.122313;
  _this setPos [2888.4856, 4401.9556, -3.0517578e-005];
};

_vehicle_3662 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_25", [2878.3762, 4445.1782, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3662 = _this;
  _this setDir 0.12865338;
  _this setPos [2878.3762, 4445.1782, -6.1035156e-005];
};

_vehicle_3665 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_6konec", [2878.3716, 4476.3994], [], 0, "CAN_COLLIDE"];
  _vehicle_3665 = _this;
  _this setDir -180.27017;
  _this setPos [2878.3716, 4476.3994];
};

_vehicle_3667 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [2882.0313, 4445.5288, 0.29244801], [], 0, "CAN_COLLIDE"];
  _vehicle_3667 = _this;
  _this setDir -2.9437296;
  _this setPos [2882.0313, 4445.5288, 0.29244801];
};

_vehicle_3669 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [2874.2605, 4445.0498, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3669 = _this;
  _this setPos [2874.2605, 4445.0498, -3.0517578e-005];
};

_vehicle_3672 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [2886.4063, 4423.4663, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3672 = _this;
  _this setPos [2886.4063, 4423.4663, 3.0517578e-005];
};

_vehicle_3674 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_craet1", [2907.3818, 4390.3428, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3674 = _this;
  _this setPos [2907.3818, 4390.3428, 0];
};

_vehicle_3675 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [2886.2036, 4396.6904, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3675 = _this;
  _this setDir -84.942764;
  _this setPos [2886.2036, 4396.6904, -9.1552734e-005];
};

_vehicle_3677 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_salix2s", [2887.7512, 4395.2017, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3677 = _this;
  _this setDir 4.1387162;
  _this setPos [2887.7512, 4395.2017, -3.0517578e-005];
};

_vehicle_3679 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [2877.2927, 4407.3081, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3679 = _this;
  _this setPos [2877.2927, 4407.3081, 3.0517578e-005];
};

_vehicle_3681 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [2872.9897, 4432.1738, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3681 = _this;
  _this setDir -59.231003;
  _this setPos [2872.9897, 4432.1738, 3.0517578e-005];
};

_vehicle_3682 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [2882.668, 4443.0962, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3682 = _this;
  _this setPos [2882.668, 4443.0962, 3.0517578e-005];
};

_vehicle_3683 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [2874.4446, 4449.7759, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3683 = _this;
  _this setPos [2874.4446, 4449.7759, 3.0517578e-005];
};

_vehicle_3684 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [2874.5469, 4483.3965, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3684 = _this;
  _this setDir -134.84663;
  _this setPos [2874.5469, 4483.3965, 3.0517578e-005];
};

_vehicle_3686 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [2885.7197, 4505.8047], [], 0, "CAN_COLLIDE"];
  _vehicle_3686 = _this;
  _this setDir -29.874996;
  _this setPos [2885.7197, 4505.8047];
};

_vehicle_3689 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [2871.1902, 4430.2056], [], 0, "CAN_COLLIDE"];
  _vehicle_3689 = _this;
  _this setDir 126.64704;
  _this setPos [2871.1902, 4430.2056];
};

_vehicle_3691 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [2894.4546, 4468.2246, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3691 = _this;
  _this setDir -248.8972;
  _this setPos [2894.4546, 4468.2246, -3.0517578e-005];
};

_vehicle_3693 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2", [2899.7268, 4476.4741], [], 0, "CAN_COLLIDE"];
  _vehicle_3693 = _this;
  _this setDir 1.5165167;
  _this setPos [2899.7268, 4476.4741];
};

_vehicle_3695 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2", [2867.9727, 4492.7666, 0.019450314], [], 0, "CAN_COLLIDE"];
  _vehicle_3695 = _this;
  _this setDir -178.65076;
  _this setPos [2867.9727, 4492.7666, 0.019450314];
};

_vehicle_3697 = objNull;
if (true) then
{
  _this = createVehicle ["MASH", [2868.0542, 4472.793, -0.038992174], [], 0, "CAN_COLLIDE"];
  _vehicle_3697 = _this;
  _this setDir 77.029076;
  _this setPos [2868.0542, 4472.793, -0.038992174];
};

_vehicle_3699 = objNull;
if (true) then
{
  _this = createVehicle ["MASH", [2867.0276, 4481.1797, -0.017579133], [], 0, "CAN_COLLIDE"];
  _vehicle_3699 = _this;
  _this setDir 90.066544;
  _this setPos [2867.0276, 4481.1797, -0.017579133];
};

_vehicle_3703 = objNull;
if (true) then
{
  _this = createVehicle ["Land_tent_east", [2887.4785, 4464.4775, 0.058411956], [], 0, "CAN_COLLIDE"];
  _vehicle_3703 = _this;
  _this setDir 84.837479;
  _this setPos [2887.4785, 4464.4775, 0.058411956];
};

_vehicle_3704 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrates_NoInteractive_Large", [2885.249, 4467.8062, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3704 = _this;
  _this setDir -248.56442;
  _this setPos [2885.249, 4467.8062, -3.0517578e-005];
};

_vehicle_3705 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrates_NoInteractive_Medium", [2885.1392, 4465.5439, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3705 = _this;
  _this setDir -178.98303;
  _this setPos [2885.1392, 4465.5439, 3.0517578e-005];
};

_vehicle_3706 = objNull;
if (true) then
{
  _this = createVehicle ["FoldChair", [2889.991, 4463.668, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3706 = _this;
  _this setDir 112.94894;
  _this setPos [2889.991, 4463.668, 3.0517578e-005];
};

_vehicle_3707 = objNull;
if (true) then
{
  _this = createVehicle ["FoldTable", [2889.0112, 4464.3042, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3707 = _this;
  _this setDir -92.020905;
  _this setPos [2889.0112, 4464.3042, -3.0517578e-005];
};

_vehicle_3709 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrates_NoInteractive_Large", [2889.8491, 4461.0171], [], 0, "CAN_COLLIDE"];
  _vehicle_3709 = _this;
  _this setDir -365.41824;
  _this setPos [2889.8491, 4461.0171];
};

_vehicle_3711 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Boots_EP1", [2888.624, 4468.7554, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3711 = _this;
  _this setDir -92.002899;
  _this setPos [2888.624, 4468.7554, 6.1035156e-005];
};

_vehicle_3713 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bag_EP1", [2889.6235, 4468.731], [], 0, "CAN_COLLIDE"];
  _vehicle_3713 = _this;
  _this setDir 119.67742;
  _this setPos [2889.6235, 4468.731];
};

_vehicle_3714 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Rack_EP1", [2885.7979, 4460.168], [], 0, "CAN_COLLIDE"];
  _vehicle_3714 = _this;
  _this setDir -98.950996;
  _this setPos [2885.7979, 4460.168];
};

_vehicle_3715 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [2885.0454, 4462.4644], [], 0, "CAN_COLLIDE"];
  _vehicle_3715 = _this;
  _this setDir -4.7290616;
  _this setPos [2885.0454, 4462.4644];
};

_vehicle_3717 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrates_NoInteractive_Medium", [2885.0759, 4466.5757, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3717 = _this;
  _this setDir -456.42203;
  _this setPos [2885.0759, 4466.5757, 6.1035156e-005];
};

_vehicle_3719 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrates_NoInteractive_Small", [2885.1082, 4464.23], [], 0, "CAN_COLLIDE"];
  _vehicle_3719 = _this;
  _this setDir 82.939728;
  _this setPos [2885.1082, 4464.23];
};

_vehicle_3720 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2885.9121, 4462.5137, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3720 = _this;
  _this setPos [2885.9121, 4462.5137, 3.0517578e-005];
};

_vehicle_3728 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2899.397, 4477.2446, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3728 = _this;
  _this setPos [2899.397, 4477.2446, -3.0517578e-005];
};

_vehicle_3730 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2869.2043, 4492.5137, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3730 = _this;
  _this setPos [2869.2043, 4492.5137, -3.0517578e-005];
};

_vehicle_3732 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2866.8384, 4492.5015, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3732 = _this;
  _this setPos [2866.8384, 4492.5015, 0];
};

_vehicle_3734 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2887.1211, 4469.3257, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3734 = _this;
  _this setPos [2887.1211, 4469.3257, 0];
};

_vehicle_3735 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_concrete_block", [2893.3828, 4514.8291, -1.7475029], [], 0, "CAN_COLLIDE"];
  _vehicle_3735 = _this;
  _this setDir -89.260216;
  _this setPos [2893.3828, 4514.8291, -1.7475029];
};

_vehicle_3749 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_quercus2f", [2906.0088, 4364.6255, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3749 = _this;
  _this setDir -293.61322;
  _this setPos [2906.0088, 4364.6255, 9.1552734e-005];
};

_vehicle_3751 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_quercus2f", [2895.54, 4356.8105, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3751 = _this;
  _this setDir -559.16315;
  _this setPos [2895.54, 4356.8105, 6.1035156e-005];
};

_vehicle_3754 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [2881.0938, 4482.8359, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3754 = _this;
  _this setDir 114.36145;
  _this setPos [2881.0938, 4482.8359, -6.1035156e-005];
};

_vehicle_3767 = objNull;
if (true) then
{
  _this = createVehicle ["CDF_WarfareBFieldhHospital", [2876.1008, 4522.3838, -0.1363658], [], 0, "CAN_COLLIDE"];
  _vehicle_3767 = _this;
  _this setDir -179.92215;
  _this setPos [2876.1008, 4522.3838, -0.1363658];
};

_vehicle_3770 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [2889.1885, 4516.0845], [], 0, "CAN_COLLIDE"];
  _vehicle_3770 = _this;
  _this setDir -174.51073;
  _this setPos [2889.1885, 4516.0845];
};

_vehicle_3772 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [2888.9209, 4518.3511], [], 0, "CAN_COLLIDE"];
  _vehicle_3772 = _this;
  _this setDir -190.70253;
  _this setPos [2888.9209, 4518.3511];
};

_vehicle_3775 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2888.6111, 4518.459, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3775 = _this;
  _this setPos [2888.6111, 4518.459, 3.0517578e-005];
};

_vehicle_3777 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2888.4175, 4516.313, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3777 = _this;
  _this setPos [2888.4175, 4516.313, 3.0517578e-005];
};

_vehicle_3779 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2885.5942, 4446.5264, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3779 = _this;
  _this setPos [2885.5942, 4446.5264, 3.0517578e-005];
};

_vehicle_3781 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2888.0591, 4459.6167, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_3781 = _this;
  _this setPos [2888.0591, 4459.6167, 0];
};

_vehicle_3783 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2887.4709, 4500.9727, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3783 = _this;
  _this setPos [2887.4709, 4500.9727, 6.1035156e-005];
};

_vehicle_3785 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2881.0103, 4522.3657, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3785 = _this;
  _this setPos [2881.0103, 4522.3657, 3.0517578e-005];
};

_vehicle_3787 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [2870.8889, 4522.3315, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3787 = _this;
  _this setPos [2870.8889, 4522.3315, -3.0517578e-005];
};

};
