/*
  CHERNARUS ENHANCEMENTS - Dichina Military Base
  --------------------------------------------------------------
    Military Base, Camp Dichina by <PERSON>, blackwiddow
    Email: <EMAIL>
    Steam: blackwiddow20
*/


if (isServer) then {

_vehicle_1 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runwayold_40_main", [4455.1274, 8284.0059], [], 0, "CAN_COLLIDE"];
  _vehicle_1 = _this;
  _this setDir 42.277748;
  _this setPos [4455.1274, 8284.0059];
};

_vehicle_3 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runwayold_40_main", [4482.0171, 8313.4883, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3 = _this;
  _this setDir 42.277748;
  _this setPos [4482.0171, 8313.4883, -3.0517578e-005];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4455.0776, 8297.8086, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setPos [4455.0776, 8297.8086, -3.0517578e-005];
};

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4465.23, 8281.5713, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setPos [4465.23, 8281.5713, 0];
};

_vehicle_9 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4484.8062, 8309.1064, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_9 = _this;
  _this setPos [4484.8062, 8309.1064, -6.1035156e-005];
};

_vehicle_11 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4477.4385, 8325.2715, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_11 = _this;
  _this setPos [4477.4385, 8325.2715, -3.0517578e-005];
};

_vehicle_13 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4477.8018, 8292.4336, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_13 = _this;
  _this setPos [4477.8018, 8292.4336, 0];
};

_vehicle_15 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4469.5166, 8314.2656, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_15 = _this;
  _this setPos [4469.5166, 8314.2656, 0];
};

_vehicle_17 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4444.3164, 8280.7627, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_17 = _this;
  _this setPos [4444.3164, 8280.7627, 0];
};

_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runwayold_40_main", [4511.6274, 8286.6084, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir 42.277748;
  _this setPos [4511.6274, 8286.6084, 3.0517578e-005];
};

_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [4502.8467, 8266.2744, -0.021881606], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setDir -233.78778;
  _this setPos [4502.8467, 8266.2744, -0.021881606];
};

_vehicle_37 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4490.5371, 8293.3535, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_37 = _this;
  _this setPos [4490.5371, 8293.3535, 0];
};

_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4501.1318, 8302.7432, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setPos [4501.1318, 8302.7432, -3.0517578e-005];
};

_vehicle_41 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4514.1353, 8295.7568, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_41 = _this;
  _this setPos [4514.1353, 8295.7568, 0];
};

_vehicle_43 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4505.6934, 8286.9746, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_43 = _this;
  _this setPos [4505.6934, 8286.9746, -3.0517578e-005];
};

_vehicle_45 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4496.6226, 8283.7568, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_45 = _this;
  _this setPos [4496.6226, 8283.7568, -3.0517578e-005];
};

_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4508.9287, 8273.1592, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setPos [4508.9287, 8273.1592, -9.1552734e-005];
};

_vehicle_49 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4523.8896, 8284.7363, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_49 = _this;
  _this setPos [4523.8896, 8284.7363, 3.0517578e-005];
};

_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4496.1973, 8316.834, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setPos [4496.1973, 8316.834, -6.1035156e-005];
};

_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4457.1152, 8271.9404, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setPos [4457.1152, 8271.9404, 0];
};

_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4467.23, 8300.0361, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setPos [4467.23, 8300.0361, -3.0517578e-005];
};

_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4442.3018, 8285.0156, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setPos [4442.3018, 8285.0156, -6.1035156e-005];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4447.8843, 8295.127, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setPos [4447.8843, 8295.127, 0];
};

_vehicle_61 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4460.8105, 8307.8906, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_61 = _this;
  _this setPos [4460.8105, 8307.8906, 9.1552734e-005];
};

_vehicle_63 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4435.0122, 8283.7227, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_63 = _this;
  _this setPos [4435.0122, 8283.7227, 0];
};

_vehicle_65 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4457.8623, 8266.7529, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_65 = _this;
  _this setPos [4457.8623, 8266.7529, -3.0517578e-005];
};

_vehicle_67 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4451.8857, 8270.4873, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_67 = _this;
  _this setPos [4451.8857, 8270.4873, -3.0517578e-005];
};

_vehicle_69 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4443.5298, 8277.0039, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_69 = _this;
  _this setPos [4443.5298, 8277.0039, 0];
};

_vehicle_71 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4517.9619, 8275.4678, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_71 = _this;
  _this setPos [4517.9619, 8275.4678, 0];
};

_vehicle_73 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4511.7456, 8266.3398, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_73 = _this;
  _this setPos [4511.7456, 8266.3398, -3.0517578e-005];
};

_vehicle_75 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4527.8608, 8284.8076, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_75 = _this;
  _this setPos [4527.8608, 8284.8076, 6.1035156e-005];
};

_vehicle_77 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4524.9526, 8290.6943, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_77 = _this;
  _this setPos [4524.9526, 8290.6943, -3.0517578e-005];
};

_vehicle_79 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4506.4487, 8310.6836, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_79 = _this;
  _this setPos [4506.4487, 8310.6836, 0];
};

_vehicle_81 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4487.3081, 8323.7646, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_81 = _this;
  _this setPos [4487.3081, 8323.7646, 0];
};

_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4482.6484, 8332.6299, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setPos [4482.6484, 8332.6299, 0];
};

_vehicle_85 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4469.8472, 8318.9629, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_85 = _this;
  _this setPos [4469.8472, 8318.9629, -3.0517578e-005];
};

_vehicle_87 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4454.0356, 8304.1074, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_87 = _this;
  _this setPos [4454.0356, 8304.1074, 3.0517578e-005];
};

_vehicle_91 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4428.293, 8281.4082, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_91 = _this;
  _this setDir 42.473866;
  _this setPos [4428.293, 8281.4082, 3.0517578e-005];
};

_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4432.6563, 8277.4131], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setDir 42.197716;
  _this setPos [4432.6563, 8277.4131];
};

_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4437.0488, 8273.3916], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setDir 42.953388;
  _this setPos [4437.0488, 8273.3916];
};

_vehicle_97 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4441.4458, 8269.3457, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_97 = _this;
  _this setDir 42.359646;
  _this setPos [4441.4458, 8269.3457, -9.1552734e-005];
};

_vehicle_99 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4445.8809, 8265.3672, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_99 = _this;
  _this setDir 42.080784;
  _this setPos [4445.8809, 8265.3672, -6.1035156e-005];
};

_vehicle_105 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4453.519, 8258.4531], [], 0, "CAN_COLLIDE"];
  _vehicle_105 = _this;
  _this setDir 42.359646;
  _this setPos [4453.519, 8258.4531];
};

_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [4450.3159, 8261.3418, 0.022853695], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setDir 42.998627;
  _this setPos [4450.3159, 8261.3418, 0.022853695];
};

_vehicle_110 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4457.6763, 8257.1621, 0.1287715], [], 0, "CAN_COLLIDE"];
  _vehicle_110 = _this;
  _this setDir -47.264111;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4457.6763, 8257.1621, 0.1287715];
};

_vehicle_115 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_Pole", [4460.4795, 8260.1895, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_115 = _this;
  _this setDir 44.297943;
  _this setPos [4460.4795, 8260.1895, 9.1552734e-005];
};

_vehicle_117 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind2B_L", [4463.3076, 8263.3379, 0.0030939691], [], 0, "CAN_COLLIDE"];
  _vehicle_117 = _this;
  _this setDir -48.245514;
  _this setPos [4463.3076, 8263.3379, 0.0030939691];
};

_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind2B_R", [4463.3267, 8263.3643, -0.076325543], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setDir -46.994564;
  _this setPos [4463.3267, 8263.3643, -0.076325543];
};

_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4467.5005, 8267.8887, -0.11441202], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir -47.264111;
  _this setPos [4467.5005, 8267.8887, -0.11441202];
};

_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4471.5005, 8272.2832, 0.034650654], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setDir -47.264111;
  _this setPos [4471.5005, 8272.2832, 0.034650654];
};

_vehicle_131 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4441.0566, 8286.2324, 0.036413539], [], 0, "CAN_COLLIDE"];
  _vehicle_131 = _this;
  _this setDir -47.698288;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4441.0566, 8286.2324, 0.036413539];
};

_vehicle_132 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Guardhouse", [4464.8999, 8274.8633, -0.016689539], [], 0, "CAN_COLLIDE"];
  _vehicle_132 = _this;
  _this setDir -47.653637;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4464.8999, 8274.8633, -0.016689539];
};

_vehicle_133 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_House", [4499.6792, 8279.9189, -0.034210011], [], 0, "CAN_COLLIDE"];
  _vehicle_133 = _this;
  _this setDir -137.60631;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4499.6792, 8279.9189, -0.034210011];
};

_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4483.7686, 8325.9619, 0.17669716], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir 42.444321;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4483.7686, 8325.9619, 0.17669716];
};

_vehicle_141 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4477.7817, 8271.874], [], 0, "CAN_COLLIDE"];
  _vehicle_141 = _this;
  _this setDir 42.475948;
  _this setPos [4477.7817, 8271.874];
};

_vehicle_144 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [4475.6421, 8274, 0.092954047], [], 0, "CAN_COLLIDE"];
  _vehicle_144 = _this;
  _this setDir 41.628315;
  _this setPos [4475.6421, 8274, 0.092954047];
};

_vehicle_147 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [4501.6543, 8267.6768, -0.021798577], [], 0, "CAN_COLLIDE"];
  _vehicle_147 = _this;
  _this setDir -218.21065;
  _this setPos [4501.6543, 8267.6768, -0.021798577];
};

_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4482.186, 8267.8232, 0.020292122], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setDir 42.475948;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4482.186, 8267.8232, 0.020292122];
};

_vehicle_154 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4486.6016, 8263.8818, 0.037601195], [], 0, "CAN_COLLIDE"];
  _vehicle_154 = _this;
  _this setDir 42.475948;
  _this setPos [4486.6016, 8263.8818, 0.037601195];
};

_vehicle_155 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4490.9927, 8259.8496, 0.048497826], [], 0, "CAN_COLLIDE"];
  _vehicle_155 = _this;
  _this setDir 42.475948;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4490.9927, 8259.8496, 0.048497826];
};

_vehicle_159 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4495.4248, 8255.8594, 0.064620793], [], 0, "CAN_COLLIDE"];
  _vehicle_159 = _this;
  _this setDir 42.475948;
  _this setPos [4495.4248, 8255.8594, 0.064620793];
};

_vehicle_162 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4514.7485, 8260.3516, -0.2503688], [], 0, "CAN_COLLIDE"];
  _vehicle_162 = _this;
  _this setDir -47.264111;
  _this setPos [4514.7485, 8260.3516, -0.2503688];
};

_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4510.7246, 8255.9971, -0.23590934], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setDir -47.264111;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4510.7246, 8255.9971, -0.23590934];
};

_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4500.2485, 8252.7598, 0.16450427], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setDir 12.170462;
  _this setPos [4500.2485, 8252.7598, 0.16450427];
};

_vehicle_170 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4505.8848, 8252.7695, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_170 = _this;
  _this setDir -26.39006;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4505.8848, 8252.7695, -3.0517578e-005];
};

_vehicle_173 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4518.7129, 8264.6973, 0.044301525], [], 0, "CAN_COLLIDE"];
  _vehicle_173 = _this;
  _this setDir -47.264111;
  _this setPos [4518.7129, 8264.6973, 0.044301525];
};

_vehicle_176 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4522.7173, 8269.0869, -0.10877174], [], 0, "CAN_COLLIDE"];
  _vehicle_176 = _this;
  _this setDir -47.264111;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4522.7173, 8269.0869, -0.10877174];
};

_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4526.7319, 8273.4238, -0.095542185], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setDir -47.603394;
  _this setPos [4526.7319, 8273.4238, -0.095542185];
};

_vehicle_182 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4530.7275, 8277.8291, -0.072480284], [], 0, "CAN_COLLIDE"];
  _vehicle_182 = _this;
  _this setDir -47.603394;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4530.7275, 8277.8291, -0.072480284];
};

_vehicle_185 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4537.1606, 8284.9883, -0.20172426], [], 0, "CAN_COLLIDE"];
  _vehicle_185 = _this;
  _this setDir -48.160027;
  _this setPos [4537.1606, 8284.9883, -0.20172426];
};

_vehicle_188 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4538.4102, 8289.2412, 0.19338486], [], 0, "CAN_COLLIDE"];
  _vehicle_188 = _this;
  _this setDir -137.29247;
  _this setPos [4538.4102, 8289.2412, 0.19338486];
};

_vehicle_193 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [4534.7017, 8282.2109, -0.071944058], [], 0, "CAN_COLLIDE"];
  _vehicle_193 = _this;
  _this setDir -48.385944;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4534.7017, 8282.2109, -0.071944058];
};

_vehicle_199 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4483.6216, 8338.9922, 0.015428776], [], 0, "CAN_COLLIDE"];
  _vehicle_199 = _this;
  _this setDir -137.29247;
  _this setPos [4483.6216, 8338.9922, 0.015428776];
};

_vehicle_202 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4487.9897, 8335.0488, 0.020406228], [], 0, "CAN_COLLIDE"];
  _vehicle_202 = _this;
  _this setDir -137.29247;
  _this setPos [4487.9897, 8335.0488, 0.020406228];
};

_vehicle_205 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4492.395, 8331.0303, 0.030041888], [], 0, "CAN_COLLIDE"];
  _vehicle_205 = _this;
  _this setDir -137.29247;
  _this setPos [4492.395, 8331.0303, 0.030041888];
};

_vehicle_208 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4496.8184, 8327.0195, 0.075861178], [], 0, "CAN_COLLIDE"];
  _vehicle_208 = _this;
  _this setDir -137.29247;
  _this setPos [4496.8184, 8327.0195, 0.075861178];
};

_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4501.2451, 8323.0127, 0.07417573], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setDir -137.88622;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4501.2451, 8323.0127, 0.07417573];
};

_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [4534.0195, 8293.2139, -0.055374932], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setDir -138.94084;
  _this setPos [4534.0195, 8293.2139, -0.055374932];
};

_vehicle_219 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ural_wrecked", [4531.7324, 8293.1963, 0.027000766], [], 0, "CAN_COLLIDE"];
  _vehicle_219 = _this;
  _this setDir -551.02521;
  _this setPos [4531.7324, 8293.1963, 0.027000766];
};

_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4505.6245, 8319.0293, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setDir -137.29247;
  _this setPos [4505.6245, 8319.0293, -3.0517578e-005];
};

_vehicle_223 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4510.0498, 8315.0107, 0.15279393], [], 0, "CAN_COLLIDE"];
  _vehicle_223 = _this;
  _this setDir -137.29247;
  _this setPos [4510.0498, 8315.0107, 0.15279393];
};

_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4514.4746, 8311.04, 0.15040971], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setDir -137.88622;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4514.4746, 8311.04, 0.15040971];
};

_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4518.9063, 8307.0352, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setDir -137.29247;
  _this setPos [4518.9063, 8307.0352, 3.0517578e-005];
};

_vehicle_235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4523.3081, 8302.9961, 0.15596418], [], 0, "CAN_COLLIDE"];
  _vehicle_235 = _this;
  _this setDir -137.29247;
  _this setPos [4523.3081, 8302.9961, 0.15596418];
};

_vehicle_238 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4527.6733, 8299.0078, 0.23441772], [], 0, "CAN_COLLIDE"];
  _vehicle_238 = _this;
  _this setDir -137.29247;
  _this setPos [4527.6733, 8299.0078, 0.23441772];
};

_vehicle_241 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4429.6812, 8285.6768, 0.0099805798], [], 0, "CAN_COLLIDE"];
  _vehicle_241 = _this;
  _this setDir -227.59558;
  _this setPos [4429.6812, 8285.6768, 0.0099805798];
};

_vehicle_244 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4433.6826, 8290.085, 0.01510006], [], 0, "CAN_COLLIDE"];
  _vehicle_244 = _this;
  _this setDir -227.59558;
  _this setPos [4433.6826, 8290.085, 0.01510006];
};

_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4437.6909, 8294.5176, 0.03542269], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setDir -227.59558;
  _this setPos [4437.6909, 8294.5176, 0.03542269];
};

_vehicle_248 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4441.6934, 8298.9346, 0.025696583], [], 0, "CAN_COLLIDE"];
  _vehicle_248 = _this;
  _this setDir -227.59558;
  _this setPos [4441.6934, 8298.9346, 0.025696583];
};

_vehicle_251 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4445.6865, 8303.3281, 0.058537133], [], 0, "CAN_COLLIDE"];
  _vehicle_251 = _this;
  _this setDir -227.59558;
  _this setPos [4445.6865, 8303.3281, 0.058537133];
};

_vehicle_254 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4449.7051, 8307.7451, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_254 = _this;
  _this setDir -227.59558;
  _this setPos [4449.7051, 8307.7451, -3.0517578e-005];
};

_vehicle_256 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4453.7222, 8312.1406], [], 0, "CAN_COLLIDE"];
  _vehicle_256 = _this;
  _this setDir -227.59558;
  _this setPos [4453.7222, 8312.1406];
};

_vehicle_258 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4457.7676, 8316.5508, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_258 = _this;
  _this setDir -227.59558;
  _this setPos [4457.7676, 8316.5508, -3.0517578e-005];
};

_vehicle_260 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4461.7715, 8320.9395, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_260 = _this;
  _this setDir -227.59558;
  _this setPos [4461.7715, 8320.9395, 9.1552734e-005];
};

_vehicle_262 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4465.7974, 8325.3379, 0.080768034], [], 0, "CAN_COLLIDE"];
  _vehicle_262 = _this;
  _this setDir -227.59558;
  _this setPos [4465.7974, 8325.3379, 0.080768034];
};

_vehicle_264 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4469.8242, 8329.7529, 0.095338255], [], 0, "CAN_COLLIDE"];
  _vehicle_264 = _this;
  _this setDir -227.59558;
  _this setPos [4469.8242, 8329.7529, 0.095338255];
};

_vehicle_266 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4473.8657, 8334.1797, 0.071605675], [], 0, "CAN_COLLIDE"];
  _vehicle_266 = _this;
  _this setDir -227.59558;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4473.8657, 8334.1797, 0.071605675];
};

_vehicle_268 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [4479.4199, 8340.2568, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_268 = _this;
  _this setDir -227.59558;
  _this setPos [4479.4199, 8340.2568, 3.0517578e-005];
};

_vehicle_271 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [4475.4189, 8335.8438, -0.1164048], [], 0, "CAN_COLLIDE"];
  _vehicle_271 = _this;
  _this setDir -228.66524;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4475.4189, 8335.8438, -0.1164048];
};

_vehicle_273 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [4477.9258, 8341.3574, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_273 = _this;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4477.9258, 8341.3574, 3.0517578e-005];
};

_vehicle_275 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [4538.7759, 8290.8135], [], 0, "CAN_COLLIDE"];
  _vehicle_275 = _this;
  _this setDir 80.945892;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4538.7759, 8290.8135];
};

_vehicle_277 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [4507.3037, 8252.6113, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_277 = _this;
  _this setDir -185.17941;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4507.3037, 8252.6113, 3.0517578e-005];
};

_vehicle_279 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [4459.4092, 8256.5488], [], 0, "CAN_COLLIDE"];
  _vehicle_279 = _this;
  _this setDir -181.13397;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4459.4092, 8256.5488];
};

_vehicle_282 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [4427.5879, 8279.7852, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_282 = _this;
  _this setDir -91.958656;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4427.5879, 8279.7852, -3.0517578e-005];
};

_vehicle_286 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4467.6084, 8308.3037, 0.1335163], [], 0, "CAN_COLLIDE"];
  _vehicle_286 = _this;
  _this setDir 42.444321;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4467.6084, 8308.3037, 0.1335163];
};

_vehicle_289 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [4505.1494, 8289.7842, -0.020064224], [], 0, "CAN_COLLIDE"];
  _vehicle_289 = _this;
  _this setDir 43.912647;
  _this setPos [4505.1494, 8289.7842, -0.020064224];
};

_vehicle_291 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [4512.1509, 8284.5713, -0.055040937], [], 0, "CAN_COLLIDE"];
  _vehicle_291 = _this;
  _this setDir 18.475332;
  _this setPos [4512.1509, 8284.5713, -0.055040937];
};

_vehicle_294 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [4512.6333, 8303.8457, -0.023175176], [], 0, "CAN_COLLIDE"];
  _vehicle_294 = _this;
  _this setDir -137.01602;
  _this setPos [4512.6333, 8303.8457, -0.023175176];
};

_vehicle_297 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [4505.1592, 8310.499, -0.038178034], [], 0, "CAN_COLLIDE"];
  _vehicle_297 = _this;
  _this setDir -137.01602;
  _this setPos [4505.1592, 8310.499, -0.038178034];
};

_vehicle_300 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [4519.0581, 8297.7236, -0.016287899], [], 0, "CAN_COLLIDE"];
  _vehicle_300 = _this;
  _this setDir -137.01602;
  _this setPos [4519.0581, 8297.7236, -0.016287899];
};

_vehicle_303 = objNull;
if (true) then
{
  _this = createVehicle ["PowGen_Big", [4484.0571, 8271.0439], [], 0, "CAN_COLLIDE"];
  _vehicle_303 = _this;
  _this setDir -42.103592;
  _this setPos [4484.0571, 8271.0439];
};

_vehicle_304 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net1", [4497.2441, 8256.4883], [], 0, "CAN_COLLIDE"];
  _vehicle_304 = _this;
  _this setPos [4497.2441, 8256.4883];
};

_vehicle_305 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net2", [4493.3359, 8259.916, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_305 = _this;
  _this setDir -52.802326;
  _this setPos [4493.3359, 8259.916, -3.0517578e-005];
};

_vehicle_309 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [4475.6099, 8266.0244, 0.00093756057], [], 0, "CAN_COLLIDE"];
  _vehicle_309 = _this;
  _this setDir -407.99448;
  _this setPos [4475.6099, 8266.0244, 0.00093756057];
};

_vehicle_319 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_6konec", [4477.5688, 8246.4346], [], 0, "CAN_COLLIDE"];
  _vehicle_319 = _this;
  _this setDir -33.578186;
  _this setPos [4477.5688, 8246.4346];
};

_vehicle_321 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_25", [4471.5161, 8255.0635, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_321 = _this;
  _this setDir 138.10011;
  _this setPos [4471.5161, 8255.0635, 0.00018310547];
};

_vehicle_323 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_25", [4468.7344, 8258.1426, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_323 = _this;
  _this setDir 132.57484;
  _this setPos [4468.7344, 8258.1426, 3.0517578e-005];
};

_vehicle_326 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_6konec", [4461.9321, 8264.9287, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_326 = _this;
  _this setDir -227.34528;
  _this setPos [4461.9321, 8264.9287, -3.0517578e-005];
};

_vehicle_329 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_mud_10_25", [4466.2358, 8260.8838], [], 0, "CAN_COLLIDE"];
  _vehicle_329 = _this;
  _this setDir 132.57484;
  _this setPos [4466.2358, 8260.8838];
};

_vehicle_335 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Piskoviste", [4456.5454, 8300.2441, 0.32460874], [], 0, "CAN_COLLIDE"];
  _vehicle_335 = _this;
  _this setDir -47.321766;
  _this setVehicleInit "this allowDammage false;this enableSimulation false;";
  _this setPos [4456.5454, 8300.2441, 0.32460874];
};

_vehicle_336 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_acer2s", [4457.0161, 8300.2656, 0.43820107], [], 0, "CAN_COLLIDE"];
  _vehicle_336 = _this;
  _this setPos [4457.0161, 8300.2656, 0.43820107];
};

_vehicle_344 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCorner", [4458.6621, 8300.3174, -0.036050014], [], 0, "CAN_COLLIDE"];
  _vehicle_344 = _this;
  _this setDir 42.768097;
  _this setPos [4458.6621, 8300.3174, -0.036050014];
};

_vehicle_346 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCorner", [4456.6318, 8298.1279, -0.040501218], [], 0, "CAN_COLLIDE"];
  _vehicle_346 = _this;
  _this setDir -226.93788;
  _this setPos [4456.6318, 8298.1279, -0.040501218];
};

_vehicle_350 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCorner", [4454.437, 8300.1719, -0.035763297], [], 0, "CAN_COLLIDE"];
  _vehicle_350 = _this;
  _this setDir -137.36478;
  _this setPos [4454.437, 8300.1719, -0.035763297];
};

_vehicle_351 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCorner", [4456.4604, 8302.3594, -0.037625141], [], 0, "CAN_COLLIDE"];
  _vehicle_351 = _this;
  _this setDir -407.07074;
  _this setPos [4456.4604, 8302.3594, -0.037625141];
};

_vehicle_360 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_acer2s", [4483.2051, 8311.3242, 0.45773962], [], 0, "CAN_COLLIDE"];
  _vehicle_360 = _this;
  _this setDir -98.890907;
  _this setPos [4483.2051, 8311.3242, 0.45773962];
};

_vehicle_361 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Piskoviste", [4483.2422, 8310.6865, 0.35910681], [], 0, "CAN_COLLIDE"];
  _vehicle_361 = _this;
  _this setDir -47.321766;
  _this setVehicleInit "this allowDammage false;this enableSimulation false;";
  _this setPos [4483.2422, 8310.6865, 0.35910681];
};

_vehicle_362 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCorner", [4485.3813, 8310.7861, -0.027825026], [], 0, "CAN_COLLIDE"];
  _vehicle_362 = _this;
  _this setDir 42.768097;
  _this setPos [4485.3813, 8310.7861, -0.027825026];
};

_vehicle_363 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCorner", [4483.3511, 8308.5967, -0.0046604462], [], 0, "CAN_COLLIDE"];
  _vehicle_363 = _this;
  _this setDir -226.93788;
  _this setPos [4483.3511, 8308.5967, -0.0046604462];
};

_vehicle_364 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCorner", [4481.1563, 8310.6406, -0.023523672], [], 0, "CAN_COLLIDE"];
  _vehicle_364 = _this;
  _this setDir -137.36478;
  _this setPos [4481.1563, 8310.6406, -0.023523672];
};

_vehicle_365 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkCorner", [4483.1797, 8312.8281, -0.02889397], [], 0, "CAN_COLLIDE"];
  _vehicle_365 = _this;
  _this setDir -407.07074;
  _this setPos [4483.1797, 8312.8281, -0.02889397];
};

_vehicle_375 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HMMWV_wrecked", [4456.3535, 8286.1621, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_375 = _this;
  _this setPos [4456.3535, 8286.1621, -3.0517578e-005];
};

_vehicle_376 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [4458.3228, 8287.082, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_376 = _this;
  _this setDir -32.348972;
  _this setPos [4458.3228, 8287.082, -3.0517578e-005];
};

_vehicle_377 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [4454.1021, 8286.6045, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_377 = _this;
  _this setPos [4454.1021, 8286.6045, 3.0517578e-005];
};

_vehicle_378 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [4481.7876, 8260.6807, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_378 = _this;
  _this setPos [4481.7876, 8260.6807, 0];
};

_vehicle_379 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [4494.5947, 8269.123, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_379 = _this;
  _this setPos [4494.5947, 8269.123, -9.1552734e-005];
};

_vehicle_380 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [4445.688, 8296.1689, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_380 = _this;
  _this setDir -162.89015;
  _this setPos [4445.688, 8296.1689, -6.1035156e-005];
};

_vehicle_381 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [4518.7588, 8293.3213, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_381 = _this;
  _this setPos [4518.7588, 8293.3213, 0];
};

_vehicle_382 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [4509.6606, 8293.6094, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_382 = _this;
  _this setPos [4509.6606, 8293.6094, -6.1035156e-005];
};

_vehicle_385 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [4482.064, 8224.5156], [], 0, "CAN_COLLIDE"];
  _vehicle_385 = _this;
  _this setDir 103.47732;
  _this setPos [4482.064, 8224.5156];
};

_vehicle_386 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [4486.4102, 8219.3271], [], 0, "CAN_COLLIDE"];
  _vehicle_386 = _this;
  _this setDir -23.370548;
  _this setPos [4486.4102, 8219.3271];
};

_vehicle_387 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [4481.0781, 8214.8809, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_387 = _this;
  _this setDir 188.33063;
  _this setPos [4481.0781, 8214.8809, -6.1035156e-005];
};

_vehicle_388 = objNull;
if (true) then
{
  _this = createVehicle ["hiluxWreck", [4463.3252, 8243.0566, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_388 = _this;
  _this setDir -473.21634;
  _this setPos [4463.3252, 8243.0566, 3.0517578e-005];
};

_vehicle_389 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_salix2s", [4475.2212, 8237.6807, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_389 = _this;
  _this setPos [4475.2212, 8237.6807, -6.1035156e-005];
};

_vehicle_390 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [4480.1514, 8239.085, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_390 = _this;
  _this setPos [4480.1514, 8239.085, 3.0517578e-005];
};

_vehicle_393 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusS2f", [4446.2432, 8250.8125, 0.4924978], [], 0, "CAN_COLLIDE"];
  _vehicle_393 = _this;
  _this setPos [4446.2432, 8250.8125, 0.4924978];
};

_vehicle_394 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [4476.7188, 8237.5547, -0.1353792], [], 0, "CAN_COLLIDE"];
  _vehicle_394 = _this;
  _this setDir -64.35302;
  _this setPos [4476.7188, 8237.5547, -0.1353792];
};

_vehicle_395 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [4479.9805, 8241.0645, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_395 = _this;
  _this setPos [4479.9805, 8241.0645, -3.0517578e-005];
};

_vehicle_398 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [4468.7432, 8228.2207, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_398 = _this;
  _this setDir -105.10426;
  _this setPos [4468.7432, 8228.2207, -3.0517578e-005];
};

_vehicle_401 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [4487.2539, 8243.0518, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_401 = _this;
  _this setDir 174.34004;
  _this setPos [4487.2539, 8243.0518, 3.0517578e-005];
};

_vehicle_403 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [4471.708, 8232.9746, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_403 = _this;
  _this setDir 307.48694;
  _this setPos [4471.708, 8232.9746, -3.0517578e-005];
};

_vehicle_408 = objNull;
if (true) then
{
  _this = createVehicle ["Mi8Wreck", [4485.4673, 8214.6191], [], 0, "CAN_COLLIDE"];
  _vehicle_408 = _this;
  _this setDir -387.42178;
  _this setPos [4485.4673, 8214.6191];
};

_vehicle_410 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [4515.7334, 8245.3721], [], 0, "CAN_COLLIDE"];
  _vehicle_410 = _this;
  _this setDir 9.5157194;
  _this setPos [4515.7334, 8245.3721];
};

_vehicle_416 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [4525.0903, 8244.1152, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_416 = _this;
  _this setDir -172.84175;
  _this setPos [4525.0903, 8244.1152, -3.0517578e-005];
};

_vehicle_419 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [4533.9092, 8242.0215, 0.15141514], [], 0, "CAN_COLLIDE"];
  _vehicle_419 = _this;
  _this setDir -170.12057;
  _this setPos [4533.9092, 8242.0215, 0.15141514];
};

_vehicle_422 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [4544.4521, 8230.1406], [], 0, "CAN_COLLIDE"];
  _vehicle_422 = _this;
  _this setDir -172.49936;
  _this setPos [4544.4521, 8230.1406];
};

_vehicle_423 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [4529.1655, 8299.084, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_423 = _this;
  _this setPos [4529.1655, 8299.084, -3.0517578e-005];
};

_vehicle_424 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4506.041, 8319.4326, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_424 = _this;
  _this setPos [4506.041, 8319.4326, 0];
};

_vehicle_426 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4483.813, 8339.2949, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_426 = _this;
  _this setPos [4483.813, 8339.2949, -3.0517578e-005];
};

_vehicle_428 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4446.7104, 8305.3838], [], 0, "CAN_COLLIDE"];
  _vehicle_428 = _this;
  _this setPos [4446.7104, 8305.3838];
};

_vehicle_430 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4445.5132, 8303.9326, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_430 = _this;
  _this setPos [4445.5132, 8303.9326, -3.0517578e-005];
};

_vehicle_433 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [4445.6558, 8305.0605, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_433 = _this;
  _this setPos [4445.6558, 8305.0605, 6.1035156e-005];
};

_vehicle_435 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [4454.4502, 8256.1465, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_435 = _this;
  _this setDir -62.601009;
  _this setPos [4454.4502, 8256.1465, 6.1035156e-005];
};

_vehicle_437 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [4483.8223, 8241.2373], [], 0, "CAN_COLLIDE"];
  _vehicle_437 = _this;
  _this setPos [4483.8223, 8241.2373];
};

_vehicle_440 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [4488.1665, 8336.3467, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_440 = _this;
  _this setDir 132.73798;
  _this setPos [4488.1665, 8336.3467, -3.0517578e-005];
};

_vehicle_443 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4533.7588, 8280.2676, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_443 = _this;
  _this setPos [4533.7588, 8280.2676, 3.0517578e-005];
};

_vehicle_445 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4537.0693, 8283.8086, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_445 = _this;
  _this setPos [4537.0693, 8283.8086, -3.0517578e-005];
};

_vehicle_447 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4539.9053, 8289.2383, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_447 = _this;
  _this setPos [4539.9053, 8289.2383, 3.0517578e-005];
};

_vehicle_449 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4539.7124, 8288.5918, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_449 = _this;
  _this setPos [4539.7124, 8288.5918, 0];
};

_vehicle_451 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4538.9121, 8289.627, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_451 = _this;
  _this setPos [4538.9121, 8289.627, 3.0517578e-005];
};

_vehicle_453 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4505.3306, 8264.4873, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_453 = _this;
  _this setPos [4505.3306, 8264.4873, 3.0517578e-005];
};

_vehicle_455 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4501.8428, 8265.2861, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_455 = _this;
  _this setPos [4501.8428, 8265.2861, -3.0517578e-005];
};

_vehicle_457 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4495.3887, 8260.167, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_457 = _this;
  _this setPos [4495.3887, 8260.167, 3.0517578e-005];
};

_vehicle_459 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2s", [4525.7373, 8306.0596, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_459 = _this;
  _this setDir 41.952873;
  _this setPos [4525.7373, 8306.0596, 3.0517578e-005];
};

_vehicle_461 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2w", [4510.3682, 8317.0732], [], 0, "CAN_COLLIDE"];
  _vehicle_461 = _this;
  _this setDir -365.49335;
  _this setPos [4510.3682, 8317.0732];
};

_vehicle_463 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [4527.3354, 8305.1084], [], 0, "CAN_COLLIDE"];
  _vehicle_463 = _this;
  _this setDir -122.79941;
  _this setPos [4527.3354, 8305.1084];
};

_vehicle_466 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4495.3677, 8273.9463, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_466 = _this;
  _this setPos [4495.3677, 8273.9463, 6.1035156e-005];
};

_vehicle_468 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4494.8472, 8273.2061, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_468 = _this;
  _this setPos [4494.8472, 8273.2061, 3.0517578e-005];
};

_vehicle_470 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4497.9238, 8271.623, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_470 = _this;
  _this setPos [4497.9238, 8271.623, 9.1552734e-005];
};

_vehicle_472 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4477.9214, 8272.0273, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_472 = _this;
  _this setPos [4477.9214, 8272.0273, 0];
};

_vehicle_474 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4477.2417, 8272.7012, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_474 = _this;
  _this setPos [4477.2417, 8272.7012, 6.1035156e-005];
};

_vehicle_476 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4485.0854, 8265.374, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_476 = _this;
  _this setPos [4485.0854, 8265.374, 0];
};

_vehicle_478 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4484.5479, 8265.8301, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_478 = _this;
  _this setPos [4484.5479, 8265.8301, 9.1552734e-005];
};

_vehicle_480 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4483.5308, 8266.7754, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_480 = _this;
  _this setPos [4483.5308, 8266.7754, 0.00018310547];
};

_vehicle_482 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4483.6069, 8266.0645, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_482 = _this;
  _this setPos [4483.6069, 8266.0645, 0];
};

_vehicle_484 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4477.9795, 8271.0879, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_484 = _this;
  _this setPos [4477.9795, 8271.0879, 0.00015258789];
};

_vehicle_486 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4503.7676, 8252.627, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_486 = _this;
  _this setPos [4503.7676, 8252.627, 9.1552734e-005];
};

_vehicle_488 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4502.7129, 8252.3984, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_488 = _this;
  _this setPos [4502.7129, 8252.3984, 9.1552734e-005];
};

_vehicle_490 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4503.3618, 8251.8457, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_490 = _this;
  _this setPos [4503.3618, 8251.8457, 3.0517578e-005];
};

_vehicle_492 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4513.8091, 8312.1807, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_492 = _this;
  _this setPos [4513.8091, 8312.1807, 3.0517578e-005];
};

_vehicle_494 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4524.9063, 8306.9951, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_494 = _this;
  _this setPos [4524.9063, 8306.9951, 6.1035156e-005];
};

_vehicle_496 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4481.7837, 8250.6455], [], 0, "CAN_COLLIDE"];
  _vehicle_496 = _this;
  _this setPos [4481.7837, 8250.6455];
};

_vehicle_498 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4478.479, 8250.166, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_498 = _this;
  _this setPos [4478.479, 8250.166, 3.0517578e-005];
};

_vehicle_500 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4477.7822, 8251.085, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_500 = _this;
  _this setPos [4477.7822, 8251.085, -3.0517578e-005];
};

_vehicle_503 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [4483.7344, 8251.4971], [], 0, "CAN_COLLIDE"];
  _vehicle_503 = _this;
  _this setDir 126.00345;
  _this setPos [4483.7344, 8251.4971];
};

_vehicle_505 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [4521.5601, 8243.2715, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_505 = _this;
  _this setPos [4521.5601, 8243.2715, -6.1035156e-005];
};

_vehicle_508 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [4515.7593, 8260.1934, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_508 = _this;
  _this setDir -255.02316;
  _this setPos [4515.7593, 8260.1934, 3.0517578e-005];
};

_vehicle_511 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4460.7529, 8259.5176, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_511 = _this;
  _this setPos [4460.7529, 8259.5176, 3.0517578e-005];
};

_vehicle_513 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4459.8691, 8258.627, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_513 = _this;
  _this setDir -49.65242;
  _this setPos [4459.8691, 8258.627, 3.0517578e-005];
};

_vehicle_515 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_concrete_block", [4487.9707, 8283.6963, -1.6928326], [], 0, "CAN_COLLIDE"];
  _vehicle_515 = _this;
  _this setDir 42.734756;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4487.9707, 8283.6963, -1.6928326];
};

_vehicle_521 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [4485.3364, 8255.4229, 0.004883647], [], 0, "CAN_COLLIDE"];
  _vehicle_521 = _this;
  _this setDir -77.943367;
  _this setPos [4485.3364, 8255.4229, 0.004883647];
};

_vehicle_524 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [4491.4771, 8250.7109, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_524 = _this;
  _this setDir -195.64108;
  _this setPos [4491.4771, 8250.7109, -3.0517578e-005];
};

_vehicle_526 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_concrete_block", [4493.5605, 8290.1895, -1.650665], [], 0, "CAN_COLLIDE"];
  _vehicle_526 = _this;
  _this setDir 42.734756;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4493.5605, 8290.1895, -1.650665];
};

_vehicle_529 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_DD_pletivo", [4494.8931, 8293.5244, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_529 = _this;
  _this setDir 42.379883;
  _this setPos [4494.8931, 8293.5244, 3.0517578e-005];
};

_vehicle_530 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder_half", [4493.4019, 8289.9111, 0.17297941], [], 0, "CAN_COLLIDE"];
  _vehicle_530 = _this;
  _this setDir -137.1438;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4493.4019, 8289.9111, 0.17297941];
};

_vehicle_532 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder_half", [4495.9341, 8275.667, 5.7321], [], 0, "CAN_COLLIDE"];
  _vehicle_532 = _this;
  _this setDir -227.45328;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4495.9341, 8275.667, 5.7321];
};

};
