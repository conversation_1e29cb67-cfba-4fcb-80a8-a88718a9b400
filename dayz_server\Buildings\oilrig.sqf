//<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s Oil Rig
 
if (isServer) then {
 
_vehicle_0 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_1", [7801.5234, 2388.9617, 7.9641795], [], 0, "CAN_COLLIDE"];
  _vehicle_0 = _this;
  _this setDir 175.11307;
  _this setPos [7801.5234, 2388.9617, 7.9641795];
};
 
_vehicle_1 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7822.4956, 2394.791, 8.3583632], [], 0, "CAN_COLLIDE"];
  _vehicle_1 = _this;
  _this setDir 174.43631;
  _this setPos [7822.4956, 2394.791, 8.3583632];
};
 
_vehicle_2 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_M_fuel", [7800.543, 2396.625, 8.1579914], [], 0, "CAN_COLLIDE"];
  _vehicle_2 = _this;
  _this setDir -5.0425749;
  _this setPos [7800.543, 2396.625, 8.1579914];
};
 
_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7780.4395, 2391.0435, 8.4153786], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir 354.74176;
  _this setPos [7780.4395, 2391.0435, 8.4153786];
};
 
_vehicle_9 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_CraneCon", [7823.5542, 2395.1196, 14.80173], [], 0, "CAN_COLLIDE"];
  _vehicle_9 = _this;
  _this setDir -95.821686;
  _this setPos [7823.5542, 2395.1196, 14.80173];
};
 
_vehicle_11 = objNull;
if (true) then
{
  _this = createVehicle ["Land_water_tank", [7817.7549, 2389.2681, 13.96977], [], 0, "CAN_COLLIDE"];
  _vehicle_11 = _this;
  _this setDir 84.725372;
  _this setPos [7817.7549, 2389.2681, 13.96977];
};
 
_vehicle_16 = objNull;
if (true) then
{
  _this = createVehicle ["Land_IndPipe2_bigL_L", [7805.3115, 2352.9216, 3.1063948], [], 0, "CAN_COLLIDE"];
  _vehicle_16 = _this;
  _this setDir 87.565651;
  _this setPos [7805.3115, 2352.9216, 3.1063948];
};
 
_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fuel_tank_stairs", [7795.4263, 2386.7737, 13.997185], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir -3.2956939;
  _this setPos [7795.4263, 2386.7737, 13.997185];
};
 
_vehicle_24 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fuel_tank_stairs", [7796.8237, 2396.2937, -0.18022373], [], 0, "CAN_COLLIDE"];
  _vehicle_24 = _this;
  _this setDir -5.2259517;
  _this setPos [7796.8237, 2396.2937, -0.18022373];
};
 
_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [7800.1113, 2356.4216, 8.6138458], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setDir -11.640123;
  _this setPos [7800.1113, 2356.4216, 8.6138458];
};
 
_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Bo", [7796.5894, 2354.7656, 8.5854998], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir -9.3914118;
  _this setPos [7796.5894, 2354.7656, 8.5854998];
};
 
_vehicle_30 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Com_tower_ep1", [7791.3818, 2354.3057, 8.4094677], [], 0, "CAN_COLLIDE"];
  _vehicle_30 = _this;
  _this setDir -5.7195969;
  _this setPos [7791.3818, 2354.3057, 8.4094677];
};
 
_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Oil_Tower_EP1", [7852.3169, 2432.2673, 10.025599], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir -1.4970045;
  _this setPos [7852.3169, 2432.2673, 10.025599];
};
 
_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7791.2705, 2353.8428, 2.7159202], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir 354.74176;
  _this setPos [7791.2705, 2353.8428, 2.7159202];
};
 
_vehicle_38 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7797.124, 2354.7974, 2.7662601], [], 0, "CAN_COLLIDE"];
  _vehicle_38 = _this;
  _this setDir 533.39691;
  _this setPos [7797.124, 2354.7974, 2.7662601];
};
 
_vehicle_40 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7804.2998, 2416.0032, 10.932385], [], 0, "CAN_COLLIDE"];
  _vehicle_40 = _this;
  _this setDir 264.36011;
  _this setPos [7804.2998, 2416.0032, 10.932385];
};
 
_vehicle_43 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7803.5649, 2423.2405, 11.217171], [], 0, "CAN_COLLIDE"];
  _vehicle_43 = _this;
  _this setDir 444.29843;
  _this setPos [7803.5649, 2423.2405, 11.217171];
};
 
_vehicle_45 = objNull;
if (true) then
{
  _this = createVehicle ["Land_radar", [7810.439, 2360.043, 4.3873405], [], 0, "CAN_COLLIDE"];
  _vehicle_45 = _this;
  _this setDir -89.75914;
  _this setPos [7810.439, 2360.043, 4.3873405];
};
 
_vehicle_48 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder", [7828.958, 2389.553, 6.7018452], [], 0, "CAN_COLLIDE"];
  _vehicle_48 = _this;
  _this setDir -98.315781;
  _this setPos [7828.958, 2389.553, 6.7018452];
};
 
_vehicle_52 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder", [7828.7583, 2392.0801, 6.5795908], [], 0, "CAN_COLLIDE"];
  _vehicle_52 = _this;
  _this setDir -98.315781;
  _this setPos [7828.7583, 2392.0801, 6.5795908];
};
 
_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7836.7651, 2379.333, 2.028904], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setDir 265.40286;
  _this setPos [7836.7651, 2379.333, 2.028904];
};
 
_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7835.2402, 2426.248, 2.4315524], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setDir 445.29108;
  _this setPos [7835.2402, 2426.248, 2.4315524];
};
 
_vehicle_60 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_M_fuel", [7832.5791, 2360.5999, -1.2718083], [], 0, "CAN_COLLIDE"];
  _vehicle_60 = _this;
  _this setDir -4.1689982;
  _this setPos [7832.5791, 2360.5999, -1.2718083];
};
 
_vehicle_63 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7808.9468, 2354.3535, -0.90665883], [], 0, "CAN_COLLIDE"];
  _vehicle_63 = _this;
  _this setDir 354.53882;
  _this setPos [7808.9468, 2354.3535, -0.90665883];
};
 
_vehicle_69 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_1", [7833.4683, 2352.3618, -1.3882734], [], 0, "CAN_COLLIDE"];
  _vehicle_69 = _this;
  _this setDir 175.80396;
  _this setPos [7833.4683, 2352.3618, -1.3882734];
};
 
_vehicle_71 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_1", [7840.1504, 2402.3174, 2.1039324], [], 0, "CAN_COLLIDE"];
  _vehicle_71 = _this;
  _this setDir 86.257843;
  _this setPos [7840.1504, 2402.3174, 2.1039324];
};
 
_vehicle_73 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_1", [7832.0151, 2402.6389, 2.0010104], [], 0, "CAN_COLLIDE"];
  _vehicle_73 = _this;
  _this setDir 265.80988;
  _this setPos [7832.0151, 2402.6389, 2.0010104];
};
 
_vehicle_112 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_1", [7856.7725, 2425.9541, 2.232837], [], 0, "CAN_COLLIDE"];
  _vehicle_112 = _this;
  _this setDir 447.58728;
  _this setPos [7856.7725, 2425.9541, 2.232837];
};
 
_vehicle_113 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_1", [7858.8164, 2386.395, 1.9561565], [], 0, "CAN_COLLIDE"];
  _vehicle_113 = _this;
  _this setDir 447.21808;
  _this setPos [7858.8164, 2386.395, 1.9561565];
};
 
_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_1", [7851.1528, 2386.1492, 1.8908451], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setDir 627.77246;
  _this setPos [7851.1528, 2386.1492, 1.8908451];
};
 
_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_1", [7848.9731, 2425.8621, 2.3568144], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setDir 627.4032;
  _this setPos [7848.9731, 2425.8621, 2.3568144];
};
 
_vehicle_143 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7857.0742, 2359.395, -0.92470479], [], 0, "CAN_COLLIDE"];
  _vehicle_143 = _this;
  _this setDir 537.12262;
  _this setPos [7857.0742, 2359.395, -0.92470479];
};
 
_vehicle_146 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder_half", [7812.6851, 2346.9231, 1.3459332], [], 0, "CAN_COLLIDE"];
  _vehicle_146 = _this;
  _this setDir -3.3809412;
  _this setPos [7812.6851, 2346.9231, 1.3459332];
};
 
_vehicle_148 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder_half", [7862.8154, 2356.6135, 1.516819], [], 0, "CAN_COLLIDE"];
  _vehicle_148 = _this;
  _this setDir -94.295189;
  _this setPos [7862.8154, 2356.6135, 1.516819];
};
 
_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder_half", [7851.2749, 2365.5845, 3.7661545], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setPos [7851.2749, 2365.5845, 3.7661545];
};
 
_vehicle_152 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder_half", [7857.7759, 2365.9915, 3.8425581], [], 0, "CAN_COLLIDE"];
  _vehicle_152 = _this;
  _this setPos [7857.7759, 2365.9915, 3.8425581];
};
 
_vehicle_155 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7851.8242, 2449.7292, 2.1973917], [], 0, "CAN_COLLIDE"];
  _vehicle_155 = _this;
  _this setDir 447.73203;
  _this setPos [7851.8242, 2449.7292, 2.1973917];
};
 
_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Oil_Tower_EP1", [7853.2817, 2411.1953, 0.17251539], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setDir -4.2339134;
  _this setPos [7853.2817, 2411.1953, 0.17251539];
};
 
_vehicle_160 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Oil_Tower_EP1", [7854.7168, 2387.7351, 0.23841423], [], 0, "CAN_COLLIDE"];
  _vehicle_160 = _this;
  _this setDir -5.5874691;
  _this setPos [7854.7168, 2387.7351, 0.23841423];
};
 
_vehicle_163 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7837.7275, 2361.8398, 4.5493474], [], 0, "CAN_COLLIDE"];
  _vehicle_163 = _this;
  _this setPos [7837.7275, 2361.8398, 4.5493474];
};
 
_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7839.978, 2376.7339, 5.1823258], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setPos [7839.978, 2376.7339, 5.1823258];
};
 
_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7794.5898, 2349.3259, 9.4193487], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setPos [7794.5898, 2349.3259, 9.4193487];
};
 
_vehicle_169 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7810.8926, 2354.1838, 4.7316952], [], 0, "CAN_COLLIDE"];
  _vehicle_169 = _this;
  _this setPos [7810.8926, 2354.1838, 4.7316952];
};
 
_vehicle_173 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7854.5459, 2367.8699, 7.3304634], [], 0, "CAN_COLLIDE"];
  _vehicle_173 = _this;
  _this setPos [7854.5459, 2367.8699, 7.3304634];
};
 
_vehicle_175 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7847.1489, 2402.478, -4.042356], [], 0, "CAN_COLLIDE"];
  _vehicle_175 = _this;
  _this setPos [7847.1489, 2402.478, -4.042356];
};
 
_vehicle_177 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7847.895, 2424.355, 8.2847652], [], 0, "CAN_COLLIDE"];
  _vehicle_177 = _this;
  _this setPos [7847.895, 2424.355, 8.2847652];
};
 
_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7856.7983, 2453.8286, -0.081102505], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setPos [7856.7983, 2453.8286, -0.081102505];
};
 
_vehicle_181 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7847.3979, 2452.9824, -0.074418224], [], 0, "CAN_COLLIDE"];
  _vehicle_181 = _this;
  _this setPos [7847.3979, 2452.9824, -0.074418224];
};
 
_vehicle_183 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7829.3179, 2428.8389, 8.5324545], [], 0, "CAN_COLLIDE"];
  _vehicle_183 = _this;
  _this setPos [7829.3179, 2428.8389, 8.5324545];
};
 
_vehicle_185 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7830.1665, 2412.9709, 6.2938504], [], 0, "CAN_COLLIDE"];
  _vehicle_185 = _this;
  _this setPos [7830.1665, 2412.9709, 6.2938504];
};
 
_vehicle_187 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7821.0044, 2395.3542, 4.9414997], [], 0, "CAN_COLLIDE"];
  _vehicle_187 = _this;
  _this setPos [7821.0044, 2395.3542, 4.9414997];
};
 
_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7832.5713, 2383.3831, 7.6539922], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setPos [7832.5713, 2383.3831, 7.6539922];
};
 
_vehicle_191 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7807.2627, 2426.9587, 0.17250684], [], 0, "CAN_COLLIDE"];
  _vehicle_191 = _this;
  _this setPos [7807.2627, 2426.9587, 0.17250684];
};
 
_vehicle_193 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7798.5825, 2398.3647, -114.48226], [], 0, "CAN_COLLIDE"];
  _vehicle_193 = _this;
  _this setPos [7798.5825, 2398.3647, -114.48226];
};
 
_vehicle_195 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7798.2754, 2426.571, 0.20920026], [], 0, "CAN_COLLIDE"];
  _vehicle_195 = _this;
  _this setPos [7798.2754, 2426.571, 0.20920026];
};

_vehicle_199 = objNull;
if (true) then
{
  _this = createVehicle ["MedBox0", [7796.4106, 2352.5491, 8.5169039], [], 0, "CAN_COLLIDE"];
  _vehicle_199 = _this;
  _this setPos [7796.4106, 2352.5491, 8.5169039];
};
 
_vehicle_200 = objNull;
if (true) then
{
  _this = createVehicle ["USOrdnanceBox", [7842.8921, 2359.4263, 5.1841178], [], 0, "CAN_COLLIDE"];
  _vehicle_200 = _this;
  _this setDir 62.275021;
  _this setPos [7842.8921, 2359.4263, 5.1841178];
  //Clear Cargo	
  clearweaponcargoGlobal _this;
  clearmagazinecargoGlobal _this;
  //Add Cargo
  /*
  _this addWeaponCargoGlobal ["ItemEtool",2];
  _this addWeaponCargoGlobal ["ItemToolbox",2];
  _this addWeaponCargoGlobal ["ItemSledge",1];
  _this addWeaponCargoGlobal ["NVGoggles",2];
  _this addWeaponCargoGlobal ["ItemCrowbar",2];
  _this addWeaponCargoGlobal ["ChainSawR",1];
 
  _this addmagazineCargoGlobal ["Skin_FR_OHara_DZ",2];   
  _this addmagazineCargoGlobal ["bulk_ItemTankTrap",2];
  _this addmagazineCargoGlobal ["bulk_ItemWire",2];
  _this addmagazineCargoGlobal ["bulk_ItemSandbag",2];
  _this addmagazineCargoGlobal ["bulk_PartGeneric",2];
  _this addmagazineCargoGlobal ["PartPlankPack",10];
  _this addmagazineCargoGlobal ["PartPlywoodPack",10];
  _this addmagazineCargoGlobal ["CinderBlocks",91];
  _this addmagazineCargoGlobal ["MortarBucket",31];
  _this addmagazineCargoGlobal ["ItemFuelBarrel",10];
  _this addmagazineCargoGlobal ["ItemFireBarrel_kit",5];
  _this addmagazineCargoGlobal ["ItemSodaMdew",10];
  _this addmagazineCargoGlobal ["ItemKiloHemp",1];
  _this addmagazineCargoGlobal ["ItemGunRackKit",1];
  _this addmagazineCargoGlobal ["FoodMRE",15];
  _this addmagazineCargoGlobal ["metal_floor_kit",40];
  _this addmagazineCargoGlobal ["full_cinder_wall_kit",91];
  _this addmagazineCargoGlobal ["cinder_garage_kit",20];
  _this addmagazineCargoGlobal ["cinder_door_kit",20];
  _this addmagazineCargoGlobal ["metal_panel_kit",50];
  _this addmagazineCargoGlobal ["ItemComboLock",15];
  _this addmagazineCargoGlobal ["ItemWoodStairs",15];
  _this addmagazineCargoGlobal ["ItemWoodStairsSupport",15];
  _this addmagazineCargoGlobal ["ItemWoodLadder",15];
  */
  _this setPos [7842.8921, 2359.4263, 5.1841178];
};
 
_vehicle_203 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Antenna", [7815.9043, 2357.9155, 5.3073573], [], 0, "CAN_COLLIDE"];
  _vehicle_203 = _this;
  _this setPos [7815.9043, 2357.9155, 5.3073573];
};
 
_vehicle_206 = objNull;
if (true) then
{
  _this = createVehicle ["Barrack2", [7824.2173, 2354.0388, 4.2615795], [], 0, "CAN_COLLIDE"];
  _vehicle_206 = _this;
  _this setDir 176.12383;
  _this setPos [7824.2173, 2354.0388, 4.2615795];
};
 
_vehicle_207 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7831.0913, 2361.8838, 4.4468656], [], 0, "CAN_COLLIDE"];
  _vehicle_207 = _this;
  _this setPos [7831.0913, 2361.8838, 4.4468656];
};
 
_vehicle_209 = objNull;
if (true) then
{
  _this = createVehicle ["Barrack2", [7832.1641, 2354.6147, 4.2072024], [], 0, "CAN_COLLIDE"];
  _vehicle_209 = _this;
  _this setDir 177.13771;
  _this setPos [7832.1641, 2354.6147, 4.2072024];
};
 
_vehicle_210 = objNull;
if (true) then
{
  _this = createVehicle ["MASH", [7840.0176, 2352.0908, 4.7326379], [], 0, "CAN_COLLIDE"];
  _vehicle_210 = _this;
  _this setDir -1.7013378;
  _this setPos [7840.0176, 2352.0908, 4.7326379];
};
 
_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["MASH", [7846.5781, 2352.5408, 4.8691878], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setDir -1.7622085;
  _this setPos [7846.5781, 2352.5408, 4.8691878];
};
 
_vehicle_212 = objNull;
if (true) then
{
  _this = createVehicle ["US_Backpack_EP1", [7794.7505, 2350.3301, 8.437005], [], 0, "CAN_COLLIDE"];
  _vehicle_212 = _this;
  _this setPos [7794.7505, 2350.3301, 8.437005];
};
 
_vehicle_213 = objNull;
if (true) then
{
  _this = createVehicle ["DZ_Backpack_EP1", [7792.5132, 2351.2759, 8.4607468], [], 0, "CAN_COLLIDE"];
  _vehicle_213 = _this;
  _this setPos [7792.5132, 2351.2759, 8.4607468];
};
 
_vehicle_231 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7819.876, 2439.0525, 2.8603978], [], 0, "CAN_COLLIDE"];
  _vehicle_231 = _this;
  _this setDir 264.36349;
  _this setPos [7819.876, 2439.0525, 2.8603978];
};
 
_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7819.0483, 2446.832, 3.1018498], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setDir 444.30182;
  _this setPos [7819.0483, 2446.832, 3.1018498];
};
 
_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7823.8354, 2451.0029, 9.3205204], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setDir -2.6067667;
  _this setPos [7823.8354, 2451.0029, 9.3205204];
};
 
_vehicle_234 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7814.1216, 2449.9727, 0.22547279], [], 0, "CAN_COLLIDE"];
  _vehicle_234 = _this;
  _this setDir -2.6067667;
  _this setPos [7814.1216, 2449.9727, 0.22547279];
};
 
_vehicle_239 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7793.208, 2436.7112, 2.7214317], [], 0, "CAN_COLLIDE"];
  _vehicle_239 = _this;
  _this setDir 264.36011;
  _this setPos [7793.208, 2436.7112, 2.7214317];
};
 
_vehicle_240 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7792.5171, 2443.6982, 2.9462829], [], 0, "CAN_COLLIDE"];
  _vehicle_240 = _this;
  _this setDir 444.29843;
  _this setPos [7792.5171, 2443.6982, 2.9462829];
};
 
_vehicle_241 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7797.3208, 2448.2222, 0.34298602], [], 0, "CAN_COLLIDE"];
  _vehicle_241 = _this;
  _this setPos [7797.3208, 2448.2222, 0.34298602];
};
 
_vehicle_242 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [7787.9019, 2446.6792, 0.32146454], [], 0, "CAN_COLLIDE"];
  _vehicle_242 = _this;
  _this setPos [7787.9019, 2446.6792, 0.32146454];
};
 
_vehicle_259 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [7820.8472, 2436.2661, 8.1975203], [], 0, "CAN_COLLIDE"];
  _vehicle_259 = _this;
  _this setPos [7820.8472, 2436.2661, 8.1975203];
};
 
_vehicle_261 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [7856.7598, 2442.874, 7.7515087], [], 0, "CAN_COLLIDE"];
  _vehicle_261 = _this;
  _this setDir -4.6847572;
  _this setPos [7856.7598, 2442.874, 7.7515087];
};
 
_vehicle_263 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [7789.3042, 2434.7292, 8.0880661], [], 0, "CAN_COLLIDE"];
  _vehicle_263 = _this;
  _this setDir -3.3023975;
  _this setPos [7789.3042, 2434.7292, 8.0880661];
};
 
_vehicle_265 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [7816.9888, 2350.0256, 3.8390603], [], 0, "CAN_COLLIDE"];
  _vehicle_265 = _this;
  _this setDir -5.5682111;
  _this setPos [7816.9888, 2350.0256, 3.8390603];
};
 
_vehicle_267 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [7809.4385, 2397.7185, 13.480723], [], 0, "CAN_COLLIDE"];
  _vehicle_267 = _this;
  _this setDir -5.7270179;
  _this setPos [7809.4385, 2397.7185, 13.480723];
};
 
_vehicle_269 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [7859.6606, 2369.2371, 6.844543], [], 0, "CAN_COLLIDE"];
  _vehicle_269 = _this;
  _this setPos [7859.6606, 2369.2371, 6.844543];
};
 
_vehicle_272 = objNull;
if (true) then
{
  _this = createVehicle ["PowerGenerator", [7820.2085, 2350.7781, 4.1357894], [], 0, "CAN_COLLIDE"];
  _vehicle_272 = _this;
  _this setPos [7820.2085, 2350.7781, 4.1357894];
};
 
_vehicle_273 = objNull;
if (true) then
{
  _this = createVehicle ["PowerGenerator", [7816.8193, 2435.8774, 8.489872], [], 0, "CAN_COLLIDE"];
  _vehicle_273 = _this;
  _this setDir -9.2216377;
  _this setPos [7816.8193, 2435.8774, 8.489872];
};
 
_vehicle_274 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo2E", [7794.0859, 2437.8269, 8.441864], [], 0, "CAN_COLLIDE"];
  _vehicle_274 = _this;
  _this setDir -4.3565807;
  _this setPos [7794.0859, 2437.8269, 8.441864];
};
 
_vehicle_275 = objNull;
if (true) then
{
  _this = createVehicle ["PowGen_Big_EP1", [7781.4907, 2390.8794, 13.894179], [], 0, "CAN_COLLIDE"];
  _vehicle_275 = _this;
  _this setDir -96.126114;
  _this setPos [7781.4907, 2390.8794, 13.894179];
};
 
_vehicle_280 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_CraneCon", [7857.1709, 2357.8555, -0.06032636], [], 0, "CAN_COLLIDE"];
  _vehicle_280 = _this;
  _this setDir 174.12891;
  _this setPos [7857.1709, 2357.8555, -0.06032636];
};
 
_vehicle_281 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1Bo_military", [7801.1421, 2388.3296, 13.850168], [], 0, "CAN_COLLIDE"];
  _vehicle_281 = _this;
  _this setDir 85.840157;
  _this setPos [7801.1421, 2388.3296, 13.850168];
};
 
_vehicle_282 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Eo_EP1", [7811.2856, 2389.3806, 13.633366], [], 0, "CAN_COLLIDE"];
  _vehicle_282 = _this;
  _this setDir -102.1998;
  _this setPos [7811.2856, 2389.3806, 13.633366];
};
 
_vehicle_286 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder_half", [7862.7949, 2354.707, 0.64519417], [], 0, "CAN_COLLIDE"];
  _vehicle_286 = _this;
  _this setDir -94.295189;
  _this setPos [7862.7949, 2354.707, 0.64519417];
};
 
_vehicle_289 = objNull;
if (true) then
{
  _this = createVehicle ["land_nav_pier_m_end", [7869.6162, 2352.2439, -6.5538111], [], 0, "CAN_COLLIDE"];
  _vehicle_289 = _this;
  _this setDir 451.08121;
  _this setPos [7869.6162, 2352.2439, -6.5538111];
};
};