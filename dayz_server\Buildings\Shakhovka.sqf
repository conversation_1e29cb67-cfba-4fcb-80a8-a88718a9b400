//This spawns in a military check point going into town at the northern end of Shakhovka.
//Created by Nox 2017-02-01. Contact: DayZ Europa, Dayz mod discord or by email: <EMAIL>
//Copyright by the DayZ Mod dev team. 

_vehicle_1 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier1" createVehicle [9564.4189, 6669.1694, -0.14846949];
 

  _vehicle_1 = _this;
  _this setDir 10.147971;
  _this setPos [9564.4189, 6669.1694, -0.14846949];
};

_vehicle_2 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier3" createVehicle [9587.4854, 6635.1445, -0.050388746];
 

  _vehicle_2 = _this;
  _this setDir -0.39430982;
  _this setPos [9587.4854, 6635.1445, -0.050388746];
};

_vehicle_3 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9591.1563, 6635.1587, -0.0066405814];
 

  _vehicle_3 = _this;
  _this setPos [9591.1563, 6635.1587, -0.0066405814];
};

_vehicle_5 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9597.0078, 6635.248, -0.056754377];
 

  _vehicle_5 = _this;
  _this setDir -6.3392906;
  _this setPos [9597.0078, 6635.248, -0.056754377];
};

_vehicle_9 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9605.1309, 6639.5796, 4.5776367e-005];
 

  _vehicle_9 = _this;
  _this setDir 85.560028;
  _this setPos [9605.1309, 6639.5796, 4.5776367e-005];
};

_vehicle_11 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9603.9854, 6645.2671, 0.00024414063];
 

  _vehicle_11 = _this;
  _this setDir 77.910789;
  _this setPos [9603.9854, 6645.2671, 0.00024414063];
};

_vehicle_15 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9599.2832, 6656.8955, -0.11580816];
 

  _vehicle_15 = _this;
  _this setDir 60.731491;
  _this setPos [9599.2832, 6656.8955, -0.11580816];
};

_vehicle_17 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9597.96, 6662.6465];
 

  _vehicle_17 = _this;
  _this setDir 79.075653;
  _this setPos [9597.96, 6662.6465];
};

_vehicle_21 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9588.4473, 6668.6147, -0.1803387];
 

  _vehicle_21 = _this;
  _this setDir 17.034077;
  _this setPos [9588.4473, 6668.6147, -0.1803387];
};

_vehicle_23 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9582.5986, 6669.1958, -0.17976369];
 

  _vehicle_23 = _this;
  _this setDir 4.2656097;
  _this setPos [9582.5986, 6669.1958, -0.17976369];
};

_vehicle_25 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9576.7637, 6669.3252, -0.13527918];
 

  _vehicle_25 = _this;
  _this setPos [9576.7637, 6669.3252, -0.13527918];
};

_vehicle_27 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9571.4014, 6667.1328, -0.38446707];
 

  _vehicle_27 = _this;
  _this setDir -21.997446;
  _this setPos [9571.4014, 6667.1328, -0.38446707];
};

_vehicle_29 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9565.7803, 6668.667, -0.13188381];
 

  _vehicle_29 = _this;
  _this setDir 21.087645;
  _this setPos [9565.7803, 6668.667, -0.13188381];
};

_vehicle_33 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9581.6533, 6634.7861, 6.1035156e-005];
 

  _vehicle_33 = _this;
  _this setDir -6.1612172;
  _this setPos [9581.6533, 6634.7861, 6.1035156e-005];
};

_vehicle_35 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier5" createVehicle [9576.1631, 6633.0225, 1.5258789e-005];
 

  _vehicle_35 = _this;
  _this setDir -15.163078;
  _this setPos [9576.1631, 6633.0225, 1.5258789e-005];
};

_vehicle_135 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Hlidac_budka" createVehicle [9561.8809, 6664.4717, 0.00010681152];
 

  _vehicle_135 = _this;
  _this setDir 45.308399;
  _this setPos [9561.8809, 6664.4717, 0.00010681152];
};

_vehicle_140 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_CamoNet_EAST" createVehicle [9554.376, 6651.6055, 0.29174802];
 

  _vehicle_140 = _this;
  _this setDir -120.51927;
  _this setPos [9554.376, 6651.6055, 0.29174802];
};

_vehicle_141 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_CamoNet_EAST_var1" createVehicle [9582.8672, 6651.4824, 1.5258789e-005];
 

  _vehicle_141 = _this;
  _this setDir 253.05736;
  _this setPos [9582.8672, 6651.4824, 1.5258789e-005];
};

_vehicle_155 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9558.3535, 6657.1187, 9.1552734e-005];
 

  _vehicle_155 = _this;
  _this setDir 49.942844;
  _this setPos [9558.3535, 6657.1187, 9.1552734e-005];
};

_vehicle_156 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_corner" createVehicle [9563.3633, 6661.6514, -0.16050753];
 

  _vehicle_156 = _this;
  _this setDir -45.279251;
  _this setPos [9563.3633, 6661.6514, -0.16050753];
};

_vehicle_157 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9554.2617, 6661.1357, 4.5776367e-005];
 

  _vehicle_157 = _this;
  _this setDir 44.137775;
  _this setPos [9554.2617, 6661.1357, 4.5776367e-005];
};

_vehicle_238 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Concrete_Wall_EP1" createVehicle [9558.7773, 6667.0723, 4.5776367e-005];
 

  _vehicle_238 = _this;
  _this setDir -33.107681;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [9558.7773, 6667.0723, 4.5776367e-005];
};

_vehicle_240 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Concrete_Wall_EP1" createVehicle [9560.7432, 6668.6226, 9.1552734e-005];
 

  _vehicle_240 = _this;
  _this setDir -45.540707;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [9560.7432, 6668.6226, 9.1552734e-005];
};

_vehicle_243 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Concrete_Wall_EP1" createVehicle [9562.5859, 6669.4194, -0.025939517];
 

  _vehicle_243 = _this;
  _this setDir -2.655802;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [9562.5859, 6669.4194, -0.025939517];
};

_vehicle_246 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Concrete_Wall_EP1" createVehicle [9552.1152, 6661.8584, -3.0517578e-005];
 

  _vehicle_246 = _this;
  _this setDir -44.170742;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [9552.1152, 6661.8584, -3.0517578e-005];
};

_vehicle_249 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Concrete_Wall_EP1" createVehicle [9550.5186, 6659.9424, -1.5258789e-005];
 

  _vehicle_249 = _this;
  _this setDir -55.849533;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [9550.5186, 6659.9424, -1.5258789e-005];
};

_vehicle_252 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Concrete_Wall_EP1" createVehicle [9550.6924, 6658.0742, -6.1035156e-005];
 

  _vehicle_252 = _this;
  _this setDir -113.51531;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [9550.6924, 6658.0742, -6.1035156e-005];
};

_vehicle_255 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Concrete_Wall_EP1" createVehicle [9550.54, 6656.1211, 4.5776367e-005];
 

  _vehicle_255 = _this;
  _this setDir -53.455177;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [9550.54, 6656.1211, 4.5776367e-005];
};

_vehicle_259 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Concrete_Wall_EP1" createVehicle [9549.1328, 6654.1563, -0.26590762];
 

  _vehicle_259 = _this;
  _this setDir -53.127342;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [9549.1328, 6654.1563, -0.26590762];
};

_vehicle_265 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9560.2314, 6654.7266, 7.6293945e-005];
 

  _vehicle_265 = _this;
  _this setDir 54.37431;
  _this setPos [9560.2314, 6654.7266, 7.6293945e-005];
};

_vehicle_268 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9561.873, 6652.1802, -0.013850577];
 

  _vehicle_268 = _this;
  _this setDir 59.53756;
  _this setPos [9561.873, 6652.1802, -0.013850577];
};

_vehicle_270 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Toilet" createVehicle [9563.5449, 6638.3589, 0.00010681152];
 

  _vehicle_270 = _this;
  _this setDir 168.35199;
  _this setPos [9563.5449, 6638.3589, 0.00010681152];
};

_vehicle_279 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Notice_board" createVehicle [9557.5332, 6656.249, 1.5258789e-005];
 

  _vehicle_279 = _this;
  _this setDir -319.83801;
  _this setPos [9557.5332, 6656.249, 1.5258789e-005];
};

_vehicle_280 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Paleta1" createVehicle [9565.8955, 6661.0801, -4.5776367e-005];
 

  _vehicle_280 = _this;
  _this setPos [9565.8955, 6661.0801, -4.5776367e-005];
};

_vehicle_281 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Paleta2" createVehicle [9565.8076, 6661.8823, -4.5776367e-005];
 

  _vehicle_281 = _this;
  _this setPos [9565.8076, 6661.8823, -4.5776367e-005];
};

_vehicle_282 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Barrel_empty" createVehicle [9565.2832, 6661.8164, -0.0092395376];
 

  _vehicle_282 = _this;
  _this setPos [9565.2832, 6661.8164, -0.0092395376];
};

_vehicle_283 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "PowerGenerator" createVehicle [9600.1602, 6641.9648, -3.0517578e-005];
 

  _vehicle_283 = _this;
  _this setDir 171.97923;
  _this setPos [9600.1602, 6641.9648, -3.0517578e-005];
};

_vehicle_300 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "LADAWreck" createVehicle [9551.9131, 6667.7915, -1.5258789e-005];
 

  _vehicle_300 = _this;
  _this setDir 8.7121878;
  _this setPos [9551.9131, 6667.7915, -1.5258789e-005];
};

_vehicle_302 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "datsun01Wreck" createVehicle [9547.9668, 6671.7925, 1.5258789e-005];
 

  _vehicle_302 = _this;
  _this setDir -19.136129;
  _this setPos [9547.9668, 6671.7925, 1.5258789e-005];
};

_vehicle_303 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "datsun02Wreck" createVehicle [9531.4277, 6674.2891, -0.11088796];
 

  _vehicle_303 = _this;
  _this setDir -53.174622;
  _this setPos [9531.4277, 6674.2891, -0.11088796];
};

_vehicle_304 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SKODAWreck" createVehicle [9545.2852, 6670.5156, 1.5258789e-005];
 

  _vehicle_304 = _this;
  _this setDir -70.602058;
  _this setPos [9545.2852, 6670.5156, 1.5258789e-005];
};

_vehicle_305 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [9540.1377, 6673.8403, 1.5258789e-005];
 

  _vehicle_305 = _this;
  _this setDir -48.801605;
  _this setPos [9540.1377, 6673.8403, 1.5258789e-005];
};

_vehicle_310 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [9548.6455, 6667.8022, 0.00010681152];
 

  _vehicle_310 = _this;
  _this setDir -103.4128;
  _this setPos [9548.6455, 6667.8022, 0.00010681152];
};

_vehicle_314 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SKODAWreck" createVehicle [9539.6318, 6676.7148];
 

  _vehicle_314 = _this;
  _this setDir -59.982613;
  _this setPos [9539.6318, 6676.7148];
};

_vehicle_316 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SKODAWreck" createVehicle [9536.4746, 6674.5669, -1.5258789e-005];
 

  _vehicle_316 = _this;
  _this setDir -91.279297;
  _this setPos [9536.4746, 6674.5669, -1.5258789e-005];
};

_vehicle_319 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UralWreck" createVehicle [9543.9141, 6675.4058, -9.1552734e-005];
 

  _vehicle_319 = _this;
  _this setDir -25.459192;
  _this setPos [9543.9141, 6675.4058, -9.1552734e-005];
};

_vehicle_328 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Fort_Barricade" createVehicle [9554.0947, 6665.3696, 0.00022888184];
 

  _vehicle_328 = _this;
  _this setDir -40.827724;
  _this setPos [9554.0947, 6665.3696, 0.00022888184];
};

_vehicle_329 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier1" createVehicle [9594.3467, 6666.8633, -0.029752402];
 

  _vehicle_329 = _this;
  _this setPos [9594.3467, 6666.8633, -0.029752402];
};

_vehicle_331 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier1" createVehicle [9597.2109, 6664.0557, -0.00010681152];
 

  _vehicle_331 = _this;
  _this setDir 36.390491;
  _this setPos [9597.2109, 6664.0557, -0.00010681152];
};

_vehicle_333 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier1" createVehicle [9597.3438, 6667.6211, -0.10654662];
 

  _vehicle_333 = _this;
  _this setDir 2.5504274;
  _this setPos [9597.3438, 6667.6211, -0.10654662];
};

_vehicle_336 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier1" createVehicle [9603.1104, 6632.9893, 0.013236066];
 

  _vehicle_336 = _this;
  _this setDir 13.66839;
  _this setPos [9603.1104, 6632.9893, 0.013236066];
};

_vehicle_338 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fortified_nest_small" createVehicle [9602.4727, 6648.5898];
 

  _vehicle_338 = _this;
  _this setDir -105.53274;
  _this setPos [9602.4727, 6648.5898];
};

_vehicle_341 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [9559.0762, 6666.4556, 7.6293945e-005];
 

  _vehicle_341 = _this;
  _this setDir -49.514622;
  _this setPos [9559.0762, 6666.4556, 7.6293945e-005];
};

_vehicle_343 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [9550.0957, 6658.5698];
 

  _vehicle_343 = _this;
  _this setDir -48.679432;
  _this setPos [9550.0957, 6658.5698];
};

_vehicle_345 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier1" createVehicle [9602.25, 6651.9214, 0.031809881];
 

  _vehicle_345 = _this;
  _this setDir 56.632462;
  _this setPos [9602.25, 6651.9214, 0.031809881];
};

_vehicle_347 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_HBarrier1" createVehicle [9603.8193, 6646.5625, 6.1035156e-005];
 

  _vehicle_347 = _this;
  _this setDir 82.395638;
  _this setPos [9603.8193, 6646.5625, 6.1035156e-005];
};

_vehicle_361 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Toilet" createVehicle [9565.0459, 6638.7808, 6.1035156e-005];
 

  _vehicle_361 = _this;
  _this setDir 168.35199;
  _this setPos [9565.0459, 6638.7808, 6.1035156e-005];
};

_vehicle_364 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Toilet" createVehicle [9566.4922, 6639.1211, -1.5258789e-005];
 

  _vehicle_364 = _this;
  _this setDir 168.35199;
  _this setPos [9566.4922, 6639.1211, -1.5258789e-005];
};

_vehicle_368 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "PowerGenerator" createVehicle [9597.7041, 6653.5708, 1.5258789e-005];
 

  _vehicle_368 = _this;
  _this setDir 167.20656;
  _this setPos [9597.7041, 6653.5708, 1.5258789e-005];
};

_vehicle_385 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "AmmoCrates_NoInteractive_Large" createVehicle [9555.7715, 6652.0767, 3.0517578e-005];
 

  _vehicle_385 = _this;
  _this setDir 57.611809;
  _this setPos [9555.7715, 6652.0767, 3.0517578e-005];
};

_vehicle_386 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "AmmoCrates_NoInteractive_Medium" createVehicle [9561.0449, 6650.1855, 0.172525];
 

  _vehicle_386 = _this;
  _this setDir -1.1921819;
  _this setPos [9561.0449, 6650.1855, 0.172525];
};

_vehicle_387 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "AmmoCrates_NoInteractive_Small" createVehicle [9562.1582, 6649.9614, 0.20693633];
 

  _vehicle_387 = _this;
  _this setDir 92.06385;
  _this setPos [9562.1582, 6649.9614, 0.20693633];
};

_vehicle_388 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "AmmoCrate_NoInteractive_" createVehicle [9561.9268, 6650.0732, 0.48922709];
 

  _vehicle_388 = _this;
  _this setDir -95.787567;
  _this setPos [9561.9268, 6650.0732, 0.48922709];
};

_vehicle_405 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "PowGen_Big" createVehicle [9571.1943, 6664.5737, 0.00021362305];
 

  _vehicle_405 = _this;
  _this setDir -73.068626;
  _this setPos [9571.1943, 6664.5737, 0.00021362305];
};

_vehicle_411 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "RU_WarfareBVehicleServicePoint" createVehicle [9579.5283, 6636.791, -0.11657652];
 

  _vehicle_411 = _this;
  _this setDir -11.671355;
  _this setPos [9579.5283, 6636.791, -0.11657652];
};

_vehicle_413 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [9571.8799, 6651.4185, 1.5258789e-005];
 

  _vehicle_413 = _this;
  _this setDir -209.37212;
  _this setPos [9571.8799, 6651.4185, 1.5258789e-005];
};

_vehicle_420 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9554.1777, 6669.5962, -0.05801864];
 

  _vehicle_420 = _this;
  _this setDir -140.9301;
  _this setPos [9554.1777, 6669.5962, -0.05801864];
};

_vehicle_421 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_Stripes" createVehicle [9550.4746, 6670.8467, 3.0517578e-005];
 

  _vehicle_421 = _this;
  _this setDir -52.539318;
  _this setPos [9550.4746, 6670.8467, 3.0517578e-005];
};

_vehicle_423 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Garbage_can" createVehicle [9592.3857, 6644.8188, 3.0517578e-005];
 

  _vehicle_423 = _this;
  _this setPos [9592.3857, 6644.8188, 3.0517578e-005];
};

_vehicle_427 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9566.1094, 6656.9019, 6.1035156e-005];
 

  _vehicle_427 = _this;
  _this setDir 55.446556;
  _this setPos [9566.1094, 6656.9019, 6.1035156e-005];
};

_vehicle_429 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9567.7451, 6654.2485, 7.6293945e-005];
 

  _vehicle_429 = _this;
  _this setDir 59.338245;
  _this setPos [9567.7451, 6654.2485, 7.6293945e-005];
};

_vehicle_431 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9569.2324, 6651.5771, -0.037321635];
 

  _vehicle_431 = _this;
  _this setDir 61.510441;
  _this setPos [9569.2324, 6651.5771, -0.037321635];
};

_vehicle_434 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9570.6484, 6648.8379, -0.016594641];
 

  _vehicle_434 = _this;
  _this setDir 63.102085;
  _this setPos [9570.6484, 6648.8379, -0.016594641];
};

_vehicle_437 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "AmmoCrates_NoInteractive_Large" createVehicle [9565.7764, 6635.7402, 2.7623479];
 

  _vehicle_437 = _this;
  _this setPos [9565.7764, 6635.7402, 2.7623479];
};

_vehicle_440 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9541.9199, 6669.1045, 0.12908489];
 

  _vehicle_440 = _this;
  _this setDir 21.954151;
  _this setPos [9541.9199, 6669.1045, 0.12908489];
};

_vehicle_443 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9545.5498, 6666.981, 6.1035156e-005];
 

  _vehicle_443 = _this;
  _this setDir 31.707098;
  _this setPos [9545.5498, 6666.981, 6.1035156e-005];
};

_vehicle_446 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9548.6289, 6664.9312, 3.0517578e-005];
 

  _vehicle_446 = _this;
  _this setDir 31.707098;
  _this setPos [9548.6289, 6664.9312, 3.0517578e-005];
};

_vehicle_449 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9538.4775, 6670.3501, 0.00024414063];
 

  _vehicle_449 = _this;
  _this setDir 21.954151;
  _this setPos [9538.4775, 6670.3501, 0.00024414063];
};

_vehicle_451 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9535.2002, 6671.7432, 9.1552734e-005];
 

  _vehicle_451 = _this;
  _this setDir 21.954151;
  _this setPos [9535.2002, 6671.7432, 9.1552734e-005];
};

_vehicle_453 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9531.7695, 6673.1455, 1.5258789e-005];
 

  _vehicle_453 = _this;
  _this setDir 21.954151;
  _this setPos [9531.7695, 6673.1455, 1.5258789e-005];
};

_vehicle_456 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9551.1621, 6672.248, 6.1035156e-005];
 

  _vehicle_456 = _this;
  _this setDir -140.9301;
  _this setPos [9551.1621, 6672.248, 6.1035156e-005];
};

_vehicle_458 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9547.4941, 6675.1948, 0.00019836426];
 

  _vehicle_458 = _this;
  _this setDir -140.9301;
  _this setPos [9547.4941, 6675.1948, 0.00019836426];
};

_vehicle_460 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9542.2471, 6678.5527, 6.1035156e-005];
 

  _vehicle_460 = _this;
  _this setDir -148.51549;
  _this setPos [9542.2471, 6678.5527, 6.1035156e-005];
};

_vehicle_467 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_Stripes" createVehicle [9542.2266, 6671.8242, 0.00015258789];
 

  _vehicle_467 = _this;
  _this setDir -62.960697;
  _this setPos [9542.2266, 6671.8242, 0.00015258789];
};

_vehicle_471 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [9514.5908, 6677.3384, -1.5258789e-005];
 

  _vehicle_471 = _this;
  _this setDir -15.435764;
  _this setPos [9514.5908, 6677.3384, -1.5258789e-005];
};

_vehicle_475 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9538.6426, 6680.8267, 0.0001373291];
 

  _vehicle_475 = _this;
  _this setDir -150.3896;
  _this setPos [9538.6426, 6680.8267, 0.0001373291];
};

_vehicle_477 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_D" createVehicle [9535.5078, 6682.3853, 0.00010681152];
 

  _vehicle_477 = _this;
  _this setDir -150.3896;
  _this setPos [9535.5078, 6682.3853, 0.00010681152];
};

_vehicle_480 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9571.8691, 6646.0469, -0.021534169];
 

  _vehicle_480 = _this;
  _this setDir 67.286621;
  _this setPos [9571.8691, 6646.0469, -0.021534169];
};

_vehicle_483 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Paleta1" createVehicle [9560.9629, 6649.9414, 0.0042284383];
 

  _vehicle_483 = _this;
  _this setPos [9560.9629, 6649.9414, 0.0042284383];
};

_vehicle_486 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_Stripes" createVehicle [9536.0547, 6680.624, 4.5776367e-005];
 

  _vehicle_486 = _this;
  _this setDir -62.960697;
  _this setPos [9536.0547, 6680.624, 4.5776367e-005];
};

_vehicle_488 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_Stripes" createVehicle [9535.083, 6678.6719, 6.1035156e-005];
 

  _vehicle_488 = _this;
  _this setDir -62.960697;
  _this setPos [9535.083, 6678.6719, 6.1035156e-005];
};

_vehicle_490 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_Stripes" createVehicle [9526.2236, 6676.5981, 3.0517578e-005];
 

  _vehicle_490 = _this;
  _this setDir -67.463562;
  _this setPos [9526.2236, 6676.5981, 3.0517578e-005];
};

_vehicle_492 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CncBlock_Stripes" createVehicle [9527.2686, 6678.9888, 4.5776367e-005];
 

  _vehicle_492 = _this;
  _this setDir -65.179108;
  _this setPos [9527.2686, 6678.9888, 4.5776367e-005];
};

_vehicle_497 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldTable" createVehicle [9558.0928, 6654.5732];
 

  _vehicle_497 = _this;
  _this setPos [9558.0928, 6654.5732];
};

_vehicle_498 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldChair" createVehicle [9559.666, 6651.0098, 3.0517578e-005];
 

  _vehicle_498 = _this;
  _this setPos [9559.666, 6651.0098, 3.0517578e-005];
};

_vehicle_500 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SmallTable" createVehicle [9560.6641, 6651.6777, -3.0517578e-005];
 

  _vehicle_500 = _this;
  _this setDir -55.706463;
  _this setPos [9560.6641, 6651.6777, -3.0517578e-005];
};

_vehicle_504 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldTable" createVehicle [9560.0322, 6653.1812, -1.5258789e-005];
 

  _vehicle_504 = _this;
  _this setDir 54.962189;
  _this setPos [9560.0322, 6653.1812, -1.5258789e-005];
};

_vehicle_506 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldTable" createVehicle [9581.5957, 6652.8267];
 

  _vehicle_506 = _this;
  _this setDir 64.290703;
  _this setPos [9581.5957, 6652.8267];
};

_vehicle_508 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldTable" createVehicle [9583.2275, 6649.4487, 0.00056053419];
 

  _vehicle_508 = _this;
  _this setDir 63.702538;
  _this setPos [9583.2275, 6649.4487, 0.00056053419];
};

_vehicle_511 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldChair" createVehicle [9557.9619, 6653.48, -1.5258789e-005];
 

  _vehicle_511 = _this;
  _this setDir -160.03514;
  _this setPos [9557.9619, 6653.48, -1.5258789e-005];
};

_vehicle_513 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldChair" createVehicle [9559.4805, 6652, -1.5258789e-005];
 

  _vehicle_513 = _this;
  _this setDir -129.82602;
  _this setPos [9559.4805, 6652, -1.5258789e-005];
};

_vehicle_515 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldChair" createVehicle [9584.4492, 6649.894, -4.5776367e-005];
 

  _vehicle_515 = _this;
  _this setDir 92.774452;
  _this setPos [9584.4492, 6649.894, -4.5776367e-005];
};

_vehicle_517 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldChair" createVehicle [9583.8535, 6652.7349, 3.0517578e-005];
 

  _vehicle_517 = _this;
  _this setDir 58.719639;
  _this setPos [9583.8535, 6652.7349, 3.0517578e-005];
};

_vehicle_522 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Sign_Checkpoint" createVehicle [9545.6172, 6665.4727, 1.5258789e-005];
 

  _vehicle_522 = _this;
  _this setDir -55.664108;
  _this setPos [9545.6172, 6665.4727, 1.5258789e-005];
};

_vehicle_523 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Sign_Checkpoint_EP1" createVehicle [9527.9072, 6677.3281, 1.5258789e-005];
 

  _vehicle_523 = _this;
  _this setDir -61.952232;
  _this setPos [9527.9072, 6677.3281, 1.5258789e-005];
};

_vehicle_533 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Info_Board_EP1" createVehicle [9565.9785, 6657.957, 4.5776367e-005];
 

  _vehicle_533 = _this;
  _this setDir 53.251987;
  _this setPos [9565.9785, 6657.957, 4.5776367e-005];
};

_vehicle_539 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "EvMap" createVehicle [9560.5049, 6652.9126, 0.79876357];
 

  _vehicle_539 = _this;
  _this setPos [9560.5049, 6652.9126, 0.79876357];
};

_vehicle_540 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Laptop_EP1" createVehicle [9559.7871, 6653.4927, 0.79212636];
 

  _vehicle_540 = _this;
  _this setPos [9559.7871, 6653.4927, 0.79212636];
};

_vehicle_541 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Loudspeaker" createVehicle [9563.0859, 6660.8022, 2.8771956];
 

  _vehicle_541 = _this;
  _this setPos [9563.0859, 6660.8022, 2.8771956];
};

_vehicle_543 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Loudspeaker" createVehicle [9552.6563, 6660.8535, 4.3402476];
 

  _vehicle_543 = _this;
  _this setDir -170.91331;
  _this setPos [9552.6563, 6660.8535, 4.3402476];
};

_vehicle_547 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UralWreck" createVehicle [9577.8174, 6642.8354, 3.0517578e-005];
 

  _vehicle_547 = _this;
  _this setDir -102.39157;
  _this setPos [9577.8174, 6642.8354, 3.0517578e-005];
};

_vehicle_554 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_misc_amplion_wood" createVehicle [9601.1328, 6636.9312, 0.0001373291];
 

  _vehicle_554 = _this;
  _this setPos [9601.1328, 6636.9312, 0.0001373291];
};

_vehicle_556 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [9576.8691, 6634.3936];
 

  _vehicle_556 = _this;
  _this setPos [9576.8691, 6634.3936];
};

_vehicle_558 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [9557.376, 6657.7007, 9.1552734e-005];
 

  _vehicle_558 = _this;
  _this setPos [9557.376, 6657.7007, 9.1552734e-005];
};

_vehicle_560 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [9569.1816, 6628.543, -4.5776367e-005];
 

  _vehicle_560 = _this;
  _this setPos [9569.1816, 6628.543, -4.5776367e-005];
};

_vehicle_563 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_misc_amplion_wood" createVehicle [9593.4365, 6665.8452, 0.00012207031];
 

  _vehicle_563 = _this;
  _this setDir -135.32204;
  _this setPos [9593.4365, 6665.8452, 0.00012207031];
};

_vehicle_573 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [9594.6006, 6632.9507, 9.1552734e-005];
 

  _vehicle_573 = _this;
  _this setPos [9594.6006, 6632.9507, 9.1552734e-005];
};

_vehicle_574 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [9601, 6658.0098];
 

  _vehicle_574 = _this;
  _this setPos [9601, 6658.0098];
};

_vehicle_576 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [9604.4541, 6653.4487, 6.1035156e-005];
 

  _vehicle_576 = _this;
  _this setPos [9604.4541, 6653.4487, 6.1035156e-005];
};

_vehicle_578 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [9593.5938, 6669.3931, 0.0001373291];
 

  _vehicle_578 = _this;
  _this setPos [9593.5938, 6669.3931, 0.0001373291];
};

_vehicle_580 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet2" createVehicle [9583.2393, 6633.0801, 0.00057983398];
 

  _vehicle_580 = _this;
  _this setPos [9583.2393, 6633.0801, 0.00057983398];
};

_vehicle_582 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Camp" createVehicle [9578.2979, 6663.8979, -1.5258789e-005];
 

  _vehicle_582 = _this;
  _this setDir 186.11646;
  _this setPos [9578.2979, 6663.8979, -1.5258789e-005];
};

_vehicle_585 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Camp" createVehicle [9584.7686, 6663.2505, -4.5776367e-005];
 

  _vehicle_585 = _this;
  _this setDir 190.53137;
  _this setPos [9584.7686, 6663.2505, -4.5776367e-005];
};

_vehicle_587 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Camp" createVehicle [9591.4375, 6661.9688, 9.1552734e-005];
 

  _vehicle_587 = _this;
  _this setDir 192.35356;
  _this setPos [9591.4375, 6661.9688, 9.1552734e-005];
};

_vehicle_590 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "CampEast" createVehicle [9595.9287, 6641.2271, 6.1035156e-005];
 

  _vehicle_590 = _this;
  _this setDir 82.859413;
  _this setPos [9595.9287, 6641.2271, 6.1035156e-005];
};

_vehicle_594 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "CampEast" createVehicle [9593.4873, 6652.4766, 1.5258789e-005];
 

  _vehicle_594 = _this;
  _this setDir 78.299477;
  _this setPos [9593.4873, 6652.4766, 1.5258789e-005];
};

_vehicle_596 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9564.9609, 6646.4536, -0.10362446];
 

  _vehicle_596 = _this;
  _this setDir 65.603111;
  _this setPos [9564.9609, 6646.4536, -0.10362446];
};

_vehicle_599 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9566.1699, 6643.5518, -0.10758121];
 

  _vehicle_599 = _this;
  _this setDir 69.278519;
  _this setPos [9566.1699, 6643.5518, -0.10758121];
};

_vehicle_602 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9568.1006, 6636.8955, -0.11261839];
 

  _vehicle_602 = _this;
  _this setDir 74.708069;
  _this setPos [9568.1006, 6636.8955, -0.11261839];
};

_vehicle_605 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9569.1953, 6632.1792, -0.095271848];
 

  _vehicle_605 = _this;
  _this setDir 76.41642;
  _this setPos [9569.1953, 6632.1792, -0.095271848];
};

_vehicle_608 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9575.2412, 6635.0933, -0.095222868];
 

  _vehicle_608 = _this;
  _this setDir 74.354935;
  _this setPos [9575.2412, 6635.0933, -0.095222868];
};

_vehicle_611 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_long" createVehicle [9574.4844, 6637.9609, -0.089696713];
 

  _vehicle_611 = _this;
  _this setDir 75.552841;
  _this setPos [9574.4844, 6637.9609, -0.089696713];
};

_vehicle_612 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Fort_Watchtower" createVehicle [9565.5977, 6632.3076, -0.0041821287];
 

  _vehicle_612 = _this;
  _this setDir -13.909041;
  _this setPos [9565.5977, 6632.3076, -0.0041821287];
};

_vehicle_615 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_pumpa" createVehicle [9678.9531, 6536.8862, -0.029886188];
 

  _vehicle_615 = _this;
  _this setPos [9678.9531, 6536.8862, -0.029886188];
};

_vehicle_617 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [9568.7949, 6627.3477];
 

  _vehicle_617 = _this;
  _this setPos [9568.7949, 6627.3477];
};

_vehicle_619 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [9559.2266, 6670.1221, -3.0517578e-005];
 

  _vehicle_619 = _this;
  _this setPos [9559.2266, 6670.1221, -3.0517578e-005];
};

_vehicle_621 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [9548.0605, 6651.9556, -9.1552734e-005];
 

  _vehicle_621 = _this;
  _this setPos [9548.0605, 6651.9556, -9.1552734e-005];
};