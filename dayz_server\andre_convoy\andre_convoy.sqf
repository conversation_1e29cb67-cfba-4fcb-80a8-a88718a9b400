//===========================================
//THIS VERSION IS FOR ARMA 2: ANDRE CONVOYS
//===========================================
//By Donnovan from Brazil
//===========================================

if (isServer) then {
    //=======================
    //BASIC KONFIGURATION 1!
    //=======================

    //Vehicles: Its ok to repeate the same car with different configuration.
    donn_carsConvy = [
        //(0) Vehicle Armored (Unarmed)
        ["HMMWV_Armored_DZE",2,["100Rnd_762x51_M240"]],
        //(1) Vehicle Unarmored
        ["LadaLM_DZE",3,[]],
        //(2) Vehicle Unarmored
        ["Tractor_DZE",0,[]],
        //(3) Vehicle High Capacity
        ["Kamaz_Winter_DZE",5,[]],
        //(4) Vehicle High Capacity ++++
        ["Kamaz_Winter_DZE4",3,[]],
        //(5) Tank
        ["BMP2_CDF",2,["2000Rnd_762x54_PKT"]],
        //(6) Vehicle Armored (Armed)
        ["BRDM2_INS",3,["100Rnd_762x54_PK"]],
        //(7) Vehicle Armored (Armed)
        ["HMMWV_M1151_M2_CZ_DES_EP1_DZE",2,["100Rnd_127x99_M2"]],
        //(8) Vehicle Armored
        ["GAZ_Vodnik_DZE",4,["100Rnd_762x54_PK"]],
        //(9) Vehicle Armored (Armed)
        ["ArmoredSUV_PMC_DZE",2,["2000Rnd_762x51_M134"]],
        //(10) Vehicle Unarmored
        ["S1203_ambulance_EP1_DZE",2,[]],
        //(11) Vehicle Armored
        ["GAZ_Vodnik_HMG_DZ",3,["2000Rnd_762x54_PKT"]]
    ];

    //Soldier of the Convoys (all the soldiers are the same)
    //donn_soldierSkin = ["UN_CDF_Soldier_EP1","UN_CDF_Soldier_EP1"];
    donn_soldierSkin = ["Dealer_DZ","MafiaBoss_DZ","gsc_scientist1_DZ"];

    //=======================
    //BASIC KONFIGURATION 2!
    //=======================

    //CONVOY CONFIGURATION
    
    //Time between each consecutive spawn
    _timeBetweenSpawns = 2400;

    //Cars in each convoy (refere to donn_carsConvy)
    _convoyFormation = [
    //Shit convoys
        [1,1,2,1,1],
        [2,3,3,3,2],
        [0,4,4,4,0],
        [0,10,0],
        [7,10,7],
        [8,10,10,8],
        [7,1,3,7],
        [10,3,4,10],
        [0,4,0],
        [8,3,4,8],
        [7,3,4,4,8],
        [0,10,0],
        [1,8,8,1],
    //Good convoys
        [0,9,9,9,0],
        [4,11,11,5],
        [4,5,4],
        [4,4,8],
        [4,5,5,6],
        [9,2,9]
    ];
    
    //Crew Skill (driver, cargors and turreters)
	_generalSkill				=	0.9;	//All skills, except ain skill, for all AI
	_driverManAin				=	0.8;	//Ain of the driver, from 0 to 1
	_cargoMansAin				=	0.7;	//Ain of the cargo ocupants, from 0 to 1
	_turretMansAin				=	0.5;	//Ain ot the turret operators, from 0 to 1
    
    //SMART BOMBER MAN CONFIG - Percentage of bomberman AI in car disembarks: 0 is 0% and 1 is 100%
    //Those bombermans will fight and run to the player in the first oportunity
    //0.25 means 1 smart bomber each 4 vehicle disembark (1/0.25 = 4)
    donn_fighterBomberManPerc = 0.05;
    donn_fighterBomberIndicator = true; //indicates the fighter bombers with a small sphere above their heads        
        
    //SMART BOMBER MAN CONFIG - Percentage of bomberman AI in car disembarks: 0 is 0% and 1 is 100%
    //Those bomberman will not fight and search for a player to explode right after disembark
    //0.2 means 1 runner bomber each 5 vehicle disembark (1/0.2 = 5)
    donn_runnerBomberManPerc = 0.05;
    donn_runnerBomberIndicator = true; //indicates the runner bombers with a small sphere above their heads
    
    //ICONS SETTINGS
    _showMapIcons = true;                    //Show spawn, convoy and AI icons on map?
    _showMapIconsOnlyWhenNotInWar = false;    //Hide convoy icons when they enter in war, so the fight is not spoted.
    _showCrewNumber = false;                    //Show crew number on the vehicle icon on map? (runner bombers don't count as vehicle crew)
    
    //SPECIAL REWARDS
    //Special kill (main char kill or combo kill) reward in gold or coins? Use false to gold / true to coins.
    _useCoinsReward = true;
    //Special Reward Array: _xxxxxRewards = [kill reward,son of general kill reward,extra for each combo kill];
    donn_coinRewards = [2000,6000,3000];
    donn_goldRewards = [["ItemSilverBar",0],["ItemGoldBar10oz",1],["ItemGoldBar",1]];

    //HUMANITY GAIN
    //How much humnity to gain for each AI kill?
    donn_humanityGainForKill = 30;
    //Bellow this value you is in the Bandit Way so donn_humanityGainForKill will subtract to your humanity
    //Above this value you is in the Hero Way so donn_humanityGainForKill will add to your humanity
    donn_humanityZeroScale = 2500;
    
    //OTHER SETTINGS
    donn_aiCarVulnerable = true;         //false or 0 is INVUNERABLE true or 1 is VULNERABLE
    _donn_delete_body_time = 9600;    //Time in seconds to delete a dead AI body    
    _lootCutter = 2;                //Number of loot piles in veh are divided by this number (minimum of 1 loot pile)
    
    //====================
    //MAP CONFIGURATION
    //====================

    //VEHICLES SPAWN: [[vehicle spawn position],[nothing],spawn angle,spawn radius]
    
    //CHERNARUS SPAWNS
    _spawns = [
        [[2000,10907,0.2],[],100,15],
        [[7730,4798,0.2],[],22,15],
        [[7195,9700,0.2],[],0,15]
    ];

    //=====================
    //EXTRA KONFIGURATION!
    //=====================

    //LOOT IN VEHICLE
    _loot1 = [
        [["addWeaponCargoGlobal","Mk48_DZ",[1,1,2]],["addMagazineCargoGlobal","100Rnd_762x51_M240",[1,2,3]]],    //Maried Loot
        [["addWeaponCargoGlobal","M249_DZ",[1,1,2]],["addMagazineCargoGlobal","200Rnd_556x45_M249",[1,2,3]]],    //Maried Loot
        [["addWeaponCargoGlobal","M4SPR_DZE",[1,1,2]],["addMagazineCargoGlobal","30Rnd_556x45_Stanag",[2,3,4]]],        //Maried Loot
        [["addWeaponCargoGlobal","DMR_DZ",[1,1,2]],["addMagazineCargoGlobal","20Rnd_762x51_DMR",[3,4,5]]],        //Maried Loot
        [["addWeaponCargoGlobal","AK74_DZ",[1,1,2]],["addMagazineCargoGlobal","30Rnd_762x39_AK47",[2,3,4]]],    //Maried Loot
        [["addWeaponCargoGlobal","NVGoggles_DZE",[2,3,4]]],
        [["addWeaponCargoGlobal","ItemEtool",[3,4,5]]],
        [["addWeaponCargoGlobal","Binocular_Vector",[2,3,4]]],
        [["addMagazineCargoGlobal","full_cinder_wall_kit",[3,6,9]]],
        [["addMagazineCargoGlobal","CinderBlocks",[6,10,12]]],
        [["addMagazineCargoGlobal","metal_floor_kit",[2,3,4]]],
        [["addMagazineCargoGlobal","MortarBucket",[8,12,16]]],
        [["addMagazineCargoGlobal","ItemTankTrap",[4,6,8]]],
        [["addMagazineCargoGlobal","ItemWire",[4,6,8]]],
        [["addMagazineCargoGlobal","ItemKiloHemp",[4,6,8]]],
        [["addMagazineCargoGlobal","ItemKiloTobacco",[2,3,5]]],
        [["addMagazineCargoGlobal","ItemKiloBlackTea",[2,3,5]]],
        [["addMagazineCargoGlobal","PartVRotor",[2,3,5]]],
        [["addMagazineCargoGlobal","metal_panel_kit",[1,2,3]]],
        [["addMagazineCargoGlobal","cinder_door_kit",[1,3,5]]],
        [["addMagazineCargoGlobal","plot_pole_kit",[1,2,3]]],
        [["addMagazineCargoGlobal","ItemWoodWallGarageDoorLocked",[1,2,3]]],
        [["addMagazineCargoGlobal","ItemWoodWall",[8,6,4]]],
        [["addMagazineCargoGlobal","HandGrenade_West",[3,5,7]]],
        [["addWeaponCargoGlobal","ItemCrowbar",[2,5,7]]],
        [["addMagazineCargoGlobal","ItemSledgeHead",[1,2,3]]],
        [["addMagazineCargoGlobal","ItemSledgeHandle",[1,2,3]]],
        [["addMagazineCargoGlobal","m240_nest_kit",[1,1,1]]],
        [["addMagazineCargoGlobal","forest_large_net_kit",[1,1,2]]],
        [["addMagazineCargoGlobal","forest_net_kit",[1,1,2]]],
        [["addMagazineCargoGlobal","desert_large_net_kit",[1,1,2]]],
        [["addMagazineCargoGlobal","desert_net_kit",[1,1,2]]],    
        [["addMagazineCargoGlobal","ItemPole",[1,3,5]]],        
        [["addMagazineCargoGlobal","ItemComboLock",[1,1,2]]],
        [["addMagazineCargoGlobal","ItemBriefcase100oz",[1,1,1]]],
        [["addMagazineCargoGlobal","ItemBriefcase100oz",[1,2,2]]],
        [["addMagazineCargoGlobal","ItemCitrine",[1,1,1]]]
    ];
    
    //AI WEAPONS & AMMO
    _donn_weapons = [
        ["Mk48_CCO_DZ","100Rnd_762x51_M240"],
        ["M249_DZ","200Rnd_556x45_M249"],
        ["M4SPR_DZE","30Rnd_556x45_Stanag"],
        ["G36A_Camo_DZ","30Rnd_556x45_Stanag"],
        ["G36K_Camo_DZ","30Rnd_556x45_Stanag"],
        ["G36K_Camo_DZ","30Rnd_556x45_Stanag"],
        ["AKM_DZ","30Rnd_762x39_AK47"],
        ["AK74_Kobra_DZ","30Rnd_545x39_AK"],
        ["Bizon_SD_DZ","64Rnd_9x19_SD_Bizon"],
        ["DMR_DZ","20Rnd_762x51_DMR"],
        ["RPK74_DZ","75Rnd_545x39_RPK"],
        ["M14_DZ","20Rnd_762x51_DMR"],
        ["RPG7V","OG7"],        
        ["XM8_SAW_DZ","100Rnd_556x45_BetaCMag"]
        // ["M136","M136"]
    ];
    
    //=========================
    //END OF KONFIGURATION
    //=========================
    
    //==============================================================================================================
    //==============================================================================================================
    //==============================================================================================================
    //==============================================================================================================
    
    //Avoid Vehicles to Explode when Entering it
    waitUntil {uisleep 2;!isNil "DZE_safeVehicle"};
    _cascaVehicles = [];
    {
        _car = _x select 0;
        if !(_car in _cascaVehicles) then {
            _cascaVehicles = _cascaVehicles + [_car];
        };
    } forEach donn_carsConvy;
    DZE_safeVehicle = DZE_safeVehicle + _cascaVehicles;    

    //Generate Functions
    call compile preprocessFileLineNumbers "\z\addons\dayz_server\andre_convoy\andre_convoy_functions.sqf";
    
    //Set Type of Kill Reward (Coins or Gold)
    if (_useCoinsReward) then {donn_cleanUnit = donn_cleanUnitCoins;} else {donn_cleanUnit = donn_cleanUnitGold;};
    
    //Find Waypoints: Automatic find street waypoints
    donn_wps = [1200] call donn_find_intersections;
    
    //Show Spawn Icons on Map / refuel Vehicles / Show convoy icons on map 
    donn_motor = [];
    donn_units_motor = [];
    donn_units_delete = [];
    if (_showMapIcons) then {
        [_spawns,_showCrewNumber,_donn_delete_body_time] spawn {
                //Convoy Icons on Map Iniciate variables
                _showCrewNumber = _this select 1;
                _donn_delete_body_time = _this select 2;
                _add = [];
                _remove = [];
                _update = [];
                _donn_units_motor_icon_old = [];
                //Add Icon Function
                _addIcon = {
                    _iName = _this select 0;
                    _iPos = _this select 1;
                    _iColor = _this select 2;
                    _iText = _this select 3;
                    _mark = createMarker [_iName, _iPos];
                    _mark setMarkerColor _iColor;
                    _mark setMarkerShape "Icon";
                    _mark setMarkerType "mil_dot";
                    _mark setMarkerText _iText;
                };
            
            //Spawn icons on map iniciate variables
            _spawns = _this select 0;
            _donn_casca_icons = [];
            {_donn_casca_icons = _donn_casca_icons + [[_x select 0,"Convoy Spawn " + str (_forEachIndex + 1)]];} forEach _spawns;
            
            //While: Spawn icons / Convoy Icons / Refuel
            _uisleep_a = 0;
            _uisleep_b = 0;
            _uisleep_c = 0;
            while {true} do {
                //Spawn Icons Add
//                if (_uisleep_a == 0) then {
//                    {
//
//                        _mark = createMarker ["Donn_Spawn_" + str _forEachIndex, _x select 0];
//                        _mark setMarkerShape "Icon";
//                        _mark setMarkerText (_x select 1);
//                        _mark setMarkerType "mil_dot";
//                        _mark setMarkerColor "ColorPink";
//                    } forEach _donn_casca_icons;
//                };

                //Refual Vehicles each 100 seconds and delete dead AI body
                if (_uisleep_b >= 100) then {
                    _uisleep_b = 0;
                    
                    //Refuel
                    {_x setFuel 1;} forEach donn_motor;

                    //Delete AI dead body
                    {
                        _unit =  _x select 0;
                        _dieTime = _x select 1;
                        if (!isNull _unit) then {
                            _unit setVariable ["DZMS_deathTime",(diag_tickTime + _donn_delete_body_time)];
                            if (time - _dieTime > _donn_delete_body_time) then {
                                _unit call donn_AI_deleteBody;
                            };
                        };
                    } forEach donn_units_delete;
                };
                
                //Convoy Icons
                {
                    deleteMarker vehicleVarName _x;
                } forEach _remove;
                {
                    _color = "";
                    if (_x isKindOf "LandVehicle") then {
                        if (_x getVariable ["donn_tu",false]) then {_color = "ColorRed";} else {_color = "ColorBlue";};
                    } else {
                        _color = "ColorYellow";
                    };
                    _array = [vehicleVarName _x,position _x,_color,""];
                    _array call _addIcon;
                } forEach _add;
                {
                    _posXY = position _x;
                    _marker = vehicleVarName _x;
                    _marker setMarkerPos (getPosATL _x);
                    if (_x isKindOf "LandVehicle") then {
                        if (_showCrewNumber) then {_marker setMarkerText str ({alive _x} count crew _x)+" UN Convoy";};
                    };

                } forEach _update;
                _donn_units_motor_icon = [];
                _vehIcon = [];
                {
                    if !((group _x) getVariable ["donn_inWar",false]) then {
                        _veh = vehicle _x;
                        _assigned = assignedVehicle _x;
                        if (_veh != _x) then {
                            if !(_veh in _vehIcon) then {
                                _vehIcon = _vehIcon + [_veh];
                                _donn_units_motor_icon = _donn_units_motor_icon + [_veh];
                            };
                        } else {
                            _donn_units_motor_icon = _donn_units_motor_icon + [_x];
                        };
                        if !(_assigned in _vehIcon) then {
                            _vehIcon = _vehIcon + [_assigned];
                            _donn_units_motor_icon = _donn_units_motor_icon + [_assigned];
                        };
                    };
                    
                } forEach donn_units_motor;
                if (_uisleep_c >= 20) then {
                    _add = _donn_units_motor_icon;
                    _remove = _donn_units_motor_icon_old;
                    _donn_units_motor_icon_old = _donn_units_motor_icon;
                    _uisleep_c = 0;
                } else {
                    _add = _donn_units_motor_icon - _donn_units_motor_icon_old;
                    _remove = _donn_units_motor_icon_old - _donn_units_motor_icon;
                    _update = _donn_units_motor_icon - _add;
                    _donn_units_motor_icon_old = _donn_units_motor_icon;
                };
                
                //uisleep Divisor of all
                _uisleepTime = 4.0;
                Sleep _uisleepTime;
                _uisleep_a = _uisleep_a + _uisleepTime;
                _uisleep_b = _uisleep_b + _uisleepTime;
                _uisleep_c = _uisleep_c + _uisleepTime;
            
                //Spawn Icons Remove
                if (_uisleep_a == 15) then {
                    _uisleep_a = 0;
                    {deleteMarker ("Donn_Spawn_" + str _forEachIndex);} forEach _donn_casca_icons;
                };
            };
        };
    } else {
        //Refual Vehicles each 100 seconds and Check for AI body delete
        _donn_delete_body_time spawn {
            while {true} do {
                _donn_delete_body_time = _this;
                
                //Refuel
                {_x setFuel 1;} forEach donn_motor;
            
                //Delete AI dead body
                {
                    _unit =  _x select 0;
                    _dieTime = _x select 1;
                    if (!isNull _unit) then {
                        if (time - _dieTime > _donn_delete_body_time) then {
                            _unit call donn_AI_deleteBody;
                        };
                    };
                } forEach donn_units_delete;
                uiSleep 100;
            };
        };
    };
    
    donn_godModeMult = 0;
    if (donn_aiCarVulnerable) then {donn_godModeMult = 1;};
    
    //Safe Sleep
    uisleep 10;
    
    //Spawn Casca Units/Cars
    for "_cs" from 1 to (count _convoyFormation) do {
        diag_log ("[CASCA UN VEHICLE CONVOY] Initialized Spawn! " + str _cs);
        
        //Inicialization
//        _spawn = _spawns select ((_cs + ((count _spawns)-1)) mod (count _spawns));
        _spawn = _spawns call BIS_fnc_selectRandom;
        diag_log format["[CASCA UN VEHICLE CONVOY]: Spawn Cords  %1", _spawn];
        _origin = _spawn select 0;
        _rad = _spawn select 3;
        _rosa_group = createGroup EAST;
//        _convoy = _convoyFormation select (_cs-1);
        _convoy = _convoyFormation call BIS_fnc_selectRandom;
        _behaviorSafe = "AWARE";
        _speed = "NORMAL";
        _qtd = count _convoy;
        _cars = [];
        {
            _cars = _cars + [donn_carsConvy select _x];
        } forEach _convoy;
        for "_n" from 1 to _qtd do {
            private ["_firstDriver"];
            
            //Spawn Car
            _car = _cars select (_n - 1);
            _motor = createVehicle [_car select 0,_origin,[],_rad,"NONE"];
            _motor allowDamage donn_aiCarVulnerable;
            _motor setVehicleVarName ("CASCA_CAR_" + str _cs + "_" + str _n);
            _motorID = str(round(random 999999));
            _motor setVariable ["ObjectID",_motorID,true];
            _motor setVariable ["ObjectUID",_motorID,true];
            //PVDZE_serverObjectMonitor set [count PVDZE_serverObjectMonitor,_motor];
            _motor setVariable ["car_group",_rosa_group,false];
            _motor call fnc_veh_ResetEH;
            _motor addEventHandler ["HandleDamage",{_this call donn_casca_veh_HD;(_this select 2)*donn_godModeMult}];
            _motor setDir (_spawn select 2);
            _motor setFuel 1;
            donn_motor = donn_motor + [_motor];
            _motor setvariable ["aiveh",1,true]; 
            _motor addEventHandler ["GetIn",{_this call donn_casca_getIn;}];
            _motor addEventHandler ["GetOut",{_this call donn_casca_getOut;}];
            PVDZE_veh_Init = _motor;
            publicVariable "PVDZE_veh_Init";
            
            //Add Vehicle Ammo
            _ammos = _car select 2;
            if (count _ammos > 0) then {
                [_ammos,_motor,"add"] call donn_cascar_tuAmmo;
                _motor setVariable ["donn_tu",true,true];
            };
            
            //count Crew
            _driverCount = 1;
            _turreterCount = count _ammos;
            _cargorsCount = _car select 1;
            _crewCount = _driverCount + _turreterCount + _cargorsCount;
            
            //Put Loot in the Car
            clearWeaponCargoGlobal _motor;
            clearMagazineCargoGlobal _motor;
            {
                {call compile format ["_motor %1 ['%2',%3];",_x select 0,_x select 1,_x select 2 select ((_qtd - 1) min 2)];} forEach _x;
            } forEach ([_loot1,ceil (_crewCount/_lootCutter)] call donn_selectLoot);
            
            //Spawn Soldiers
            _gunnerPos = 0;
            for "_y" from 1 to _crewCount do {
                
                //Create Soldier
                _skin = donn_soldierSkin call BIS_fnc_selectRandom;
                _unit = _rosa_group createUnit [_skin,[50,50,50],[],50,"PRIVATE"];
                _unit setVehicleVarName ("CASCA_AI_" + str _cs + "_" + str _n + "_" + str _y);                
                [_unit] joinSilent _rosa_group;
                _unit addEventHandler ["Killed",{_this call donn_cleanUnit;}];
                _unit addEventHandler ["HandleDamage",{_this call donn_casca_unit_HD}];
                donn_units_motor = donn_units_motor + [_unit];
                _unit disableAi "TARGET";
                _unit disableAi "AUTOTARGET";
                
                //Give Soldier Weapon and Ammo
                removeAllWeapons _unit;
                {_unit removeMagazine _x} forEach magazines _unit;
                _weap_mag = _donn_weapons call BIS_fnc_selectRandom;
                _weapon = _weap_mag select 0;
                _magazine = _weap_mag select 1;
                _unit addWeapon _weapon;
                _unit selectWeapon _weapon;
                for "_pa" from 1 to (ceil (1 + random 2)) do {
                    _unit addMagazine _magazine;
                };
                
                //Put AI in the car
                if (_y == 1) then {
                    //Driver
                    _unit assignAsDriver _motor;
                    _unit moveInDriver _motor;
                    _unit setSkill _generalSkill;
                    _unit setSkill ["aimingAccuracy",_driverManAin];
                };
                if (_y > 1 && _y <= 1 + _turreterCount) then {
                    //Turreters
                    _unit assignAsGunner _motor;
                    //_unit moveInTurret [_motor,[_gunnerPos]];
                    _unit moveInTurret [_motor,[0]];                    
                    _unit setSkill _generalSkill;
                    _unit setSkill ["aimingAccuracy",_turretMansAin];
                    _gunnerPos = _gunnerPos + 1;
                 };
                if (_y > 1 + _turreterCount) then {
                    //Cargors
                    _unit assignAsCargo _motor;
                    _unit moveInCargo _motor;
                    _unit setSkill _generalSkill;
                    _unit setSkill ["aimingAccuracy",_cargoMansAin];
                };
            };
            _rosa_group setCombatMode "RED";
            _rosa_group setBehaviour _behaviorSafe;
            _rosa_group setVariable ["donn_pshot",false,false];
            _rosa_group setVariable ["donn_inWar",false,false];
            {_x allowFleeing 0} forEach units _rosa_group;        
            {[_x] allowGetIn true;uisleep 0.2;} forEach units _rosa_group;
            {[_x] orderGetIn true;uisleep 0.2;} forEach units _rosa_group;
        };
        
        //Make Units leave or Embark car
        [_rosa_group,_behaviorSafe] spawn {
            _rosa_group = _this select 0;
            _behaviorSafe = _this select 1;
            _noHurtTime = 0;
            _tm = 1;
            _targetOff = true;
            while {!isNil "_rosa_group"} do {
                _newHurt = _rosa_group getVariable ["donn_pshot",false];
                if (isNil "_newHurt") then {_newHurt = false;};
                if (_newHurt) then {
                    _noHurtTime = 0;
                    _rosa_group setVariable ["donn_pshot",false,false];
                    if (_targetOff) then {
                        _targetOff = false;
                        _rosa_group setVariable ["donn_inWar",true,false];
                        {_x enableAi "TARGET";uisleep 0.025;} forEach units _rosa_group;
                        {_x enableAi "AUTOTARGET";uisleep 0.025;} forEach units _rosa_group;
                        uisleep 0.5;
                        _rosa_group setBehaviour "COMBAT";
                        _rosa_group setCombatMode "RED";
                        uisleep 0.5;
                        _turreters = [];
                        {if ((assignedVehicleRole _x) select 0 == "Turret") then {_turreters = _turreters + [_x];};} forEach units _rosa_group;
                        _leaveToFight = (units _rosa_group) - _turreters;
                        {[_x] orderGetIn false;uisleep 0.05;} forEach _leaveToFight;
                        {[_x] allowGetIn false;uisleep 0.05;} forEach _leaveToFight;
                        _tm = 15;
                    };
                } else {
                    _noHurtTime = _noHurtTime + _tm;
                    if (_noHurtTime > 120) then {
                        _noDangerFell = true;
                        {if (_rosa_group knowsAbout _x > 0) exitWith {_noDangerFell = false;};} forEach playableUnits;
                        if (_noDangerFell) then {
                            _targetOff = true;
                            _rosa_group setVariable ["donn_inWar",false,false];
                            {_x disableAi "TARGET";} forEach units _rosa_group;
                            {_x disableAi "AUTOTARGET";} forEach units _rosa_group;
                            uisleep 1;
                            _rosa_group setBehaviour _behaviorSafe;
                            _rosa_group setCombatMode "RED";
                            uisleep 2;
                            {[_x] allowGetIn true;uisleep 0.2;} forEach units _rosa_group;
                            {[_x] orderGetIn true;uisleep 0.2;} forEach units _rosa_group;
                            _tm = 1;
                        };
                    };
                };
                uisleep _tm;
            };
        };
        
        //Make Convoy Route
        [_spawn select 0,_rosa_group,_speed,donn_wps] call donn_makeroute;
    
        //Sleep Before Next Spawn
        uisleep _timeBetweenSpawns;
    };
};