/*
  CHERNARUS ENHANCEMENTS - Krasnostav
  ----------------------------------------------------------
    Krasnostav Enhancements by <PERSON>, blackwiddow
    Email: <EMAIL>
    Steam: blackwiddow20
*/
 
if (isServer) then {

_vehicle_0 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_Hospital", [11337.791, 12346.173, 0.23690474], [], 0, "CAN_COLLIDE"];
  _vehicle_0 = _this;
  _this setDir -64.59996;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11337.791, 12346.173, 0.23690474];
};

_vehicle_1 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rail_najazdovarampa", [11344.063, 12345.254, -0.38231778], [], 0, "CAN_COLLIDE"];
  _vehicle_1 = _this;
  _this setDir -153.87964;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11344.063, 12345.254, -0.38231778];
};

_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [11323.422, 12293.98, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setDir 26.928396;
  _this setPos [11323.422, 12293.98, -6.1035156e-005];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [11337.367, 12321.489, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir -152.79149;
  _this setPos [11337.367, 12321.489, -4.5776367e-005];
};

_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_12", [11318.109, 12284.26, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setDir 28.925186;
  _this setPos [11318.109, 12284.26, 0.0001373291];
};

_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [11352.901, 12319.145, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setDir -122.72022;
  _this setPos [11352.901, 12319.145, 6.1035156e-005];
};

_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [11356.473, 12322.751, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir -150.51463;
  _this setPos [11356.473, 12322.751, 0.00021362305];
};

_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [11357.39, 12362.203, 0.00039672852], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir -337.81003;
  _this setPos [11357.39, 12362.203, 0.00039672852];
};

_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [11360.476, 12366.231, 0.076004028], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir -310.01538;
  _this setPos [11360.476, 12366.231, 0.076004028];
};

_vehicle_42 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [11370.304, 12368.104, -0.036773682], [], 0, "CAN_COLLIDE"];
  _vehicle_42 = _this;
  _this setDir -248.55298;
  _this setPos [11370.304, 12368.104, -0.036773682];
};

_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [11362.904, 12331.771, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setDir -149.44414;
  _this setPos [11362.904, 12331.771, -4.5776367e-005];
};

_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [11362.769, 12331.554, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setDir 30.565157;
  _this setPos [11362.769, 12331.554, 3.0517578e-005];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [11375.87, 12362.563, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir -205.81717;
  _this setPos [11375.87, 12362.563, -0.00015258789];
};

_vehicle_90 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [11347.056, 12345.885, 1.1750857], [], 0, "CAN_COLLIDE"];
  _vehicle_90 = _this;
  _this setDir 53.007046;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11347.056, 12345.885, 1.1750857];
};

_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [11333.92, 12360.172, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setDir -231.17392;
  _this setPos [11333.92, 12360.172, 0.00010681152];
};

_vehicle_94 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [11334.188, 12353.741, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_94 = _this;
  _this setDir -129.88271;
  _this setPos [11334.188, 12353.741, 6.1035156e-005];
};

_vehicle_96 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [11333.319, 12352.155, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_96 = _this;
  _this setDir -170.28485;
  _this setPos [11333.319, 12352.155, -4.5776367e-005];
};

_vehicle_105 = objNull;
if (true) then
{
  _this = createVehicle ["datsun02Wreck", [11344.639, 12342.079, 1.0493355], [], 0, "CAN_COLLIDE"];
  _vehicle_105 = _this;
  _this setDir -182.5072;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11344.639, 12342.079, 1.0493355];
};

_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_populus3s", [11372.479, 12336.946, 0.096088737], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setDir 69.535782;
  _this setPos [11372.479, 12336.946, 0.096088737];
};

_vehicle_125 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_populus3s", [11380.293, 12349.619, 0.060044393], [], 0, "CAN_COLLIDE"];
  _vehicle_125 = _this;
  _this setPos [11380.293, 12349.619, 0.060044393];
};

_vehicle_128 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_populus3s", [11364.398, 12323.122, 0.11479469], [], 0, "CAN_COLLIDE"];
  _vehicle_128 = _this;
  _this setDir -97.434906;
  _this setPos [11364.398, 12323.122, 0.11479469];
};

_vehicle_134 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11377.917, 12350.842, 0.12860581], [], 0, "CAN_COLLIDE"];
  _vehicle_134 = _this;
  _this setDir -333.40445;
  _this setPos [11377.917, 12350.842, 0.12860581];
};

_vehicle_137 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11375.967, 12347.285, 0.13852501], [], 0, "CAN_COLLIDE"];
  _vehicle_137 = _this;
  _this setDir -328.79764;
  _this setPos [11375.967, 12347.285, 0.13852501];
};

_vehicle_140 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11373.896, 12343.8, 0.14153713], [], 0, "CAN_COLLIDE"];
  _vehicle_140 = _this;
  _this setDir -329.50931;
  _this setPos [11373.896, 12343.8, 0.14153713];
};

_vehicle_143 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11371.832, 12340.288, 0.14790808], [], 0, "CAN_COLLIDE"];
  _vehicle_143 = _this;
  _this setDir -329.50931;
  _this setPos [11371.832, 12340.288, 0.14790808];
};

_vehicle_145 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11369.791, 12336.793, 0.13576107], [], 0, "CAN_COLLIDE"];
  _vehicle_145 = _this;
  _this setDir -329.50931;
  _this setPos [11369.791, 12336.793, 0.13576107];
};

_vehicle_147 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11367.759, 12333.304, 0.13454741], [], 0, "CAN_COLLIDE"];
  _vehicle_147 = _this;
  _this setDir -329.50931;
  _this setPos [11367.759, 12333.304, 0.13454741];
};

_vehicle_149 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11365.7, 12329.803, 0.12005007], [], 0, "CAN_COLLIDE"];
  _vehicle_149 = _this;
  _this setDir -329.50931;
  _this setPos [11365.7, 12329.803, 0.12005007];
};

_vehicle_151 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11363.609, 12326.327, 0.13143206], [], 0, "CAN_COLLIDE"];
  _vehicle_151 = _this;
  _this setDir -327.91275;
  _this setPos [11363.609, 12326.327, 0.13143206];
};

_vehicle_153 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11361.386, 12322.933, 0.14498851], [], 0, "CAN_COLLIDE"];
  _vehicle_153 = _this;
  _this setDir -325.02466;
  _this setPos [11361.386, 12322.933, 0.14498851];
};

_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patnik", [11360.091, 12321.084, 0.13273162], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setDir -323.73926;
  _this setPos [11360.091, 12321.084, 0.13273162];
};

_vehicle_161 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2s", [11324.21, 12318.145, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_161 = _this;
  _this setPos [11324.21, 12318.145, 1.5258789e-005];
};

_vehicle_162 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_quercus3s", [11390.842, 12391.007, -0.3703402], [], 0, "CAN_COLLIDE"];
  _vehicle_162 = _this;
  _this setDir -67.908531;
  _this setPos [11390.842, 12391.007, -0.3703402];
};

_vehicle_166 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_craet1", [11379.628, 12347.552, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_166 = _this;
  _this setPos [11379.628, 12347.552, 3.0517578e-005];
};

_vehicle_169 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [11368.329, 12325.343, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_169 = _this;
  _this setDir 18.243589;
  _this setPos [11368.329, 12325.343, 9.1552734e-005];
};

_vehicle_171 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [11327.37, 12290.568, 0.034176785], [], 0, "CAN_COLLIDE"];
  _vehicle_171 = _this;
  _this setDir -360.58145;
  _this setPos [11327.37, 12290.568, 0.034176785];
};

_vehicle_173 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11356.342, 12354.986, 2.1549673], [], 0, "CAN_COLLIDE"];
  _vehicle_173 = _this;
  _this setDir 26.161257;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11356.342, 12354.986, 2.1549673];
};

_vehicle_175 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11354.56, 12351.353, 2.0273917], [], 0, "CAN_COLLIDE"];
  _vehicle_175 = _this;
  _this setDir 25.820328;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11354.56, 12351.353, 2.0273917];
};

_vehicle_177 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11352.778, 12347.708, 1.887355], [], 0, "CAN_COLLIDE"];
  _vehicle_177 = _this;
  _this setDir 25.820328;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11352.778, 12347.708, 1.887355];
};

_vehicle_182 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11350.985, 12344.056, 1.7468486], [], 0, "CAN_COLLIDE"];
  _vehicle_182 = _this;
  _this setDir 26.161257;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11350.985, 12344.056, 1.7468486];
};

_vehicle_183 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11349.183, 12340.408, 1.6277843], [], 0, "CAN_COLLIDE"];
  _vehicle_183 = _this;
  _this setDir 25.820328;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11349.183, 12340.408, 1.6277843];
};

_vehicle_184 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11347.395, 12336.767, 1.4888835], [], 0, "CAN_COLLIDE"];
  _vehicle_184 = _this;
  _this setDir 25.820328;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11347.395, 12336.767, 1.4888835];
};

_vehicle_188 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11345.626, 12333.099, 1.3710896], [], 0, "CAN_COLLIDE"];
  _vehicle_188 = _this;
  _this setDir 25.761169;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11345.626, 12333.099, 1.3710896];
};

_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11343.85, 12329.455, 1.2450264], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setDir 25.969135;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11343.85, 12329.455, 1.2450264];
};

_vehicle_195 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patnik", [11342.835, 12327.439, 1.1750482], [], 0, "CAN_COLLIDE"];
  _vehicle_195 = _this;
  _this setDir -334.74792;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11342.835, 12327.439, 1.1750482];
};

_vehicle_206 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_misc", [11336.259, 12358.322, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_206 = _this;
  _this setDir -34.579819;
  _this setPos [11336.259, 12358.322, -3.0517578e-005];
};

_vehicle_207 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_paleta", [11334.057, 12355.354, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_207 = _this;
  _this setDir 28.545532;
  _this setVehicleInit "this allowDammage false;this enableSimulation false;";
  _this setPos [11334.057, 12355.354, 3.0517578e-005];
};

_vehicle_208 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [11333.187, 12357.925, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_208 = _this;
  _this setPos [11333.187, 12357.925, 7.6293945e-005];
};

_vehicle_209 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [11332.02, 12353.009, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_209 = _this;
  _this setPos [11332.02, 12353.009, 0.0001373291];
};

_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench1", [11323.374, 12319.986, -0.034882538], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setDir -57.827255;
  _this setPos [11323.374, 12319.986, -0.034882538];
};

_vehicle_217 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench1", [11322.75, 12317.415, -0.038931139], [], 0, "CAN_COLLIDE"];
  _vehicle_217 = _this;
  _this setDir -106.28706;
  _this setPos [11322.75, 12317.415, -0.038931139];
};

_vehicle_219 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square3", [11321.913, 12319.082, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_219 = _this;
  _this setPos [11321.913, 12319.082, 3.0517578e-005];
};

_vehicle_222 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_craet1", [11396.298, 12366.126], [], 0, "CAN_COLLIDE"];
  _vehicle_222 = _this;
  _this setPos [11396.298, 12366.126];
};

_vehicle_227 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [11393.987, 12373.263, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_227 = _this;
  _this setDir 18.243589;
  _this setPos [11393.987, 12373.263, 0.00021362305];
};

_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_salix2s", [11410.11, 12371.008, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setPos [11410.11, 12371.008, 0.00015258789];
};

_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [11410.468, 12383.846, 0.086342551], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setDir -63.446114;
  _this setPos [11410.468, 12383.846, 0.086342551];
};

_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [11416.653, 12397.563, -0.32475674], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setDir -253.75885;
  _this setPos [11416.653, 12397.563, -0.32475674];
};

_vehicle_235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [11422.216, 12403.926, -0.0344024], [], 0, "CAN_COLLIDE"];
  _vehicle_235 = _this;
  _this setDir -409.56668;
  _this setPos [11422.216, 12403.926, -0.0344024];
};

_vehicle_238 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [11405.032, 12376.986, 0.020401206], [], 0, "CAN_COLLIDE"];
  _vehicle_238 = _this;
  _this setDir -52.608543;
  _this setPos [11405.032, 12376.986, 0.020401206];
};

_vehicle_241 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [11397.697, 12372.571, 0.13426413], [], 0, "CAN_COLLIDE"];
  _vehicle_241 = _this;
  _this setDir -19.350262;
  _this setPos [11397.697, 12372.571, 0.13426413];
};

_vehicle_243 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_sambucus", [11418.894, 12400.554, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_243 = _this;
  _this setPos [11418.894, 12400.554, -4.5776367e-005];
};

_vehicle_244 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Shed_Ind02", [11622.393, 12435.499, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_244 = _this;
  _this setDir -68.827881;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11622.393, 12435.499, -1.5258789e-005];
};

_vehicle_245 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_SideRoof", [11625.059, 12440.356, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_245 = _this;
  _this setDir -68.104393;
  _this setPos [11625.059, 12440.356, 1.5258789e-005];
};

_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [11621.117, 12400.045, -0.058565751], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setPos [11621.117, 12400.045, -0.058565751];
};

_vehicle_247 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Bo", [11616.007, 12405.556, -0.087858096], [], 0, "CAN_COLLIDE"];
  _vehicle_247 = _this;
  _this setDir 29.09869;
  _this setPos [11616.007, 12405.556, -0.087858096];
};

_vehicle_249 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [11609.64, 12389.114, -0.06446901], [], 0, "CAN_COLLIDE"];
  _vehicle_249 = _this;
  _this setDir -160.07086;
  _this setPos [11609.64, 12389.114, -0.06446901];
};

_vehicle_252 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [11632.214, 12418.49, -0.073188595], [], 0, "CAN_COLLIDE"];
  _vehicle_252 = _this;
  _this setDir -361.23132;
  _this setPos [11632.214, 12418.49, -0.073188595];
};

_vehicle_257 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11404.134, 12790.737, 0.060106646], [], 0, "CAN_COLLIDE"];
  _vehicle_257 = _this;
  _this setDir 349.38559;
  _this setPos [11404.134, 12790.737, 0.060106646];
};

_vehicle_258 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [11397.894, 12778.237, 0.00032043457], [], 0, "CAN_COLLIDE"];
  _vehicle_258 = _this;
  _this setPos [11397.894, 12778.237, 0.00032043457];
};

_vehicle_260 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11407.219, 12795.603, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_260 = _this;
  _this setPos [11407.219, 12795.603, 7.6293945e-005];
};

_vehicle_263 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [11398.604, 12785.082, 0.27444816], [], 0, "CAN_COLLIDE"];
  _vehicle_263 = _this;
  _this setDir -432.7468;
  _this setPos [11398.604, 12785.082, 0.27444816];
};

_vehicle_266 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [11401.543, 12793.559, -0.058347404], [], 0, "CAN_COLLIDE"];
  _vehicle_266 = _this;
  _this setDir -609.31696;
  _this setPos [11401.543, 12793.559, -0.058347404];
};

_vehicle_272 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [11409.953, 12800.516, 0.073278412], [], 0, "CAN_COLLIDE"];
  _vehicle_272 = _this;
  _this setDir 128.77248;
  _this setPos [11409.953, 12800.516, 0.073278412];
};

_vehicle_275 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Stone", [11406.246, 12801.396, 0.00697954], [], 0, "CAN_COLLIDE"];
  _vehicle_275 = _this;
  _this setDir -791.59796;
  _this setPos [11406.246, 12801.396, 0.00697954];
};

_vehicle_278 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [11403.271, 12783.334, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_278 = _this;
  _this setPos [11403.271, 12783.334, 3.0517578e-005];
};

_vehicle_287 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Panelak", [11235.345, 12174.393, 0.33339533], [], 0, "CAN_COLLIDE"];
  _vehicle_287 = _this;
  _this setDir -327.0134;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11235.345, 12174.393, 0.33339533];
};

_vehicle_289 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Panelak", [11249.539, 12161.921, 0.095474377], [], 0, "CAN_COLLIDE"];
  _vehicle_289 = _this;
  _this setDir -242.23604;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11249.539, 12161.921, 0.095474377];
};

_vehicle_292 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11243.164, 12196.562, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_292 = _this;
  _this setDir -311.21585;
  _this setPos [11243.164, 12196.562, -1.5258789e-005];
};

_vehicle_295 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11240.634, 12193.561], [], 0, "CAN_COLLIDE"];
  _vehicle_295 = _this;
  _this setDir -328.80685;
  _this setPos [11240.634, 12193.561];
};

_vehicle_298 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11238.564, 12190.073, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_298 = _this;
  _this setDir -330.03091;
  _this setPos [11238.564, 12190.073, -1.5258789e-005];
};

_vehicle_301 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patnik", [11237.428, 12188.12, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_301 = _this;
  _this setDir -327.32532;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11237.428, 12188.12, 0.00010681152];
};

_vehicle_303 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4_D", [11270.271, 12187.712, 0.078061849], [], 0, "CAN_COLLIDE"];
  _vehicle_303 = _this;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [11270.271, 12187.712, 0.078061849];
};

_vehicle_304 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [11264.868, 12189.502, -0.25326604], [], 0, "CAN_COLLIDE"];
  _vehicle_304 = _this;
  _this setDir 27.600693;
  _this setPos [11264.868, 12189.502, -0.25326604];
};

_vehicle_306 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_4", [11255.914, 12195.594, -0.15410687], [], 0, "CAN_COLLIDE"];
  _vehicle_306 = _this;
  _this setDir -117.26082;
  _this setPos [11255.914, 12195.594, -0.15410687];
};

_vehicle_320 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11246.641, 12197.767], [], 0, "CAN_COLLIDE"];
  _vehicle_320 = _this;
  _this setDir -268.72299;
  _this setPos [11246.641, 12197.767];
};

_vehicle_323 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Patniky", [11250.367, 12196.73, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_323 = _this;
  _this setDir -241.71883;
  _this setPos [11250.367, 12196.73, 1.5258789e-005];
};

_vehicle_326 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind1_L", [11259.561, 12192.479, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_326 = _this;
  _this setDir -150.55795;
  _this setPos [11259.561, 12192.479, 9.1552734e-005];
};

_vehicle_328 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_3", [11258.271, 12193.087, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_328 = _this;
  _this setDir 28.861897;
  _this setPos [11258.271, 12193.087, 1.5258789e-005];
};

_vehicle_329 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndCnc_End_2", [11257.716, 12193.353, -0.19211197], [], 0, "CAN_COLLIDE"];
  _vehicle_329 = _this;
  _this setDir -154.08897;
  _this setPos [11257.716, 12193.353, -0.19211197];
};

_vehicle_331 = objNull;
if (true) then
{
  _this = createVehicle ["datsun01Wreck", [11243.317, 12191.525], [], 0, "CAN_COLLIDE"];
  _vehicle_331 = _this;
  _this setDir 125.09939;
  _this setPos [11243.317, 12191.525];
};

_vehicle_340 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [11270.008, 12210.314, -0.019465569], [], 0, "CAN_COLLIDE"];
  _vehicle_340 = _this;
  _this setDir -458.35577;
  _this setVehicleInit "this setvectorup [0.1,0.5,0]";
  _this setPos [11270.008, 12210.314, -0.019465569];
};

_vehicle_353 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_populus3s", [11250.674, 12142.285, 0.0005645752], [], 0, "CAN_COLLIDE"];
  _vehicle_353 = _this;
  _this setDir -97.434906;
  _this setPos [11250.674, 12142.285, 0.0005645752];
};

_vehicle_365 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2s", [11235.205, 12193.438, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_365 = _this;
  _this setDir 17.539228;
  _this setPos [11235.205, 12193.438, -3.0517578e-005];
};

_vehicle_366 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [11601.225, 12446.738, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_366 = _this;
  _this setDir 109.24969;
  _this setPos [11601.225, 12446.738, 4.5776367e-005];
};

_vehicle_368 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [11599.157, 12440.85, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_368 = _this;
  _this setDir 109.24969;
  _this setPos [11599.157, 12440.85, 0.00010681152];
};

_vehicle_370 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [11597.21, 12434.973, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_370 = _this;
  _this setDir 107.23083;
  _this setPos [11597.21, 12434.973, -1.5258789e-005];
};

_vehicle_375 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [11596.63, 12433.184, -0.11479776], [], 0, "CAN_COLLIDE"];
  _vehicle_375 = _this;
  _this setDir -73.56588;
  _this setPos [11596.63, 12433.184, -0.11479776];
};

_vehicle_378 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [11598.972, 12429.292, -0.097373635], [], 0, "CAN_COLLIDE"];
  _vehicle_378 = _this;
  _this setDir 201.16747;
  _this setPos [11598.972, 12429.292, -0.097373635];
};

_vehicle_380 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel4", [11268.931, 12213.346, -0.29440057], [], 0, "CAN_COLLIDE"];
  _vehicle_380 = _this;
  _this setVehicleInit "this setvectorup [0.1,0.1,0]";
  _this setPos [11268.931, 12213.346, -0.29440057];
};

_vehicle_382 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel4", [11270.327, 12212.865, -0.29241362], [], 0, "CAN_COLLIDE"];
  _vehicle_382 = _this;
  _this setVehicleInit "this setvectorup [0.1,0.9,0]";
  _this setPos [11270.327, 12212.865, -0.29241362];
};

_vehicle_385 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel4", [11268.021, 12214.184, -0.27502903], [], 0, "CAN_COLLIDE"];
  _vehicle_385 = _this;
  _this setVehicleInit "this setvectorup [0.1,1.5,0]";
  _this setPos [11268.021, 12214.184, -0.27502903];
};

_vehicle_387 = objNull;
if (true) then
{
  _this = createVehicle ["Barrel5", [11269.788, 12214.78, -0.28182131], [], 0, "CAN_COLLIDE"];
  _vehicle_387 = _this;
  _this setDir -108.27442;
  _this setVehicleInit "this setvectorup [0.9,0.5,0]";
  _this setPos [11269.788, 12214.78, -0.28182131];
};

_vehicle_388 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_Crate_wood", [11258.426, 12206.348, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_388 = _this;
  _this setDir -59.869324;
  _this setPos [11258.426, 12206.348, 3.0517578e-005];
};

_vehicle_390 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_Crate_wood", [11258.947, 12207.544, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_390 = _this;
  _this setDir -154.48875;
  _this setPos [11258.947, 12207.544, 1.5258789e-005];
};

_vehicle_392 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [11270.699, 12211.978, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_392 = _this;
  _this setPos [11270.699, 12211.978, -6.1035156e-005];
};

_vehicle_393 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [11269.475, 12214.016, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_393 = _this;
  _this setPos [11269.475, 12214.016, 1.5258789e-005];
};

_vehicle_398 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_SideRoof", [11283.65, 12211.266, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_398 = _this;
  _this setDir -169.18829;
  _this setPos [11283.65, 12211.266, -1.5258789e-005];
};

_vehicle_403 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_Crate_wood", [11282.237, 12214.429, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_403 = _this;
  _this setDir -58.407749;
  _this setPos [11282.237, 12214.429, 1.5258789e-005];
};

_vehicle_408 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_Crate_wood", [11283.688, 12217.982], [], 0, "CAN_COLLIDE"];
  _vehicle_408 = _this;
  _this setDir -189.39333;
  _this setVehicleInit "this setvectorup [0.1,0.5,0]";
  _this setPos [11283.688, 12217.982];
};

_vehicle_420 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [11219.398, 12162.564, -0.27363795], [], 0, "CAN_COLLIDE"];
  _vehicle_420 = _this;
  _this setDir 39.275791;
  _this setPos [11219.398, 12162.564, -0.27363795];
};

_vehicle_422 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [11223.06, 12159.239, -0.32718986], [], 0, "CAN_COLLIDE"];
  _vehicle_422 = _this;
  _this setDir 44.903137;
  _this setPos [11223.06, 12159.239, -0.32718986];
};

_vehicle_425 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [11215.49, 12165.549, -0.22936052], [], 0, "CAN_COLLIDE"];
  _vehicle_425 = _this;
  _this setDir 35.615211;
  _this setPos [11215.49, 12165.549, -0.22936052];
};

_vehicle_428 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [11226.549, 12155.753, -0.3882868], [], 0, "CAN_COLLIDE"];
  _vehicle_428 = _this;
  _this setDir 44.903137;
  _this setPos [11226.549, 12155.753, -0.3882868];
};

_vehicle_430 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_End1", [11212.358, 12167.785, -0.22253457], [], 0, "CAN_COLLIDE"];
  _vehicle_430 = _this;
  _this setDir 35.307404;
  _this setPos [11212.358, 12167.785, -0.22253457];
};

_vehicle_431 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_End2", [11229.226, 12153.013, -0.38326243], [], 0, "CAN_COLLIDE"];
  _vehicle_431 = _this;
  _this setDir -133.69095;
  _this setPos [11229.226, 12153.013, -0.38326243];
};

_vehicle_433 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_End2", [11231.839, 12150.517, -0.23118725], [], 0, "CAN_COLLIDE"];
  _vehicle_433 = _this;
  _this setDir -313.67947;
  _this setPos [11231.839, 12150.517, -0.23118725];
};

_vehicle_436 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [11234.436, 12147.693, -0.23312858], [], 0, "CAN_COLLIDE"];
  _vehicle_436 = _this;
  _this setDir 47.810097;
  _this setPos [11234.436, 12147.693, -0.23312858];
};

_vehicle_439 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [11237.901, 12144.142, -0.25766447], [], 0, "CAN_COLLIDE"];
  _vehicle_439 = _this;
  _this setDir 43.665428;
  _this setPos [11237.901, 12144.142, -0.25766447];
};

_vehicle_445 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_End2", [11240.65, 12141.4, -0.28910893], [], 0, "CAN_COLLIDE"];
  _vehicle_445 = _this;
  _this setDir -133.69095;
  _this setPos [11240.65, 12141.4, -0.28910893];
};

_vehicle_448 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [11259.359, 12168.337, -0.33633307], [], 0, "CAN_COLLIDE"];
  _vehicle_448 = _this;
  _this setDir 118.72887;
  _this setPos [11259.359, 12168.337, -0.33633307];
};

_vehicle_451 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_End2", [11261.244, 12171.61, -0.33170003], [], 0, "CAN_COLLIDE"];
  _vehicle_451 = _this;
  _this setDir -238.77698;
  _this setPos [11261.244, 12171.61, -0.33170003];
};

_vehicle_454 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [11603.855, 12425.772, -0.11338878], [], 0, "CAN_COLLIDE"];
  _vehicle_454 = _this;
  _this setDir 230.15543;
  _this setPos [11603.855, 12425.772, -0.11338878];
};

_vehicle_457 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [11605.884, 12420.428, -0.099191383], [], 0, "CAN_COLLIDE"];
  _vehicle_457 = _this;
  _this setDir -90.694107;
  _this setPos [11605.884, 12420.428, -0.099191383];
};

_vehicle_460 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [11605.059, 12414.433, -0.1170404], [], 0, "CAN_COLLIDE"];
  _vehicle_460 = _this;
  _this setDir -73.135353;
  _this setPos [11605.059, 12414.433, -0.1170404];
};

_vehicle_463 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [11611.67, 12405.776, -0.055259533], [], 0, "CAN_COLLIDE"];
  _vehicle_463 = _this;
  _this setDir 109.04238;
  _this setPos [11611.67, 12405.776, -0.055259533];
};

_vehicle_466 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [11609.467, 12399.909, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_466 = _this;
  _this setDir 112.79254;
  _this setPos [11609.467, 12399.909, 0.00010681152];
};

_vehicle_471 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind2A_L", [11608.402, 12410.054, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_471 = _this;
  _this setDir 20.26931;
  _this setPos [11608.402, 12410.054, 9.1552734e-005];
};

_vehicle_472 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Gate_Ind2A_R", [11608.445, 12410.064, -0.058442492], [], 0, "CAN_COLLIDE"];
  _vehicle_472 = _this;
  _this setDir 22.760908;
  _this setPos [11608.445, 12410.064, -0.058442492];
};

_vehicle_474 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [11607.112, 12394.298, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_474 = _this;
  _this setDir 112.79254;
  _this setPos [11607.112, 12394.298, 7.6293945e-005];
};

_vehicle_479 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [11601.344, 12381.631, -0.095193848], [], 0, "CAN_COLLIDE"];
  _vehicle_479 = _this;
  _this setDir -65.815269;
  _this setPos [11601.344, 12381.631, -0.095193848];
};

_vehicle_482 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [11604.663, 12388.762], [], 0, "CAN_COLLIDE"];
  _vehicle_482 = _this;
  _this setDir -245.41568;
  _this setPos [11604.663, 12388.762];
};

_vehicle_499 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4", [11599.892, 12378.499, -0.042650223], [], 0, "CAN_COLLIDE"];
  _vehicle_499 = _this;
  _this setDir -65.186813;
  _this setVehicleInit "this setvectorup [0,0,0.1]";
  _this setPos [11599.892, 12378.499, -0.042650223];
};

_vehicle_501 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_VilVar2_4", [11602.539, 12378.782], [], 0, "CAN_COLLIDE"];
  _vehicle_501 = _this;
  _this setDir 9.713006;
  _this setVehicleInit "this setvectorup [0,0,0.1]";
  _this setPos [11602.539, 12378.782];
};

_vehicle_510 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [11277.895, 12199.617, -0.075097203], [], 0, "CAN_COLLIDE"];
  _vehicle_510 = _this;
  _this setDir 32.496109;
  _this setPos [11277.895, 12199.617, -0.075097203];
};

_vehicle_511 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_misc", [11257.595, 12168.245, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_511 = _this;
  _this setDir -246.85748;
  _this setPos [11257.595, 12168.245, 1.5258789e-005];
};

_vehicle_512 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Kontejner", [11256.072, 12168.447, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_512 = _this;
  _this setDir -69.029404;
  _this setPos [11256.072, 12168.447, -3.0517578e-005];
};

_vehicle_514 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Kontejner", [11259.897, 12171.063, -0.015186885], [], 0, "CAN_COLLIDE"];
  _vehicle_514 = _this;
  _this setDir -161.41843;
  _this setPos [11259.897, 12171.063, -0.015186885];
};

_vehicle_517 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [11258.154, 12171.074, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_517 = _this;
  _this setPos [11258.154, 12171.074, 4.5776367e-005];
};

_vehicle_519 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [11255.344, 12170.027, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_519 = _this;
  _this setPos [11255.344, 12170.027, 3.0517578e-005];
};

_vehicle_521 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [11238.709, 12180.79, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_521 = _this;
  _this setPos [11238.709, 12180.79, -0.00010681152];
};

_vehicle_523 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [11260.132, 12206.365, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_523 = _this;
  _this setPos [11260.132, 12206.365, 3.0517578e-005];
};

_vehicle_525 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [11256.659, 12159.15, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_525 = _this;
  _this setPos [11256.659, 12159.15, 3.0517578e-005];
};

_vehicle_528 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_salix2s", [11237.469, 12190.087, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_528 = _this;
  _this setPos [11237.469, 12190.087, -1.5258789e-005];
};

_vehicle_530 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [11349.747, 12330.072, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_530 = _this;
  _this setDir -63.798119;
  _this setPos [11349.747, 12330.072, -9.1552734e-005];
};

_vehicle_532 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [11355.285, 12327.334], [], 0, "CAN_COLLIDE"];
  _vehicle_532 = _this;
  _this setDir -63.832283;
  _this setPos [11355.285, 12327.334];
};

_vehicle_535 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [11353.281, 12337.08, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_535 = _this;
  _this setDir -63.798119;
  _this setPos [11353.281, 12337.08, 9.1552734e-005];
};

_vehicle_536 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [11358.817, 12334.344, 0.010650635], [], 0, "CAN_COLLIDE"];
  _vehicle_536 = _this;
  _this setDir -63.832283;
  _this setPos [11358.817, 12334.344, 0.010650635];
};

_vehicle_543 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [11356.739, 12344.146], [], 0, "CAN_COLLIDE"];
  _vehicle_543 = _this;
  _this setDir -63.798119;
  _this setPos [11356.739, 12344.146];
};

_vehicle_544 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [11362.277, 12341.409, -0.030670166], [], 0, "CAN_COLLIDE"];
  _vehicle_544 = _this;
  _this setDir -63.832283;
  _this setPos [11362.277, 12341.409, -0.030670166];
};

_vehicle_545 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [11360.274, 12351.157, -0.012649536], [], 0, "CAN_COLLIDE"];
  _vehicle_545 = _this;
  _this setDir -63.798119;
  _this setPos [11360.274, 12351.157, -0.012649536];
};

_vehicle_546 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [11365.812, 12348.42, -0.059127808], [], 0, "CAN_COLLIDE"];
  _vehicle_546 = _this;
  _this setDir -63.832283;
  _this setPos [11365.812, 12348.42, -0.059127808];
};

_vehicle_551 = objNull;
if (true) then
{
  _this = createVehicle ["LADAWreck", [11348.896, 12332.766, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_551 = _this;
  _this setDir -71.866302;
  _this setPos [11348.896, 12332.766, 1.5258789e-005];
};

_vehicle_552 = objNull;
if (true) then
{
  _this = createVehicle ["hiluxWreck", [11355.79, 12346.781, -0.037518233], [], 0, "CAN_COLLIDE"];
  _vehicle_552 = _this;
  _this setDir -242.0695;
  _this setPos [11355.79, 12346.781, -0.037518233];
};

_vehicle_554 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [11359.564, 12356.326, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_554 = _this;
  _this setDir 27.605587;
  _this setPos [11359.564, 12356.326, 0.00012207031];
};

_vehicle_556 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_CGryLow_5", [11345.343, 12327.474, -0.22855735], [], 0, "CAN_COLLIDE"];
  _vehicle_556 = _this;
  _this setDir 25.28801;
  _this setPos [11345.343, 12327.474, -0.22855735];
};

};
