// Written by Sigma for Stomping Grounds PvE

sched_keyrings = {
	private ["_player", "_playerUID", "_sigKeyRing", "_storeSKR", "_storeSKRexec", "_skipPLR", "_sigKeyRingStr"];

	{
		_skipPLR = 0;
		_player = _x;
		_playerUID = getPlayerUID _player;

		_sigKeyRing = _player getVariable ["sigmaKeyRing", [""]];

		// Skip if the keyring is empty
		if ((count _sigKeyRing == 1) && ((_sigKeyRing select 0) == "")) then {
			_skipPLR = 1;
		};

		if (_skipPLR == 0) then {
			_sigKeyRingStr = str _sigKeyRing;
			_storeSKR = format ["UPDATE player_data SET Sigma_KeyRing = '%2' WHERE PlayerUID = '%1'",_playerUID,_sigKeyRingStr];
			_storeSKRexec = [_storeSKR, 1, true] call fn_asyncCall;
		};

	} forEach playableUnits;

	objNull
};
