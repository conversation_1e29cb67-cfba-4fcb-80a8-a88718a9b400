if (isServer) then {


_vehicle_1 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2270.7625, 10837.391], [], 0, "CAN_COLLIDE"];
  _vehicle_1 = _this;
  _this setPos [2270.7625, 10837.391];
};

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2270.9988, 10826.276], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir -1.1194952;
  _this setPos [2270.9988, 10826.276];
};

_vehicle_6 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2262.593, 10826.354, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_6 = _this;
  _this setDir 5.4365845;
  _this setPos [2262.593, 10826.354, -3.0517578e-005];
};

_vehicle_8 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2262.5974, 10837.107], [], 0, "CAN_COLLIDE"];
  _vehicle_8 = _this;
  _this setPos [2262.5974, 10837.107];
};

_vehicle_16 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [2279.9155, 10827.086, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_16 = _this;
  _this setPos [2279.9155, 10827.086, -9.1552734e-005];
};

_vehicle_17 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart_EP1", [2278.3086, 10836.917, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_17 = _this;
  _this setDir -182.87387;
  _this setPos [2278.3086, 10836.917, 6.1035156e-005];
};

_vehicle_18 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Guardhouse", [2253.6309, 10841.117, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_18 = _this;
  _this setDir -90.766418;
  _this setPos [2253.6309, 10841.117, 6.1035156e-005];
};

_vehicle_19 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2254.8486, 10826.433], [], 0, "CAN_COLLIDE"];
  _vehicle_19 = _this;
  _this setPos [2254.8486, 10826.433];
};

_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [2250.0408, 10835.201, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir -89.947121;
  _this setPos [2250.0408, 10835.201, 6.1035156e-005];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [2283.2866, 10835.563], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir -96.348045;
  _this setPos [2283.2866, 10835.563];
};

_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2239.1204, 10825.454, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir -1.3347;
  _this setPos [2239.1204, 10825.454, 9.1552734e-005];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2267.5386, 10842.439, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir -88.810982;
  _this setPos [2267.5386, 10842.439, 6.1035156e-005];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2259.5139, 10812.631, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir 91.27655;
  _this setPos [2259.5139, 10812.631, -3.0517578e-005];
};

_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2267.3538, 10848.82, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setDir 93.341263;
  _this setPos [2267.3538, 10848.82, 3.0517578e-005];
};

_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2236.1602, 10820.354, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir 90.265709;
  _this setPos [2236.1602, 10820.354, -3.0517578e-005];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2239.4375, 10808.785, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setPos [2239.4375, 10808.785, -3.0517578e-005];
};

_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2254.1416, 10809.534, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setPos [2254.1416, 10809.534, 3.0517578e-005];
};

_vehicle_37 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2259.3018, 10821.072, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_37 = _this;
  _this setDir 90.157715;
  _this setPos [2259.3018, 10821.072, 6.1035156e-005];
};

_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["Land_repair_center", [2253.5859, 10815.482, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setDir 0.56491464;
  _this setPos [2253.5859, 10815.482, -3.0517578e-005];
};

_vehicle_40 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2236.3606, 10812.951, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_40 = _this;
  _this setDir 90.827248;
  _this setPos [2236.3606, 10812.951, 0.00012207031];
};

_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fuel_tank_big", [2242.032, 10813.104, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setDir 87.515961;
  _this setPos [2242.032, 10813.104, 6.1035156e-005];
};

_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [2265.885, 10858.273, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setDir -177.57123;
  _this setPos [2265.885, 10858.273, 0.00018310547];
};

_vehicle_56 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [2257.739, 10804.476], [], 0, "CAN_COLLIDE"];
  _vehicle_56 = _this;
  _this setDir 179.78925;
  _this setPos [2257.739, 10804.476];
};

_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2256.2361, 10863.346, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setDir 5.4951143;
  _this setPos [2256.2361, 10863.346, 6.1035156e-005];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2239.8892, 10864.136, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir 4.1648855;
  _this setPos [2239.8892, 10864.136, 6.1035156e-005];
};

_vehicle_61 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2242.1548, 10830.673, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_61 = _this;
  _this setDir 91.759613;
  _this setPos [2242.1548, 10830.673, 0.00012207031];
};

_vehicle_63 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2248.1858, 10863.748, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_63 = _this;
  _this setDir 4.1215582;
  _this setPos [2248.1858, 10863.748, 3.0517578e-005];
};

_vehicle_65 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2242.1399, 10838.908, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_65 = _this;
  _this setDir 92.826775;
  _this setPos [2242.1399, 10838.908, 9.1552734e-005];
};

_vehicle_67 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2238.9343, 10843.979], [], 0, "CAN_COLLIDE"];
  _vehicle_67 = _this;
  _this setPos [2238.9343, 10843.979];
};

_vehicle_71 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [2253.2554, 10857.985, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_71 = _this;
  _this setDir 178.33101;
  _this setPos [2253.2554, 10857.985, 0.00012207031];
};

_vehicle_72 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [2246.0471, 10857.914, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_72 = _this;
  _this setDir 178.07764;
  _this setPos [2246.0471, 10857.914, 0.00012207031];
};

_vehicle_73 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [2239.5332, 10857.894, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_73 = _this;
  _this setDir 179.58653;
  _this setPos [2239.5332, 10857.894, 0.00015258789];
};

_vehicle_76 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [2236.3115, 10837.605, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_76 = _this;
  _this setDir 89.13475;
  _this setPos [2236.3115, 10837.605, 0.00021362305];
};

_vehicle_77 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [2236.6306, 10829.888, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_77 = _this;
  _this setDir 88.601318;
  _this setPos [2236.6306, 10829.888, 0.00021362305];
};

_vehicle_78 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2235.6396, 10846.98, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_78 = _this;
  _this setDir 89.777565;
  _this setPos [2235.6396, 10846.98, 0.00030517578];
};

_vehicle_80 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2250.7871, 10800.2, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_80 = _this;
  _this setDir 5.0732994;
  _this setPos [2250.7871, 10800.2, 9.1552734e-005];
};

_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2242.4482, 10800.743, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setDir 6.9151173;
  _this setPos [2242.4482, 10800.743, -3.0517578e-005];
};

_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2234.4128, 10801.388, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setDir 5.8993549;
  _this setPos [2234.4128, 10801.388, 3.0517578e-005];
};

_vehicle_86 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2231.6831, 10863.998, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_86 = _this;
  _this setDir -1.601032;
  _this setPos [2231.6831, 10863.998, 0.00012207031];
};

_vehicle_88 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2223.3604, 10863.332, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_88 = _this;
  _this setDir -3.2583158;
  _this setPos [2223.3604, 10863.332, 0.00015258789];
};

_vehicle_90 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2219.1145, 10865.925, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_90 = _this;
  _this setDir 90.741287;
  _this setPos [2219.1145, 10865.925, 9.1552734e-005];
};

_vehicle_92 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [2233.4299, 10858.017, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_92 = _this;
  _this setDir 179.04044;
  _this setPos [2233.4299, 10858.017, -3.0517578e-005];
};

_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [2226.2139, 10857.82, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setDir 179.20949;
  _this setPos [2226.2139, 10857.82, 6.1035156e-005];
};

_vehicle_96 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2219.4067, 10857.673, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_96 = _this;
  _this setDir 89.706993;
  _this setPos [2219.4067, 10857.673, 0.00021362305];
};

_vehicle_98 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2219.6504, 10849.295, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_98 = _this;
  _this setDir 89.635002;
  _this setPos [2219.6504, 10849.295, 6.1035156e-005];
};

_vehicle_100 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2230.4243, 10846.629, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_100 = _this;
  _this setPos [2230.4243, 10846.629, 9.1552734e-005];
};

_vehicle_103 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [2208.2019, 10858.244, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_103 = _this;
  _this setPos [2208.2019, 10858.244, 6.1035156e-005];
};

_vehicle_104 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2206.3606, 10849.95, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_104 = _this;
  _this setDir 90.526802;
  _this setPos [2206.3606, 10849.95, 0.00012207031];
};

_vehicle_106 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2206.4941, 10841.735, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_106 = _this;
  _this setDir -85.912621;
  _this setPos [2206.4941, 10841.735, 0.00021362305];
};

_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [2226.2878, 10815.227, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setDir -91.392838;
  _this setPos [2226.2878, 10815.227, 9.1552734e-005];
};

_vehicle_109 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [2216.9138, 10815.247, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_109 = _this;
  _this setDir -90.547531;
  _this setPos [2216.9138, 10815.247, -3.0517578e-005];
};

_vehicle_110 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [2207.7527, 10815.222, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_110 = _this;
  _this setDir -89.950462;
  _this setPos [2207.7527, 10815.222, 3.0517578e-005];
};

_vehicle_111 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2226.1292, 10801.535, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_111 = _this;
  _this setDir -0.87473017;
  _this setPos [2226.1292, 10801.535, 3.0517578e-005];
};

_vehicle_113 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2217.9517, 10801.427], [], 0, "CAN_COLLIDE"];
  _vehicle_113 = _this;
  _this setDir 4.0225062;
  _this setPos [2217.9517, 10801.427];
};

_vehicle_115 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2209.5764, 10801.806, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_115 = _this;
  _this setDir 3.8035104;
  _this setPos [2209.5764, 10801.806, -6.1035156e-005];
};

_vehicle_117 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2201.3364, 10802.294, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_117 = _this;
  _this setDir 6.9033446;
  _this setPos [2201.3364, 10802.294, -9.1552734e-005];
};

_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [2197.6152, 10807.526, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setPos [2197.6152, 10807.526, -3.0517578e-005];
};

_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2219.7163, 10841.047], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir 91.835747;
  _this setPos [2219.7163, 10841.047];
};

_vehicle_122 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2198.1462, 10816.957, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_122 = _this;
  _this setDir 90.528412;
  _this setPos [2198.1462, 10816.957, -3.0517578e-005];
};

_vehicle_124 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2202.2458, 10837.39, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_124 = _this;
  _this setDir -4.392684;
  _this setPos [2202.2458, 10837.39, -3.0517578e-005];
};

_vehicle_126 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2197.7932, 10833.537, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_126 = _this;
  _this setDir 95.654564;
  _this setPos [2197.7932, 10833.537, -3.0517578e-005];
};

_vehicle_129 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrates_NoInteractive_Large", [2219.3887, 10836.002, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_129 = _this;
  _this setPos [2219.3887, 10836.002, 3.0517578e-005];
};

_vehicle_130 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Antenna", [2262.4739, 10842.828], [], 0, "CAN_COLLIDE"];
  _vehicle_130 = _this;
  _this setPos [2262.4739, 10842.828];
};

_vehicle_131 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNet_EAST", [2214.321, 10842.482, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_131 = _this;
  _this setDir 268.82498;
  _this setPos [2214.321, 10842.482, -3.0517578e-005];
};

_vehicle_133 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Well_C_EP1", [2228.1665, 10843.68, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_133 = _this;
  _this setPos [2228.1665, 10843.68, -3.0517578e-005];
};

_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2197.6169, 10825.387], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir 88.098961;
  _this setPos [2197.6169, 10825.387];
};

_vehicle_137 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2224.5869, 10838.28], [], 0, "CAN_COLLIDE"];
  _vehicle_137 = _this;
  _this setPos [2224.5869, 10838.28];
};

_vehicle_140 = objNull;
if (true) then
{
  _this = createVehicle ["Land_water_tank", [2202.074, 10832.321, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_140 = _this;
  _this setDir 85.296265;
  _this setPos [2202.074, 10832.321, -0.00015258789];
};

_vehicle_141 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2262.415, 10862.805], [], 0, "CAN_COLLIDE"];
  _vehicle_141 = _this;
  _this setDir 3.3111284;
  _this setPos [2262.415, 10862.805];
};

_vehicle_144 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2206.3237, 10867.58], [], 0, "CAN_COLLIDE"];
  _vehicle_144 = _this;
  _this setDir 92.806366;
  _this setPos [2206.3237, 10867.58];
};

_vehicle_146 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2206.4292, 10875.886], [], 0, "CAN_COLLIDE"];
  _vehicle_146 = _this;
  _this setDir 91.58902;
  _this setPos [2206.4292, 10875.886];
};

_vehicle_148 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2217.4543, 10881.333, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_148 = _this;
  _this setDir 1.2467076;
  _this setPos [2217.4543, 10881.333, 3.0517578e-005];
};

_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2209.4626, 10881.161, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setPos [2209.4626, 10881.161, 3.0517578e-005];
};

_vehicle_156 = objNull;
if (true) then
{
  _this = createVehicle ["Land_bags_EP1", [2209.3936, 10878.751, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_156 = _this;
  _this setPos [2209.3936, 10878.751, 3.0517578e-005];
};

_vehicle_157 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [2219.2805, 10871.814, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_157 = _this;
  _this setDir 88.65921;
  _this setPos [2219.2805, 10871.814, 6.1035156e-005];
};

_vehicle_159 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2225.5286, 10881.437], [], 0, "CAN_COLLIDE"];
  _vehicle_159 = _this;
  _this setPos [2225.5286, 10881.437];
};

_vehicle_161 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2252.3367, 10868.573, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_161 = _this;
  _this setDir 90.930122;
  _this setPos [2252.3367, 10868.573, 3.0517578e-005];
};

_vehicle_163 = objNull;
if (true) then
{
  _this = createVehicle ["HeliH", [2239.6597, 10878.212, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_163 = _this;
  _this setPos [2239.6597, 10878.212, 6.1035156e-005];
};

_vehicle_164 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2251.9473, 10885.267, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_164 = _this;
  _this setDir 89.113289;
  _this setPos [2251.9473, 10885.267, -6.1035156e-005];
};

_vehicle_166 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2228.6614, 10886.098, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_166 = _this;
  _this setDir 91.037552;
  _this setPos [2228.6614, 10886.098, 6.1035156e-005];
};

_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2252.1409, 10876.973], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setDir 90.622368;
  _this setPos [2252.1409, 10876.973];
};

_vehicle_170 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2231.6172, 10891.391, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_170 = _this;
  _this setPos [2231.6172, 10891.391, 6.1035156e-005];
};

_vehicle_172 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2246.6978, 10888.769, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_172 = _this;
  _this setDir 6.1963167;
  _this setPos [2246.6978, 10888.769, -6.1035156e-005];
};

_vehicle_174 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [2239.092, 10890.282, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_174 = _this;
  _this setDir 20.716139;
  _this setPos [2239.092, 10890.282, 3.0517578e-005];
};
};
