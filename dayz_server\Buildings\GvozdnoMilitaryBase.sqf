//This spawns in a military base north west of Gvozdno aprox. 078 027.
//Created by Nox 2017-02-01. Contact: DayZ Europa, Dayz mod discord or by email: <EMAIL>
//Copyright by the DayZ Mod dev team.

_vehicle_7 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [8217.8623, 12045.383];


  _vehicle_7 = _this;
  _this setDir -87.636604;
  _this setPos [8217.8623, 12045.383];
};

_vehicle_13 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_6konec" createVehicle [8223.8506, 12045.236, 9.1552734e-005];


  _vehicle_13 = _this;
  _this setDir -89.042412;
  _this setPos [8223.8506, 12045.236, 9.1552734e-005];
};

_vehicle_33 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [8198.6875, 12048.641, 0.0004119873];


  _vehicle_33 = _this;
  _this setDir -71.856621;
  _this setPos [8198.6875, 12048.641, 0.0004119873];
};

_vehicle_36 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_22_50" createVehicle [8181.145, 12057.059, 0.00015258789];


  _vehicle_36 = _this;
  _this setDir -56.933987;
  _this setPos [8181.145, 12057.059, 0.00015258789];
};

_vehicle_39 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [8154.0176, 12085.092, 0.00018310547];


  _vehicle_39 = _this;
  _this setDir 130.08002;
  _this setPos [8154.0176, 12085.092, 0.00018310547];
};

_vehicle_41 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [8137.2593, 12095.128, 0.00015258789];


  _vehicle_41 = _this;
  _this setDir 113.8353;
  _this setPos [8137.2593, 12095.128, 0.00015258789];
};

_vehicle_44 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [8046.4663, 12123.255, -0.0002746582];


  _vehicle_44 = _this;
  _this setDir 99.237953;
  _this setPos [8046.4663, 12123.255, -0.0002746582];
};

_vehicle_46 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [8114.2876, 12104.962, 1.5258789e-005];


  _vehicle_46 = _this;
  _this setDir 113.31706;
  _this setPos [8114.2876, 12104.962, 1.5258789e-005];
};

_vehicle_49 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [8071.0942, 12119.239, 0.0001373291];


  _vehicle_49 = _this;
  _this setDir 99.416901;
  _this setPos [8071.0942, 12119.239, 0.0001373291];
};

_vehicle_52 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_7_100" createVehicle [8101.9497, 12109.146, 0.00021362305];


  _vehicle_52 = _this;
  _this setDir 105.47369;
  _this setPos [8101.9497, 12109.146, 0.00021362305];
};

_vehicle_54 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_7_100" createVehicle [8102.0586, 12109.168, -0.00015258789];


  _vehicle_54 = _this;
  _this setDir -73.842079;
  _this setPos [8102.0586, 12109.168, -0.00015258789];
};

_vehicle_57 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [8046.5605, 12123.283, 0.0004119873];


  _vehicle_57 = _this;
  _this setDir 278.38492;
  _this setPos [8046.5605, 12123.283, 0.0004119873];
};

_vehicle_59 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [8017.9492, 12136.748, 0.00032043457];


  _vehicle_59 = _this;
  _this setDir -32.435734;
  _this setPos [8017.9492, 12136.748, 0.00032043457];
};

_vehicle_61 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [8001.4272, 12172.213, 0.00019836426];


  _vehicle_61 = _this;
  _this setDir 507.56216;
  _this setPos [8001.4272, 12172.213, 0.00019836426];
};

_vehicle_67 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_30_25" createVehicle [8027.8071, 12128.592, 0.00024414063];


  _vehicle_67 = _this;
  _this setDir -64.79258;
  _this setPos [8027.8071, 12128.592, 0.00024414063];
};

_vehicle_70 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7988.5303, 12186.879, 9.1552734e-005];


  _vehicle_70 = _this;
  _this setDir 491.43872;
  _this setPos [7988.5303, 12186.879, 9.1552734e-005];
};

_vehicle_72 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7972.5259, 12198.112, 0.0002746582];


  _vehicle_72 = _this;
  _this setDir 477.67319;
  _this setPos [7972.5259, 12198.112, 0.0002746582];
};

_vehicle_75 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7950.3237, 12209.446, 3.0517578e-005];


  _vehicle_75 = _this;
  _this setDir 117.16778;
  _this setPos [7950.3237, 12209.446, 3.0517578e-005];
};

_vehicle_78 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7928.1069, 12220.817, 0.00016784668];


  _vehicle_78 = _this;
  _this setDir 117.16778;
  _this setPos [7928.1069, 12220.817, 0.00016784668];
};

_vehicle_80 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7909.626, 12227.259, 9.1552734e-005];


  _vehicle_80 = _this;
  _this setDir 101.88165;
  _this setPos [7909.626, 12227.259, 9.1552734e-005];
};

_vehicle_82 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7890.1157, 12228.488, -6.1035156e-005];


  _vehicle_82 = _this;
  _this setDir 86.425301;
  _this setPos [7890.1157, 12228.488, -6.1035156e-005];
};

_vehicle_85 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7865.27, 12227.089, 1.5258789e-005];


  _vehicle_85 = _this;
  _this setDir 86.99337;
  _this setPos [7865.27, 12227.089, 1.5258789e-005];
};

_vehicle_88 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7865.3438, 12227.133, 0.0001373291];


  _vehicle_88 = _this;
  _this setDir 266.49588;
  _this setPos [7865.3438, 12227.133, 0.0001373291];
};

_vehicle_91 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7821.3315, 12233.353, -0.00015258789];


  _vehicle_91 = _this;
  _this setDir 101.37552;
  _this setPos [7821.3315, 12233.353, -0.00015258789];
};

_vehicle_93 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7796.812, 12238.152, -0.0004119873];


  _vehicle_93 = _this;
  _this setDir 101.25838;
  _this setPos [7796.812, 12238.152, -0.0004119873];
};

_vehicle_96 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7796.8403, 12238.103, 0.00044250488];


  _vehicle_96 = _this;
  _this setDir 281.05661;
  _this setPos [7796.8403, 12238.103, 0.00044250488];
};

_vehicle_98 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7759.7505, 12250.568, 0.0001373291];


  _vehicle_98 = _this;
  _this setDir 461.19827;
  _this setPos [7759.7505, 12250.568, 0.0001373291];
};

_vehicle_100 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7759.8174, 12250.595, 0.00025939941];


  _vehicle_100 = _this;
  _this setDir 281.93988;
  _this setPos [7759.8174, 12250.595, 0.00025939941];
};

_vehicle_102 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7741.4116, 12256.995, -0.00048828125];


  _vehicle_102 = _this;
  _this setDir 297.77063;
  _this setPos [7741.4116, 12256.995, -0.00048828125];
};

_vehicle_104 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7725.4653, 12268.158, -3.0517578e-005];


  _vehicle_104 = _this;
  _this setDir 313.08887;
  _this setPos [7725.4653, 12268.158, -3.0517578e-005];
};

_vehicle_106 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7712.9741, 12283.097, -0.00022888184];


  _vehicle_106 = _this;
  _this setDir 326.79205;
  _this setPos [7712.9741, 12283.097, -0.00022888184];
};

_vehicle_109 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7696.311, 12324.312, 9.1552734e-005];


  _vehicle_109 = _this;
  _this setDir 161.17343;
  _this setPos [7696.311, 12324.312, 9.1552734e-005];
};

_vehicle_111 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7688.2163, 12347.921, -1.5258789e-005];


  _vehicle_111 = _this;
  _this setDir 161.19525;
  _this setPos [7688.2163, 12347.921, -1.5258789e-005];
};

_vehicle_114 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7688.1885, 12347.8, 0.00010681152];


  _vehicle_114 = _this;
  _this setDir 340.15903;
  _this setPos [7688.1885, 12347.8, 0.00010681152];
};

_vehicle_117 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7681.5503, 12391.767, 0.00010681152];


  _vehicle_117 = _this;
  _this setDir 174.63683;
  _this setPos [7681.5503, 12391.767, 0.00010681152];
};

_vehicle_120 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_25" createVehicle [7681.5605, 12391.715, 0.00028991699];


  _vehicle_120 = _this;
  _this setDir -4.4702563;
  _this setPos [7681.5605, 12391.715, 0.00028991699];
};

_vehicle_125 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7682.4551, 12420.889, -1.6100464];


  _vehicle_125 = _this;
  _this setDir 182.06709;
  _this setPos [7682.4551, 12420.889, -1.6100464];
};

_vehicle_129 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7682.9434, 12445.813, 0.00025939941];


  _vehicle_129 = _this;
  _this setDir 181.2209;
  _this setPos [7682.9434, 12445.813, 0.00025939941];
};

_vehicle_259 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Barrack2" createVehicle [7855.5781, 12674.382, -2.2888184e-005];


  _vehicle_259 = _this;
  _this setDir -8.9040527;
  _this setPos [7855.5781, 12674.382, -2.2888184e-005];
};

_vehicle_266 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Hlidac_budka" createVehicle [7760.1177, 12621.654, 5.3405762e-005];


  _vehicle_266 = _this;
  _this setDir 234.80757;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7760.1177, 12621.654, 5.3405762e-005];
};

_vehicle_269 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Mil_Barracks" createVehicle [7800.626, 12578.181, 0.22802056];


  _vehicle_269 = _this;
  _this setDir -199.28587;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7800.626, 12578.181, 0.22802056];
};

_vehicle_270 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Mil_Barracks_i" createVehicle [7730.416, 12572.723, -0.022225222];


  _vehicle_270 = _this;
  _this setDir -30.663641;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7730.416, 12572.723, -0.022225222];
};

_vehicle_271 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Mil_Barracks_L" createVehicle [7730.0332, 12589.021, 0.021191195];


  _vehicle_271 = _this;
  _this setDir -35.217594;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7730.0332, 12589.021, 0.021191195];
};

_vehicle_274 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowGen_Big" createVehicle [7796.498, 12624.03, 0.041037399];


  _vehicle_274 = _this;
  _this setDir -25.869724;
  _this setPos [7796.498, 12624.03, 0.041037399];
};

_vehicle_276 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [7730.9253, 12680.308, 0.049742039];


  _vehicle_276 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7730.9253, 12680.308, 0.049742039];
};

_vehicle_283 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_A_Office01" createVehicle [7771.9414, 12591.414, -0.054918062];


  _vehicle_283 = _this;
  _this setDir -17.639032;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7771.9414, 12591.414, -0.054918062];
};

_vehicle_294 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_Workshop01_01" createVehicle [7907.7407, 12627.608, 0.06007079];


  _vehicle_294 = _this;
  _this setDir 351.56985;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7907.7407, 12627.608, 0.06007079];
};

_vehicle_295 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_Workshop01_02" createVehicle [7866.2534, 12636.624, 0.061232373];


  _vehicle_295 = _this;
  _this setDir 80.804077;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7866.2534, 12636.624, 0.061232373];
};

_vehicle_296 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_Workshop01_03" createVehicle [7903.5737, 12590.472, 2.2888184e-005];


  _vehicle_296 = _this;
  _this setDir 256.71408;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7903.5737, 12590.472, 2.2888184e-005];
};

_vehicle_297 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_Workshop01_04" createVehicle [7800.7817, 12607.486, 0.14819752];


  _vehicle_297 = _this;
  _this setDir 68.328827;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7800.7817, 12607.486, 0.14819752];
};

_vehicle_298 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_Workshop01_box" createVehicle [7900.5908, 12586.722, 0.30176869];


  _vehicle_298 = _this;
  _this setDir 166.11166;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7900.5908, 12586.722, 0.30176869];
};

_vehicle_299 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_Workshop01_L" createVehicle [7901.4082, 12613.831, 0.10587258];


  _vehicle_299 = _this;
  _this setDir -9.7514687;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7901.4082, 12613.831, 0.10587258];
};

_vehicle_369 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Misc_PowerStation" createVehicle [7907.3433, 12642.34, 0.060888842];


  _vehicle_369 = _this;
  _this setDir 86.9804;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7907.3433, 12642.34, 0.060888842];
};

_vehicle_397 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Barrack2" createVehicle [7845.4849, 12672.779, 5.3405762e-005];


  _vehicle_397 = _this;
  _this setDir -6.9164743;
  _this setPos [7845.4849, 12672.779, 5.3405762e-005];
};

_vehicle_400 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Mil_Barracks_i" createVehicle [7827.1924, 12588.295, 0.034341037];


  _vehicle_400 = _this;
  _this setDir -196.30893;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7827.1924, 12588.295, 0.034341037];
};

_vehicle_403 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Mil_Guardhouse" createVehicle [7685.6812, 12508.432, 0.24781299];


  _vehicle_403 = _this;
  _this setDir -150.90938;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7685.6812, 12508.432, 0.24781299];
};

_vehicle_405 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Hlidac_budka" createVehicle [7691.4565, 12478.391, 0.23015514];


  _vehicle_405 = _this;
  _this setDir -83.429619;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7691.4565, 12478.391, 0.23015514];
};

_vehicle_408 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7684.939, 12470.01, -3.0517578e-005];


  _vehicle_408 = _this;
  _this setDir 2.6140795;
  _this setPos [7684.939, 12470.01, -3.0517578e-005];
};

_vehicle_414 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7682.8418, 12445.553, 0.0002746582];


  _vehicle_414 = _this;
  _this setDir 4.9584579;
  _this setPos [7682.8418, 12445.553, 0.0002746582];
};

_vehicle_417 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7687.9946, 12488.628, 3.0517578e-005];


  _vehicle_417 = _this;
  _this setDir 15.009583;
  _this setPos [7687.9946, 12488.628, 3.0517578e-005];
};

_vehicle_424 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_100" createVehicle [7695.2261, 12506.596, 0.00016784668];


  _vehicle_424 = _this;
  _this setDir 28.252714;
  _this setPos [7695.2261, 12506.596, 0.00016784668];
};

_vehicle_427 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7719.9194, 12540.933, 7.6293945e-005];


  _vehicle_427 = _this;
  _this setDir 217.82405;
  _this setPos [7719.9194, 12540.933, 7.6293945e-005];
};

_vehicle_429 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7734.9678, 12560.776, 4.5776367e-005];


  _vehicle_429 = _this;
  _this setDir 217.19189;
  _this setPos [7734.9678, 12560.776, 4.5776367e-005];
};

_vehicle_435 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_30_25" createVehicle [7734.7847, 12560.321, -6.1035156e-005];


  _vehicle_435 = _this;
  _this setDir 36.408237;
  _this setPos [7734.7847, 12560.321, -6.1035156e-005];
};

_vehicle_438 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7791.2637, 12586.227, 0.00012969971];


  _vehicle_438 = _this;
  _this setDir 250.29263;
  _this setPos [7791.2637, 12586.227, 0.00012969971];
};

_vehicle_440 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7814.6704, 12594.101, -7.6293945e-006];


  _vehicle_440 = _this;
  _this setDir 251.51181;
  _this setPos [7814.6704, 12594.101, -7.6293945e-006];
};

_vehicle_444 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_A_Hospital" createVehicle [7836.5088, 12622.839, 0.36634916];


  _vehicle_444 = _this;
  _this setDir 71.406975;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7836.5088, 12622.839, 0.36634916];
  _this addEventHandler ["HandleDamage",{0}]; //Forbid destruction (mi8wreck and static objects on top are left floating)
  _this enableSimulation false;
};

_vehicle_446 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_6konec" createVehicle [7693.8306, 12502.225, 0.0001373291];


  _vehicle_446 = _this;
  _this setDir 298.83359;
  _this setPos [7693.8306, 12502.225, 0.0001373291];
};

_vehicle_469 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Zabradli_pruhovane_pravykonec" createVehicle [7833.144, 12615.484, 0.1531699];


  _vehicle_469 = _this;
  _this setDir -19.132517;
  _this setPos [7833.144, 12615.484, 0.1531699];
};

_vehicle_470 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Zabradli_pruhovane_stred" createVehicle [7830.8218, 12622.246, 0.13109393];


  _vehicle_470 = _this;
  _this setDir -19.099663;
  _this setPos [7830.8218, 12622.246, 0.13109393];
};

_vehicle_485 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7684.0625, 12496.49, 0.23487064];


  _vehicle_485 = _this;
  _this setDir 199.51678;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7684.0625, 12496.49, 0.23487064];
};

_vehicle_488 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7697.4707, 12491.88, 7.6293945e-005];


  _vehicle_488 = _this;
  _this setDir 199.51678;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7697.4707, 12491.88, 7.6293945e-005];
};

_vehicle_491 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7703.2681, 12490.969, -0.0056756362];


  _vehicle_491 = _this;
  _this setDir -176.86342;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7703.2681, 12490.969, -0.0056756362];
};

_vehicle_494 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7698.0239, 12482.269, 0.00016021729];


  _vehicle_494 = _this;
  _this setDir -172.3792;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7698.0239, 12482.269, 0.00016021729];
};

_vehicle_501 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ZavoraAnim" createVehicle [7690.6299, 12483.554, 0.00019073486];


  _vehicle_501 = _this;
  _this setDir 13.80172;
  _this setPos [7690.6299, 12483.554, 0.00019073486];
};

_vehicle_573 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7706.6328, 12492.072, 0.089856431];


  _vehicle_573 = _this;
  _this setDir -38.345325;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7706.6328, 12492.072, 0.089856431];
};

_vehicle_575 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7710.7935, 12496.035, 0.050014712];


  _vehicle_575 = _this;
  _this setDir -54.276836;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7710.7935, 12496.035, 0.050014712];
};

_vehicle_582 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7690.6001, 12483.223, 0.00017547607];


  _vehicle_582 = _this;
  _this setDir -166.62869;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7690.6001, 12483.223, 0.00017547607];
};

_vehicle_585 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7714.1069, 12500.699, 0.093352884];


  _vehicle_585 = _this;
  _this setDir -54.276836;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7714.1069, 12500.699, 0.093352884];
};

_vehicle_587 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7728.1328, 12520.248, 0.42747217];


  _vehicle_587 = _this;
  _this setDir -53.08115;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7728.1328, 12520.248, 0.42747217];
};

_vehicle_589 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7724.6108, 12515.468, 0.49183604];


  _vehicle_589 = _this;
  _this setDir -52.829365;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7724.6108, 12515.468, 0.49183604];
};

_vehicle_592 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_2" createVehicle [7717.6074, 12505.546, 6.1035156e-005];


  _vehicle_592 = _this;
  _this setDir -52.953217;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7717.6074, 12505.546, 6.1035156e-005];
};

_vehicle_595 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7722.5786, 12512.588, 0.0002746582];


  _vehicle_595 = _this;
  _this setDir -52.74184;
  _this setPos [7722.5786, 12512.588, 0.0002746582];
};

_vehicle_600 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7731.688, 12525.019, 0.45460182];


  _vehicle_600 = _this;
  _this setDir -52.068256;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7731.688, 12525.019, 0.45460182];
};

_vehicle_605 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7721.2891, 12509.285, 1.5258789e-005];


  _vehicle_605 = _this;
  _this setDir -3.3424151;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7721.2891, 12509.285, 1.5258789e-005];
};

_vehicle_608 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7706.4175, 12484.57, 0.046834037];


  _vehicle_608 = _this;
  _this setDir -199.30894;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7706.4175, 12484.57, 0.046834037];
};

_vehicle_625 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7720.4375, 12509.775, 7.6293945e-005];


  _vehicle_625 = _this;
  _this setDir 126.02657;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7720.4375, 12509.775, 7.6293945e-005];
};

_vehicle_630 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7744.7632, 12534.649, 0.43572149];


  _vehicle_630 = _this;
  _this setDir -50.668568;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7744.7632, 12534.649, 0.43572149];
};

_vehicle_632 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7763.4429, 12550.229, 9.1552734e-005];


  _vehicle_632 = _this;
  _this setDir -13.265052;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7763.4429, 12550.229, 9.1552734e-005];
};

_vehicle_634 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7769.1895, 12551.577, 9.9182129e-005];


  _vehicle_634 = _this;
  _this setDir -12.915766;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7769.1895, 12551.577, 9.9182129e-005];
};

_vehicle_636 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7793.8687, 12558.297, 8.392334e-005];


  _vehicle_636 = _this;
  _this setDir -19.116528;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7793.8687, 12558.297, 8.392334e-005];
};

_vehicle_638 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7799.2637, 12560.127, 2.2888184e-005];


  _vehicle_638 = _this;
  _this setDir -17.482645;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7799.2637, 12560.127, 2.2888184e-005];
};

_vehicle_643 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_60_10" createVehicle [7875.5601, 12614.05, 0.00019836426];


  _vehicle_643 = _this;
  _this setDir 188.35074;
  _this setPos [7875.5601, 12614.05, 0.00019836426];
};

_vehicle_647 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkClearMiddle" createVehicle [7828.9653, 12631.519, 0.1144487];


  _vehicle_647 = _this;
  _this setDir -18.97414;
  _this setPos [7828.9653, 12631.519, 0.1144487];
};

_vehicle_648 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkCorner" createVehicle [7838.0479, 12604.131, 0.091015004];


  _vehicle_648 = _this;
  _this setDir 164.14371;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7838.0479, 12604.131, 0.091015004];
};

_vehicle_654 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkClearLong" createVehicle [7834.5859, 12615.059, 0.10511727];


  _vehicle_654 = _this;
  _this setDir -17.796219;
  _this setPos [7834.5859, 12615.059, 0.10511727];
};

_vehicle_656 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkClearLong" createVehicle [7773.8228, 12583.926, -0.0033394136];


  _vehicle_656 = _this;
  _this setDir -109.70084;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7773.8228, 12583.926, -0.0033394136];
};

_vehicle_658 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkClearLong" createVehicle [7831.4375, 12624.461, 0.12398755];


  _vehicle_658 = _this;
  _this setDir 160.52249;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7831.4375, 12624.461, 0.12398755];
};

_vehicle_661 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_75" createVehicle [7814.5981, 12594.012, 7.6293945e-006];


  _vehicle_661 = _this;
  _this setDir 72.150352;
  _this setPos [7814.5981, 12594.012, 7.6293945e-006];
};

_vehicle_664 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7876.9131, 12638.724, 0.00012207031];


  _vehicle_664 = _this;
  _this setDir 183.34026;
  _this setPos [7876.9131, 12638.724, 0.00012207031];
};

_vehicle_667 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_0_2000" createVehicle [7827.2705, 12597.03, 0.00019073486];


  _vehicle_667 = _this;
  _this setDir 81.697701;
  _this setPos [7827.2705, 12597.03, 0.00019073486];
};

_vehicle_670 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_25" createVehicle [7848.7212, 12600.439, 0.00019836426];


  _vehicle_670 = _this;
  _this setDir -106.15102;
  _this setPos [7848.7212, 12600.439, 0.00019836426];
};

_vehicle_673 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_0_2000" createVehicle [7848.4995, 12600.455, 0.00010681152];


  _vehicle_673 = _this;
  _this setDir 75.657135;
  _this setPos [7848.4995, 12600.455, 0.00010681152];
};

_vehicle_676 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_25" createVehicle [7869.4673, 12606.082, 0.0001449585];


  _vehicle_676 = _this;
  _this setDir -112.63044;
  _this setPos [7869.4673, 12606.082, 0.0001449585];
};

_vehicle_680 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_0_2000" createVehicle [7856.7549, 12658.315, 0.055086739];


  _vehicle_680 = _this;
  _this setDir 119.2688;
  _this setPos [7856.7549, 12658.315, 0.055086739];
};

_vehicle_683 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_60_10" createVehicle [7871.8984, 12649.773, 0.054911263];


  _vehicle_683 = _this;
  _this setDir 120.96582;
  _this setPos [7871.8984, 12649.773, 0.054911263];
};

_vehicle_686 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_60_10" createVehicle [7847.0659, 12659.126, 0.054857858];


  _vehicle_686 = _this;
  _this setDir 66.514038;
  _this setPos [7847.0659, 12659.126, 0.054857858];
};

_vehicle_688 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7847.187, 12659.116, 0.054781564];


  _vehicle_688 = _this;
  _this setDir 247.99504;
  _this setPos [7847.187, 12659.116, 0.054781564];
};

_vehicle_694 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_25" createVehicle [7825.3296, 12649.726, 1.5258789e-005];


  _vehicle_694 = _this;
  _this setDir 61.396759;
  _this setPos [7825.3296, 12649.726, 1.5258789e-005];
};

_vehicle_697 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7816.8354, 12646.344, 0.0085413568];


  _vehicle_697 = _this;
  _this setDir 241.69037;
  _this setPos [7816.8354, 12646.344, 0.0085413568];
};

_vehicle_699 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7824.3506, 12624.865, 1.5258789e-005];


  _vehicle_699 = _this;
  _this setDir 251.32651;
  _this setPos [7824.3506, 12624.865, 1.5258789e-005];
};

_vehicle_701 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7835.4102, 12600.333, -7.6293945e-006];


  _vehicle_701 = _this;
  _this setDir 341.61823;
  _this setPos [7835.4102, 12600.333, -7.6293945e-006];
};

_vehicle_703 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7798.9565, 12636.164, 4.5776367e-005];


  _vehicle_703 = _this;
  _this setDir 241.74373;
  _this setPos [7798.9565, 12636.164, 4.5776367e-005];
};

_vehicle_706 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_25" createVehicle [7773.5469, 12622.104, 1.5258789e-005];


  _vehicle_706 = _this;
  _this setDir 53.137917;
  _this setPos [7773.5469, 12622.104, 1.5258789e-005];
};

_vehicle_709 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_30_25" createVehicle [7745.7935, 12597.207, 5.3405762e-005];


  _vehicle_709 = _this;
  _this setDir 21.677221;
  _this setPos [7745.7935, 12597.207, 5.3405762e-005];
};

_vehicle_711 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_30_25" createVehicle [7744.8613, 12584.377, 6.1035156e-005];


  _vehicle_711 = _this;
  _this setDir -10.79381;
  _this setPos [7744.8613, 12584.377, 6.1035156e-005];
};

_vehicle_713 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_30_25" createVehicle [7748.4727, 12574.094, 7.6293945e-006];


  _vehicle_713 = _this;
  _this setDir -32.457275;
  _this setPos [7748.4727, 12574.094, 7.6293945e-006];
};

_vehicle_721 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_6konec" createVehicle [7751.1309, 12568.83, 3.8146973e-005];


  _vehicle_721 = _this;
  _this setDir -26.599419;
  _this setPos [7751.1309, 12568.83, 3.8146973e-005];
};

_vehicle_725 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_25" createVehicle [7820.189, 12647.895, 6.8664551e-005];


  _vehicle_725 = _this;
  _this setDir 60.082756;
  _this setPos [7820.189, 12647.895, 6.8664551e-005];
};

_vehicle_728 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Mil_Barracks" createVehicle [7865.3301, 12594.441, 0.43977916];


  _vehicle_728 = _this;
  _this setDir -193.14217;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7865.3301, 12594.441, 0.43977916];
};

_vehicle_731 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Mil_Barracks" createVehicle [7710.4917, 12541.445, 0.53622085];


  _vehicle_731 = _this;
  _this setDir -53.659977;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7710.4917, 12541.445, 0.53622085];
};

_vehicle_742 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo1B" createVehicle [7707.2412, 12502.804, -7.6293945e-005];


  _vehicle_742 = _this;
  _this setDir 121.07423;
  _this setPos [7707.2412, 12502.804, -7.6293945e-005];
};

_vehicle_750 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo2B" createVehicle [7709.0073, 12505.421, 3.8146973e-005];


  _vehicle_750 = _this;
  _this setDir -50.96957;
  _this setPos [7709.0073, 12505.421, 3.8146973e-005];
};

_vehicle_755 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo1B" createVehicle [7713.6841, 12507.613, 5.3405762e-005];


  _vehicle_755 = _this;
  _this setDir 125.20144;
  _this setPos [7713.6841, 12507.613, 5.3405762e-005];
};

_vehicle_758 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7805.9312, 12567.498, 0.00019073486];


  _vehicle_758 = _this;
  _this setDir -13.082738;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7805.9312, 12567.498, 0.00019073486];
};

_vehicle_761 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7803.418, 12563.219, 2.2888184e-005];


  _vehicle_761 = _this;
  _this setDir -79.438599;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7803.418, 12563.219, 2.2888184e-005];
};

_vehicle_764 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7817.2456, 12566.893, 2.2888184e-005];


  _vehicle_764 = _this;
  _this setDir -14.840211;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7817.2456, 12566.893, 2.2888184e-005];
};

_vehicle_767 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7834.6816, 12570.39, 0.00015258789];


  _vehicle_767 = _this;
  _this setDir -9.7365408;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7834.6816, 12570.39, 0.00015258789];
};

_vehicle_769 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7857.8774, 12575.037, 0.00015258789];


  _vehicle_769 = _this;
  _this setDir -10.629781;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7857.8774, 12575.037, 0.00015258789];
};

_vehicle_773 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7863.6865, 12576.196, 0.00026702881];


  _vehicle_773 = _this;
  _this setDir -11.683409;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7863.6865, 12576.196, 0.00026702881];
};

_vehicle_775 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7892.749, 12582.031, 0.00012207031];


  _vehicle_775 = _this;
  _this setDir -13.617166;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7892.749, 12582.031, 0.00012207031];
};

_vehicle_777 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7898.4868, 12583.469, 6.8664551e-005];


  _vehicle_777 = _this;
  _this setDir -13.567074;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7898.4868, 12583.469, 6.8664551e-005];
};

_vehicle_779 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7927.4224, 12592.181, 0.03926748];


  _vehicle_779 = _this;
  _this setDir -16.835325;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7927.4224, 12592.181, 0.03926748];
};

_vehicle_781 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7930.7114, 12595.124, 0.11132366];


  _vehicle_781 = _this;
  _this setDir -103.90511;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7930.7114, 12595.124, 0.11132366];
};

_vehicle_784 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7928.082, 12606.62, 0.00038909912];


  _vehicle_784 = _this;
  _this setDir -100.37136;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7928.082, 12606.62, 0.00038909912];
};

_vehicle_786 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7923.4575, 12629.722, 0.0001373291];


  _vehicle_786 = _this;
  _this setDir -99.3834;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7923.4575, 12629.722, 0.0001373291];
};

_vehicle_791 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7899.3545, 12650.11, 0.055193551];


  _vehicle_791 = _this;
  _this setDir -7.127285;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7899.3545, 12650.11, 0.055193551];
};

_vehicle_793 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7893.416, 12649.437, 0.054926522];


  _vehicle_793 = _this;
  _this setDir -4.4865141;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7893.416, 12649.437, 0.054926522];
};

_vehicle_795 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7887.4092, 12648.818, 0.18095694];


  _vehicle_795 = _this;
  _this setDir -6.5264587;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7887.4092, 12648.818, 0.18095694];
};

_vehicle_797 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7883.9736, 12652.296, 0.21083659];


  _vehicle_797 = _this;
  _this setDir 66.964455;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7883.9736, 12652.296, 0.21083659];
};

_vehicle_802 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7866.7295, 12668.918, 0.05482734];


  _vehicle_802 = _this;
  _this setDir -13.351489;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7866.7295, 12668.918, 0.05482734];
};

_vehicle_805 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7860.7261, 12677.117, 0.18084966];


  _vehicle_805 = _this;
  _this setDir 65.093285;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7860.7261, 12677.117, 0.18084966];
};

_vehicle_808 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7858.2217, 12682.443, 0.22590555];


  _vehicle_808 = _this;
  _this setDir 65.093285;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7858.2217, 12682.443, 0.22590555];
};

_vehicle_810 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_misc_amplion_wood" createVehicle [7726.4653, 12603.682, -9.1552734e-005];


  _vehicle_810 = _this;
  _this setDir -34.208656;
  _this setPos [7726.4653, 12603.682, -9.1552734e-005];
};

_vehicle_867 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7847.7979, 12682.25, 0.10678213];


  _vehicle_867 = _this;
  _this setDir -10.629051;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7847.7979, 12682.25, 0.10678213];
};

_vehicle_869 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7841.9434, 12681.131, 0.17862763];


  _vehicle_869 = _this;
  _this setDir -10.886585;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7841.9434, 12681.131, 0.17862763];
};

_vehicle_872 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7832.459, 12662.427, -1.5258789e-005];


  _vehicle_872 = _this;
  _this setDir -17.639917;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7832.459, 12662.427, -1.5258789e-005];
};

_vehicle_875 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7839.3071, 12676.909, -0.025063775];


  _vehicle_875 = _this;
  _this setDir -78.318642;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7839.3071, 12676.909, -0.025063775];
};

_vehicle_878 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7837.9673, 12671.104, 0.0035295449];


  _vehicle_878 = _this;
  _this setDir 284.03558;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7837.9673, 12671.104, 0.0035295449];
};

_vehicle_881 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7821.7012, 12659.141, 0.13356313];


  _vehicle_881 = _this;
  _this setDir -15.100958;
  _this setPos [7821.7012, 12659.141, 0.13356313];
};

_vehicle_883 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7810.3833, 12655.851, 0.34592381];


  _vehicle_883 = _this;
  _this setDir -16.251894;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7810.3833, 12655.851, 0.34592381];
};

_vehicle_886 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7793.6279, 12649.558, 0.47203225];


  _vehicle_886 = _this;
  _this setDir -26.897541;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7793.6279, 12649.558, 0.47203225];
};

_vehicle_888 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7783.3882, 12643.191, 0.39535707];


  _vehicle_888 = _this;
  _this setDir -33.228615;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7783.3882, 12643.191, 0.39535707];
};

_vehicle_891 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7778.5776, 12640.035, 0.32749435];


  _vehicle_891 = _this;
  _this setDir -33.228615;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7778.5776, 12640.035, 0.32749435];
};

_vehicle_894 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7767.563, 12632.272, 1.5258789e-005];


  _vehicle_894 = _this;
  _this setDir -33.228615;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7767.563, 12632.272, 1.5258789e-005];
};

_vehicle_896 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7757.7778, 12625.658, 6.8664551e-005];


  _vehicle_896 = _this;
  _this setDir -33.228615;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7757.7778, 12625.658, 6.8664551e-005];
};

_vehicle_898 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7746.001, 12617.278, 3.8146973e-005];


  _vehicle_898 = _this;
  _this setDir -35.702667;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7746.001, 12617.278, 3.8146973e-005];
};

_vehicle_902 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7732.6816, 12608.401, 5.3405762e-005];


  _vehicle_902 = _this;
  _this setDir -32.901619;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7732.6816, 12608.401, 5.3405762e-005];
};

_vehicle_904 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7722.9072, 12601.872, 0.00010681152];


  _vehicle_904 = _this;
  _this setDir -33.980953;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7722.9072, 12601.872, 0.00010681152];
};

_vehicle_906 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7718.2114, 12598.712, 3.8146973e-005];


  _vehicle_906 = _this;
  _this setDir -33.716564;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7718.2114, 12598.712, 3.8146973e-005];
};

_vehicle_908 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7708.0195, 12588.974, 5.3405762e-005];


  _vehicle_908 = _this;
  _this setDir -59.070103;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7708.0195, 12588.974, 5.3405762e-005];
};

_vehicle_910 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7703.4111, 12578.161, -0.064826578];


  _vehicle_910 = _this;
  _this setDir -68.85527;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7703.4111, 12578.161, -0.064826578];
};

_vehicle_915 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7695.498, 12556.22, -0.10462105];


  _vehicle_915 = _this;
  _this setDir -70.145775;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7695.498, 12556.22, -0.10462105];
};

_vehicle_917 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7689.8384, 12539.952, -0.068956152];


  _vehicle_917 = _this;
  _this setDir -65.422539;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7689.8384, 12539.952, -0.068956152];
};

_vehicle_919 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7687.6196, 12534.532, -0.15200771];


  _vehicle_919 = _this;
  _this setDir -68.380638;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7687.6196, 12534.532, -0.15200771];
};

_vehicle_924 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkClearShort" createVehicle [7826.3003, 12639.023, 0.26242876];


  _vehicle_924 = _this;
  _this setDir -20.166483;
  _this setPos [7826.3003, 12639.023, 0.26242876];
};

_vehicle_925 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkClearMiddle" createVehicle [7841.8374, 12605.22, 0.17033358];


  _vehicle_925 = _this;
  _this setDir 73.87928;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7841.8374, 12605.22, 0.17033358];
};

_vehicle_927 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkClearMiddle" createVehicle [7836.877, 12607.948, 0.064775042];


  _vehicle_927 = _this;
  _this setDir 162.2057;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7836.877, 12607.948, 0.064775042];
};

_vehicle_930 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7828.4399, 12621.266, -7.6293945e-006];


  _vehicle_930 = _this;
  _this setDir 341.61823;
  _this setPos [7828.4399, 12621.266, -7.6293945e-006];
};

_vehicle_933 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_6konec" createVehicle [7818.9854, 12650.186, 1.5258789e-005];


  _vehicle_933 = _this;
  _this setDir 161.00658;
  _this setPos [7818.9854, 12650.186, 1.5258789e-005];
};

_vehicle_941 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Mil_Barracks_i" createVehicle [7717.2246, 12651.172, 0.20807664];


  _vehicle_941 = _this;
  _this setDir -32.88995;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7717.2246, 12651.172, 0.20807664];
};

_vehicle_944 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7766.2925, 12636.271, 0.12808849];


  _vehicle_944 = _this;
  _this setDir 55.254215;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7766.2925, 12636.271, 0.12808849];
};

_vehicle_947 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7762.8984, 12641.222, 0.16551374];


  _vehicle_947 = _this;
  _this setDir 55.254215;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7762.8984, 12641.222, 0.16551374];
};

_vehicle_949 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7759.5381, 12646.073, 0.21728064];


  _vehicle_949 = _this;
  _this setDir 55.254215;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7759.5381, 12646.073, 0.21728064];
};

_vehicle_951 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7746.1807, 12665.759, 0.33064225];


  _vehicle_951 = _this;
  _this setDir 55.254215;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7746.1807, 12665.759, 0.33064225];
};

_vehicle_953 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7739.6006, 12675.721, 0.36060011];


  _vehicle_953 = _this;
  _this setDir 55.254215;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7739.6006, 12675.721, 0.36060011];
};

_vehicle_955 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7731.7896, 12679.784, 0.1581676];


  _vehicle_955 = _this;
  _this setDir -32.411633;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7731.7896, 12679.784, 0.1581676];
};

_vehicle_958 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7726.709, 12676.622, 0.1179219];


  _vehicle_958 = _this;
  _this setDir -31.223825;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7726.709, 12676.622, 0.1179219];
};

_vehicle_960 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7716.6465, 12670.276, -0.00012969971];


  _vehicle_960 = _this;
  _this setDir -31.223825;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7716.6465, 12670.276, -0.00012969971];
};

_vehicle_962 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7711.4736, 12667.091, 5.3405762e-005];


  _vehicle_962 = _this;
  _this setDir -31.223825;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7711.4736, 12667.091, 5.3405762e-005];
};

_vehicle_968 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7696.7773, 12653.144, -0.14467625];


  _vehicle_968 = _this;
  _this setDir -120.4924;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7696.7773, 12653.144, -0.14467625];
};

_vehicle_971 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7699.7202, 12648.123, -0.14029245];


  _vehicle_971 = _this;
  _this setDir -120.70387;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7699.7202, 12648.123, -0.14029245];
};

_vehicle_973 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7718.8198, 12615.579, -7.6293945e-006];


  _vehicle_973 = _this;
  _this setDir -122.58968;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7718.8198, 12615.579, -7.6293945e-006];
};

_vehicle_975 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7722.0029, 12610.618, 7.6293945e-006];


  _vehicle_975 = _this;
  _this setDir -122.58968;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7722.0029, 12610.618, 7.6293945e-006];
};

_vehicle_977 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7725.1201, 12605.705, 7.6293945e-005];


  _vehicle_977 = _this;
  _this setDir -122.58968;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7725.1201, 12605.705, 7.6293945e-005];
};

_vehicle_981 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_02_end" createVehicle [7720.6729, 12618.237, -0.01264854];


  _vehicle_981 = _this;
  _this setDir -34.836926;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7720.6729, 12618.237, -0.01264854];
};

_vehicle_982 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_02_main" createVehicle [7726.4136, 12610.137, -0.018727042];


  _vehicle_982 = _this;
  _this setDir -34.883072;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7726.4136, 12610.137, -0.018727042];
};

_vehicle_986 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_02_main" createVehicle [7724.1426, 12613.416];


  _vehicle_986 = _this;
  _this setDir -34.883072;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7724.1426, 12613.416];
};

_vehicle_1047 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_100" createVehicle [7753.4473, 12607.526, 1.5258789e-005];


  _vehicle_1047 = _this;
  _this setDir 48.536385;
  _this setPos [7753.4473, 12607.526, 1.5258789e-005];
};

_vehicle_1053 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_12" createVehicle [7765.2251, 12616.556, 7.6293945e-005];


  _vehicle_1053 = _this;
  _this setDir 57.104;
  _this setPos [7765.2251, 12616.556, 7.6293945e-005];
};

_vehicle_1056 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_grav_6konec" createVehicle [7759.7349, 12612.493, 1.5258789e-005];


  _vehicle_1056 = _this;
  _this setDir -36.31155;
  _this setPos [7759.7349, 12612.493, 1.5258789e-005];
};

_vehicle_1058 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_grav_6" createVehicle [7729.5171, 12612.94, -0.00010681152];


  _vehicle_1058 = _this;
  _this setDir -36.097012;
  _this setPos [7729.5171, 12612.94, -0.00010681152];
};

_vehicle_1059 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_grav_0_2000" createVehicle [7756.1611, 12617.616, 7.6293945e-006];


  _vehicle_1059 = _this;
  _this setDir -36.332043;
  _this setPos [7756.1611, 12617.616, 7.6293945e-006];
};

_vehicle_1060 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_grav_1_1000" createVehicle [7745.9106, 12631.722, -4.5776367e-005];


  _vehicle_1060 = _this;
  _this setDir -36.590664;
  _this setPos [7745.9106, 12631.722, -4.5776367e-005];
};

_vehicle_1062 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_grav_6konec" createVehicle [7722.2256, 12664.523, 3.0517578e-005];


  _vehicle_1062 = _this;
  _this setDir 145.28281;
  _this setPos [7722.2256, 12664.523, 3.0517578e-005];
};

_vehicle_1065 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_grav_1_1000" createVehicle [7736.0493, 12645.316, 3.0517578e-005];


  _vehicle_1065 = _this;
  _this setDir -36.590664;
  _this setPos [7736.0493, 12645.316, 3.0517578e-005];
};

_vehicle_1073 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_grav_60_10" createVehicle [7725.8506, 12617.95, 5.3405762e-005];


  _vehicle_1073 = _this;
  _this setDir -37.407166;
  _this setPos [7725.8506, 12617.95, 5.3405762e-005];
};

_vehicle_1074 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_grav_6" createVehicle [7731.9292, 12638.387, 2.2888184e-005];


  _vehicle_1074 = _this;
  _this setDir 51.239017;
  _this setPos [7731.9292, 12638.387, 2.2888184e-005];
};

_vehicle_1075 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_grav_30_25" createVehicle [7724.5269, 12627.86, 3.0517578e-005];


  _vehicle_1075 = _this;
  _this setDir 19.970526;
  _this setPos [7724.5269, 12627.86, 3.0517578e-005];
};

_vehicle_1082 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [7695.1968, 12653.645, 7.6293945e-006];


  _vehicle_1082 = _this;
  _this setDir -73.749771;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7695.1968, 12653.645, 7.6293945e-006];
};

_vehicle_1085 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_grav_6konec" createVehicle [7733.1118, 12607.861, 3.0517578e-005];


  _vehicle_1085 = _this;
  _this setDir 324.60812;
  _this setPos [7733.1118, 12607.861, 3.0517578e-005];
};

_vehicle_1096 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Shed_M01" createVehicle [7760.9165, 12632.6, 1.5258789e-005];


  _vehicle_1096 = _this;
  _this setDir 146.22749;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7760.9165, 12632.6, 1.5258789e-005];
};

_vehicle_1099 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7749.3135, 12619.683, -6.8664551e-005];


  _vehicle_1099 = _this;
  _this setDir -34.93668;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7749.3135, 12619.683, -6.8664551e-005];
};

_vehicle_1102 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7737.7109, 12611.75, 2.2888184e-005];


  _vehicle_1102 = _this;
  _this setDir -33.228615;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7737.7109, 12611.75, 2.2888184e-005];
};

_vehicle_1105 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7727.7031, 12605.131];


  _vehicle_1105 = _this;
  _this setDir -32.901619;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7727.7031, 12605.131];
};

_vehicle_1110 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7712.6367, 12625.332];


  _vehicle_1110 = _this;
  _this setDir -122.58968;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7712.6367, 12625.332];
};

_vehicle_1112 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7709.5278, 12630.21, -1.5258789e-005];


  _vehicle_1112 = _this;
  _this setDir -122.58968;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7709.5278, 12630.21, -1.5258789e-005];
};

_vehicle_1143 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Gate_Ind2A_L" createVehicle [7752.7573, 12622.056, 0.098224759];


  _vehicle_1143 = _this;
  _this setDir -35.34116;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7752.7573, 12622.056, 0.098224759];
};

_vehicle_1144 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Gate_Ind2A_R" createVehicle [7752.7671, 12622.078, 0.047656305];


  _vehicle_1144 = _this;
  _this setDir -35.193165;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7752.7671, 12622.078, 0.047656305];
};

_vehicle_1150 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_2" createVehicle [7742.5352, 12614.991, 7.6293945e-006];


  _vehicle_1150 = _this;
  _this setDir -35.497524;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7742.5352, 12614.991, 7.6293945e-006];
};

_vehicle_1151 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7744.3071, 12615.829, -0.141716];


  _vehicle_1151 = _this;
  _this setDir -31.944662;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7744.3071, 12615.829, -0.141716];
};

_vehicle_1155 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7762.7192, 12628.993, 3.8146973e-005];


  _vehicle_1155 = _this;
  _this setDir -34.754059;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7762.7192, 12628.993, 3.8146973e-005];
};

_vehicle_1158 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_CncBlock" createVehicle [7723.6113, 12662.21, -7.6293945e-006];


  _vehicle_1158 = _this;
  _this setDir -32.197491;
  _this setPos [7723.6113, 12662.21, -7.6293945e-006];
};

_vehicle_1160 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_CncBlock" createVehicle [7732.6021, 12611.523, 1.5258789e-005];


  _vehicle_1160 = _this;
  _this setDir -31.637428;
  _this setPos [7732.6021, 12611.523, 1.5258789e-005];
};

_vehicle_1163 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_CncBlock" createVehicle [7730.0259, 12609.908, 1.5258789e-005];


  _vehicle_1163 = _this;
  _this setDir -31.564798;
  _this setPos [7730.0259, 12609.908, 1.5258789e-005];
};

_vehicle_1166 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo1B" createVehicle [7728.1997, 12615.005, 2.2888184e-005];


  _vehicle_1166 = _this;
  _this setDir -32.776592;
  _this setPos [7728.1997, 12615.005, 2.2888184e-005];
};

_vehicle_1169 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo2B" createVehicle [7750.3257, 12641.221, -7.6293945e-006];


  _vehicle_1169 = _this;
  _this setDir 53.845627;
  _this setPos [7750.3257, 12641.221, -7.6293945e-006];
};

_vehicle_1172 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo1B" createVehicle [7747.9375, 12644.247];


  _vehicle_1172 = _this;
  _this setDir 61.758411;
  _this setPos [7747.9375, 12644.247];
};

_vehicle_1175 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_quercus3s" createVehicle [7737.3955, 12660.661];


  _vehicle_1175 = _this;
  _this setDir 49.709446;
  _this setPos [7737.3955, 12660.661];
};

_vehicle_1180 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7692.1167, 12507.463, 7.6293945e-006];


  _vehicle_1180 = _this;
  _this setDir 124.50201;
  _this setPos [7692.1167, 12507.463, 7.6293945e-006];
};

_vehicle_1183 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7734.3438, 12643.585, -4.5776367e-005];


  _vehicle_1183 = _this;
  _this setDir 43.441601;
  _this setPos [7734.3438, 12643.585, -4.5776367e-005];
};

_vehicle_1186 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_100" createVehicle [7744.6782, 12568.355, 3.8146973e-005];


  _vehicle_1186 = _this;
  _this setDir 62.738022;
  _this setPos [7744.6782, 12568.355, 3.8146973e-005];
};

_vehicle_1192 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_12" createVehicle [7758.2881, 12574.159, 3.0517578e-005];


  _vehicle_1192 = _this;
  _this setDir 69.827896;
  _this setPos [7758.2881, 12574.159, 3.0517578e-005];
};

_vehicle_1195 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7709.3003, 12656.343, 1.5258789e-005];


  _vehicle_1195 = _this;
  _this setPos [7709.3003, 12656.343, 1.5258789e-005];
};

_vehicle_1197 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7705.187, 12654.055, -6.8664551e-005];


  _vehicle_1197 = _this;
  _this setDir 59.238518;
  _this setPos [7705.187, 12654.055, -6.8664551e-005];
};

_vehicle_1199 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7734.4502, 12625.535, -7.6293945e-006];


  _vehicle_1199 = _this;
  _this setDir 28.990669;
  _this setPos [7734.4502, 12625.535, -7.6293945e-006];
};

_vehicle_1201 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7735.1274, 12629.831, -1.5258789e-005];


  _vehicle_1201 = _this;
  _this setDir 124.03037;
  _this setPos [7735.1274, 12629.831, -1.5258789e-005];
};

_vehicle_1204 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_paleta" createVehicle [7732.1016, 12628.231, -1.5258789e-005];


  _vehicle_1204 = _this;
  _this setDir 10.805914;
  _this setPos [7732.1016, 12628.231, -1.5258789e-005];
};

_vehicle_1237 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_KBud" createVehicle [7700.4297, 12658.559, 2.2888184e-005];


  _vehicle_1237 = _this;
  _this setDir -34.483868;
  _this setPos [7700.4297, 12658.559, 2.2888184e-005];
};

_vehicle_1239 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_KBud" createVehicle [7702.2012, 12659.793, 0.033954725];


  _vehicle_1239 = _this;
  _this setDir -33.13018;
  _this setPos [7702.2012, 12659.793, 0.033954725];
};

_vehicle_1259 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench2_noRoad" createVehicle [7734.8789, 12660.362, 1.5258789e-005];


  _vehicle_1259 = _this;
  _this setDir -123.72775;
  _this setPos [7734.8789, 12660.362, 1.5258789e-005];
};

_vehicle_1263 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench2_noRoad" createVehicle [7738.6924, 12658.795, 4.5776367e-005];


  _vehicle_1263 = _this;
  _this setDir -212.95084;
  _this setPos [7738.6924, 12658.795, 4.5776367e-005];
};

_vehicle_1270 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_path_60_10" createVehicle [7792.2695, 12575.065, 0.0001373291];


  _vehicle_1270 = _this;
  _this setDir -84.290199;
  _this setPos [7792.2695, 12575.065, 0.0001373291];
};

_vehicle_1271 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7784.7842, 12580.054, 3.0517578e-005];


  _vehicle_1271 = _this;
  _this setPos [7784.7842, 12580.054, 3.0517578e-005];
};

_vehicle_1273 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7785.4751, 12578.764, 3.0517578e-005];


  _vehicle_1273 = _this;
  _this setPos [7785.4751, 12578.764, 3.0517578e-005];
};

_vehicle_1275 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7786.3364, 12577.739];


  _vehicle_1275 = _this;
  _this setPos [7786.3364, 12577.739];
};

_vehicle_1277 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7787.874, 12576.682, 3.0517578e-005];


  _vehicle_1277 = _this;
  _this setPos [7787.874, 12576.682, 3.0517578e-005];
};

_vehicle_1279 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7789.583, 12575.798, 5.3405762e-005];


  _vehicle_1279 = _this;
  _this setPos [7789.583, 12575.798, 5.3405762e-005];
};

_vehicle_1281 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7790.6367, 12574.889];


  _vehicle_1281 = _this;
  _this setPos [7790.6367, 12574.889];
};

_vehicle_1283 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7788.291, 12575.793, 1.5258789e-005];


  _vehicle_1283 = _this;
  _this setPos [7788.291, 12575.793, 1.5258789e-005];
};

_vehicle_1293 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_path_60_10" createVehicle [7818.978, 12583.803, -2.2888184e-005];


  _vehicle_1293 = _this;
  _this setDir -84.290199;
  _this setPos [7818.978, 12583.803, -2.2888184e-005];
};

_vehicle_1294 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7811.1563, 12588.838, -0.032325745];


  _vehicle_1294 = _this;
  _this setPos [7811.1563, 12588.838, -0.032325745];
};

_vehicle_1295 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7811.8472, 12587.546, -0.036102295];


  _vehicle_1295 = _this;
  _this setPos [7811.8472, 12587.546, -0.036102295];
};

_vehicle_1296 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7812.7085, 12586.519, -0.036987305];


  _vehicle_1296 = _this;
  _this setPos [7812.7085, 12586.519, -0.036987305];
};

_vehicle_1297 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7813.7905, 12585.723, -0.027893066];


  _vehicle_1297 = _this;
  _this setPos [7813.7905, 12585.723, -0.027893066];
};

_vehicle_1298 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7815.9551, 12584.583, -0.01638031];


  _vehicle_1298 = _this;
  _this setPos [7815.9551, 12584.583, -0.01638031];
};

_vehicle_1299 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7817.0088, 12583.671, -0.0083618164];


  _vehicle_1299 = _this;
  _this setPos [7817.0088, 12583.671, -0.0083618164];
};

_vehicle_1300 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7814.6631, 12584.569, -0.023269653];


  _vehicle_1300 = _this;
  _this setPos [7814.6631, 12584.569, -0.023269653];
};

_vehicle_1303 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_02_end" createVehicle [7790.4336, 12626.612, 0.062508754];


  _vehicle_1303 = _this;
  _this setDir 62.528984;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7790.4336, 12626.612, 0.062508754];
};

_vehicle_1304 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_02_main" createVehicle [7788.668, 12625.691, 0.086072117];


  _vehicle_1304 = _this;
  _this setDir 62.482872;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7788.668, 12625.691, 0.086072117];
};

_vehicle_1308 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_02_main" createVehicle [7785.1343, 12623.848, 0.037920918];


  _vehicle_1308 = _this;
  _this setDir 62.417503;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7785.1343, 12623.848, 0.037920918];
};

_vehicle_1310 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_02_main" createVehicle [7781.6538, 12622.015, -0.036657136];


  _vehicle_1310 = _this;
  _this setDir 62.482872;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7781.6538, 12622.015, -0.036657136];
};

_vehicle_1312 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_02_main" createVehicle [7778.1138, 12620.172, -0.10245585];


  _vehicle_1312 = _this;
  _this setDir 62.482872;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7778.1138, 12620.172, -0.10245585];
};

_vehicle_1317 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_3" createVehicle [7824.5879, 12603.449, -7.6293945e-006];


  _vehicle_1317 = _this;
  _this setDir -17.653694;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7824.5879, 12603.449, -7.6293945e-006];
};

_vehicle_1318 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7790.6348, 12592.038, -0.0069994885];


  _vehicle_1318 = _this;
  _this setDir -19.085501;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7790.6348, 12592.038, -0.0069994885];
};

_vehicle_1319 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9_2" createVehicle [7799.2979, 12594.885, 0.038049944];


  _vehicle_1319 = _this;
  _this setDir -18.062798;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7799.2979, 12594.885, 0.038049944];
};

_vehicle_1324 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7807.6309, 12597.762, -7.6293945e-006];


  _vehicle_1324 = _this;
  _this setDir -19.085501;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7807.6309, 12597.762, -7.6293945e-006];
};

_vehicle_1326 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7816.1279, 12600.644, 0.00011444092];


  _vehicle_1326 = _this;
  _this setDir -18.411188;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7816.1279, 12600.644, 0.00011444092];
};

_vehicle_1329 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7828.2422, 12606.102, 7.6293945e-006];


  _vehicle_1329 = _this;
  _this setDir -109.33883;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7828.2422, 12606.102, 7.6293945e-006];
};

_vehicle_1332 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_3" createVehicle [7871.0596, 12612.797, 3.8146973e-005];


  _vehicle_1332 = _this;
  _this setDir -52.523972;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7871.0596, 12612.797, 3.8146973e-005];
};

_vehicle_1336 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench2_noRoad" createVehicle [7734.853, 12587.698, 4.5776367e-005];


  _vehicle_1336 = _this;
  _this setDir -215.74066;
  _this setPos [7734.853, 12587.698, 4.5776367e-005];
};

_vehicle_1339 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench2_noRoad" createVehicle [7738.0195, 12589.964, 4.5776367e-005];


  _vehicle_1339 = _this;
  _this setDir -217.41992;
  _this setPos [7738.0195, 12589.964, 4.5776367e-005];
};

_vehicle_1342 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_popelnice" createVehicle [7732.8066, 12587.332, 1.5258789e-005];


  _vehicle_1342 = _this;
  _this setDir 10.805914;
  _this setPos [7732.8066, 12587.332, 1.5258789e-005];
};

_vehicle_1346 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_Workshop01_01" createVehicle [7774.48, 12631.132];


  _vehicle_1346 = _this;
  _this setDir 237.9478;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7774.48, 12631.132];
};

_vehicle_1349 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_big_ground1" createVehicle [7800.6016, 12564.797, 0.21088617];


  _vehicle_1349 = _this;
  _this setDir 343.12219;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7800.6016, 12564.797, 0.21088617];
};

_vehicle_1354 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_Workshop01_box" createVehicle [7804.9658, 12579.756, -0.081947327];


  _vehicle_1354 = _this;
  _this setDir 341.55197;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7804.9658, 12579.756, -0.081947327];
};

_vehicle_1364 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_big_18" createVehicle [7791.7129, 12592.332, 0.29488069];


  _vehicle_1364 = _this;
  _this setDir -18.378023;
  _this setPos [7791.7129, 12592.332, 0.29488069];
};

_vehicle_1370 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_big_18" createVehicle [7797.4272, 12575.175, 0.33114976];


  _vehicle_1370 = _this;
  _this setDir -18.394876;
  _this setPos [7797.4272, 12575.175, 0.33114976];
};

_vehicle_1373 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_big_18" createVehicle [7800.6802, 12608.633, 0.24707189];


  _vehicle_1373 = _this;
  _this setDir 73.276405;
  _this setPos [7800.6802, 12608.633, 0.24707189];
};

_vehicle_1376 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_bigL_R" createVehicle [7789.2705, 12604.2, 0.25786576];


  _vehicle_1376 = _this;
  _this setDir -17.899719;
  _this setPos [7789.2705, 12604.2, 0.25786576];
};

_vehicle_1379 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_T_L" createVehicle [7812.0537, 12612.071, 0.25391951];


  _vehicle_1379 = _this;
  _this setDir 72.675041;
  _this setPos [7812.0537, 12612.071, 0.25391951];
};

_vehicle_1382 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_bigL_L" createVehicle [7821.1577, 12616.521, 0.258434];


  _vehicle_1382 = _this;
  _this setDir 73.759819;
  _this setPos [7821.1577, 12616.521, 0.258434];
};

_vehicle_1385 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_T_L" createVehicle [7820.8931, 12641.068, 0.25757682];


  _vehicle_1385 = _this;
  _this setDir 72.589394;
  _this setPos [7820.8931, 12641.068, 0.25757682];
};

_vehicle_1388 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_big_18" createVehicle [7817.8477, 12626.724, 0.27502748];


  _vehicle_1388 = _this;
  _this setDir -16.959375;
  _this setPos [7817.8477, 12626.724, 0.27502748];
};

_vehicle_1391 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_bigL_R" createVehicle [7815.6924, 12638.475, 0.25603756];


  _vehicle_1391 = _this;
  _this setDir -17.838436;
  _this setPos [7815.6924, 12638.475, 0.25603756];
};

_vehicle_1393 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_big_18" createVehicle [7835.0586, 12645.84, 0.27234098];


  _vehicle_1393 = _this;
  _this setDir 71.066978;
  _this setPos [7835.0586, 12645.84, 0.27234098];
};

_vehicle_1399 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_3" createVehicle [7827.3853, 12604.325, 0.00012969971];


  _vehicle_1399 = _this;
  _this setDir -17.653694;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7827.3853, 12604.325, 0.00012969971];
};

_vehicle_1402 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9_2" createVehicle [7816.9624, 12637.286, 0.00015258789];


  _vehicle_1402 = _this;
  _this setDir -197.48633;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7816.9624, 12637.286, 0.00015258789];
};

_vehicle_1405 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7825.396, 12614.617, 6.1035156e-005];


  _vehicle_1405 = _this;
  _this setDir -107.01475;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7825.396, 12614.617, 6.1035156e-005];
};

_vehicle_1409 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7821.4033, 12628.045, -0.090993099];


  _vehicle_1409 = _this;
  _this setDir -107.01475;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7821.4033, 12628.045, -0.090993099];
};

_vehicle_1412 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_3" createVehicle [7818.7178, 12636.638, 5.3405762e-005];


  _vehicle_1412 = _this;
  _this setDir -107.25523;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7818.7178, 12636.638, 5.3405762e-005];
};

_vehicle_1415 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7808.5537, 12634.129, 7.6293945e-005];


  _vehicle_1415 = _this;
  _this setDir -203.28873;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7808.5537, 12634.129, 7.6293945e-005];
};

_vehicle_1418 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9_2" createVehicle [7800.2783, 12630.582, 6.1035156e-005];


  _vehicle_1418 = _this;
  _this setDir -203.13016;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7800.2783, 12630.582, 6.1035156e-005];
};

_vehicle_1421 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_3" createVehicle [7791.981, 12627.083, 7.6293945e-005];


  _vehicle_1421 = _this;
  _this setDir -201.83371;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7791.981, 12627.083, 7.6293945e-005];
};

_vehicle_1424 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_6konec" createVehicle [7754.3921, 12610.604, 3.0517578e-005];


  _vehicle_1424 = _this;
  _this setDir 140.20654;
  _this setPos [7754.3921, 12610.604, 3.0517578e-005];
};

_vehicle_1427 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe2_big_ground2" createVehicle [7845.0791, 12649.034, 0.30646935];


  _vehicle_1427 = _this;
  _this setDir 70.427155;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7845.0791, 12649.034, 0.30646935];
};

_vehicle_1431 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe1_20m" createVehicle [7874.3184, 12582.905, 0.055875607];


  _vehicle_1431 = _this;
  _this setDir -103.63924;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7874.3184, 12582.905, 0.055875607];
};

_vehicle_1435 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe1_stair" createVehicle [7864.2466, 12580.492, 0.23088312];


  _vehicle_1435 = _this;
  _this setDir 256.7135;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7864.2466, 12580.492, 0.23088312];
};

_vehicle_1436 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe1_valve" createVehicle [7890.5239, 12586.863, 0.14203395];


  _vehicle_1436 = _this;
  _this setDir -104.63007;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7890.5239, 12586.863, 0.14203395];
};

_vehicle_1440 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe1_20m" createVehicle [7848.0825, 12576.658, 0.06051933];


  _vehicle_1440 = _this;
  _this setDir -103.56433;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7848.0825, 12576.658, 0.06051933];
};

_vehicle_1442 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe1_20m" createVehicle [7827.9824, 12571.974, -0.015335854];


  _vehicle_1442 = _this;
  _this setDir -103.1151;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7827.9824, 12571.974, -0.015335854];
};

_vehicle_1445 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_pumpa" createVehicle [7718.9258, 12578.927, 8.392334e-005];


  _vehicle_1445 = _this;
  _this setPos [7718.9258, 12578.927, 8.392334e-005];
};

_vehicle_1448 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7705.9531, 12528.762, 0.00011444092];


  _vehicle_1448 = _this;
  _this setDir 124.50201;
  _this setPos [7705.9531, 12528.762, 0.00011444092];
};

_vehicle_1450 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7720.355, 12546.997, 5.3405762e-005];


  _vehicle_1450 = _this;
  _this setDir 124.50201;
  _this setPos [7720.355, 12546.997, 5.3405762e-005];
};

_vehicle_1452 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7734.7603, 12565.076, -7.6293945e-006];


  _vehicle_1452 = _this;
  _this setDir 124.50201;
  _this setPos [7734.7603, 12565.076, -7.6293945e-006];
};

_vehicle_1454 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7751.3984, 12574.767, 0.0001373291];


  _vehicle_1454 = _this;
  _this setDir 212.74118;
  _this setPos [7751.3984, 12574.767, 0.0001373291];
};

_vehicle_1457 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7745.4253, 12602.001, 9.9182129e-005];


  _vehicle_1457 = _this;
  _this setDir 133.25655;
  _this setPos [7745.4253, 12602.001, 9.9182129e-005];
};

_vehicle_1460 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7772.4795, 12625.15, 2.2888184e-005];


  _vehicle_1460 = _this;
  _this setDir 152.8499;
  _this setPos [7772.4795, 12625.15, 2.2888184e-005];
};

_vehicle_1463 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7796.8926, 12638.414, 0.00010681152];


  _vehicle_1463 = _this;
  _this setDir 152.8499;
  _this setPos [7796.8926, 12638.414, 0.00010681152];
};

_vehicle_1465 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7821.3228, 12651.434, 0.00015258789];


  _vehicle_1465 = _this;
  _this setDir 152.8499;
  _this setPos [7821.3228, 12651.434, 0.00015258789];
};

_vehicle_1467 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7865.5903, 12656.458, 7.6293945e-005];


  _vehicle_1467 = _this;
  _this setDir 205.9785;
  _this setPos [7865.5903, 12656.458, 7.6293945e-005];
};

_vehicle_1469 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7879.3843, 12640.64, 0.00011444092];


  _vehicle_1469 = _this;
  _this setDir 269.20233;
  _this setPos [7879.3843, 12640.64, 0.00011444092];
};

_vehicle_1471 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7878.5439, 12617.705, 7.6293945e-005];


  _vehicle_1471 = _this;
  _this setDir 284.20206;
  _this setPos [7878.5439, 12617.705, 7.6293945e-005];
};

_vehicle_1473 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7851.144, 12597.788, 9.9182129e-005];


  _vehicle_1473 = _this;
  _this setDir 338.24484;
  _this setPos [7851.144, 12597.788, 9.9182129e-005];
};

_vehicle_1475 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7821.7944, 12593.017, 3.8146973e-005];


  _vehicle_1475 = _this;
  _this setDir 338.04602;
  _this setPos [7821.7944, 12593.017, 3.8146973e-005];
};

_vehicle_1477 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7789.3413, 12582.643, 0.00015258789];


  _vehicle_1477 = _this;
  _this setDir 337.26459;
  _this setPos [7789.3413, 12582.643, 0.00015258789];
};

_vehicle_1479 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7720.3975, 12619.502, 0.00010681152];


  _vehicle_1479 = _this;
  _this setDir 16.923697;
  _this setPos [7720.3975, 12619.502, 0.00010681152];
};

_vehicle_1481 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7721.5454, 12662.101, -1.5258789e-005];


  _vehicle_1481 = _this;
  _this setDir 127.10234;
  _this setPos [7721.5454, 12662.101, -1.5258789e-005];
};

_vehicle_1487 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Chair_EP1" createVehicle [7726.2422, 12648.227, -0.0029579438];


  _vehicle_1487 = _this;
  _this setDir 42.077667;
  _this setPos [7726.2422, 12648.227, -0.0029579438];
};

_vehicle_1491 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Chair_EP1" createVehicle [7726.5444, 12645.13, 0.0049426812];


  _vehicle_1491 = _this;
  _this setDir -59.887779;
  _this setPos [7726.5444, 12645.13, 0.0049426812];
};

_vehicle_1494 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Chair_EP1" createVehicle [7725.8042, 12646.955, -0.12132435];


  _vehicle_1494 = _this;
  _this setDir 110.9845;
  _this setVehicleInit "this setVectorUp [1,1,0];";
  _this setPos [7725.8042, 12646.955, -0.12132435];
};

_vehicle_1500 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SmallCraftTable_dz" createVehicle [7727.9194, 12649.016, -2.2888184e-005];


  _vehicle_1500 = _this;
  _this setPos [7727.9194, 12649.016, -2.2888184e-005];
};

_vehicle_1502 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Bench_EP1" createVehicle [7724.6289, 12651.257, -2.2888184e-005];


  _vehicle_1502 = _this;
  _this setDir 58.487434;
  _this setPos [7724.6289, 12651.257, -2.2888184e-005];
};

_vehicle_1504 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldChair" createVehicle [7728.2085, 12645.371, 7.6293945e-006];


  _vehicle_1504 = _this;
  _this setDir 152.88995;
  _this setPos [7728.2085, 12645.371, 7.6293945e-006];
};

_vehicle_1505 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Crates_EP1" createVehicle [7727.9028, 12648.913, 0.80981505];


  _vehicle_1505 = _this;
  _this setPos [7727.9028, 12648.913, 0.80981505];
};

_vehicle_1506 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Bucket_EP1" createVehicle [7728.0898, 12648.938, 7.6293945e-006];


  _vehicle_1506 = _this;
  _this setDir 5.7637253;
  _this setPos [7728.0898, 12648.938, 7.6293945e-006];
};

_vehicle_1509 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Bucket_EP1" createVehicle [7700.0747, 12657.369, 5.3405762e-005];


  _vehicle_1509 = _this;
  _this setDir -11.365155;
  _this setPos [7700.0747, 12657.369, 5.3405762e-005];
};

_vehicle_1511 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_campfire" createVehicle [7727.3496, 12646.854, 5.3405762e-005];


  _vehicle_1511 = _this;
  _this setPos [7727.3496, 12646.854, 5.3405762e-005];
};

_vehicle_1513 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7708.0449, 12681.436, 0.022593636];


  _vehicle_1513 = _this;
  _this setDir 58.261475;
  _this setPos [7708.0449, 12681.436, 0.022593636];
};

_vehicle_1515 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7691.8184, 12678.618, -0.0001373291];


  _vehicle_1515 = _this;
  _this setDir 58.261475;
  _this setPos [7691.8184, 12678.618, -0.0001373291];
};

_vehicle_1517 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7669.6797, 12680.837, 0.00012207031];


  _vehicle_1517 = _this;
  _this setDir 58.261475;
  _this setPos [7669.6797, 12680.837, 0.00012207031];
};

_vehicle_1519 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7693.0049, 12673.183, -7.6293945e-006];


  _vehicle_1519 = _this;
  _this setDir 58.261475;
  _this setPos [7693.0049, 12673.183, -7.6293945e-006];
};

_vehicle_1521 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7718.6807, 12699.686, -0.00010681152];


  _vehicle_1521 = _this;
  _this setDir 58.261475;
  _this setPos [7718.6807, 12699.686, -0.00010681152];
};

_vehicle_1523 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7725.3823, 12695.758, -7.6293945e-005];


  _vehicle_1523 = _this;
  _this setDir 58.261475;
  _this setPos [7725.3823, 12695.758, -7.6293945e-005];
};

_vehicle_1525 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7699.5283, 12709.805, 2.2888184e-005];


  _vehicle_1525 = _this;
  _this setDir 58.261475;
  _this setPos [7699.5283, 12709.805, 2.2888184e-005];
};

_vehicle_1527 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7680.6895, 12641.788, -2.2888184e-005];


  _vehicle_1527 = _this;
  _this setDir 58.261475;
  _this setPos [7680.6895, 12641.788, -2.2888184e-005];
};

_vehicle_1529 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7683.1489, 12632.538, 2.2888184e-005];


  _vehicle_1529 = _this;
  _this setDir 58.261475;
  _this setPos [7683.1489, 12632.538, 2.2888184e-005];
};

_vehicle_1532 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_60_10" createVehicle [7772.603, 12601.43, 1.5258789e-005];


  _vehicle_1532 = _this;
  _this setDir -115.46741;
  _this setPos [7772.603, 12601.43, 1.5258789e-005];
};

_vehicle_1538 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_15_75" createVehicle [7783.2573, 12607.872, 0.00012207031];


  _vehicle_1538 = _this;
  _this setDir 55.497849;
  _this setPos [7783.2573, 12607.872, 0.00012207031];
};

_vehicle_1541 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_75" createVehicle [7783.4966, 12607.726, 4.5776367e-005];


  _vehicle_1541 = _this;
  _this setDir -124.51134;
  _this setPos [7783.4966, 12607.726, 4.5776367e-005];
};

_vehicle_1544 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_60_10" createVehicle [7766.1431, 12600.765, 7.6293945e-005];


  _vehicle_1544 = _this;
  _this setDir -85.747574;
  _this setPos [7766.1431, 12600.765, 7.6293945e-005];
};

_vehicle_1547 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7727.374, 12647.231, -7.6293945e-006];


  _vehicle_1547 = _this;
  _this setPos [7727.374, 12647.231, -7.6293945e-006];
};

_vehicle_1549 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7775.0596, 12618.317, 2.2888184e-005];


  _vehicle_1549 = _this;
  _this setDir -209.98817;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7775.0596, 12618.317, 2.2888184e-005];
};

_vehicle_1552 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7767.2803, 12613.756, 5.3405762e-005];


  _vehicle_1552 = _this;
  _this setDir -212.13522;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7767.2803, 12613.756, 5.3405762e-005];
};

_vehicle_1555 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7753.2993, 12593.465, 7.6293945e-006];


  _vehicle_1555 = _this;
  _this setDir -106.88021;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7753.2993, 12593.465, 7.6293945e-006];
};

_vehicle_1558 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_3" createVehicle [7752.0552, 12601.854, -2.2888184e-005];


  _vehicle_1558 = _this;
  _this setDir -412.35217;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7752.0552, 12601.854, -2.2888184e-005];
};

_vehicle_1560 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_3" createVehicle [7754.0532, 12604.128, 3.8146973e-005];


  _vehicle_1560 = _this;
  _this setDir -44.117306;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7754.0532, 12604.128, 3.8146973e-005];
};

_vehicle_1563 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7755.1924, 12599.356, 1.5258789e-005];


  _vehicle_1563 = _this;
  _this setDir 44.08004;
  _this setPos [7755.1924, 12599.356, 1.5258789e-005];
};

_vehicle_1565 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7719.4375, 12629.505, -2.2888184e-005];


  _vehicle_1565 = _this;
  _this setDir 52.136158;
  _this setPos [7719.4375, 12629.505, -2.2888184e-005];
};

_vehicle_1568 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_Workshop01_02" createVehicle [7812.5239, 12628.276, 0.23267296];


  _vehicle_1568 = _this;
  _this setDir 71.326126;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7812.5239, 12628.276, 0.23267296];
};

_vehicle_1571 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7756.271, 12651.111, 0.20699508];


  _vehicle_1571 = _this;
  _this setDir 57.462753;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7756.271, 12651.111, 0.20699508];
};

_vehicle_1574 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7752.9683, 12655.967, 0.14828065];


  _vehicle_1574 = _this;
  _this setDir 55.254215;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7752.9683, 12655.967, 0.14828065];
};

_vehicle_1577 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7749.5708, 12660.865, 0.20536713];


  _vehicle_1577 = _this;
  _this setDir 55.254215;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7749.5708, 12660.865, 0.20536713];
};

_vehicle_1580 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7742.9448, 12670.754, 0.24165122];


  _vehicle_1580 = _this;
  _this setDir 57.462753;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7742.9448, 12670.754, 0.24165122];
};

_vehicle_1585 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7736.2993, 12680.466, 0.44504744];


  _vehicle_1585 = _this;
  _this setDir 55.254215;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7736.2993, 12680.466, 0.44504744];
};

_vehicle_1588 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7721.7217, 12673.45, 0.033670809];


  _vehicle_1588 = _this;
  _this setDir -32.387539;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7721.7217, 12673.45, 0.033670809];
};

_vehicle_1591 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_WindBreak" createVehicle [7753.0444, 12248.188, 9.1552734e-005];


  _vehicle_1591 = _this;
  _this setDir 18.532312;
  _this setPos [7753.0444, 12248.188, 9.1552734e-005];
};

_vehicle_1593 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_WindBreak" createVehicle [7756.7485, 12247.196, 0.00012207031];


  _vehicle_1593 = _this;
  _this setDir 14.660294;
  _this setPos [7756.7485, 12247.196, 0.00012207031];
};

_vehicle_1595 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_WindBreak" createVehicle [7749.3086, 12249.368, 0.00022888184];


  _vehicle_1595 = _this;
  _this setDir 18.800556;
  _this setPos [7749.3086, 12249.368, 0.00022888184];
};

_vehicle_1597 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_WindBreak" createVehicle [7745.7295, 12250.79, 0.00019836426];


  _vehicle_1597 = _this;
  _this setDir 22.338715;
  _this setPos [7745.7295, 12250.79, 0.00019836426];
};

_vehicle_1599 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_WindBreak" createVehicle [7879.0093, 12222.403, -4.5776367e-005];


  _vehicle_1599 = _this;
  _this setPos [7879.0093, 12222.403, -4.5776367e-005];
};

_vehicle_1601 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_WindBreak" createVehicle [7875.0942, 12222.522, -3.0517578e-005];


  _vehicle_1601 = _this;
  _this setPos [7875.0942, 12222.522, -3.0517578e-005];
};

_vehicle_1603 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_WindBreak" createVehicle [7871.0586, 12222.522, 3.0517578e-005];


  _vehicle_1603 = _this;
  _this setPos [7871.0586, 12222.522, 3.0517578e-005];
};

_vehicle_1605 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_WindBreak" createVehicle [7867.0483, 12222.542, 7.6293945e-005];


  _vehicle_1605 = _this;
  _this setPos [7867.0483, 12222.542, 7.6293945e-005];
};

_vehicle_1611 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [7632.231, 12356.156, 0.36922231];


  _vehicle_1611 = _this;
  _this setDir 17.029335;
  _this setPos [7632.231, 12356.156, 0.36922231];
};

_vehicle_1613 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [7653.7334, 12346.097, 0.30834171];


  _vehicle_1613 = _this;
  _this setDir 21.233608;
  _this setPos [7653.7334, 12346.097, 0.30834171];
};

_vehicle_1615 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [7970.8716, 12155.951, 0.51185071];


  _vehicle_1615 = _this;
  _this setDir -4.6415567;
  _this setPos [7970.8716, 12155.951, 0.51185071];
};

_vehicle_1618 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [7985.9258, 12156.199, 0.25162002];


  _vehicle_1618 = _this;
  _this setDir -4.6415567;
  _this setPos [7985.9258, 12156.199, 0.25162002];
};

_vehicle_1620 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [7924.1143, 12150.918, 0.00035095215];


  _vehicle_1620 = _this;
  _this setDir -9.4444351;
  _this setPos [7924.1143, 12150.918, 0.00035095215];
};

_vehicle_1622 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [8032.0952, 12150.245, 0.16680051];


  _vehicle_1622 = _this;
  _this setDir -8.2560425;
  _this setPos [8032.0952, 12150.245, 0.16680051];
};

_vehicle_1624 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [8062.2163, 12144.774, 0.26019099];


  _vehicle_1624 = _this;
  _this setDir -0.21729359;
  _this setPos [8062.2163, 12144.774, 0.26019099];
};

_vehicle_1626 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [8071.687, 12144.063, 6.1035156e-005];


  _vehicle_1626 = _this;
  _this setDir -175.67734;
  _this setPos [8071.687, 12144.063, 6.1035156e-005];
};

_vehicle_1628 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [7947.0083, 12153.862, 0.14351761];


  _vehicle_1628 = _this;
  _this setDir -4.6415567;
  _this setPos [7947.0083, 12153.862, 0.14351761];
};

_vehicle_1631 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7721.687, 12282.861, -0.00022888184];


  _vehicle_1631 = _this;
  _this setDir -4.9652934;
  _this setPos [7721.687, 12282.861, -0.00022888184];
};

_vehicle_1634 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7679.5713, 12353.59, 0.00019836426];


  _vehicle_1634 = _this;
  _this setDir 58.261475;
  _this setPos [7679.5713, 12353.59, 0.00019836426];
};

_vehicle_1643 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7680.332, 12348.311, -1.5258789e-005];


  _vehicle_1643 = _this;
  _this setDir -4.9652934;
  _this setPos [7680.332, 12348.311, -1.5258789e-005];
};

_vehicle_1646 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_betula2w" createVehicle [7741.4585, 12252.815, 0.00022888184];


  _vehicle_1646 = _this;
  _this setDir -4.9652934;
  _this setPos [7741.4585, 12252.815, 0.00022888184];
};

_vehicle_1649 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus2s" createVehicle [7928.0942, 12227.546, 7.6293945e-005];


  _vehicle_1649 = _this;
  _this setDir -4.9652934;
  _this setPos [7928.0942, 12227.546, 7.6293945e-005];
};

_vehicle_1654 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_betula2w" createVehicle [7949.2651, 12201.791, 0.00010681152];


  _vehicle_1654 = _this;
  _this setDir -20.418583;
  _this setPos [7949.2651, 12201.791, 0.00010681152];
};

_vehicle_1655 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7945.4023, 12203.514, -0.24807739];


  _vehicle_1655 = _this;
  _this setDir -20.418583;
  _this setPos [7945.4023, 12203.514, -0.24807739];
};

_vehicle_1656 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus2s" createVehicle [7952.457, 12199.094, 0.37802124];


  _vehicle_1656 = _this;
  _this setDir -20.418583;
  _this setPos [7952.457, 12199.094, 0.37802124];
};

_vehicle_1660 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_betula2w" createVehicle [8096.8701, 12104.438, 4.5776367e-005];


  _vehicle_1660 = _this;
  _this setDir -26.477226;
  _this setPos [8096.8701, 12104.438, 4.5776367e-005];
};

_vehicle_1661 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [8092.8447, 12105.727, -0.27862549];


  _vehicle_1661 = _this;
  _this setDir -26.477226;
  _this setPos [8092.8447, 12105.727, -0.27862549];
};

_vehicle_1662 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus2s" createVehicle [8100.3252, 12102.077, 0.36973572];


  _vehicle_1662 = _this;
  _this setDir -26.477226;
  _this setPos [8100.3252, 12102.077, 0.36973572];
};

_vehicle_1667 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7722.1094, 12289.558, 0.00033569336];


  _vehicle_1667 = _this;
  _this setDir -20.816936;
  _this setPos [7722.1094, 12289.558, 0.00033569336];
};

_vehicle_1669 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7924.7227, 12227.829, 0.00024414063];


  _vehicle_1669 = _this;
  _this setDir -20.816936;
  _this setPos [7924.7227, 12227.829, 0.00024414063];
};

_vehicle_1671 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [8096.3896, 12101.713, 0.00016784668];


  _vehicle_1671 = _this;
  _this setDir -20.816936;
  _this setPos [8096.3896, 12101.713, 0.00016784668];
};

_vehicle_1683 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Boulder1" createVehicle [7676.7256, 12398.646, 0.00025939941];


  _vehicle_1683 = _this;
  _this setPos [7676.7256, 12398.646, 0.00025939941];
};

_vehicle_1684 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Boulder2" createVehicle [7675.2363, 12396.066, 0.061553955];


  _vehicle_1684 = _this;
  _this setDir 21.0639;
  _this setPos [7675.2363, 12396.066, 0.061553955];
};

_vehicle_1685 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Stone" createVehicle [7677.4258, 12395.498, -0.0050964355];


  _vehicle_1685 = _this;
  _this setPos [7677.4258, 12395.498, -0.0050964355];
};

_vehicle_1689 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Boulder1" createVehicle [7948.042, 12204.901, 9.1552734e-005];


  _vehicle_1689 = _this;
  _this setPos [7948.042, 12204.901, 9.1552734e-005];
};

_vehicle_1690 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Boulder2" createVehicle [7998.562, 12156.688, 0.023369178];


  _vehicle_1690 = _this;
  _this setPos [7998.562, 12156.688, 0.023369178];
};

_vehicle_1691 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Stone" createVehicle [7951.0693, 12202.53, 0.044786636];


  _vehicle_1691 = _this;
  _this setDir 135.40482;
  _this setPos [7951.0693, 12202.53, 0.044786636];
};

_vehicle_1695 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Boulder1" createVehicle [8217.9365, 12050.63, 6.1035156e-005];


  _vehicle_1695 = _this;
  _this setDir 14.772076;
  _this setPos [8217.9365, 12050.63, 6.1035156e-005];
};

_vehicle_1696 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Boulder2" createVehicle [8217.1416, 12048.955, 0.29940227];


  _vehicle_1696 = _this;
  _this setPos [8217.1416, 12048.955, 0.29940227];
};

_vehicle_1697 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Stone" createVehicle [8215.5469, 12050.654, 0.043756753];


  _vehicle_1697 = _this;
  _this setDir -89.606598;
  _this setPos [8215.5469, 12050.654, 0.043756753];
};

_vehicle_1702 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7676.6133, 12396.769, 0.00024414063];


  _vehicle_1702 = _this;
  _this setDir -4.9652934;
  _this setPos [7676.6133, 12396.769, 0.00024414063];
};

_vehicle_1705 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Boulder1" createVehicle [7753.2261, 12578.139, 3.0517578e-005];


  _vehicle_1705 = _this;
  _this setDir 104.75888;
  _this setPos [7753.2261, 12578.139, 3.0517578e-005];
};

_vehicle_1707 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Boulder1" createVehicle [7740.1084, 12625.808, 6.8664551e-005];


  _vehicle_1707 = _this;
  _this setPos [7740.1084, 12625.808, 6.8664551e-005];
};

_vehicle_1710 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_R2_Stone" createVehicle [7756.6348, 12598.038, 3.0517578e-005];


  _vehicle_1710 = _this;
  _this setDir 164.66101;
  _this setPos [7756.6348, 12598.038, 3.0517578e-005];
};

_vehicle_1713 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9_2" createVehicle [7853.7866, 12607.339, 9.9182129e-005];


  _vehicle_1713 = _this;
  _this setDir -12.80795;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7853.7866, 12607.339, 9.9182129e-005];
};

_vehicle_1716 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9_2" createVehicle [7851.5659, 12653.754, 0.15024815];


  _vehicle_1716 = _this;
  _this setDir 18.774645;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7851.5659, 12653.754, 0.15024815];
};

_vehicle_1719 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7868.7178, 12611.093];


  _vehicle_1719 = _this;
  _this setDir -197.19313;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7868.7178, 12611.093];
};

_vehicle_1722 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7872.0127, 12615.463, 0.0001449585];


  _vehicle_1722 = _this;
  _this setDir -448.06226;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7872.0127, 12615.463, 0.0001449585];
};

_vehicle_1725 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7751.7793, 12578.25, 4.5776367e-005];


  _vehicle_1725 = _this;
  _this setDir 41.869556;
  _this setPos [7751.7793, 12578.25, 4.5776367e-005];
};

_vehicle_1728 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe1_stair" createVehicle [7818.2563, 12577.563, 0.15283844];


  _vehicle_1728 = _this;
  _this setDir 344.94992;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7818.2563, 12577.563, 0.15283844];
};

_vehicle_1731 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe1_90degR" createVehicle [7818.6284, 12569.696, 0.0096162893];


  _vehicle_1731 = _this;
  _this setDir -104.1507;
  _this setPos [7818.6284, 12569.696, 0.0096162893];
};

_vehicle_1738 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe1_90degL" createVehicle [7816.7324, 12582.574, 0.289303];


  _vehicle_1738 = _this;
  _this setDir -17.522335;
  _this setPos [7816.7324, 12582.574, 0.289303];
};

_vehicle_1741 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_IndPipe1_valve" createVehicle [7810.3877, 12578.745, 0.07488855];


  _vehicle_1741 = _this;
  _this setDir -107.87357;
  _this setPos [7810.3877, 12578.745, 0.07488855];
};

_vehicle_1745 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_GContainer_Big" createVehicle [7823.6919, 12606.321, 3.0517578e-005];


  _vehicle_1745 = _this;
  _this setDir -18.933008;
  _this setPos [7823.6919, 12606.321, 3.0517578e-005];
};

_vehicle_1748 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Kontejner" createVehicle [7789.6138, 12592.117, 0.00019073486];


  _vehicle_1748 = _this;
  _this setDir -13.263685;
  _this setPos [7789.6138, 12592.117, 0.00019073486];
};

_vehicle_1757 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_pneu" createVehicle [7794.6445, 12623.011, -1.5258789e-005];


  _vehicle_1757 = _this;
  _this setDir 54.558628;
  _this setPos [7794.6445, 12623.011, -1.5258789e-005];
};

_vehicle_1758 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_TyreHeap" createVehicle [7817.3745, 12607.859, -0.072570801];


  _vehicle_1758 = _this;
  _this setDir 10.805914;
  _this setPos [7817.3745, 12607.859, -0.072570801];
};

_vehicle_1763 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Misc_Garb_Heap_EP1" createVehicle [7819.5273, 12602.975, -0.0021688398];


  _vehicle_1763 = _this;
  _this setDir -39.03159;
  _this setPos [7819.5273, 12602.975, -0.0021688398];
};

_vehicle_1772 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Kontejner" createVehicle [7789.3247, 12593.746, -2.2888184e-005];


  _vehicle_1772 = _this;
  _this setDir -25.274664;
  _this setPos [7789.3247, 12593.746, -2.2888184e-005];
};

_vehicle_1775 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_GContainer_Big" createVehicle [7822.4424, 12609.861, 5.3405762e-005];


  _vehicle_1775 = _this;
  _this setDir -18.933008;
  _this setPos [7822.4424, 12609.861, 5.3405762e-005];
};

_vehicle_1778 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_paleta" createVehicle [7817.8477, 12605.875, 1.5258789e-005];


  _vehicle_1778 = _this;
  _this setDir 10.805914;
  _this setPos [7817.8477, 12605.875, 1.5258789e-005];
};

_vehicle_1781 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7813.7783, 12603.403, 2.2888184e-005];


  _vehicle_1781 = _this;
  _this setDir 75.529778;
  _this setPos [7813.7783, 12603.403, 2.2888184e-005];
};

_vehicle_1783 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7812.2813, 12606.841, 3.0517578e-005];


  _vehicle_1783 = _this;
  _this setDir 66.481865;
  _this setPos [7812.2813, 12606.841, 3.0517578e-005];
};

_vehicle_1785 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7807.4063, 12602.67, 6.8664551e-005];


  _vehicle_1785 = _this;
  _this setDir 204.63066;
  _this setPos [7807.4063, 12602.67, 6.8664551e-005];
};

_vehicle_1788 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo1B" createVehicle [7786.9214, 12619.254, 4.5776367e-005];


  _vehicle_1788 = _this;
  _this setDir 133.15564;
  _this setPos [7786.9214, 12619.254, 4.5776367e-005];
};

_vehicle_1790 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo1B" createVehicle [7784.0835, 12617.356, -7.6293945e-006];


  _vehicle_1790 = _this;
  _this setDir 155.2531;
  _this setPos [7784.0835, 12617.356, -7.6293945e-006];
};

_vehicle_1793 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo2B" createVehicle [7776.9707, 12614.32, 3.0517578e-005];


  _vehicle_1793 = _this;
  _this setDir -26.055584;
  _this setPos [7776.9707, 12614.32, 3.0517578e-005];
};

_vehicle_1802 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7768.4341, 12607.726, 3.8146973e-005];


  _vehicle_1802 = _this;
  _this setDir -4.9652934;
  _this setPos [7768.4341, 12607.726, 3.8146973e-005];
};

_vehicle_1804 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7816.7383, 12635.597, 8.392334e-005];


  _vehicle_1804 = _this;
  _this setDir -4.9652934;
  _this setPos [7816.7383, 12635.597, 8.392334e-005];
};

_vehicle_1806 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7813.0674, 12600.722, 3.0517578e-005];


  _vehicle_1806 = _this;
  _this setDir -4.9652934;
  _this setPos [7813.0674, 12600.722, 3.0517578e-005];
};

_vehicle_1809 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_betula2w" createVehicle [7811.939, 12598.788, 7.6293945e-006];


  _vehicle_1809 = _this;
  _this setDir 102.25393;
  _this setPos [7811.939, 12598.788, 7.6293945e-006];
};

_vehicle_1812 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7784.6221, 12636.64, 7.6293945e-005];


  _vehicle_1812 = _this;
  _this setDir 44.08004;
  _this setPos [7784.6221, 12636.64, 7.6293945e-005];
};

_vehicle_1814 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7827.9907, 12601.883, -7.6293945e-006];


  _vehicle_1814 = _this;
  _this setDir 44.08004;
  _this setPos [7827.9907, 12601.883, -7.6293945e-006];
};

_vehicle_1816 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7843.4541, 12582.604, 7.6293945e-006];


  _vehicle_1816 = _this;
  _this setDir 44.08004;
  _this setPos [7843.4541, 12582.604, 7.6293945e-006];
};

_vehicle_1818 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7884.8657, 12593.197, 8.392334e-005];


  _vehicle_1818 = _this;
  _this setDir 44.08004;
  _this setPos [7884.8657, 12593.197, 8.392334e-005];
};

_vehicle_1820 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7867.2935, 12617.762, 4.5776367e-005];


  _vehicle_1820 = _this;
  _this setDir 44.08004;
  _this setPos [7867.2935, 12617.762, 4.5776367e-005];
};

_vehicle_1822 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7851.9492, 12640.779, -0.015652796];


  _vehicle_1822 = _this;
  _this setDir -16.151775;
  _this setPos [7851.9492, 12640.779, -0.015652796];
};

_vehicle_1824 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7815.7041, 12651.781, 0.03782098];


  _vehicle_1824 = _this;
  _this setDir 44.08004;
  _this setPos [7815.7041, 12651.781, 0.03782098];
};

_vehicle_1826 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7829.1621, 12658.735, 3.8146973e-005];


  _vehicle_1826 = _this;
  _this setDir 121.24178;
  _this setVehicleInit "this setVectorUp [0,0.2,1];";
  _this setPos [7829.1621, 12658.735, 3.8146973e-005];
};

_vehicle_1829 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7745.3521, 12551.158, 0.00015258789];


  _vehicle_1829 = _this;
  _this setDir -20.816936;
  _this setPos [7745.3521, 12551.158, 0.00015258789];
};

_vehicle_1831 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7713.4946, 12581.323, 5.3405762e-005];


  _vehicle_1831 = _this;
  _this setDir -20.816936;
  _this setPos [7713.4946, 12581.323, 5.3405762e-005];
};

_vehicle_1833 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7738.9575, 12624.373, 0];


  _vehicle_1833 = _this;
  _this setDir -20.816936;
  _this setPos [7738.9575, 12624.373, 0];
};

_vehicle_1835 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7850.395, 12675.336, 4.5776367e-005];


  _vehicle_1835 = _this;
  _this setDir -20.816936;
  _this setPos [7850.395, 12675.336, 4.5776367e-005];
};

_vehicle_1837 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7856.1421, 12616.145, 8.392334e-005];


  _vehicle_1837 = _this;
  _this setDir 81.362343;
  _this setPos [7856.1421, 12616.145, 8.392334e-005];
};

_vehicle_1839 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7688.3843, 12524.267, 3.0517578e-005];


  _vehicle_1839 = _this;
  _this setDir -20.816936;
  _this setPos [7688.3843, 12524.267, 3.0517578e-005];
};

_vehicle_1843 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7712.8008, 12560.506, 1.5258789e-005];


  _vehicle_1843 = _this;
  _this setDir -4.9652934;
  _this setPos [7712.8008, 12560.506, 1.5258789e-005];
};

_vehicle_1845 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7699.7178, 12526.124, 2.2888184e-005];


  _vehicle_1845 = _this;
  _this setDir -171.407;
  _this setPos [7699.7178, 12526.124, 2.2888184e-005];
};

_vehicle_1847 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7726.1094, 12534.006, 0.00010681152];


  _vehicle_1847 = _this;
  _this setDir -4.9652934;
  _this setPos [7726.1094, 12534.006, 0.00010681152];
};

_vehicle_1849 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7748.147, 12558.806, 2.2888184e-005];


  _vehicle_1849 = _this;
  _this setDir -102.5173;
  _this setPos [7748.147, 12558.806, 2.2888184e-005];
};

_vehicle_1867 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_TankSmall2" createVehicle [7804.4976, 12627.524, 3.0517578e-005];


  _vehicle_1867 = _this;
  _this setDir 70.371391;
  _this setPos [7804.4976, 12627.524, 3.0517578e-005];
};

_vehicle_1870 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_TankSmall2" createVehicle [7800.3613, 12626.056, 4.5776367e-005];


  _vehicle_1870 = _this;
  _this setDir 70.876022;
  _this setPos [7800.3613, 12626.056, 4.5776367e-005];
};

_vehicle_1873 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_A_FuelStation_Feed" createVehicle [7805.6572, 12624.504];


  _vehicle_1873 = _this;
  _this setDir -19.979933;
  _this setPos [7805.6572, 12624.504];
};

_vehicle_1877 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_A_FuelStation_Feed" createVehicle [7801.3711, 12622.957, 3.0517578e-005];


  _vehicle_1877 = _this;
  _this setDir -18.771927;
  _this setPos [7801.3711, 12622.957, 3.0517578e-005];
};

_vehicle_1915 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_path_60_10" createVehicle [7856.3525, 12591.732, -5.3405762e-005];


  _vehicle_1915 = _this;
  _this setDir -84.290199;
  _this setPos [7856.3525, 12591.732, -5.3405762e-005];
};

_vehicle_1916 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7848.5308, 12596.772, -0.016960144];


  _vehicle_1916 = _this;
  _this setPos [7848.5308, 12596.772, -0.016960144];
};

_vehicle_1917 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7849.2217, 12595.48, -0.035995483];


  _vehicle_1917 = _this;
  _this setPos [7849.2217, 12595.48, -0.035995483];
};

_vehicle_1918 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7850.083, 12594.446, -0.047325134];


  _vehicle_1918 = _this;
  _this setPos [7850.083, 12594.446, -0.047325134];
};

_vehicle_1919 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7851.165, 12593.656, -0.045433044];


  _vehicle_1919 = _this;
  _this setPos [7851.165, 12593.656, -0.045433044];
};

_vehicle_1920 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7853.3296, 12592.517, -0.035491943];


  _vehicle_1920 = _this;
  _this setPos [7853.3296, 12592.517, -0.035491943];
};

_vehicle_1921 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7854.3833, 12591.605, -0.021697998];


  _vehicle_1921 = _this;
  _this setPos [7854.3833, 12591.605, -0.021697998];
};

_vehicle_1922 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7852.0376, 12592.499, -0.050796509];


  _vehicle_1922 = _this;
  _this setPos [7852.0376, 12592.499, -0.050796509];
};

_vehicle_1943 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Mi8Wreck" createVehicle [7831.5459, 12639.111, 11.895364];


  _vehicle_1943 = _this;
  _this setDir 107.72562;
  _this setVehicleInit "this setVectorUp [0,0.1,1];";
  _this setPos [7831.5459, 12639.111, 11.895364];
};

_vehicle_1945 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UralWreck" createVehicle [7692.6631, 12497.638, 0.00016784668];


  _vehicle_1945 = _this;
  _this setDir -5.9451671;
  _this setPos [7692.6631, 12497.638, 0.00016784668];
};

_vehicle_1948 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [7688.6582, 12495.378, 0.0001449585];


  _vehicle_1948 = _this;
  _this setDir 44.098309;
  _this setPos [7688.6582, 12495.378, 0.0001449585];
};

_vehicle_1951 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "datsun01Wreck" createVehicle [7686.6333, 12489.239, 0.00015258789];


  _vehicle_1951 = _this;
  _this setDir 232.77777;
  _this setPos [7686.6333, 12489.239, 0.00015258789];
};

_vehicle_1955 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "datsun02Wreck" createVehicle [7688.6646, 12483.206, 0.00012969971];


  _vehicle_1955 = _this;
  _this setDir 34.466957;
  _this setPos [7688.6646, 12483.206, 0.00012969971];
};

_vehicle_1956 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SKODAWreck" createVehicle [7684.0874, 12472.625, -0.058862612];


  _vehicle_1956 = _this;
  _this setDir -180.0759;
  _this setPos [7684.0874, 12472.625, -0.058862612];
};

_vehicle_1960 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "hiluxWreck" createVehicle [7698.8408, 12512.724, 3.0517578e-005];


  _vehicle_1960 = _this;
  _this setDir 24.858015;
  _this setPos [7698.8408, 12512.724, 3.0517578e-005];
};

_vehicle_1963 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UralWreck" createVehicle [7748.3662, 12572.502, 0.1073505];


  _vehicle_1963 = _this;
  _this setDir 10.213;
  _this setPos [7748.3662, 12572.502, 0.1073505];
};

_vehicle_1966 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SKODAWreck" createVehicle [7829.0386, 12621.867, 0.00010681152];


  _vehicle_1966 = _this;
  _this setDir 175.336;
  _this setPos [7829.0386, 12621.867, 0.00010681152];
};

_vehicle_1969 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [7833.1943, 12610.861, 0.11985391];


  _vehicle_1969 = _this;
  _this setDir 186.48119;
  _this setVehicleInit "this setVectorUp [-0.08,-0.07,1];";
  _this setPos [7833.1943, 12610.861, 0.11985391];
};

_vehicle_1971 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [7743.4219, 12635.737, 7.6293945e-006];


  _vehicle_1971 = _this;
  _this setDir 143.22217;
  _this setPos [7743.4219, 12635.737, 7.6293945e-006];
};

_vehicle_1974 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "datsun02Wreck" createVehicle [7724.7065, 12548.633, -7.6293945e-006];


  _vehicle_1974 = _this;
  _this setDir 149.01137;
  _this setPos [7724.7065, 12548.633, -7.6293945e-006];
};

_vehicle_1977 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SKODAWreck" createVehicle [7764.3203, 12602.021, 3.8146973e-005];


  _vehicle_1977 = _this;
  _this setDir 118.90509;
  _this setPos [7764.3203, 12602.021, 3.8146973e-005];
};

_vehicle_2019 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_cargo_cont_small_EP1" createVehicle [7718.7051, 12515.634, 6.8664551e-005];


  _vehicle_2019 = _this;
  _this setDir -245.5322;
  _this setPos [7718.7051, 12515.634, 6.8664551e-005];
};

_vehicle_2020 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_Cargo1Bo_military" createVehicle [7720.3442, 12519.457, 0.12693252];


  _vehicle_2020 = _this;
  _this setDir -53.617828;
  _this setPos [7720.3442, 12519.457, 0.12693252];
};

_vehicle_2021 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Misc_Cargo1E_EP1" createVehicle [7712.5977, 12512.864, -0.19803619];


  _vehicle_2021 = _this;
  _this setDir -65.276146;
  _this setPos [7712.5977, 12512.864, -0.19803619];
};

_vehicle_2026 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_Cargo1Bo_military" createVehicle [7781.064, 12614.826, 0.00012969971];


  _vehicle_2026 = _this;
  _this setDir -27.895704;
  _this setPos [7781.064, 12614.826, 0.00012969971];
};

_vehicle_2029 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UralWreck" createVehicle [7803.5903, 12618.259, 9.1552734e-005];


  _vehicle_2029 = _this;
  _this setDir 46.550224;
  _this setPos [7803.5903, 12618.259, 9.1552734e-005];
};

_vehicle_2034 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [7776.3394, 12582.546, 0.09212894];


  _vehicle_2034 = _this;
  _this setDir 232.32445;
  _this setVehicleInit "this setVectorUp [-0.08,-0.07,1];";
  _this setPos [7776.3394, 12582.546, 0.09212894];
};

_vehicle_2035 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_datsun01T" createVehicle [7787.7085, 12566.985, -0.018056341];


  _vehicle_2035 = _this;
  _this setDir 296.00208;
  _this setPos [7787.7085, 12566.985, -0.018056341];
};

_vehicle_2036 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SKODAWreck" createVehicle [7772.373, 12580.785, -0.008916216];


  _vehicle_2036 = _this;
  _this setDir 250.5936;
  _this setPos [7772.373, 12580.785, -0.008916216];
};

_vehicle_2041 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Shed_M02" createVehicle [7801.4766, 12603.357];


  _vehicle_2041 = _this;
  _this setDir -112.39171;
  _this setPos [7801.4766, 12603.357];
};

_vehicle_2044 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Bench_EP1" createVehicle [7738.9175, 12576.024];


  _vehicle_2044 = _this;
  _this setDir 149.35732;
  _this setPos [7738.9175, 12576.024];
};

_vehicle_2047 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench1" createVehicle [7717.0659, 12577.701, 6.1035156e-005];


  _vehicle_2047 = _this;
  _this setDir 54.380444;
  _this setPos [7717.0659, 12577.701, 6.1035156e-005];
};

_vehicle_2049 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench1" createVehicle [7717.9004, 12581.289, 3.0517578e-005];


  _vehicle_2049 = _this;
  _this setDir -37.520626;
  _this setPos [7717.9004, 12581.289, 3.0517578e-005];
};

_vehicle_2052 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Kontejner" createVehicle [7725.9644, 12581.821, 4.5776367e-005];


  _vehicle_2052 = _this;
  _this setDir 144.67116;
  _this setPos [7725.9644, 12581.821, 4.5776367e-005];
};

_vehicle_2055 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench2_noRoad" createVehicle [7753.9185, 12589.495, -3.8146973e-005];


  _vehicle_2055 = _this;
  _this setDir -107.29886;
  _this setPos [7753.9185, 12589.495, -3.8146973e-005];
};

_vehicle_2057 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench2_noRoad" createVehicle [7756.1606, 12582.707, 1.5258789e-005];


  _vehicle_2057 = _this;
  _this setDir -107.79748;
  _this setPos [7756.1606, 12582.707, 1.5258789e-005];
};

_vehicle_2060 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7800.3452, 12596.388, -2.2888184e-005];


  _vehicle_2060 = _this;
  _this setDir 14.772622;
  _this setPos [7800.3452, 12596.388, -2.2888184e-005];
};

_vehicle_2063 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "hiluxWreck" createVehicle [7799.5977, 12234.467, 0.00025939941];


  _vehicle_2063 = _this;
  _this setDir 91.926781;
  _this setPos [7799.5977, 12234.467, 0.00025939941];
};

_vehicle_2066 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SKODAWreck" createVehicle [7980.8081, 12190.571, 0.00038146973];


  _vehicle_2066 = _this;
  _this setDir -45.135147;
  _this setPos [7980.8081, 12190.571, 0.00038146973];
};

_vehicle_2068 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkClearMiddle" createVehicle [7827.312, 12636.207, 0.18608657];


  _vehicle_2068 = _this;
  _this setDir -19.798302;
  _this setPos [7827.312, 12636.207, 0.18608657];
};

_vehicle_2071 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_1_1000" createVehicle [7901.4883, 12629.815, 0.00019073486];


  _vehicle_2071 = _this;
  _this setDir 169.65346;
  _this setPos [7901.4883, 12629.815, 0.00019073486];
};

_vehicle_2074 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_25" createVehicle [7903.521, 12634.43, 0.0001373291];


  _vehicle_2074 = _this;
  _this setDir -93.487778;
  _this setPos [7903.521, 12634.43, 0.0001373291];
};

_vehicle_2081 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_12" createVehicle [7903.3262, 12634.442, 9.9182129e-005];


  _vehicle_2081 = _this;
  _this setDir 87.149544;
  _this setPos [7903.3262, 12634.442, 9.9182129e-005];
};

_vehicle_2084 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_6konec" createVehicle [7921.0518, 12635.277, 0.00012207031];


  _vehicle_2084 = _this;
  _this setDir -92.425903;
  _this setPos [7921.0518, 12635.277, 0.00012207031];
};

_vehicle_2087 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_6konec" createVehicle [7900.6299, 12635.931, 3.0517578e-005];


  _vehicle_2087 = _this;
  _this setDir -187.66949;
  _this setPos [7900.6299, 12635.931, 3.0517578e-005];
};

_vehicle_2096 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_01_main" createVehicle [7917.4717, 12599.935, -0.018983008];


  _vehicle_2096 = _this;
  _this setDir -9.5751905;
  _this setPos [7917.4717, 12599.935, -0.018983008];
};

_vehicle_2099 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_01_main" createVehicle [7918.1396, 12595.843, 5.3405762e-005];


  _vehicle_2099 = _this;
  _this setDir -9.7125874;
  _this setPos [7918.1396, 12595.843, 5.3405762e-005];
};

_vehicle_2104 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_01_end" createVehicle [7915.5469, 12612.005, -0.019024676];


  _vehicle_2104 = _this;
  _this setDir -8.7725954;
  _this setPos [7915.5469, 12612.005, -0.019024676];
};

_vehicle_2107 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_01_end" createVehicle [7918.1895, 12595.648, 0.00017547607];


  _vehicle_2107 = _this;
  _this setDir -9.6751947;
  _this setPos [7918.1895, 12595.648, 0.00017547607];
};

_vehicle_2110 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Zabradli_pruhovane_stred" createVehicle [7831.6016, 12619.991, 0.13792871];


  _vehicle_2110 = _this;
  _this setDir -19.099663;
  _this setPos [7831.6016, 12619.991, 0.13792871];
};

_vehicle_2113 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Zabradli_pruhovane_stred" createVehicle [7832.3599, 12617.729, 0.14893979];


  _vehicle_2113 = _this;
  _this setDir -18.267462;
  _this setPos [7832.3599, 12617.729, 0.14893979];
};

_vehicle_2119 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_25" createVehicle [7876.7686, 12642.437, 0.00029754639];


  _vehicle_2119 = _this;
  _this setDir -184.40236;
  _this setPos [7876.7686, 12642.437, 0.00029754639];
};

_vehicle_2122 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Zabradli_pruhovane_stred" createVehicle [7830.0586, 12624.47, 0.11895157];


  _vehicle_2122 = _this;
  _this setDir -19.099663;
  _this setPos [7830.0586, 12624.47, 0.11895157];
};

_vehicle_2124 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Zabradli_pruhovane_stred" createVehicle [7829.2891, 12626.701, 0.11903284];


  _vehicle_2124 = _this;
  _this setDir -19.099663;
  _this setPos [7829.2891, 12626.701, 0.11903284];
};

_vehicle_2127 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Zabradli_pruhovane_pravykonec" createVehicle [7828.5542, 12628.926, 0.12051488];


  _vehicle_2127 = _this;
  _this setDir -198.84883;
  _this setPos [7828.5542, 12628.926, 0.12051488];
};

_vehicle_2151 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7696.3408, 12657.642, 0.022173939];


  _vehicle_2151 = _this;
  _this setDir -32.738686;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7696.3408, 12657.642, 0.022173939];
};

_vehicle_2157 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Shed_M02" createVehicle [7918.1016, 12641.177, 9.1552734e-005];


  _vehicle_2157 = _this;
  _this setDir -186.76025;
  _this setPos [7918.1016, 12641.177, 9.1552734e-005];
};

_vehicle_2160 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_01_end" createVehicle [7917.457, 12599.764, 0.0026898091];


  _vehicle_2160 = _this;
  _this setDir -9.8302097;
  _this setPos [7917.457, 12599.764, 0.0026898091];
};

_vehicle_2163 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7798.1846, 12645.867, 3.0517578e-005];


  _vehicle_2163 = _this;
  _this setDir 47.447834;
  _this setPos [7798.1846, 12645.867, 3.0517578e-005];
};

_vehicle_2165 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7873.6797, 12659.805, -7.6293945e-006];


  _vehicle_2165 = _this;
  _this setDir -84.92836;
  _this setPos [7873.6797, 12659.805, -7.6293945e-006];
};

_vehicle_2167 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7769.3921, 12610.049, 6.1035156e-005];


  _vehicle_2167 = _this;
  _this setDir 42.820072;
  _this setPos [7769.3921, 12610.049, 6.1035156e-005];
};

_vehicle_2170 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_quercus3s" createVehicle [7767.6626, 12566.103, 0.00019836426];


  _vehicle_2170 = _this;
  _this setDir -4.3791738;
  _this setPos [7767.6626, 12566.103, 0.00019836426];
};

_vehicle_2173 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7676.3979, 12481.855, -0.025486741];


  _vehicle_2173 = _this;
  _this setDir 55.066406;
  _this setPos [7676.3979, 12481.855, -0.025486741];
};

_vehicle_2175 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7669.0313, 12479.948, 0.00021362305];


  _vehicle_2175 = _this;
  _this setDir 58.261475;
  _this setPos [7669.0313, 12479.948, 0.00021362305];
};

_vehicle_2178 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7679.6304, 12476.101, 5.3405762e-005];


  _vehicle_2178 = _this;
  _this setDir -4.9652934;
  _this setPos [7679.6304, 12476.101, 5.3405762e-005];
};

_vehicle_2180 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7674.7051, 12475.48, 0.00016021729];


  _vehicle_2180 = _this;
  _this setDir -4.9652934;
  _this setPos [7674.7051, 12475.48, 0.00016021729];
};

_vehicle_2182 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7673.8481, 12468.463, 7.6293945e-005];


  _vehicle_2182 = _this;
  _this setDir -4.9652934;
  _this setPos [7673.8481, 12468.463, 7.6293945e-005];
};

_vehicle_2237 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo1B" createVehicle [7923.9453, 12599.736, 7.6293945e-005];


  _vehicle_2237 = _this;
  _this setDir 169.28972;
  _this setPos [7923.9453, 12599.736, 7.6293945e-005];
};

_vehicle_2238 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_Cargo1Bo_military" createVehicle [7912.646, 12609.032, 0.059593841];


  _vehicle_2238 = _this;
  _this setDir -15.679279;
  _this setPos [7912.646, 12609.032, 0.059593841];
};

_vehicle_2239 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo2B" createVehicle [7920.1362, 12599.6, -0.22031403];


  _vehicle_2239 = _this;
  _this setDir -26.055584;
  _this setPos [7920.1362, 12599.6, -0.22031403];
};

_vehicle_2246 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Misc_Cargo1E_EP1" createVehicle [7920.5352, 12606.302, 2.2888184e-005];


  _vehicle_2246 = _this;
  _this setDir -92.491623;
  _this setPos [7920.5352, 12606.302, 2.2888184e-005];
};

_vehicle_2249 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo1B" createVehicle [7916.1504, 12598.137, -0.0051482185];


  _vehicle_2249 = _this;
  _this setDir 167.60512;
  _this setPos [7916.1504, 12598.137, -0.0051482185];
};

_vehicle_2255 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_01_main" createVehicle [7916.1035, 12608.105, -0.00482486];


  _vehicle_2255 = _this;
  _this setDir -9.2521973;
  _this setPos [7916.1035, 12608.105, -0.00482486];
};

_vehicle_2256 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_01_main" createVehicle [7916.7808, 12604.018, -0.044430524];


  _vehicle_2256 = _this;
  _this setDir -9.7085018;
  _this setPos [7916.7808, 12604.018, -0.044430524];
};

_vehicle_2257 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_01_end" createVehicle [7916.0864, 12607.971, -0.0049230447];


  _vehicle_2257 = _this;
  _this setDir -9.8671026;
  _this setPos [7916.0864, 12607.971, -0.0049230447];
};

_vehicle_2258 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_Shed_01_end" createVehicle [7916.8193, 12603.875, -0.01462337];


  _vehicle_2258 = _this;
  _this setDir -9.6751947;
  _this setPos [7916.8193, 12603.875, -0.01462337];
};

_vehicle_2264 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_Cargo1B" createVehicle [7912.3643, 12597.938, 3.0517578e-005];


  _vehicle_2264 = _this;
  _this setDir 167.35693;
  _this setPos [7912.3643, 12597.938, 3.0517578e-005];
};

_vehicle_2267 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_10_25" createVehicle [7816.8047, 12646.265, 2.2888184e-005];


  _vehicle_2267 = _this;
  _this setDir 60.082756;
  _this setPos [7816.8047, 12646.265, 2.2888184e-005];
};

_vehicle_2273 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UralWreck" createVehicle [7798.125, 12636.984, 1.5258789e-005];


  _vehicle_2273 = _this;
  _this setDir 50.620239;
  _this setPos [7798.125, 12636.984, 1.5258789e-005];
};

_vehicle_2276 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_paleta" createVehicle [7819.3384, 12604.194, 3.0517578e-005];


  _vehicle_2276 = _this;
  _this setDir 10.805914;
  _this setPos [7819.3384, 12604.194, 3.0517578e-005];
};

_vehicle_2278 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_paleta" createVehicle [7800.3169, 12634.292, -1.5258789e-005];


  _vehicle_2278 = _this;
  _this setDir 10.805914;
  _this setPos [7800.3169, 12634.292, -1.5258789e-005];
};

_vehicle_2281 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UralWreck" createVehicle [7916.1577, 12622.509, 7.6293945e-005];


  _vehicle_2281 = _this;
  _this setDir 4.7043962;
  _this setPos [7916.1577, 12622.509, 7.6293945e-005];
};

_vehicle_2283 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UralWreck" createVehicle [7912.0835, 12635.12, 6.1035156e-005];


  _vehicle_2283 = _this;
  _this setDir 84.43335;
  _this setPos [7912.0835, 12635.12, 6.1035156e-005];
};

_vehicle_2286 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Kontejner" createVehicle [7910.8804, 12629.214, 5.3405762e-005];


  _vehicle_2286 = _this;
  _this setDir -5.6146502;
  _this setPos [7910.8804, 12629.214, 5.3405762e-005];
};

_vehicle_2288 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Kontejner" createVehicle [7906.6372, 12648.102, -9.9182129e-005];


  _vehicle_2288 = _this;
  _this setDir 264.36893;
  _this setPos [7906.6372, 12648.102, -9.9182129e-005];
};

_vehicle_2296 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Shed_M01" createVehicle [7920.6548, 12628.425, 3.0517578e-005];


  _vehicle_2296 = _this;
  _this setDir 81.905571;
  _this setPos [7920.6548, 12628.425, 3.0517578e-005];
};

_vehicle_2349 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_30_25" createVehicle [7861.8057, 12628.811, 2.2888184e-005];


  _vehicle_2349 = _this;
  _this setDir 64.579704;
  _this setPos [7861.8057, 12628.811, 2.2888184e-005];
};

_vehicle_2352 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_12" createVehicle [7850.6353, 12623.466, 0.00010681152];


  _vehicle_2352 = _this;
  _this setDir 64.730011;
  _this setPos [7850.6353, 12623.466, 0.00010681152];
};

_vehicle_2355 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_6konec" createVehicle [7845.0562, 12621.014, 6.8664551e-005];


  _vehicle_2355 = _this;
  _this setDir 67.084747;
  _this setPos [7845.0562, 12621.014, 6.8664551e-005];
};

_vehicle_2358 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SKODAWreck" createVehicle [7846.8521, 12623.641, 7.6293945e-006];


  _vehicle_2358 = _this;
  _this setDir -288.9689;
  _this setPos [7846.8521, 12623.641, 7.6293945e-006];
};

_vehicle_2361 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [7847.9414, 12620.344, 1.5258789e-005];


  _vehicle_2361 = _this;
  _this setDir 434.09283;
  _this setPos [7847.9414, 12620.344, 1.5258789e-005];
};

_vehicle_2364 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_3" createVehicle [7872.3706, 12624.475, 5.3405762e-005];


  _vehicle_2364 = _this;
  _this setDir -87.370415;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7872.3706, 12624.475, 5.3405762e-005];
};

_vehicle_2367 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7872.9951, 12641.563, 4.5776367e-005];


  _vehicle_2367 = _this;
  _this setDir -267.89236;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7872.9951, 12641.563, 4.5776367e-005];
};

_vehicle_2370 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_3" createVehicle [7872.1099, 12644.165, -4.5776367e-005];


  _vehicle_2370 = _this;
  _this setDir -311.98642;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7872.1099, 12644.165, -4.5776367e-005];
};

_vehicle_2373 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7864.2173, 12648.305, 0.091620408];


  _vehicle_2373 = _this;
  _this setDir -336.43393;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7864.2173, 12648.305, 0.091620408];
};

_vehicle_2375 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_9" createVehicle [7843.188, 12651.341, 0.17144112];


  _vehicle_2375 = _this;
  _this setDir -382.58527;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7843.188, 12651.341, 0.17144112];
};

_vehicle_2381 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_TinCom_3" createVehicle [7861.6509, 12649.819, 0.32889649];


  _vehicle_2381 = _this;
  _this setDir -322.23495;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7861.6509, 12649.819, 0.32889649];
};

_vehicle_2385 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_paletyC" createVehicle [7845.5713, 12616.46, -0.041063845];


  _vehicle_2385 = _this;
  _this setDir 107.43632;
  _this setPos [7845.5713, 12616.46, -0.041063845];
};

_vehicle_2388 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7856.7183, 12646.489, 1.5258789e-005];


  _vehicle_2388 = _this;
  _this setDir 45.554283;
  _this setPos [7856.7183, 12646.489, 1.5258789e-005];
};

_vehicle_2390 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7859.8701, 12644.338, 7.6293945e-006];


  _vehicle_2390 = _this;
  _this setDir 146.18176;
  _this setPos [7859.8701, 12644.338, 7.6293945e-006];
};

_vehicle_2393 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Kontejner" createVehicle [7842.7344, 12626.341];


  _vehicle_2393 = _this;
  _this setDir -17.987211;
  _this setPos [7842.7344, 12626.341];
};

_vehicle_2395 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Kontejner" createVehicle [7843.2969, 12624.635, 1.5258789e-005];


  _vehicle_2395 = _this;
  _this setDir -19.427258;
  _this setPos [7843.2969, 12624.635, 1.5258789e-005];
};

_vehicle_2397 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Kontejner" createVehicle [7843.834, 12623.019, -2.2888184e-005];


  _vehicle_2397 = _this;
  _this setDir -19.427258;
  _this setPos [7843.834, 12623.019, -2.2888184e-005];
};

_vehicle_2403 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [7900.7437, 12627.188, 3.0517578e-005];


  _vehicle_2403 = _this;
  _this setDir 307.57349;
  _this setPos [7900.7437, 12627.188, 3.0517578e-005];
};

_vehicle_2451 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7897.0864, 12592.63, 0.43450448];


  _vehicle_2451 = _this;
  _this setDir -102.44237;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7897.0864, 12592.63, 0.43450448];
};

_vehicle_2454 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7895.8496, 12598.417, 0.15936959];


  _vehicle_2454 = _this;
  _this setDir -102.44237;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7895.8496, 12598.417, 0.15936959];
};

_vehicle_2457 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7895.1772, 12601.391, 0.062335387];


  _vehicle_2457 = _this;
  _this setDir -102.44237;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7895.1772, 12601.391, 0.062335387];
};

_vehicle_2463 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7891.5059, 12621.659, -0.28607419];


  _vehicle_2463 = _this;
  _this setDir 83.600182;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7891.5059, 12621.659, -0.28607419];
};

_vehicle_2466 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7889.5044, 12639.026, -0.12501641];


  _vehicle_2466 = _this;
  _this setDir -98.119759;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7889.5044, 12639.026, -0.12501641];
};

_vehicle_2469 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7888.8286, 12644.877, -0.10842062];


  _vehicle_2469 = _this;
  _this setDir -95.251991;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7888.8286, 12644.877, -0.10842062];
};

_vehicle_2474 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fraxinus2s" createVehicle [7683.5459, 12500.161];


  _vehicle_2474 = _this;
  _this setDir -64.130959;
  _this setPos [7683.5459, 12500.161];
};

_vehicle_2479 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7699.3257, 12499.032, -9.1552734e-005];


  _vehicle_2479 = _this;
  _this setDir -20.816936;
  _this setPos [7699.3257, 12499.032, -9.1552734e-005];
};

_vehicle_2481 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7805.916, 12595.266, 2.2888184e-005];


  _vehicle_2481 = _this;
  _this setDir -20.816936;
  _this setPos [7805.916, 12595.266, 2.2888184e-005];
};

_vehicle_2483 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7893.3145, 12643.526, 0.00010681152];


  _vehicle_2483 = _this;
  _this setDir -20.816936;
  _this setPos [7893.3145, 12643.526, 0.00010681152];
};

_vehicle_2485 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7885.0601, 12609.094, 6.1035156e-005];


  _vehicle_2485 = _this;
  _this setDir -20.816936;
  _this setPos [7885.0601, 12609.094, 6.1035156e-005];
};

_vehicle_2491 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7854.0889, 12595.258, 9.1552734e-005];


  _vehicle_2491 = _this;
  _this setDir -4.9652934;
  _this setPos [7854.0889, 12595.258, 9.1552734e-005];
};

_vehicle_2493 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7907.9736, 12622.494, 9.9182129e-005];


  _vehicle_2493 = _this;
  _this setDir 137.61153;
  _this setPos [7907.9736, 12622.494, 9.9182129e-005];
};

_vehicle_2496 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7737.6196, 12548.15, 0.00016021729];


  _vehicle_2496 = _this;
  _this setDir 41.869556;
  _this setPos [7737.6196, 12548.15, 0.00016021729];
};

_vehicle_2498 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7734.5928, 12548.466, 4.5776367e-005];


  _vehicle_2498 = _this;
  _this setDir 41.869556;
  _this setPos [7734.5928, 12548.466, 4.5776367e-005];
};

_vehicle_2500 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7737.9351, 12598.32, 8.392334e-005];


  _vehicle_2500 = _this;
  _this setDir 41.869556;
  _this setPos [7737.9351, 12598.32, 8.392334e-005];
};

_vehicle_2502 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7743.3281, 12651.467, 0.00015258789];


  _vehicle_2502 = _this;
  _this setDir 41.869556;
  _this setPos [7743.3281, 12651.467, 0.00015258789];
};

_vehicle_2504 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7836.5063, 12593.169, 6.8664551e-005];


  _vehicle_2504 = _this;
  _this setDir 41.869556;
  _this setPos [7836.5063, 12593.169, 6.8664551e-005];
};

_vehicle_2506 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7886.9243, 12645.209, -3.0517578e-005];


  _vehicle_2506 = _this;
  _this setDir 41.869556;
  _this setPos [7886.9243, 12645.209, -3.0517578e-005];
};

_vehicle_2511 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Ind_TankSmall" createVehicle [7898.334, 12642.092, 6.8664551e-005];


  _vehicle_2511 = _this;
  _this setDir 266.2706;
  _this setPos [7898.334, 12642.092, 6.8664551e-005];
};

_vehicle_2514 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7836.6123, 12665.418, 0.016612394];


  _vehicle_2514 = _this;
  _this setDir 283.07846;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7836.6123, 12665.418, 0.016612394];
};

_vehicle_2518 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7880.3984, 12690.699, -0.00018310547];


  _vehicle_2518 = _this;
  _this setDir 58.261475;
  _this setPos [7880.3984, 12690.699, -0.00018310547];
};

_vehicle_2519 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7841.0933, 12702.571, -0.84425354];


  _vehicle_2519 = _this;
  _this setDir -4.9652934;
  _this setPos [7841.0933, 12702.571, -0.84425354];
};

_vehicle_2523 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7897.8608, 12671.663];


  _vehicle_2523 = _this;
  _this setDir 58.261475;
  _this setPos [7897.8608, 12671.663];
};

_vehicle_2525 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7832.4219, 12701.154];


  _vehicle_2525 = _this;
  _this setDir 58.261475;
  _this setPos [7832.4219, 12701.154];
};

_vehicle_2527 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7869.3022, 12702.514, 1.5258789e-005];


  _vehicle_2527 = _this;
  _this setDir 58.261475;
  _this setPos [7869.3022, 12702.514, 1.5258789e-005];
};

_vehicle_2529 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7859.2402, 12705.655, 8.392334e-005];


  _vehicle_2529 = _this;
  _this setDir 58.261475;
  _this setPos [7859.2402, 12705.655, 8.392334e-005];
};

_vehicle_2532 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7854.4102, 12703.072, -0.0017993762];


  _vehicle_2532 = _this;
  _this setDir -4.9652934;
  _this setPos [7854.4102, 12703.072, -0.0017993762];
};

_vehicle_2535 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_paletyC" createVehicle [7831.6724, 12638.806, 10.972757];


  _vehicle_2535 = _this;
  _this setDir 118.84065;
  _this setPos [7831.6724, 12638.806, 10.972757];
};

_vehicle_2543 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_paleta" createVehicle [7847.373, 12615.553];


  _vehicle_2543 = _this;
  _this setDir -7.8187428;
  _this setPos [7847.373, 12615.553];
};

_vehicle_2546 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench2" createVehicle [7768.6094, 12582.776, 0.16974762];


  _vehicle_2546 = _this;
  _this setDir 160.50186;
  _this setPos [7768.6094, 12582.776, 0.16974762];
};

_vehicle_2622 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Hanged_MD" createVehicle [7826.8047, 12640.284, 3.9746211];


  _vehicle_2622 = _this;
  _this setDir 73.022423;
  _this setPos [7826.8047, 12640.284, 3.9746211];
};

_vehicle_2624 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "PowerGenerator" createVehicle [7722.9585, 12567.209, -4.5776367e-005];


  _vehicle_2624 = _this;
  _this setDir -30.481247;
  _this setPos [7722.9585, 12567.209, -4.5776367e-005];
};

_vehicle_2627 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Pile_of_wood" createVehicle [7709.9028, 12645.979, -2.2888184e-005];


  _vehicle_2627 = _this;
  _this setDir 326.71265;
  _this setPos [7709.9028, 12645.979, -2.2888184e-005];
};

_vehicle_2630 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Axe_woodblock" createVehicle [7705.5313, 12644.957, -7.6293945e-005];


  _vehicle_2630 = _this;
  _this setDir 39.628578;
  _this setPos [7705.5313, 12644.957, -7.6293945e-005];
};

_vehicle_2647 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_path_10_25" createVehicle [7731.022, 12584.595, 7.6293945e-006];


  _vehicle_2647 = _this;
  _this setDir 141.57121;
  _this setPos [7731.022, 12584.595, 7.6293945e-006];
};

_vehicle_2651 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_path_6" createVehicle [7737.0386, 12578.938, -2.2888184e-005];


  _vehicle_2651 = _this;
  _this setDir 65.159111;
  _this setPos [7737.0386, 12578.938, -2.2888184e-005];
};

_vehicle_2657 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_path_60_10" createVehicle [7729.6094, 12579.44, -2.2888184e-005];


  _vehicle_2657 = _this;
  _this setDir 55.592091;
  _this setPos [7729.6094, 12579.44, -2.2888184e-005];
};

_vehicle_2659 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_path_60_10" createVehicle [7729.6846, 12579.634, -7.6293945e-006];


  _vehicle_2659 = _this;
  _this setDir -127.91202;
  _this setPos [7729.6846, 12579.634, -7.6293945e-006];
};

_vehicle_2661 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_path_60_10" createVehicle [7715.3892, 12548.751, 6.1035156e-005];


  _vehicle_2661 = _this;
  _this setDir 56.682251;
  _this setPos [7715.3892, 12548.751, 6.1035156e-005];
};

_vehicle_2664 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_path_7_100" createVehicle [7747.2085, 12582.899, 2.2888184e-005];


  _vehicle_2664 = _this;
  _this setDir 65.284477;
  _this setPos [7747.2085, 12582.899, 2.2888184e-005];
};

_vehicle_2667 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_path_60_10" createVehicle [7851.7954, 12660.756, -7.6293945e-006];


  _vehicle_2667 = _this;
  _this setPos [7851.7954, 12660.756, -7.6293945e-006];
};

_vehicle_2669 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_path_60_10" createVehicle [7846.0469, 12668.105, -3.0517578e-005];


  _vehicle_2669 = _this;
  _this setDir 166.80687;
  _this setPos [7846.0469, 12668.105, -3.0517578e-005];
};

_vehicle_2672 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7849.5854, 12668.607, -3.0517578e-005];


  _vehicle_2672 = _this;
  _this setDir 163.46527;
  _this setPos [7849.5854, 12668.607, -3.0517578e-005];
};

_vehicle_2674 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7741.6987, 12581.266, 1.5258789e-005];


  _vehicle_2674 = _this;
  _this setPos [7741.6987, 12581.266, 1.5258789e-005];
};

_vehicle_2681 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7740.4624, 12580.458, 1.5258789e-005];


  _vehicle_2681 = _this;
  _this setPos [7740.4624, 12580.458, 1.5258789e-005];
};

_vehicle_2683 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7738.1528, 12580.833, -1.5258789e-005];


  _vehicle_2683 = _this;
  _this setPos [7738.1528, 12580.833, -1.5258789e-005];
};

_vehicle_2685 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7736.5425, 12581.152, -3.8146973e-005];


  _vehicle_2685 = _this;
  _this setPos [7736.5425, 12581.152, -3.8146973e-005];
};

_vehicle_2687 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7733.8154, 12581.05, -7.6293945e-005];


  _vehicle_2687 = _this;
  _this setPos [7733.8154, 12581.05, -7.6293945e-005];
};

_vehicle_2689 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7732.4019, 12582.67, 2.2888184e-005];


  _vehicle_2689 = _this;
  _this setPos [7732.4019, 12582.67, 2.2888184e-005];
};

_vehicle_2691 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7731.8267, 12580.383, -7.6293945e-006];


  _vehicle_2691 = _this;
  _this setPos [7731.8267, 12580.383, -7.6293945e-006];
};

_vehicle_2693 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7730.5151, 12579.853, 7.6293945e-006];


  _vehicle_2693 = _this;
  _this setPos [7730.5151, 12579.853, 7.6293945e-006];
};

_vehicle_2695 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7728.8174, 12579.088, -7.6293945e-006];


  _vehicle_2695 = _this;
  _this setPos [7728.8174, 12579.088, -7.6293945e-006];
};

_vehicle_2697 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7726.126, 12577.869, 3.0517578e-005];


  _vehicle_2697 = _this;
  _this setPos [7726.126, 12577.869, 3.0517578e-005];
};

_vehicle_2699 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7724.1802, 12577.647, -7.6293945e-005];


  _vehicle_2699 = _this;
  _this setPos [7724.1802, 12577.647, -7.6293945e-005];
};

_vehicle_2701 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7722.2827, 12577.598, -3.0517578e-005];


  _vehicle_2701 = _this;
  _this setPos [7722.2827, 12577.598, -3.0517578e-005];
};

_vehicle_2703 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7720.6851, 12577.972, 0];


  _vehicle_2703 = _this;
  _this setPos [7720.6851, 12577.972, 0];
};

_vehicle_2705 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7749.6177, 12584.021, -1.5258789e-005];


  _vehicle_2705 = _this;
  _this setPos [7749.6177, 12584.021, -1.5258789e-005];
};

_vehicle_2707 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7752.0259, 12584.998, 3.8146973e-005];


  _vehicle_2707 = _this;
  _this setPos [7752.0259, 12584.998, 3.8146973e-005];
};

_vehicle_2709 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7753.9585, 12585.731, -2.2888184e-005];


  _vehicle_2709 = _this;
  _this setPos [7753.9585, 12585.731, -2.2888184e-005];
};

_vehicle_2711 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7722.4795, 12550.232, -2.2888184e-005];


  _vehicle_2711 = _this;
  _this setPos [7722.4795, 12550.232, -2.2888184e-005];
};

_vehicle_2713 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7720.9795, 12550.259, -3.0517578e-005];


  _vehicle_2713 = _this;
  _this setPos [7720.9795, 12550.259, -3.0517578e-005];
};

_vehicle_2715 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7719.1289, 12550.037, 0];


  _vehicle_2715 = _this;
  _this setPos [7719.1289, 12550.037, 0];
};

_vehicle_2717 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7716.7842, 12549.391, 0];


  _vehicle_2717 = _this;
  _this setPos [7716.7842, 12549.391, 0];
};

_vehicle_2719 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7846.2397, 12664.341, -1.5258789e-005];


  _vehicle_2719 = _this;
  _this setPos [7846.2397, 12664.341, -1.5258789e-005];
};

_vehicle_2721 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7846.1387, 12666.291, -4.5776367e-005];


  _vehicle_2721 = _this;
  _this setPos [7846.1387, 12666.291, -4.5776367e-005];
};

_vehicle_2723 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7852.7241, 12664.888, 2.2888184e-005];


  _vehicle_2723 = _this;
  _this setPos [7852.7241, 12664.888, 2.2888184e-005];
};

_vehicle_2725 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7853.6465, 12666.433, -7.6293945e-006];


  _vehicle_2725 = _this;
  _this setPos [7853.6465, 12666.433, -7.6293945e-006];
};

_vehicle_2727 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7855.0913, 12667.96, 0];


  _vehicle_2727 = _this;
  _this setPos [7855.0913, 12667.96, 0];
};

_vehicle_2729 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7856.1802, 12668.548, 7.6293945e-006];


  _vehicle_2729 = _this;
  _this setPos [7856.1802, 12668.548, 7.6293945e-006];
};

_vehicle_2732 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7788.4004, 12646.416, 0.48569798];


  _vehicle_2732 = _this;
  _this setDir -32.428715;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7788.4004, 12646.416, 0.48569798];
};

_vehicle_2737 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7799.0337, 12652.086, 0.41840205];


  _vehicle_2737 = _this;
  _this setDir -22.381056;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7799.0337, 12652.086, 0.41840205];
};

_vehicle_2739 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7815.9912, 12657.489, 0.25806719];


  _vehicle_2739 = _this;
  _this setDir -16.251894;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7815.9912, 12657.489, 0.25806719];
};

_vehicle_2741 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7827.0703, 12660.715, 7.6293945e-006];


  _vehicle_2741 = _this;
  _this setDir -16.911179;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7827.0703, 12660.715, 7.6293945e-006];
};

_vehicle_2744 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7863.2344, 12671.981, 0.17152716];


  _vehicle_2744 = _this;
  _this setDir 65.093285;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7863.2344, 12671.981, 0.17152716];
};

_vehicle_2747 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7872.4746, 12670.326, -6.8664551e-005];


  _vehicle_2747 = _this;
  _this setDir -13.351489;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7872.4746, 12670.326, -6.8664551e-005];
};

_vehicle_2752 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7884.1387, 12657.971, -2.2888184e-005];


  _vehicle_2752 = _this;
  _this setDir 104.02966;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7884.1387, 12657.971, -2.2888184e-005];
};

_vehicle_2757 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7926.8789, 12612.339, 0.069882564];


  _vehicle_2757 = _this;
  _this setDir -103.68739;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7926.8789, 12612.339, 0.069882564];
};

_vehicle_2759 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7925.5815, 12618.064, 9.1552734e-005];


  _vehicle_2759 = _this;
  _this setDir -102.28685;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7925.5815, 12618.064, 9.1552734e-005];
};

_vehicle_2763 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7922.5342, 12635.469, 7.6293945e-006];


  _vehicle_2763 = _this;
  _this setDir -99.127357;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7922.5342, 12635.469, 7.6293945e-006];
};

_vehicle_2766 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7886.9155, 12580.815, 6.8664551e-005];


  _vehicle_2766 = _this;
  _this setDir -10.263275;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7886.9155, 12580.815, 6.8664551e-005];
};

_vehicle_2769 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7811.5605, 12567.753, -0.068127185];


  _vehicle_2769 = _this;
  _this setDir 18.753082;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7811.5605, 12567.753, -0.068127185];
};

_vehicle_2772 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7823.0278, 12568.326, 1.5258789e-005];


  _vehicle_2772 = _this;
  _this setDir -11.278671;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7823.0278, 12568.326, 1.5258789e-005];
};

_vehicle_2777 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7840.4604, 12571.457, 0.00010681152];


  _vehicle_2777 = _this;
  _this setDir -11.366906;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7840.4604, 12571.457, 0.00010681152];
};

_vehicle_2780 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7846.3159, 12572.66, 5.3405762e-005];


  _vehicle_2780 = _this;
  _this setDir -11.637337;
  _this setPos [7846.3159, 12572.66, 5.3405762e-005];
};

_vehicle_2786 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7705.6123, 12583.728, -0.0090833195];


  _vehicle_2786 = _this;
  _this setDir -68.85527;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7705.6123, 12583.728, -0.0090833195];
};

_vehicle_2789 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7701.3989, 12572.67, -0.077093057];


  _vehicle_2789 = _this;
  _this setDir -70.145775;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7701.3989, 12572.67, -0.077093057];
};

_vehicle_2791 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7697.4766, 12561.832, -0.043621272];


  _vehicle_2791 = _this;
  _this setDir -70.145775;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7697.4766, 12561.832, -0.043621272];
};

_vehicle_2793 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7693.5815, 12550.558, -0.081338122];


  _vehicle_2793 = _this;
  _this setDir -71.949638;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7693.5815, 12550.558, -0.081338122];
};

_vehicle_2795 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7691.8652, 12545.137, -0.12187106];


  _vehicle_2795 = _this;
  _this setDir -72.041855;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7691.8652, 12545.137, -0.12187106];
};

_vehicle_2798 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7685.5112, 12529.16, -0.10458195];


  _vehicle_2798 = _this;
  _this setDir -68.062004;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7685.5112, 12529.16, -0.10458195];
};

_vehicle_2802 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_asf3_6konec" createVehicle [7830.2661, 12626.381, 0.00010681152];


  _vehicle_2802 = _this;
  _this setDir 254.9729;
  _this setPos [7830.2661, 12626.381, 0.00010681152];
};

_vehicle_2808 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkShortEnd" createVehicle [7767.2466, 12581.592, 0.11073505];


  _vehicle_2808 = _this;
  _this setDir 250.95987;
  _this setPos [7767.2466, 12581.592, 0.11073505];
};

_vehicle_2809 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkClearShort" createVehicle [7768.645, 12582.078, 0.091730028];


  _vehicle_2809 = _this;
  _this setDir -109.51961;
  _this setPos [7768.645, 12582.078, 0.091730028];
};

_vehicle_2812 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkShortEnd" createVehicle [7780.3906, 12586.243, 0.06813246];


  _vehicle_2812 = _this;
  _this setDir 431.10373;
  _this setPos [7780.3906, 12586.243, 0.06813246];
};

_vehicle_2813 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkClearShort" createVehicle [7778.9834, 12585.766, 0.086411767];


  _vehicle_2813 = _this;
  _this setDir 71.101097;
  _this setPos [7778.9834, 12585.766, 0.086411767];
};

_vehicle_2818 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7783.1631, 12586.698, -1.5258789e-005];


  _vehicle_2818 = _this;
  _this setDir -4.9652934;
  _this setPos [7783.1631, 12586.698, -1.5258789e-005];
};

_vehicle_2820 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7790.7109, 12590.166];


  _vehicle_2820 = _this;
  _this setDir 43.023148;
  _this setPos [7790.7109, 12590.166];
};

_vehicle_2823 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [7792.1123, 12590.613, 3.0517578e-005];


  _vehicle_2823 = _this;
  _this setDir 45.607632;
  _this setPos [7792.1123, 12590.613, 3.0517578e-005];
};

_vehicle_2825 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [7765.0806, 12580.899, 7.6293945e-005];


  _vehicle_2825 = _this;
  _this setDir -4.9652934;
  _this setPos [7765.0806, 12580.899, 7.6293945e-005];
};

_vehicle_2828 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench2" createVehicle [7778.4893, 12586.005, 0.16151376];


  _vehicle_2828 = _this;
  _this setDir 160.50186;
  _this setPos [7778.4893, 12586.005, 0.16151376];
};

_vehicle_2831 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkShortEnd" createVehicle [7788.3169, 12596.713, 0.10145225];


  _vehicle_2831 = _this;
  _this setDir 432.43317;
  _this setPos [7788.3169, 12596.713, 0.10145225];
};

_vehicle_2834 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7788.7144, 12596.757, 3.0517578e-005];


  _vehicle_2834 = _this;
  _this setPos [7788.7144, 12596.757, 3.0517578e-005];
};

_vehicle_2836 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7788.8086, 12595.581, 1.5258789e-005];


  _vehicle_2836 = _this;
  _this setPos [7788.8086, 12595.581, 1.5258789e-005];
};

_vehicle_2838 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ClutterCutter_small_2_EP1" createVehicle [7788.6099, 12598.019, 7.6293945e-006];


  _vehicle_2838 = _this;
  _this setPos [7788.6099, 12598.019, 7.6293945e-006];
};

_vehicle_2841 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [7819.3579, 12590.41, 2.2888184e-005];


  _vehicle_2841 = _this;
  _this setDir 99.60154;
  _this setPos [7819.3579, 12590.41, 2.2888184e-005];
};

_vehicle_2843 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [7856.5059, 12588.113];


  _vehicle_2843 = _this;
  _this setDir 62.803932;
  _this setPos [7856.5059, 12588.113];
};

_vehicle_2845 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [7897.4468, 12586.325, -1.5258789e-005];


  _vehicle_2845 = _this;
  _this setDir 45.607632;
  _this setPos [7897.4468, 12586.325, -1.5258789e-005];
};

_vehicle_2848 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Barrels" createVehicle [7788.3008, 12622.553, -1.5258789e-005];


  _vehicle_2848 = _this;
  _this setPos [7788.3008, 12622.553, -1.5258789e-005];
};

_vehicle_2850 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Barrels" createVehicle [7789.8125, 12622.216, 3.8146973e-005];


  _vehicle_2850 = _this;
  _this setPos [7789.8125, 12622.216, 3.8146973e-005];
};

_vehicle_2853 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_WoodPile" createVehicle [7724.6001, 12651.206, 0.50919509];


  _vehicle_2853 = _this;
  _this setDir 57.547928;
  _this setPos [7724.6001, 12651.206, 0.50919509];
};

_vehicle_2856 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Bucket_EP1" createVehicle [7723.6875, 12650.473, 0];


  _vehicle_2856 = _this;
  _this setDir -11.365155;
  _this setPos [7723.6875, 12650.473, 0];
};

_vehicle_2858 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7786.3052, 12565.237, 9.9182129e-005];


  _vehicle_2858 = _this;
  _this setDir 80.297432;
  _this setPos [7786.3052, 12565.237, 9.9182129e-005];
};

_vehicle_2861 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7706.5942, 12635.159, 0.068727486];


  _vehicle_2861 = _this;
  _this setDir -117.35567;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7706.5942, 12635.159, 0.068727486];
};

_vehicle_2867 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_2" createVehicle [7703.8662, 12640.458, 0.22951004];


  _vehicle_2867 = _this;
  _this setDir -117.40552;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7703.8662, 12640.458, 0.22951004];
};

_vehicle_2868 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7702.3062, 12643.828, -0.0019067111];


  _vehicle_2868 = _this;
  _this setDir -117.27776;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7702.3062, 12643.828, -0.0019067111];
};

_vehicle_2869 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7702.9819, 12642.594, -0.12313259];


  _vehicle_2869 = _this;
  _this setDir -33.315556;
  _this setPos [7702.9819, 12642.594, -0.12313259];
};

_vehicle_2874 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_2" createVehicle [7701.8018, 12644.919, 7.6293945e-006];


  _vehicle_2874 = _this;
  _this setDir 54.449554;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7701.8018, 12644.919, 7.6293945e-006];
};

_vehicle_2877 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7703.0762, 12642.196, -1.5258789e-005];


  _vehicle_2877 = _this;
  _this setDir -117.27776;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7703.0762, 12642.196, -1.5258789e-005];
};

_vehicle_2881 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_2" createVehicle [7893.2495, 12609.875, 0.16128136];


  _vehicle_2881 = _this;
  _this setDir -281.12323;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7893.2495, 12609.875, 0.16128136];
};

_vehicle_2883 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7893.6641, 12608.293, -0.22745343];


  _vehicle_2883 = _this;
  _this setDir -13.438876;
  _this setPos [7893.6641, 12608.293, -0.22745343];
};

_vehicle_2895 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7892.2505, 12615.704, -0.10485651];


  _vehicle_2895 = _this;
  _this setDir 81.790993;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7892.2505, 12615.704, -0.10485651];
};

_vehicle_2898 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7894.5352, 12604.322, 0.026208308];


  _vehicle_2898 = _this;
  _this setDir -102.44237;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7894.5352, 12604.322, 0.026208308];
};

_vehicle_2901 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7892.021, 12607.672, 0.00012207031];


  _vehicle_2901 = _this;
  _this setDir 16.278889;
  _this setPos [7892.021, 12607.672, 0.00012207031];
};

_vehicle_2904 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7890.8062, 12627.629, -0.32075951];


  _vehicle_2904 = _this;
  _this setDir 83.600182;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7890.8062, 12627.629, -0.32075951];
};

_vehicle_2907 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_palletsfoiled_heap" createVehicle [7919.7637, 12611.158, 5.3405762e-005];


  _vehicle_2907 = _this;
  _this setDir -13.194213;
  _this setPos [7919.7637, 12611.158, 5.3405762e-005];
};

_vehicle_2910 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_palletsfoiled_heap" createVehicle [7918.4023, 12616.667, -2.2888184e-005];


  _vehicle_2910 = _this;
  _this setDir 76.181114;
  _this setPos [7918.4023, 12616.667, -2.2888184e-005];
};

_vehicle_2913 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Misc_IronPipes_EP1" createVehicle [7903.9624, 12596.549, 0.00016021729];


  _vehicle_2913 = _this;
  _this setPos [7903.9624, 12596.549, 0.00016021729];
};

_vehicle_2915 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Misc_IronPipes_EP1" createVehicle [7902.5469, 12601.124, -6.1035156e-005];


  _vehicle_2915 = _this;
  _this setDir 133.01071;
  _this setPos [7902.5469, 12601.124, -6.1035156e-005];
};

_vehicle_2917 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Misc_IronPipes_EP1" createVehicle [7879.5103, 12662.375, -9.9182129e-005];


  _vehicle_2917 = _this;
  _this setDir 62.539028;
  _this setPos [7879.5103, 12662.375, -9.9182129e-005];
};

_vehicle_2920 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_plot_green_vrat_l" createVehicle [7684.1865, 12497.163, 0.00018310547];


  _vehicle_2920 = _this;
  _this setDir -139.65869;
  _this setPos [7684.1865, 12497.163, 0.00018310547];
};

_vehicle_2922 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_plot_green_vrat_l" createVehicle [7691.2856, 12493.597, 3.8146973e-005];


  _vehicle_2922 = _this;
  _this setDir -172.01868;
  _this setPos [7691.2856, 12493.597, 3.8146973e-005];
};

_vehicle_2925 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Misc_TyreHeap" createVehicle [7920.23, 12631.634, 7.6293945e-006];


  _vehicle_2925 = _this;
  _this setDir -15.161761;
  _this setPos [7920.23, 12631.634, 7.6293945e-006];
};

_vehicle_2928 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7908.3623, 12617.798];


  _vehicle_2928 = _this;
  _this setDir -18.497639;
  _this setPos [7908.3623, 12617.798];
};

_vehicle_2930 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7894.9995, 12625.774, 9.1552734e-005];


  _vehicle_2930 = _this;
  _this setDir 71.645378;
  _this setPos [7894.9995, 12625.774, 9.1552734e-005];
};

_vehicle_2933 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7912.375, 12628.531, 2.2888184e-005];


  _vehicle_2933 = _this;
  _this setDir -20.816936;
  _this setPos [7912.375, 12628.531, 2.2888184e-005];
};

_vehicle_2935 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_fagus2s" createVehicle [7904.6724, 12599.622, 9.1552734e-005];


  _vehicle_2935 = _this;
  _this setDir 37.40802;
  _this setPos [7904.6724, 12599.622, 9.1552734e-005];
};

_vehicle_2938 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7853.4409, 12606.005, 3.0517578e-005];


  _vehicle_2938 = _this;
  _this setDir 41.869556;
  _this setPos [7853.4409, 12606.005, 3.0517578e-005];
};

_vehicle_2940 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7893.9722, 12628.161, 5.3405762e-005];


  _vehicle_2940 = _this;
  _this setDir 41.869556;
  _this setPos [7893.9722, 12628.161, 5.3405762e-005];
};

_vehicle_2942 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7920.7988, 12625.492, 8.392334e-005];


  _vehicle_2942 = _this;
  _this setDir 41.869556;
  _this setPos [7920.7988, 12625.492, 8.392334e-005];
};

_vehicle_2944 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7894.4316, 12598.606, -4.5776367e-005];


  _vehicle_2944 = _this;
  _this setDir 63.536247;
  _this setPos [7894.4316, 12598.606, -4.5776367e-005];
};

_vehicle_2946 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7895.8291, 12604.231, 3.0517578e-005];


  _vehicle_2946 = _this;
  _this setDir 35.821102;
  _this setPos [7895.8291, 12604.231, 3.0517578e-005];
};

_vehicle_2949 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7888.1567, 12624.854, 3.0517578e-005];


  _vehicle_2949 = _this;
  _this setDir -4.9652934;
  _this setPos [7888.1567, 12624.854, 3.0517578e-005];
};

_vehicle_2952 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7883.6001, 12639.88, 3.8146973e-005];


  _vehicle_2952 = _this;
  _this setDir 113.4809;
  _this setPos [7883.6001, 12639.88, 3.8146973e-005];
};

_vehicle_2954 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7840.2651, 12663.763, 2.2888184e-005];


  _vehicle_2954 = _this;
  _this setDir 49.784916;
  _this setPos [7840.2651, 12663.763, 2.2888184e-005];
};

_vehicle_2957 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7853.4639, 12683.384, 0.041011512];


  _vehicle_2957 = _this;
  _this setDir -10.893875;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7853.4639, 12683.384, 0.041011512];
};

_vehicle_2960 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7847.1821, 12630.907, 3.0517578e-005];


  _vehicle_2960 = _this;
  _this setDir -4.9652934;
  _this setPos [7847.1821, 12630.907, 3.0517578e-005];
};

_vehicle_2962 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7851.3979, 12612.929, 2.2888184e-005];


  _vehicle_2962 = _this;
  _this setDir -4.9652934;
  _this setPos [7851.3979, 12612.929, 2.2888184e-005];
};

_vehicle_2964 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7880.6084, 12600.479, -7.6293945e-006];


  _vehicle_2964 = _this;
  _this setDir -4.9652934;
  _this setPos [7880.6084, 12600.479, -7.6293945e-006];
};

_vehicle_2966 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7808.2822, 12581.278, 1.5258789e-005];


  _vehicle_2966 = _this;
  _this setDir -4.9652934;
  _this setPos [7808.2822, 12581.278, 1.5258789e-005];
};

_vehicle_2971 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7788.7817, 12568.525, 6.8664551e-005];


  _vehicle_2971 = _this;
  _this setDir 16.278889;
  _this setPos [7788.7817, 12568.525, 6.8664551e-005];
};

_vehicle_2973 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7736.478, 12550.03, 3.8146973e-005];


  _vehicle_2973 = _this;
  _this setDir 16.278889;
  _this setPos [7736.478, 12550.03, 3.8146973e-005];
};

_vehicle_2976 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7714.5732, 12552.241, 9.1552734e-005];


  _vehicle_2976 = _this;
  _this setDir 63.536247;
  _this setPos [7714.5732, 12552.241, 9.1552734e-005];
};

_vehicle_2978 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7691.7754, 12541.696, -0.041296557];


  _vehicle_2978 = _this;
  _this setDir 63.536247;
  _this setPos [7691.7754, 12541.696, -0.041296557];
};

_vehicle_2981 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7700.9712, 12468.36, 9.1552734e-005];


  _vehicle_2981 = _this;
  _this setDir -4.9652934;
  _this setPos [7700.9712, 12468.36, 9.1552734e-005];
};

_vehicle_2984 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7827.2559, 12645.349, -9.9182129e-005];


  _vehicle_2984 = _this;
  _this setDir 112.19613;
  _this setPos [7827.2559, 12645.349, -9.9182129e-005];
};

_vehicle_2987 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_SidewalkShortEnd" createVehicle [7826.2051, 12639.279, 0.25476184];


  _vehicle_2987 = _this;
  _this setDir 340.02982;
  _this setPos [7826.2051, 12639.279, 0.25476184];
};

_vehicle_2991 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7804.71, 12561.979];


  _vehicle_2991 = _this;
  _this setDir -4.9652934;
  _this setPos [7804.71, 12561.979];
};

_vehicle_2994 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7875.3857, 12671.03, -2.2888184e-005];


  _vehicle_2994 = _this;
  _this setDir -13.351489;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7875.3857, 12671.03, -2.2888184e-005];
};

_vehicle_2999 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7713.5894, 12489.782, 0.040294319];


  _vehicle_2999 = _this;
  _this setDir -219.2569;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7713.5894, 12489.782, 0.040294319];
};

_vehicle_3001 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7724.541, 12500.413, 0.15339805];


  _vehicle_3001 = _this;
  _this setDir -226.0134;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7724.541, 12500.413, 0.15339805];
};

_vehicle_3006 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7739.4492, 12516.73, 0.094237469];


  _vehicle_3006 = _this;
  _this setDir -229.81754;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7739.4492, 12516.73, 0.094237469];
};

_vehicle_3009 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7731.0732, 12507.853, -7.6293945e-006];


  _vehicle_3009 = _this;
  _this setDir 138.28297;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7731.0732, 12507.853, -7.6293945e-006];
};

_vehicle_3012 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7715.9858, 12491.676, 0.093380965];


  _vehicle_3012 = _this;
  _this setDir 138.80905;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7715.9858, 12491.676, 0.093380965];
};

_vehicle_3015 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7719.5005, 12494.864, 0.1988048];


  _vehicle_3015 = _this;
  _this setDir 129.32574;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7719.5005, 12494.864, 0.1988048];
};

_vehicle_3018 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7727.7026, 12504.183, -1.5258789e-005];


  _vehicle_3018 = _this;
  _this setDir 124.69922;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7727.7026, 12504.183, -1.5258789e-005];
};

_vehicle_3020 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7729.9043, 12506.788, 5.3405762e-005];


  _vehicle_3020 = _this;
  _this setDir 124.69922;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7729.9043, 12506.788, 5.3405762e-005];
};

_vehicle_3024 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7734.6318, 12511.025, 0.1724512];


  _vehicle_3024 = _this;
  _this setDir 131.63809;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7734.6318, 12511.025, 0.1724512];
};

_vehicle_3028 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7681.9321, 12485.354, 9.9182129e-005];


  _vehicle_3028 = _this;
  _this setDir -169.04593;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7681.9321, 12485.354, 9.9182129e-005];
};

_vehicle_3034 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_Hole" createVehicle [7741.2163, 12519.042, -0.038108263];


  _vehicle_3034 = _this;
  _this setDir 123.96923;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7741.2163, 12519.042, -0.038108263];
};

_vehicle_3040 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3" createVehicle [7742.9619, 12521.446, -0.054247636];


  _vehicle_3040 = _this;
  _this setDir 125.64857;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7742.9619, 12521.446, -0.054247636];
};

_vehicle_3043 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3" createVehicle [7744.6714, 12523.787];


  _vehicle_3043 = _this;
  _this setDir 125.64857;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7744.6714, 12523.787];
};

_vehicle_3046 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7735.252, 12529.604, 0.44720191];


  _vehicle_3046 = _this;
  _this setDir -52.068256;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7735.252, 12529.604, 0.44720191];
};

_vehicle_3048 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7739.6309, 12532.876, 0.29643553];


  _vehicle_3048 = _this;
  _this setDir -4.5086451;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7739.6309, 12532.876, 0.29643553];
};

_vehicle_3054 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7723.6431, 12514.021, 3.8146973e-005];


  _vehicle_3054 = _this;
  _this setDir -52.74184;
  _this setPos [7723.6431, 12514.021, 3.8146973e-005];
};

_vehicle_3057 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7757.564, 12549.778, 0.028785238];


  _vehicle_3057 = _this;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7757.564, 12549.778, 0.028785238];
};

_vehicle_3072 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_paletyD" createVehicle [7917.0625, 12611.361, 7.6293945e-005];


  _vehicle_3072 = _this;
  _this setDir 10.805914;
  _this setPos [7917.0625, 12611.361, 7.6293945e-005];
};

_vehicle_3075 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Chair_EP1" createVehicle [7850.5767, 12616.743, 8.392334e-005];


  _vehicle_3075 = _this;
  _this setPos [7850.5767, 12616.743, 8.392334e-005];
};

_vehicle_3094 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_kasna_new" createVehicle [7860.8657, 12618.007, -6.1035156e-005];


  _vehicle_3094 = _this;
  _this setDir -24.03895;
  _this setPos [7860.8657, 12618.007, -6.1035156e-005];
};

_vehicle_3131 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_drevena_bedna" createVehicle [7846.0449, 12617.47, 3.0517578e-005];


  _vehicle_3131 = _this;
  _this setDir -11.041709;
  _this setPos [7846.0449, 12617.47, 3.0517578e-005];
};

_vehicle_3149 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_drevena_bedna" createVehicle [7845.6689, 12618.589, 1.5258789e-005];


  _vehicle_3149 = _this;
  _this setDir -31.70224;
  _this setPos [7845.6689, 12618.589, 1.5258789e-005];
};

_vehicle_3152 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_drevena_bedna" createVehicle [7845.7871, 12618.038, 0.97278953];


  _vehicle_3152 = _this;
  _this setDir 48.749416;
  _this setPos [7845.7871, 12618.038, 0.97278953];
};

_vehicle_3155 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Crates_stack_EP1" createVehicle [7787.1172, 12598.455];


  _vehicle_3155 = _this;
  _this setDir -18.051004;
  _this setPos [7787.1172, 12598.455];
};

_vehicle_3158 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_P_pipe_big" createVehicle [7898.7129, 12598.843, 2.2888184e-005];


  _vehicle_3158 = _this;
  _this setDir 100.24046;
  _this setPos [7898.7129, 12598.843, 2.2888184e-005];
};

_vehicle_3163 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_misc" createVehicle [7817.3384, 12633.955, 8.392334e-005];


  _vehicle_3163 = _this;
  _this setDir 10.767595;
  _this setPos [7817.3384, 12633.955, 8.392334e-005];
};

_vehicle_3165 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_misc" createVehicle [7900.5684, 12598.122, 9.9182129e-005];


  _vehicle_3165 = _this;
  _this setDir 10.767595;
  _this setPos [7900.5684, 12598.122, 9.9182129e-005];
};

_vehicle_3167 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7747.6357, 12539.605, 0.35511637];


  _vehicle_3167 = _this;
  _this setDir -77.675339;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7747.6357, 12539.605, 0.35511637];
};

_vehicle_3170 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7749.6011, 12545.132, 0.15544561];


  _vehicle_3170 = _this;
  _this setDir -54.994434;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7749.6011, 12545.132, 0.15544561];
};

_vehicle_3173 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_2" createVehicle [7753.6138, 12549.237, -0.04979641];


  _vehicle_3173 = _this;
  _this setDir -26.920389;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7753.6138, 12549.237, -0.04979641];
};

_vehicle_3177 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7780.6899, 12554.199, 3.0517578e-005];


  _vehicle_3177 = _this;
  _this setDir -12.915766;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7780.6899, 12554.199, 3.0517578e-005];
};

_vehicle_3179 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7786.2002, 12555.71];


  _vehicle_3179 = _this;
  _this setDir -17.978888;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7786.2002, 12555.71];
};

_vehicle_3182 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7791.1021, 12557.339, 9.9182129e-005];


  _vehicle_3182 = _this;
  _this setDir -19.071436;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7791.1021, 12557.339, 9.9182129e-005];
};

_vehicle_3185 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowLines_WoodL" createVehicle [7790.5249, 12572.307, 6.8664551e-005];


  _vehicle_3185 = _this;
  _this setDir -40.325634;
  _this setPos [7790.5249, 12572.307, 6.8664551e-005];
};

_vehicle_3188 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7788.9834, 12558.271, 1.5258789e-005];


  _vehicle_3188 = _this;
  _this setDir 86.013474;
  _this setPos [7788.9834, 12558.271, 1.5258789e-005];
};

_vehicle_3202 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7916.0996, 12588.691, 0.098016635];


  _vehicle_3202 = _this;
  _this setDir -16.835325;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7916.0996, 12588.691, 0.098016635];
};

_vehicle_3204 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7910.501, 12586.967, 0.24299401];


  _vehicle_3204 = _this;
  _this setDir -16.835325;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7910.501, 12586.967, 0.24299401];
};

_vehicle_3207 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7910.0327, 12586.813, 3.0517578e-005];


  _vehicle_3207 = _this;
  _this setDir 162.86247;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7910.0327, 12586.813, 3.0517578e-005];
};

_vehicle_3210 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_2" createVehicle [7904.1211, 12584.9, 6.8664551e-005];


  _vehicle_3210 = _this;
  _this setDir -15.40204;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7904.1211, 12584.9, 6.8664551e-005];
};

_vehicle_3213 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7906.2573, 12585.625, 0.43084747];


  _vehicle_3213 = _this;
  _this setDir -15.635259;
  _this setPos [7906.2573, 12585.625, 0.43084747];
};

_vehicle_3216 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7924.439, 12623.852, 4.5776367e-005];


  _vehicle_3216 = _this;
  _this setDir -99.650764;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7924.439, 12623.852, 4.5776367e-005];
};

_vehicle_3219 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7920.8911, 12647.258, 0.1117107];


  _vehicle_3219 = _this;
  _this setDir -96.840584;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7920.8911, 12647.258, 0.1117107];
};

_vehicle_3222 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7910.7197, 12650.915, -6.8664551e-005];


  _vehicle_3222 = _this;
  _this setDir -0.90912426;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7910.7197, 12650.915, -6.8664551e-005];
};

_vehicle_3224 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7916.3057, 12651.122, 0.0078656524];


  _vehicle_3224 = _this;
  _this setDir -2.2477331;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7916.3057, 12651.122, 0.0078656524];
};

_vehicle_3227 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7920.3511, 12651.286, 0.38704887];


  _vehicle_3227 = _this;
  _this setDir -15.635259;
  _this setPos [7920.3511, 12651.286, 0.38704887];
};

_vehicle_3230 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Chair_EP1" createVehicle [7731.0479, 12578.598, 7.6293945e-005];


  _vehicle_3230 = _this;
  _this setDir 110.9845;
  _this setVehicleInit "this setVectorUp [1,1,0];";
  _this setPos [7731.0479, 12578.598, 7.6293945e-005];
};

_vehicle_3233 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Chair_EP1" createVehicle [7730.731, 12582.142, -2.2888184e-005];


  _vehicle_3233 = _this;
  _this setDir 42.077667;
  _this setPos [7730.731, 12582.142, -2.2888184e-005];
};

_vehicle_3238 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7713.7119, 12595.712, 5.3405762e-005];


  _vehicle_3238 = _this;
  _this setDir -33.716564;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7713.7119, 12595.712, 5.3405762e-005];
};

_vehicle_3241 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7683.3022, 12523.707, 0.063509457];


  _vehicle_3241 = _this;
  _this setDir -68.062004;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7683.3022, 12523.707, 0.063509457];
};

_vehicle_3243 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7681.1528, 12518.295, 0.32377225];


  _vehicle_3243 = _this;
  _this setDir -68.062004;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7681.1528, 12518.295, 0.32377225];
};

_vehicle_3245 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7678.9429, 12512.729, 0.56284893];


  _vehicle_3245 = _this;
  _this setDir -68.312981;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7678.9429, 12512.729, 0.56284893];
};

_vehicle_3254 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4" createVehicle [7678.5972, 12498.443, 0.43101427];


  _vehicle_3254 = _this;
  _this setDir 199.51678;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7678.5972, 12498.443, 0.43101427];
};

_vehicle_3257 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7710.0029, 12592.348, -3.0517578e-005];


  _vehicle_3257 = _this;
  _this setDir 28.464458;
  _this setPos [7710.0029, 12592.348, -3.0517578e-005];
};

_vehicle_3259 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7693.7144, 12493.223, 0.00019073486];


  _vehicle_3259 = _this;
  _this setDir 18.083979;
  _this setPos [7693.7144, 12493.223, 0.00019073486];
};

_vehicle_3262 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7676.7632, 12507.205, 0.70251739];


  _vehicle_3262 = _this;
  _this setDir -68.156609;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7676.7632, 12507.205, 0.70251739];
};

_vehicle_3265 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_2" createVehicle [7675.7144, 12504.048, 0.17813382];


  _vehicle_3265 = _this;
  _this setDir -75.220985;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7675.7144, 12504.048, 0.17813382];
};

_vehicle_3268 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7675.5029, 12501.442, 0.00018310547];


  _vehicle_3268 = _this;
  _this setDir -78.054497;
  _this setPos [7675.5029, 12501.442, 0.00018310547];
};

_vehicle_3271 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7674.7954, 12499.78, 0.31374401];


  _vehicle_3271 = _this;
  _this setDir 22.770645;
  _this setPos [7674.7954, 12499.78, 0.31374401];
};

_vehicle_3273 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7775.0093, 12552.913, 6.1035156e-005];


  _vehicle_3273 = _this;
  _this setDir -12.757367;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7775.0093, 12552.913, 6.1035156e-005];
};

_vehicle_3276 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7828.8638, 12569.324, 0.0001449585];


  _vehicle_3276 = _this;
  _this setDir -9.0099821;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7828.8638, 12569.324, 0.0001449585];
};

_vehicle_3279 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7804.6582, 12654.191, 0.43110442];


  _vehicle_3279 = _this;
  _this setDir -16.062721;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7804.6582, 12654.191, 0.43110442];
};

_vehicle_3281 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7883.1304, 12663.501, 0.23480181];


  _vehicle_3281 = _this;
  _this setDir 67.869278;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7883.1304, 12663.501, 0.23480181];
};

_vehicle_3283 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7904.8823, 12650.732, 6.1035156e-005];


  _vehicle_3283 = _this;
  _this setDir -2.6829262;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7904.8823, 12650.732, 6.1035156e-005];
};

_vehicle_3285 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7921.6479, 12641.445, 1.5258789e-005];


  _vehicle_3285 = _this;
  _this setDir -97.977661;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7921.6479, 12641.445, 1.5258789e-005];
};

_vehicle_3287 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7929.2993, 12600.905, 1.5258789e-005];


  _vehicle_3287 = _this;
  _this setDir -102.66679;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7929.2993, 12600.905, 1.5258789e-005];
};

_vehicle_3289 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7921.7261, 12590.457, 3.0517578e-005];


  _vehicle_3289 = _this;
  _this setDir -16.533419;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7921.7261, 12590.457, 3.0517578e-005];
};

_vehicle_3291 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7881.0518, 12579.889, 0.098541617];


  _vehicle_3291 = _this;
  _this setDir -8.2958708;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7881.0518, 12579.889, 0.098541617];
};

_vehicle_3294 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_2" createVehicle [7869.4355, 12577.435, 3.8146973e-005];


  _vehicle_3294 = _this;
  _this setDir -11.19252;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7869.4355, 12577.435, 3.8146973e-005];
};

_vehicle_3298 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7876.0879, 12579.213, 5.3405762e-005];


  _vehicle_3298 = _this;
  _this setDir -16.845036;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7876.0879, 12579.213, 5.3405762e-005];
};

_vehicle_3299 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7874.3472, 12578.682, -0.042869568];


  _vehicle_3299 = _this;
  _this setDir -5.8251467;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7874.3472, 12578.682, -0.042869568];
};

_vehicle_3303 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7872.5093, 12578.586, 0.00022125244];


  _vehicle_3303 = _this;
  _this setDir 177.78392;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7872.5093, 12578.586, 0.00022125244];
};

_vehicle_3306 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7877.4063, 12579.306, 0.00012969971];


  _vehicle_3306 = _this;
  _this setDir 169.91122;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7877.4063, 12579.306, 0.00012969971];
};

_vehicle_3309 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7850.2612, 12573.518, 0.00010681152];


  _vehicle_3309 = _this;
  _this setDir -9.6555786;
  _this setPos [7850.2612, 12573.518, 0.00010681152];
};

_vehicle_3312 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7852.459, 12573.997, -0.00010681152];


  _vehicle_3312 = _this;
  _this setDir 169.91122;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7852.459, 12573.997, -0.00010681152];
};

_vehicle_3315 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7880.8066, 12668.735, -5.3405762e-005];


  _vehicle_3315 = _this;
  _this setDir -11.877622;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7880.8066, 12668.735, -5.3405762e-005];
};

_vehicle_3317 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7879.251, 12671.934, 4.5776367e-005];


  _vehicle_3317 = _this;
  _this setDir -5.8251467;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7879.251, 12671.934, 4.5776367e-005];
};

_vehicle_3320 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7881.4248, 12666.988, 7.6293945e-006];


  _vehicle_3320 = _this;
  _this setDir 73.51503;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7881.4248, 12666.988, 7.6293945e-006];
};

_vehicle_3323 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7879.9277, 12670.18, 2.2888184e-005];


  _vehicle_3323 = _this;
  _this setDir 68.191025;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7879.9277, 12670.18, 2.2888184e-005];
};

_vehicle_3327 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7774.0859, 12637.081, 3.8146973e-005];


  _vehicle_3327 = _this;
  _this setDir -37.830696;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7774.0859, 12637.081, 3.8146973e-005];
};

_vehicle_3328 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7773.8022, 12635.552, -0.075187683];


  _vehicle_3328 = _this;
  _this setDir 127.62495;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7773.8022, 12635.552, -0.075187683];
};

_vehicle_3332 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7770.9243, 12634.457, 7.6293945e-006];


  _vehicle_3332 = _this;
  _this setDir -37.830696;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7770.9243, 12634.457, 7.6293945e-006];
};

_vehicle_3335 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7774.2578, 12636.867, 1.5258789e-005];


  _vehicle_3335 = _this;
  _this setDir 318.07513;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7774.2578, 12636.867, 1.5258789e-005];
};

_vehicle_3338 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7772.3154, 12635.726, -1.5258789e-005];


  _vehicle_3338 = _this;
  _this setDir 318.07513;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7772.3154, 12635.726, -1.5258789e-005];
};

_vehicle_3341 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_End_3" createVehicle [7711.2363, 12593.675, 3.8146973e-005];


  _vehicle_3341 = _this;
  _this setDir 308.71304;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7711.2363, 12593.675, 3.8146973e-005];
};

_vehicle_3344 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7699.4878, 12567.283, 7.6293945e-006];


  _vehicle_3344 = _this;
  _this setDir -69.504143;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7699.4878, 12567.283, 7.6293945e-006];
};

_vehicle_3347 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7687.5742, 12613.419, -7.6293945e-006];


  _vehicle_3347 = _this;
  _this setDir -4.9652934;
  _this setPos [7687.5742, 12613.419, -7.6293945e-006];
};

_vehicle_3349 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7696.2666, 12675.953, -5.3405762e-005];


  _vehicle_3349 = _this;
  _this setDir -4.9652934;
  _this setPos [7696.2666, 12675.953, -5.3405762e-005];
};

_vehicle_3351 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7671.8506, 12685.063, -0.00018310547];


  _vehicle_3351 = _this;
  _this setDir -4.9652934;
  _this setPos [7671.8506, 12685.063, -0.00018310547];
};

_vehicle_3353 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7719.8081, 12693.51, 1.5258789e-005];


  _vehicle_3353 = _this;
  _this setDir -4.9652934;
  _this setPos [7719.8081, 12693.51, 1.5258789e-005];
};

_vehicle_3355 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7687.6631, 12673.383, 9.1552734e-005];


  _vehicle_3355 = _this;
  _this setDir -4.9652934;
  _this setPos [7687.6631, 12673.383, 9.1552734e-005];
};

_vehicle_3357 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7687.1963, 12637.019];


  _vehicle_3357 = _this;
  _this setDir -4.9652934;
  _this setPos [7687.1963, 12637.019];
};

_vehicle_3360 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_4_D" createVehicle [7715.7471, 12620.471, 9.9182129e-005];


  _vehicle_3360 = _this;
  _this setDir -122.20431;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7715.7471, 12620.471, 9.9182129e-005];
};

_vehicle_3363 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_paleta" createVehicle [7825.1743, 12639.88, 0.023668554];


  _vehicle_3363 = _this;
  _this setDir 48.730839;
  _this setPos [7825.1743, 12639.88, 0.023668554];
};

_vehicle_3369 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SKODAWreck" createVehicle [7824.6357, 12636.652, -2.2888184e-005];


  _vehicle_3369 = _this;
  _this setDir 160.88672;
  _this setPos [7824.6357, 12636.652, -2.2888184e-005];
};

_vehicle_3372 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench1" createVehicle [7790.9272, 12577.936, -0.040685773];


  _vehicle_3372 = _this;
  _this setDir 70.428314;
  _this setPos [7790.9272, 12577.936, -0.040685773];
};

_vehicle_3374 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Park_bench1" createVehicle [7850.689, 12673.545, -2.2888184e-005];


  _vehicle_3374 = _this;
  _this setDir -4.6770239;
  _this setPos [7850.689, 12673.545, -2.2888184e-005];
};

_vehicle_3377 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Kontejner" createVehicle [7860.0698, 12670.504, 7.6293945e-006];


  _vehicle_3377 = _this;
  _this setDir -8.4194508;
  _this setPos [7860.0698, 12670.504, 7.6293945e-006];
};

_vehicle_3380 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_paletyC" createVehicle [7843.272, 12667.403, 2.2888184e-005];


  _vehicle_3380 = _this;
  _this setDir 107.43632;
  _this setPos [7843.272, 12667.403, 2.2888184e-005];
};

_vehicle_3383 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_paleta" createVehicle [7843.666, 12664.731, 7.6293945e-006];


  _vehicle_3383 = _this;
  _this setDir -7.8187428;
  _this setPos [7843.666, 12664.731, 7.6293945e-006];
};

_vehicle_3386 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Chair_EP1" createVehicle [7851.5146, 12667.262, -7.6293945e-006];


  _vehicle_3386 = _this;
  _this setPos [7851.5146, 12667.262, -7.6293945e-006];
};

_vehicle_3388 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Chair_EP1" createVehicle [7853.0234, 12667.976, -1.5258789e-005];


  _vehicle_3388 = _this;
  _this setDir 95.150948;
  _this setPos [7853.0234, 12667.976, -1.5258789e-005];
};

_vehicle_3391 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7875.1362, 12656.505, -2.2888184e-005];


  _vehicle_3391 = _this;
  _this setDir 141.53357;
  _this setPos [7875.1362, 12656.505, -2.2888184e-005];
};

_vehicle_3393 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_concrete_High" createVehicle [7870.8428, 12661.853, 2.2888184e-005];


  _vehicle_3393 = _this;
  _this setDir 20.668053;
  _this setPos [7870.8428, 12661.853, 2.2888184e-005];
};

_vehicle_3396 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7815.6128, 12659.087, -7.6293945e-005];


  _vehicle_3396 = _this;
  _this setDir 41.869556;
  _this setPos [7815.6128, 12659.087, -7.6293945e-005];
};

_vehicle_3398 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7757.667, 12652.342, 3.0517578e-005];


  _vehicle_3398 = _this;
  _this setDir 41.869556;
  _this setPos [7757.667, 12652.342, 3.0517578e-005];
};

_vehicle_3400 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7735.9771, 12683.471, -9.1552734e-005];


  _vehicle_3400 = _this;
  _this setDir 41.869556;
  _this setPos [7735.9771, 12683.471, -9.1552734e-005];
};

_vehicle_3402 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7714.7217, 12671.391, -3.0517578e-005];


  _vehicle_3402 = _this;
  _this setDir 41.869556;
  _this setPos [7714.7217, 12671.391, -3.0517578e-005];
};

_vehicle_3404 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7703.877, 12643.557, -7.6293945e-006];


  _vehicle_3404 = _this;
  _this setDir 41.869556;
  _this setPos [7703.877, 12643.557, -7.6293945e-006];
};

_vehicle_3406 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7711.9375, 12596.388, 7.6293945e-005];


  _vehicle_3406 = _this;
  _this setDir 41.869556;
  _this setPos [7711.9375, 12596.388, 7.6293945e-005];
};

_vehicle_3409 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7849.5859, 12677.765, 7.6293945e-005];


  _vehicle_3409 = _this;
  _this setDir 16.278889;
  _this setPos [7849.5859, 12677.765, 7.6293945e-005];
};

_vehicle_3411 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7863.8018, 12665.798, 3.0517578e-005];


  _vehicle_3411 = _this;
  _this setDir 16.278889;
  _this setPos [7863.8018, 12665.798, 3.0517578e-005];
};

_vehicle_3413 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [7880.6128, 12649.854, 1.5258789e-005];


  _vehicle_3413 = _this;
  _this setDir 16.278889;
  _this setPos [7880.6128, 12649.854, 1.5258789e-005];
};

_vehicle_3416 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7880.0132, 12647.433, 4.5776367e-005];


  _vehicle_3416 = _this;
  _this setDir 41.869556;
  _this setPos [7880.0132, 12647.433, 4.5776367e-005];
};

_vehicle_3418 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7879.3931, 12651.148, 0.0001373291];


  _vehicle_3418 = _this;
  _this setDir 41.869556;
  _this setPos [7879.3931, 12651.148, 0.0001373291];
};

_vehicle_3427 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7750.354, 12530.602, -0.26349542];


  _vehicle_3427 = _this;
  _this setDir -231.90158;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7750.354, 12530.602, -0.26349542];
};

_vehicle_3430 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7765.0024, 12541.633, 0.0001449585];


  _vehicle_3430 = _this;
  _this setDir -194.98007;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7765.0024, 12541.633, 0.0001449585];
};

_vehicle_3433 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7773.6895, 12543.951, 9.1552734e-005];


  _vehicle_3433 = _this;
  _this setDir -194.98007;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7773.6895, 12543.951, 9.1552734e-005];
};

_vehicle_3435 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7782.3369, 12546.228];


  _vehicle_3435 = _this;
  _this setDir -194.98007;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7782.3369, 12546.228];
};

_vehicle_3439 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7797.4351, 12551.152, -3.8146973e-005];


  _vehicle_3439 = _this;
  _this setDir -196.01526;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7797.4351, 12551.152, -3.8146973e-005];
};

_vehicle_3441 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7808.938, 12553.894, 8.392334e-005];


  _vehicle_3441 = _this;
  _this setDir -190.84889;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7808.938, 12553.894, 8.392334e-005];
};

_vehicle_3449 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_CncBlock" createVehicle [7918.1958, 12637.156];


  _vehicle_3449 = _this;
  _this setDir -93.736725;
  _this setPos [7918.1958, 12637.156];
};

_vehicle_3453 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_CncBlock" createVehicle [7918.4067, 12634.313, -7.6293945e-006];


  _vehicle_3453 = _this;
  _this setDir -93.736725;
  _this setPos [7918.4067, 12634.313, -7.6293945e-006];
};

_vehicle_3456 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7869.0654, 12625.33, 4.5776367e-005];


  _vehicle_3456 = _this;
  _this setDir 41.869556;
  _this setPos [7869.0654, 12625.33, 4.5776367e-005];
};

_vehicle_3459 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [7696.9199, 12559.537, -2.2888184e-005];


  _vehicle_3459 = _this;
  _this setDir -73.749771;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7696.9199, 12559.537, -2.2888184e-005];
};

_vehicle_3461 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [7728.1592, 12520.459, 3.8146973e-005];


  _vehicle_3461 = _this;
  _this setDir 131.5825;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7728.1592, 12520.459, 3.8146973e-005];
};

_vehicle_3463 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [7784.6045, 12555.271, 6.1035156e-005];


  _vehicle_3463 = _this;
  _this setDir -198.33414;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7784.6045, 12555.271, 6.1035156e-005];
};

_vehicle_3465 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [7860.542, 12575.177, 0.00016021729];


  _vehicle_3465 = _this;
  _this setDir -187.83096;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7860.542, 12575.177, 0.00016021729];
};

_vehicle_3467 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [7917.9106, 12652.888, 2.2888184e-005];


  _vehicle_3467 = _this;
  _this setDir 34.198452;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7917.9106, 12652.888, 2.2888184e-005];
};

_vehicle_3469 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [7932.2051, 12596.282, 9.1552734e-005];


  _vehicle_3469 = _this;
  _this setDir 112.35009;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7932.2051, 12596.282, 9.1552734e-005];
};

_vehicle_3471 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [7847.9741, 12682.687, 2.2888184e-005];


  _vehicle_3471 = _this;
  _this setDir -7.2314796;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7847.9741, 12682.687, 2.2888184e-005];
};

_vehicle_3473 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_vez" createVehicle [7803.2559, 12653.333, -5.3405762e-005];


  _vehicle_3473 = _this;
  _this setDir -17.094494;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7803.2559, 12653.333, -5.3405762e-005];
};

_vehicle_3476 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7635.4204, 12554.7, 5.3405762e-005];


  _vehicle_3476 = _this;
  _this setDir 58.261475;
  _this setPos [7635.4204, 12554.7, 5.3405762e-005];
};

_vehicle_3478 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7630.3857, 12564.393, 7.6293945e-005];


  _vehicle_3478 = _this;
  _this setDir 58.261475;
  _this setPos [7630.3857, 12564.393, 7.6293945e-005];
};

_vehicle_3480 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7626.687, 12574.174, 0.00012207031];


  _vehicle_3480 = _this;
  _this setDir 58.261475;
  _this setPos [7626.687, 12574.174, 0.00012207031];
};

_vehicle_3482 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7615.2529, 12558.074, 0.00011444092];


  _vehicle_3482 = _this;
  _this setDir 58.261475;
  _this setPos [7615.2529, 12558.074, 0.00011444092];
};

_vehicle_3484 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7589.7627, 12607.56, 4.5776367e-005];


  _vehicle_3484 = _this;
  _this setDir 58.261475;
  _this setPos [7589.7627, 12607.56, 4.5776367e-005];
};

_vehicle_3486 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7601.6611, 12625.639, 1.5258789e-005];


  _vehicle_3486 = _this;
  _this setDir 58.261475;
  _this setPos [7601.6611, 12625.639, 1.5258789e-005];
};

_vehicle_3488 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7592.4956, 12629.593, 6.8664551e-005];


  _vehicle_3488 = _this;
  _this setDir 58.261475;
  _this setPos [7592.4956, 12629.593, 6.8664551e-005];
};

_vehicle_3490 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7581.3525, 12624.001, 1.5258789e-005];


  _vehicle_3490 = _this;
  _this setDir 58.261475;
  _this setPos [7581.3525, 12624.001, 1.5258789e-005];
};

_vehicle_3492 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7568.6475, 12554.68, 1.5258789e-005];


  _vehicle_3492 = _this;
  _this setDir 58.261475;
  _this setPos [7568.6475, 12554.68, 1.5258789e-005];
};

_vehicle_3494 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7561.8696, 12565.086, 3.8146973e-005];


  _vehicle_3494 = _this;
  _this setDir 58.261475;
  _this setPos [7561.8696, 12565.086, 3.8146973e-005];
};

_vehicle_3496 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7558.2539, 12556.716, 0.00018310547];


  _vehicle_3496 = _this;
  _this setDir 58.261475;
  _this setPos [7558.2539, 12556.716, 0.00018310547];
};

_vehicle_3499 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7642.6465, 12621.509, -3.0517578e-005];


  _vehicle_3499 = _this;
  _this setDir -4.9652934;
  _this setPos [7642.6465, 12621.509, -3.0517578e-005];
};

_vehicle_3501 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7632.4028, 12572.5, 2.2888184e-005];


  _vehicle_3501 = _this;
  _this setDir -4.9652934;
  _this setPos [7632.4028, 12572.5, 2.2888184e-005];
};

_vehicle_3503 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7607.3389, 12620.054, -9.1552734e-005];


  _vehicle_3503 = _this;
  _this setDir -4.9652934;
  _this setPos [7607.3389, 12620.054, -9.1552734e-005];
};

_vehicle_3505 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea1s" createVehicle [7610.2139, 12609.206, 4.5776367e-005];


  _vehicle_3505 = _this;
  _this setDir -4.9652934;
  _this setPos [7610.2139, 12609.206, 4.5776367e-005];
};

_vehicle_3508 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Pile_of_wood" createVehicle [7808.8906, 12575.16, 0.00019073486];


  _vehicle_3508 = _this;
  _this setDir 430.78894;
  _this setPos [7808.8906, 12575.16, 0.00019073486];
};

_vehicle_3510 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Pile_of_wood" createVehicle [7873.2017, 12594.613, 0.00020599365];


  _vehicle_3510 = _this;
  _this setDir 346.4054;
  _this setPos [7873.2017, 12594.613, 0.00020599365];
};

_vehicle_3513 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Chair_EP1" createVehicle [7761.7554, 12597.08, 3.0517578e-005];


  _vehicle_3513 = _this;
  _this setDir -59.887779;
  _this setPos [7761.7554, 12597.08, 3.0517578e-005];
};

_vehicle_3515 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Chair_EP1" createVehicle [7855.0962, 12589.014, 1.5258789e-005];


  _vehicle_3515 = _this;
  _this setDir -187.77448;
  _this setPos [7855.0962, 12589.014, 1.5258789e-005];
};

_vehicle_3517 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Chair_EP1" createVehicle [7853.3965, 12588.111, 3.0517578e-005];


  _vehicle_3517 = _this;
  _this setDir -102.72573;
  _this setPos [7853.3965, 12588.111, 3.0517578e-005];
};

_vehicle_3520 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7800.2422, 12552.025, 7.6293945e-006];


  _vehicle_3520 = _this;
  _this setDir 161.6131;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7800.2422, 12552.025, 7.6293945e-006];
};

_vehicle_3523 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7817.7192, 12555.568, 4.5776367e-005];


  _vehicle_3523 = _this;
  _this setDir -190.84889;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7817.7192, 12555.568, 4.5776367e-005];
};

_vehicle_3527 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7826.4404, 12557.247, 9.1552734e-005];


  _vehicle_3527 = _this;
  _this setDir -190.84889;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7826.4404, 12557.247, 9.1552734e-005];
};

_vehicle_3532 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7843.8813, 12560.556, 7.6293945e-006];


  _vehicle_3532 = _this;
  _this setDir -190.84889;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7843.8813, 12560.556, 7.6293945e-006];
};

_vehicle_3534 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7852.5923, 12562.2, 7.6293945e-005];


  _vehicle_3534 = _this;
  _this setDir -190.84889;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7852.5923, 12562.2, 7.6293945e-005];
};

_vehicle_3536 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7861.3203, 12563.857, 5.3405762e-005];


  _vehicle_3536 = _this;
  _this setDir -190.84889;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7861.3203, 12563.857, 5.3405762e-005];
};

_vehicle_3538 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7870.0376, 12565.476, 3.0517578e-005];


  _vehicle_3538 = _this;
  _this setDir -190.84889;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7870.0376, 12565.476, 3.0517578e-005];
};

_vehicle_3541 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7878.7002, 12567.382, -0.10870387];


  _vehicle_3541 = _this;
  _this setDir -194.2381;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7878.7002, 12567.382, -0.10870387];
};

_vehicle_3544 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7829.4023, 12648.106, 6.1035156e-005];


  _vehicle_3544 = _this;
  _this setDir 43.023148;
  _this setPos [7829.4023, 12648.106, 6.1035156e-005];
};

_vehicle_3546 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [7836.9497, 12650.66, -7.6293945e-006];


  _vehicle_3546 = _this;
  _this setDir 43.023148;
  _this setPos [7836.9497, 12650.66, -7.6293945e-006];
};

_vehicle_3549 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_betula2w" createVehicle [7832.8066, 12649.644];


  _vehicle_3549 = _this;
  _this setDir 102.25393;
  _this setPos [7832.8066, 12649.644];
};

_vehicle_3550 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Misc_deerstand" createVehicle [7612.5811, 12159.192, 0.78900099];


  _vehicle_3550 = _this;
  _this setDir 197.37183;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [7612.5811, 12159.192, 0.78900099];
};

_vehicle_3554 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7898.6812, 12571.969, 6.8664551e-005];


  _vehicle_3554 = _this;
  _this setDir -191.04311;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7898.6812, 12571.969, 6.8664551e-005];
};

_vehicle_3556 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7907.1528, 12573.562, 9.1552734e-005];


  _vehicle_3556 = _this;
  _this setDir -190.84889;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7907.1528, 12573.562, 9.1552734e-005];
};

_vehicle_3558 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7918.853, 12576.157, 0.059340518];


  _vehicle_3558 = _this;
  _this setDir -190.84889;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7918.853, 12576.157, 0.059340518];
};

_vehicle_3560 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7927.6113, 12577.684, -0.058003716];


  _vehicle_3560 = _this;
  _this setDir -189.07741;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7927.6113, 12577.684, -0.058003716];
};

_vehicle_3562 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7936.4082, 12579.176, -0.28788462];


  _vehicle_3562 = _this;
  _this setDir -189.83501;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7936.4082, 12579.176, -0.28788462];
};

_vehicle_3566 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7941.5527, 12588.576, -0.05608245];


  _vehicle_3566 = _this;
  _this setDir -280.71292;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7941.5527, 12588.576, -0.05608245];
};

_vehicle_3572 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7835.1636, 12558.864, 7.6293945e-005];


  _vehicle_3572 = _this;
  _this setDir 169.15225;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7835.1636, 12558.864, 7.6293945e-005];
};

_vehicle_3575 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7757.7329, 12539.644, 6.1035156e-005];


  _vehicle_3575 = _this;
  _this setDir 164.48991;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7757.7329, 12539.644, 6.1035156e-005];
};

_vehicle_3578 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7753.0132, 12534.382, -0.29692084];


  _vehicle_3578 = _this;
  _this setDir 114.88708;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7753.0132, 12534.382, -0.29692084];
};

_vehicle_3580 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7790.2749, 12549.117];


  _vehicle_3580 = _this;
  _this setDir 164.48991;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7790.2749, 12549.117];
};

_vehicle_3582 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7786.7734, 12547.655, 5.3405762e-005];


  _vehicle_3582 = _this;
  _this setDir 157.5959;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7786.7734, 12547.655, 5.3405762e-005];
};

_vehicle_3585 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7830.0254, 12557.875, 7.6293945e-005];


  _vehicle_3585 = _this;
  _this setDir 157.5959;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7830.0254, 12557.875, 7.6293945e-005];
};

_vehicle_3587 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7833.6489, 12558.597, 2.2888184e-005];


  _vehicle_3587 = _this;
  _this setDir 157.5959;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7833.6489, 12558.597, 2.2888184e-005];
};

_vehicle_3589 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7883.8467, 12568.244, 0.00011444092];


  _vehicle_3589 = _this;
  _this setDir 170.67369;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7883.8467, 12568.244, 0.00011444092];
};

_vehicle_3592 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7889.9209, 12570.269, 0.071947746];


  _vehicle_3592 = _this;
  _this setDir 169.15225;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7889.9209, 12570.269, 0.071947746];
};

_vehicle_3595 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7888.3931, 12569.974, -7.6293945e-006];


  _vehicle_3595 = _this;
  _this setDir 165.55768;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7888.3931, 12569.974, -7.6293945e-006];
};

_vehicle_3597 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7911.4673, 12574.765, 9.1552734e-005];


  _vehicle_3597 = _this;
  _this setDir 160.05223;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7911.4673, 12574.765, 9.1552734e-005];
};

_vehicle_3599 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7940.5278, 12580.279, -0.18834148];


  _vehicle_3599 = _this;
  _this setDir 165.026;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7940.5278, 12580.279, -0.18834148];
};

_vehicle_3602 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7941.8813, 12580.862, 0.22457542];


  _vehicle_3602 = _this;
  _this setDir 155.68962;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7941.8813, 12580.862, 0.22457542];
};

_vehicle_3605 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7754.8975, 12537.334, 8.392334e-005];


  _vehicle_3605 = _this;
  _this setDir 125.70568;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7754.8975, 12537.334, 8.392334e-005];
};

_vehicle_3608 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7939.6157, 12600.632, -0.14459565];


  _vehicle_3608 = _this;
  _this setDir -280.71292;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7939.6157, 12600.632, -0.14459565];
};

_vehicle_3611 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7940.9541, 12593.282, -0.085611328];


  _vehicle_3611 = _this;
  _this setDir 76.927849;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7940.9541, 12593.282, -0.085611328];
};

_vehicle_3614 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7936.8413, 12616.052, 8.392334e-005];


  _vehicle_3614 = _this;
  _this setDir -278.80399;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7936.8413, 12616.052, 8.392334e-005];
};

_vehicle_3617 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7935.6973, 12624.903, -0.11276236];


  _vehicle_3617 = _this;
  _this setDir -278.80399;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7935.6973, 12624.903, -0.11276236];
};

_vehicle_3620 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7933.9863, 12637.072];


  _vehicle_3620 = _this;
  _this setDir -278.80399;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7933.9863, 12637.072];
};

_vehicle_3623 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7932.5918, 12645.894, 0.09865436];


  _vehicle_3623 = _this;
  _this setDir -278.80399;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7932.5918, 12645.894, 0.09865436];
};

_vehicle_3626 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7930.6602, 12657.835, -0.00011444092];


  _vehicle_3626 = _this;
  _this setDir -278.80399;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7930.6602, 12657.835, -0.00011444092];
};

_vehicle_3629 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7939.1187, 12603.5, 4.5776367e-005];


  _vehicle_3629 = _this;
  _this setDir 74.251991;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7939.1187, 12603.5, 4.5776367e-005];
};

_vehicle_3632 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7938.0171, 12608.755, 3.0517578e-005];


  _vehicle_3632 = _this;
  _this setDir 75.935005;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7938.0171, 12608.755, 3.0517578e-005];
};

_vehicle_3634 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7935.1255, 12629.682, 8.392334e-005];


  _vehicle_3634 = _this;
  _this setDir 83.045418;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7935.1255, 12629.682, 8.392334e-005];
};

_vehicle_3636 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7932.0264, 12650.381, -4.5776367e-005];


  _vehicle_3636 = _this;
  _this setDir 90.130608;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7932.0264, 12650.381, -4.5776367e-005];
};

_vehicle_3639 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7928.7988, 12669.413, 0.17254101];


  _vehicle_3639 = _this;
  _this setDir -276.18494;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7928.7988, 12669.413, 0.17254101];
};

_vehicle_3642 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7929.7754, 12661.957, 0.037623398];


  _vehicle_3642 = _this;
  _this setDir 90.130608;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7929.7754, 12661.957, 0.037623398];
};

_vehicle_3645 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7921.231, 12669.913, 0.19976065];


  _vehicle_3645 = _this;
  _this setDir -366.39554;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7921.231, 12669.913, 0.19976065];
};

_vehicle_3648 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7912.5381, 12668.906, -0.00010681152];


  _vehicle_3648 = _this;
  _this setDir -366.39554;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7912.5381, 12668.906, -0.00010681152];
};

_vehicle_3651 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7903.71, 12667.912, -1.5258789e-005];


  _vehicle_3651 = _this;
  _this setDir -366.39554;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7903.71, 12667.912, -1.5258789e-005];
};

_vehicle_3654 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7894.896, 12666.874, -3.0517578e-005];


  _vehicle_3654 = _this;
  _this setDir -366.39554;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7894.896, 12666.874, -3.0517578e-005];
};

_vehicle_3657 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7891.8975, 12673.918, 0.054757368];


  _vehicle_3657 = _this;
  _this setDir -281.36374;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7891.8975, 12673.918, 0.054757368];
};

_vehicle_3660 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7886.0503, 12680.339, 0.39403251];


  _vehicle_3660 = _this;
  _this setDir -317.40268;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7886.0503, 12680.339, 0.39403251];
};

_vehicle_3663 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7874.3066, 12689.733, 0.041069835];


  _vehicle_3663 = _this;
  _this setDir -317.40268;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7874.3066, 12689.733, 0.041069835];
};

_vehicle_3666 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7881.0425, 12683.956, -0.089835726];


  _vehicle_3666 = _this;
  _this setDir 32.931194;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7881.0425, 12683.956, -0.089835726];
};

_vehicle_3669 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7867.8188, 12695.954, 0.49989811];


  _vehicle_3669 = _this;
  _this setDir -315.96619;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7867.8188, 12695.954, 0.49989811];
};

_vehicle_3672 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7882.374, 12682.999, 0.32489157];


  _vehicle_3672 = _this;
  _this setDir 36.549068;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7882.374, 12682.999, 0.32489157];
};

_vehicle_3675 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [7515.3779, 12288.584, 0.00030517578];


  _vehicle_3675 = _this;
  _this setDir 83.419479;
  _this setPos [7515.3779, 12288.584, 0.00030517578];
};

_vehicle_3677 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [7518.5801, 12251.837, 0.0001373291];


  _vehicle_3677 = _this;
  _this setDir 95.108383;
  _this setPos [7518.5801, 12251.837, 0.0001373291];
};

_vehicle_3679 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_Stone" createVehicle [7519.666, 12259.382, 0.00019836426];


  _vehicle_3679 = _this;
  _this setDir 96.025909;
  _this setPos [7519.666, 12259.382, 0.00019836426];
};

_vehicle_3681 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7852.8643, 12698.323, 0.094097026];


  _vehicle_3681 = _this;
  _this setDir -376.17581;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7852.8643, 12698.323, 0.094097026];
};

_vehicle_3685 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7844.417, 12695.81, 0.032060124];


  _vehicle_3685 = _this;
  _this setDir -376.17581;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7844.417, 12695.81, 0.032060124];
};

_vehicle_3687 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7835.8374, 12693.281, -0.00010681152];


  _vehicle_3687 = _this;
  _this setDir -376.17581;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7835.8374, 12693.281, -0.00010681152];
};

_vehicle_3689 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7832.4136, 12685.776, 0.058873706];


  _vehicle_3689 = _this;
  _this setDir -433.74115;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7832.4136, 12685.776, 0.058873706];
};

_vehicle_3691 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7821.1914, 12670.242, -2.2888184e-005];


  _vehicle_3691 = _this;
  _this setDir -379.34442;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7821.1914, 12670.242, -2.2888184e-005];
};

_vehicle_3694 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7945.4258, 12619.851, 4.5776367e-005];


  _vehicle_3694 = _this;
  _this setDir 58.261475;
  _this setPos [7945.4258, 12619.851, 4.5776367e-005];
};

_vehicle_3696 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7945.9219, 12629.768, -7.6293945e-006];


  _vehicle_3696 = _this;
  _this setDir 58.261475;
  _this setPos [7945.9219, 12629.768, -7.6293945e-006];
};

_vehicle_3698 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7944.4424, 12604.818, 0.00017547607];


  _vehicle_3698 = _this;
  _this setDir 58.261475;
  _this setPos [7944.4424, 12604.818, 0.00017547607];
};

_vehicle_3700 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7988.0654, 12627.088, 0];


  _vehicle_3700 = _this;
  _this setDir 58.261475;
  _this setPos [7988.0654, 12627.088, 0];
};

_vehicle_3702 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_t_picea2s" createVehicle [7983.354, 12619.925, 1.5258789e-005];


  _vehicle_3702 = _this;
  _this setDir 58.261475;
  _this setPos [7983.354, 12619.925, 1.5258789e-005];
};

_vehicle_3705 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7828.7407, 12674.177, 0.11104791];


  _vehicle_3705 = _this;
  _this setDir -433.74115;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7828.7407, 12674.177, 0.11104791];
};

_vehicle_3708 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7812.8335, 12667.224, -3.8146973e-005];


  _vehicle_3708 = _this;
  _this setDir -379.34442;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7812.8335, 12667.224, -3.8146973e-005];
};

_vehicle_3711 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7804.4321, 12664.15, -3.0517578e-005];


  _vehicle_3711 = _this;
  _this setDir -379.34442;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7804.4321, 12664.15, -3.0517578e-005];
};

_vehicle_3714 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7795.9932, 12661.12];


  _vehicle_3714 = _this;
  _this setDir -379.34442;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7795.9932, 12661.12];
};

_vehicle_3717 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7785.1274, 12656.686, -1.5258789e-005];


  _vehicle_3717 = _this;
  _this setDir -379.34442;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7785.1274, 12656.686, -1.5258789e-005];
};

_vehicle_3721 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7831.3223, 12682.724, 0.27015206];


  _vehicle_3721 = _this;
  _this setDir 289.21323;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7831.3223, 12682.724, 0.27015206];
};

_vehicle_3723 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7793.4043, 12659.935, 0.11463118];


  _vehicle_3723 = _this;
  _this setDir 329.76712;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7793.4043, 12659.935, 0.11463118];
};

_vehicle_3726 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7774.0967, 12651.105, -7.6293945e-006];


  _vehicle_3726 = _this;
  _this setDir -390.63788;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7774.0967, 12651.105, -7.6293945e-006];
};

_vehicle_3729 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7780.5903, 12654.931, -1.5258789e-005];


  _vehicle_3729 = _this;
  _this setDir -28.487507;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7780.5903, 12654.931, -1.5258789e-005];
};

_vehicle_3732 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7763.8101, 12654.082, 7.6293945e-006];


  _vehicle_3732 = _this;
  _this setDir -298.59677;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7763.8101, 12654.082, 7.6293945e-006];
};

_vehicle_3735 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7770.1182, 12649.017, -4.5776367e-005];


  _vehicle_3735 = _this;
  _this setDir -28.487507;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7770.1182, 12649.017, -4.5776367e-005];
};

_vehicle_3737 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7767.4136, 12647.56, 6.1035156e-005];


  _vehicle_3737 = _this;
  _this setDir 58.466633;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7767.4136, 12647.56, 6.1035156e-005];
};

_vehicle_3740 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7759.436, 12661.885, 0.22985847];


  _vehicle_3740 = _this;
  _this setDir -298.59677;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7759.436, 12661.885, 0.22985847];
};

_vehicle_3743 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7740.521, 12694.79, 0.15102395];


  _vehicle_3743 = _this;
  _this setDir -298.59677;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7740.521, 12694.79, 0.15102395];
};

_vehicle_3748 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7749.7056, 12680.121, 0.397219];


  _vehicle_3748 = _this;
  _this setDir -298.59677;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7749.7056, 12680.121, 0.397219];
};

_vehicle_3750 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7754.0308, 12672.51, 0.1227911];


  _vehicle_3750 = _this;
  _this setDir -298.59677;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7754.0308, 12672.51, 0.1227911];
};

_vehicle_3753 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7860.0112, 12700.52, 0.26131272];


  _vehicle_3753 = _this;
  _this setDir -13.088957;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7860.0112, 12700.52, 0.26131272];
};

_vehicle_3755 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7864.3965, 12700.982, 0.32672706];


  _vehicle_3755 = _this;
  _this setDir 15.347591;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7864.3965, 12700.982, 0.32672706];
};

_vehicle_3758 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7757.7549, 12665.895, 0.16840039];


  _vehicle_3758 = _this;
  _this setDir 64.550453;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7757.7549, 12665.895, 0.16840039];
};

_vehicle_3761 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7746.624, 12684.626, 0.20611048];


  _vehicle_3761 = _this;
  _this setDir 55.571999;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7746.624, 12684.626, 0.20611048];
};

_vehicle_3764 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7744.2021, 12688.232, -6.8664551e-005];


  _vehicle_3764 = _this;
  _this setDir 64.550453;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7744.2021, 12688.232, -6.8664551e-005];
};

_vehicle_3767 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7733.5059, 12691.933, 0.12631439];


  _vehicle_3767 = _this;
  _this setDir -392.07358;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7733.5059, 12691.933, 0.12631439];
};

_vehicle_3770 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7725.9746, 12687.176, 0.078332059];


  _vehicle_3770 = _this;
  _this setDir -392.07358;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7725.9746, 12687.176, 0.078332059];
};

_vehicle_3773 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7718.3472, 12682.502];


  _vehicle_3773 = _this;
  _this setDir -390.39151;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7718.3472, 12682.502];
};

_vehicle_3776 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7710.5122, 12678.08, -0.088133812];


  _vehicle_3776 = _this;
  _this setDir -389.11398;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7710.5122, 12678.08, -0.088133812];
};

_vehicle_3785 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7684.541, 12663.372, 0.10646725];


  _vehicle_3785 = _this;
  _this setDir -389.11398;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7684.541, 12663.372, 0.10646725];
};

_vehicle_3788 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7692.313, 12667.729, 0.10397159];


  _vehicle_3788 = _this;
  _this setDir 329.76712;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7692.313, 12667.729, 0.10397159];
};

_vehicle_3791 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7686.4502, 12656.112, 0.10135774];


  _vehicle_3791 = _this;
  _this setDir -475.38388;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7686.4502, 12656.112, 0.10135774];
};

_vehicle_3795 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7690.2935, 12648.013, -0.12145831];


  _vehicle_3795 = _this;
  _this setDir -475.38388;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7690.2935, 12648.013, -0.12145831];
};

_vehicle_3798 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7695.791, 12636.961, 0.21814525];


  _vehicle_3798 = _this;
  _this setDir -478.97537;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7695.791, 12636.961, 0.21814525];
};

_vehicle_3801 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7700.3428, 12629.255, 1.5258789e-005];


  _vehicle_3801 = _this;
  _this setDir -478.97537;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7700.3428, 12629.255, 1.5258789e-005];
};

_vehicle_3803 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7704.6304, 12621.347, 6.8664551e-005];


  _vehicle_3803 = _this;
  _this setDir -478.97537;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7704.6304, 12621.347, 6.8664551e-005];
};

_vehicle_3805 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7708.9419, 12613.563, 7.6293945e-006];


  _vehicle_3805 = _this;
  _this setDir -478.97537;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7708.9419, 12613.563, 7.6293945e-006];
};

_vehicle_3808 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7692.1807, 12643.49, 0.23030418];


  _vehicle_3808 = _this;
  _this setDir -122.87712;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7692.1807, 12643.49, 0.23030418];
};

_vehicle_3811 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7704.8667, 12605.661, 7.6293945e-006];


  _vehicle_3811 = _this;
  _this setDir -392.88797;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7704.8667, 12605.661, 7.6293945e-006];
};

_vehicle_3814 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3_D" createVehicle [7710.3999, 12611.035, 7.6293945e-005];


  _vehicle_3814 = _this;
  _this setDir 238.61043;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7710.3999, 12611.035, 7.6293945e-005];
};

_vehicle_3817 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7700.7393, 12598.035, 7.6293945e-006];


  _vehicle_3817 = _this;
  _this setDir -426.58185;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7700.7393, 12598.035, 7.6293945e-006];
};

_vehicle_3821 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7697.2236, 12589.82, 3.8146973e-005];


  _vehicle_3821 = _this;
  _this setDir -426.58185;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7697.2236, 12589.82, 3.8146973e-005];
};

_vehicle_3823 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7693.6392, 12581.562, 0.060096473];


  _vehicle_3823 = _this;
  _this setDir -426.58185;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7693.6392, 12581.562, 0.060096473];
};

_vehicle_3825 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7690.1274, 12573.336, 0.13187456];


  _vehicle_3825 = _this;
  _this setDir -426.58185;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7690.1274, 12573.336, 0.13187456];
};

_vehicle_3828 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7682.3799, 12554.125, 0.10686911];


  _vehicle_3828 = _this;
  _this setDir -427.76764;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7682.3799, 12554.125, 0.10686911];
};

_vehicle_3831 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7685.7139, 12562.415, -7.6293945e-005];


  _vehicle_3831 = _this;
  _this setDir -427.76764;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7685.7139, 12562.415, -7.6293945e-005];
};

_vehicle_3834 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7678.2969, 12543.154, -0.096332438];


  _vehicle_3834 = _this;
  _this setDir -427.76764;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7678.2969, 12543.154, -0.096332438];
};

_vehicle_3838 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7675.0693, 12534.84, 4.5776367e-005];


  _vehicle_3838 = _this;
  _this setDir -427.76764;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7675.0693, 12534.84, 4.5776367e-005];
};

_vehicle_3841 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7671.7109, 12526.514, 0.25200054];


  _vehicle_3841 = _this;
  _this setDir -427.76764;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7671.7109, 12526.514, 0.25200054];
};

_vehicle_3844 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7667.4136, 12515.466, 0.012280177];


  _vehicle_3844 = _this;
  _this setDir -427.76764;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7667.4136, 12515.466, 0.012280177];
};

_vehicle_3847 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7664.1646, 12507.112, 0.29858291];


  _vehicle_3847 = _this;
  _this setDir -427.76764;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7664.1646, 12507.112, 0.29858291];
};

_vehicle_3850 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7657.8345, 12491.564, 0.25715676];


  _vehicle_3850 = _this;
  _this setDir -427.76764;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7657.8345, 12491.564, 0.25715676];
};

_vehicle_3853 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_9" createVehicle [7664.6504, 12488.631, 0.31909257];


  _vehicle_3853 = _this;
  _this setDir -527.44135;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7664.6504, 12488.631, 0.31909257];
};

_vehicle_3857 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7688.4268, 12569.386, 3.8146973e-005];


  _vehicle_3857 = _this;
  _this setDir -71.094231;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7688.4268, 12569.386, 3.8146973e-005];
};

_vehicle_3859 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7681.0703, 12550.113, -3.0517578e-005];


  _vehicle_3859 = _this;
  _this setDir -72.192223;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7681.0703, 12550.113, -3.0517578e-005];
};

_vehicle_3861 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7670.2686, 12522.403, 0.25116289];


  _vehicle_3861 = _this;
  _this setDir -74.26413;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7670.2686, 12522.403, 0.25116289];
};

_vehicle_3863 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7660.5239, 12498.464, 0.15817609];


  _vehicle_3863 = _this;
  _this setDir -70.399147;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7660.5239, 12498.464, 0.15817609];
};

_vehicle_3866 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7662.0376, 12501.934, 0.21309479];


  _vehicle_3866 = _this;
  _this setDir -70.399147;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7662.0376, 12501.934, 0.21309479];
};

_vehicle_3869 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7674.6816, 12486.638, 0.067260846];


  _vehicle_3869 = _this;
  _this setDir -171.59384;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7674.6816, 12486.638, 0.067260846];
};

_vehicle_3872 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7670.3384, 12487.492, 0.16047135];


  _vehicle_3872 = _this;
  _this setDir -172.66554;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7670.3384, 12487.492, 0.16047135];
};

_vehicle_3873 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_misc_amplion_conc" createVehicle [7842.2539, 12602.724, -9.9182129e-005];


  _vehicle_3873 = _this;
  _this setDir -283.87323;
  _this setPos [7842.2539, 12602.724, -9.9182129e-005];
};

_vehicle_3876 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldChair" createVehicle [7693.752, 12478.734, -0.024700247];


  _vehicle_3876 = _this;
  _this setDir 245.09908;
  _this setPos [7693.752, 12478.734, -0.024700247];
};

_vehicle_3877 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "SmallCraftTable_dz" createVehicle [7693.9414, 12479.914, 0.032504044];


  _vehicle_3877 = _this;
  _this setDir -88.115166;
  _this setPos [7693.9414, 12479.914, 0.032504044];
};

_vehicle_3882 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "FoldChair" createVehicle [7694.1172, 12481.166, -0.0073470501];


  _vehicle_3882 = _this;
  _this setDir 319.16434;
  _this setPos [7694.1172, 12481.166, -0.0073470501];
};

_vehicle_3887 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Concrete_Ramp" createVehicle [7718.6621, 12551.068, -2.9783118];


  _vehicle_3887 = _this;
  _this setDir 35.779377;
  _this setPos [7718.6621, 12551.068, -2.9783118];
};

_vehicle_3889 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7705.3091, 12675.212, 9.1552734e-005];


  _vehicle_3889 = _this;
  _this setDir -28.487507;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7705.3091, 12675.212, 9.1552734e-005];
};

_vehicle_3891 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7700.9819, 12672.795, -2.2888184e-005];


  _vehicle_3891 = _this;
  _this setDir -28.487507;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7700.9819, 12672.795, -2.2888184e-005];
};

_vehicle_3893 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [7693.9443, 12668.853, -9.1552734e-005];


  _vehicle_3893 = _this;
  _this setDir -28.487507;
  _this setVehicleInit "this setVectorUp surfaceNormal position this;";
  _this setPos [7693.9443, 12668.853, -9.1552734e-005];
};

_vehicle_3896 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndCnc_Pole" createVehicle [7699.8076, 12659.819, 5.3405762e-005];


  _vehicle_3896 = _this;
  _this setDir -33.315556;
  _this setPos [7699.8076, 12659.819, 5.3405762e-005];
};

