//This spawns in a military tent compound just north of the NW Airfield at 039 043.
//Created by Nox 2017-02-01. Contact: DayZ Europa, Dayz mod discord or by email: <EMAIL>
//Copyright by the DayZ Mod dev team. 

_vehicle_94 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_Cargo1Bo_military" createVehicle [3890.2683, 11011.416, 0.042650443];

  _vehicle_94 = _this;
  _this setDir -132.28661;
  _this setPos [3890.2683, 11011.416, 0.042650443];
};

_vehicle_118 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "HMMWVWreck" createVehicle [3926.7175, 10959.546, -3.0517578e-005];
 

  _vehicle_118 = _this;
  _this setDir -20.396053;
  _this setPos [3926.7175, 10959.546, -3.0517578e-005];
};

_vehicle_119 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Mi8Wreck" createVehicle [3952.6052, 11055.924];
 

  _vehicle_119 = _this;
  _this setDir 3.9880834;
  _this setPos [3952.6052, 11055.924];
};

_vehicle_122 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UralWreck" createVehicle [3889.4087, 11042.636, 3.0517578e-005];
 

  _vehicle_122 = _this;
  _this setDir -375.14511;
  _this setPos [3889.4087, 11042.636, 3.0517578e-005];
};

_vehicle_123 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "BMP2Wreck" createVehicle [3923.8643, 10978.193, -0.0064422311];
 

  _vehicle_123 = _this;
  _this setDir -49.264545;
  _this setPos [3923.8643, 10978.193, -0.0064422311];
};

_vehicle_127 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [3879.3914, 11035.235, 9.1552734e-005];
 

  _vehicle_127 = _this;
  _this setDir -225.91878;
  _this setPos [3879.3914, 11035.235, 9.1552734e-005];
};

_vehicle_134 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "datsun02Wreck" createVehicle [3930.4602, 10968.363, 9.1552734e-005];
 

  _vehicle_134 = _this;
  _this setDir -19.265348;
  _this setPos [3930.4602, 10968.363, 9.1552734e-005];
};

_vehicle_142 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Sign_Checkpoint_EP1" createVehicle [3926.8413, 10984.315];
 

  _vehicle_142 = _this;
  _this setPos [3926.8413, 10984.315];
};

_vehicle_158 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "AmmoCrates_NoInteractive_Large" createVehicle [3916.5845, 11006.377, -3.0517578e-005];
 

  _vehicle_158 = _this;
  _this setDir 133.20999;
  _this setPos [3916.5845, 11006.377, -3.0517578e-005];
};

_vehicle_159 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "AmmoCrates_NoInteractive_Medium" createVehicle [3965.4653, 11060.445];
 

  _vehicle_159 = _this;
  _this setPos [3965.4653, 11060.445];
};

_vehicle_160 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "AmmoCrates_NoInteractive_Small" createVehicle [3964.8689, 11061.323, -9.1552734e-005];
 

  _vehicle_160 = _this;
  _this setPos [3964.8689, 11061.323, -9.1552734e-005];
};

_vehicle_167 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "PowGen_Big" createVehicle [3945.8833, 11077.228];
 

  _vehicle_167 = _this;
  _this setDir 94.93306;
  _this setPos [3945.8833, 11077.228];
};

_vehicle_170 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Camp" createVehicle [3916.978, 11056.58, -3.0517578e-005];
 

  _vehicle_170 = _this;
  _this setDir 46.633144;
  _this setPos [3916.978, 11056.58, -3.0517578e-005];
};

_vehicle_174 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_Cargo1B_military" createVehicle [3892.7014, 11008.477, -0.019631863];
 

  _vehicle_174 = _this;
  _this setDir 48.62606;
  _this setPos [3892.7014, 11008.477, -0.019631863];
};

_vehicle_203 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Camp" createVehicle [3921.8413, 11051.56, -3.0517578e-005];
 

  _vehicle_203 = _this;
  _this setDir 46.633144;
  _this setPos [3921.8413, 11051.56, -3.0517578e-005];
};

_vehicle_205 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Camp" createVehicle [3926.9348, 11045.705];
 

  _vehicle_205 = _this;
  _this setDir 46.633144;
  _this setPos [3926.9348, 11045.705];
};

_vehicle_210 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Camp" createVehicle [3930.9038, 11058.846, 3.0517578e-005];
 

  _vehicle_210 = _this;
  _this setDir 230.97359;
  _this setPos [3930.9038, 11058.846, 3.0517578e-005];
};

_vehicle_212 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Camp" createVehicle [3926.1023, 11064.544];
 

  _vehicle_212 = _this;
  _this setDir 230.97359;
  _this setPos [3926.1023, 11064.544];
};

_vehicle_217 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Toilet" createVehicle [3909.1519, 11049.229, 3.0517578e-005];
 

  _vehicle_217 = _this;
  _this setDir 407.06842;
  _this setPos [3909.1519, 11049.229, 3.0517578e-005];
};

_vehicle_231 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_radar_EP1" createVehicle [3934.9553, 11235.147];
 

  _vehicle_231 = _this;
  _this setDir 159.35098;
  _this setPos [3934.9553, 11235.147];
};

_vehicle_236 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_TankSmall" createVehicle [3879.323, 11041.94, -6.1035156e-005];
 

  _vehicle_236 = _this;
  _this setDir -44.442314;
  _this setPos [3879.323, 11041.94, -6.1035156e-005];
};

_vehicle_238 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_TankSmall2" createVehicle [3961.1497, 11067.28, -3.0517578e-005];
 

  _vehicle_238 = _this;
  _this setDir 48.722527;
  _this setPos [3961.1497, 11067.28, -3.0517578e-005];
};

_vehicle_247 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Fort_Watchtower" createVehicle [3919.3335, 11004.488, 0.021191061];
 

  _vehicle_247 = _this;
  _this setDir -44.249626;
  _this setPos [3919.3335, 11004.488, 0.021191061];
};

_vehicle_257 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier3" createVehicle [3890.3984, 11002.224, -3.0517578e-005];
 

  _vehicle_257 = _this;
  _this setDir -40.811821;
  _this setPos [3890.3984, 11002.224, -3.0517578e-005];
};

_vehicle_258 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3934.1211, 11023.296];
 

  _vehicle_258 = _this;
  _this setDir 107.08715;
  _this setPos [3934.1211, 11023.296];
};

_vehicle_266 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "ZavoraAnim" createVehicle [3933.1504, 10995.512, 3.0517578e-005];
 

  _vehicle_266 = _this;
  _this setDir 360.07468;
  _this setPos [3933.1504, 10995.512, 3.0517578e-005];
};

_vehicle_273 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3960.4856, 11047.874];
 

  _vehicle_273 = _this;
  _this setDir -40.935375;
  _this setPos [3960.4856, 11047.874];
};

_vehicle_290 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3" createVehicle [3934.2615, 11011.859, 6.1035156e-005];
 

  _vehicle_290 = _this;
  _this setDir -33.592312;
  _this setPos [3934.2615, 11011.859, 6.1035156e-005];
};

_vehicle_297 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_3" createVehicle [3923.3259, 11002.253, -3.0517578e-005];
 

  _vehicle_297 = _this;
  _this setDir -47.739304;
  _this setPos [3923.3259, 11002.253, -3.0517578e-005];
};

_vehicle_302 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Wall_IndFnc_Pole" createVehicle [3924.2529, 11003.306];
 

  _vehicle_302 = _this;
  _this setDir -47.435883;
  _this setPos [3924.2529, 11003.306];
};

_vehicle_304 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Toilet" createVehicle [3911.8259, 11046.045, -3.0517578e-005];
 

  _vehicle_304 = _this;
  _this setDir 410.85168;
  _this setPos [3911.8259, 11046.045, -3.0517578e-005];
};

_vehicle_306 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Toilet" createVehicle [3910.3691, 11047.762];
 

  _vehicle_306 = _this;
  _this setDir 407.35742;
  _this setPos [3910.3691, 11047.762];
};

_vehicle_315 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Ind_TankSmall" createVehicle [3955.8899, 11072.895, 3.0517578e-005];
 

  _vehicle_315 = _this;
  _this setDir 48.479504;
  _this setPos [3955.8899, 11072.895, 3.0517578e-005];
};

_vehicle_318 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3896.4202, 11007.482, 0.00012207031];
 

  _vehicle_318 = _this;
  _this setDir -221.52559;
  _this setPos [3896.4202, 11007.482, 0.00012207031];
};

_vehicle_320 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3897.2339, 11008.399, 3.0517578e-005];
 

  _vehicle_320 = _this;
  _this setDir -42.035709;
  _this setPos [3897.2339, 11008.399, 3.0517578e-005];
};

_vehicle_322 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3889.6357, 11000.879, -3.0517578e-005];
 

  _vehicle_322 = _this;
  _this setDir -130.7234;
  _this setPos [3889.6357, 11000.879, -3.0517578e-005];
};

_vehicle_324 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3885.7905, 11005.141, 0.00021362305];
 

  _vehicle_324 = _this;
  _this setDir -132.96863;
  _this setPos [3885.7905, 11005.141, 0.00021362305];
};

_vehicle_329 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3908.5933, 11011.088, 3.0517578e-005];
 

  _vehicle_329 = _this;
  _this setDir -139.14868;
  _this setPos [3908.5933, 11011.088, 3.0517578e-005];
};

_vehicle_331 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3909.1624, 11009.737, 0.00021362305];
 

  _vehicle_331 = _this;
  _this setDir -321.31256;
  _this setPos [3909.1624, 11009.737, 0.00021362305];
};

_vehicle_334 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3933.5264, 11011.874, -6.1035156e-005];
 

  _vehicle_334 = _this;
  _this setDir -88.364952;
  _this setPos [3933.5264, 11011.874, -6.1035156e-005];
};

_vehicle_336 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3933.6663, 11017.552, 3.0517578e-005];
 

  _vehicle_336 = _this;
  _this setDir -87.355087;
  _this setPos [3933.6663, 11017.552, 3.0517578e-005];
};

_vehicle_338 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3938.293, 11027.42, -3.0517578e-005];
 

  _vehicle_338 = _this;
  _this setDir -220.63132;
  _this setPos [3938.293, 11027.42, -3.0517578e-005];
};

_vehicle_340 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3939.1626, 11028.37];
 

  _vehicle_340 = _this;
  _this setDir -43.761044;
  _this setPos [3939.1626, 11028.37];
};

_vehicle_343 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3943.4441, 11032.145, -3.0517578e-005];
 

  _vehicle_343 = _this;
  _this setDir -42.807655;
  _this setPos [3943.4441, 11032.145, -3.0517578e-005];
};

_vehicle_345 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3948.6501, 11036.696];
 

  _vehicle_345 = _this;
  _this setDir -40.709114;
  _this setPos [3948.6501, 11036.696];
};

_vehicle_350 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3955.1833, 11042.4, 6.1035156e-005];
 

  _vehicle_350 = _this;
  _this setDir -39.870502;
  _this setPos [3955.1833, 11042.4, 6.1035156e-005];
};

_vehicle_352 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3964.7356, 11051.678];
 

  _vehicle_352 = _this;
  _this setDir -41.617138;
  _this setPos [3964.7356, 11051.678];
};

_vehicle_403 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Heli_H_cross" createVehicle [3953.8809, 11055.072, 6.1035156e-005];
 

  _vehicle_403 = _this;
  _this setPos [3953.8809, 11055.072, 6.1035156e-005];
};

_vehicle_410 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Heli_H_cross" createVehicle [3940.5549, 11071.127, -6.1035156e-005];
 

  _vehicle_410 = _this;
  _this setPos [3940.5549, 11071.127, -6.1035156e-005];
};

_vehicle_413 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3969.5073, 11056.625, 3.0517578e-005];
 

  _vehicle_413 = _this;
  _this setDir -132.29822;
  _this setPos [3969.5073, 11056.625, 3.0517578e-005];
};

_vehicle_420 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3967.3645, 11061.326];
 

  _vehicle_420 = _this;
  _this setDir -131.6759;
  _this setPos [3967.3645, 11061.326];
};

_vehicle_423 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3969.0088, 11055.492, 3.0517578e-005];
 

  _vehicle_423 = _this;
  _this setDir 136.46523;
  _this setPos [3969.0088, 11055.492, 3.0517578e-005];
};

_vehicle_426 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3969.9678, 11056.365, 3.0517578e-005];
 

  _vehicle_426 = _this;
  _this setDir 136.46523;
  _this setPos [3969.9678, 11056.365, 3.0517578e-005];
};

_vehicle_431 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3961.585, 11070.019, 3.0517578e-005];
 

  _vehicle_431 = _this;
  _this setDir -132.29822;
  _this setPos [3961.585, 11070.019, 3.0517578e-005];
};

_vehicle_433 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3953.4353, 11076.45, -3.0517578e-005];
 

  _vehicle_433 = _this;
  _this setDir -132.29822;
  _this setPos [3953.4353, 11076.45, -3.0517578e-005];
};

_vehicle_446 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3965.4937, 11065.751];
 

  _vehicle_446 = _this;
  _this setDir -132.29822;
  _this setPos [3965.4937, 11065.751];
};

_vehicle_449 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3957.6367, 11074.252, -3.0517578e-005];
 

  _vehicle_449 = _this;
  _this setDir -132.29822;
  _this setPos [3957.6367, 11074.252, -3.0517578e-005];
};

_vehicle_455 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Camp" createVehicle [3935.4446, 11053.462, -3.0517578e-005];
 

  _vehicle_455 = _this;
  _this setDir 230.97359;
  _this setPos [3935.4446, 11053.462, -3.0517578e-005];
};

_vehicle_458 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3949.5503, 11080.751];
 

  _vehicle_458 = _this;
  _this setDir -132.29822;
  _this setPos [3949.5503, 11080.751];
};

_vehicle_463 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3941.6497, 11087.372, 3.0517578e-005];
 

  _vehicle_463 = _this;
  _this setDir -223.29887;
  _this setPos [3941.6497, 11087.372, 3.0517578e-005];
};

_vehicle_466 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3931.9006, 11011.67, 3.0517578e-005];
 

  _vehicle_466 = _this;
  _this setDir 82.981972;
  _this setPos [3931.9006, 11011.67, 3.0517578e-005];
};

_vehicle_478 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3937.5693, 11083.276, -9.1552734e-005];
 

  _vehicle_478 = _this;
  _this setDir -223.29887;
  _this setPos [3937.5693, 11083.276, -9.1552734e-005];
};

_vehicle_482 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3933.3994, 11079.257, -9.1552734e-005];
 

  _vehicle_482 = _this;
  _this setDir -223.29887;
  _this setPos [3933.3994, 11079.257, -9.1552734e-005];
};

_vehicle_484 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3928.6194, 11074.534, 9.1552734e-005];
 

  _vehicle_484 = _this;
  _this setDir -223.29887;
  _this setPos [3928.6194, 11074.534, 9.1552734e-005];
};

_vehicle_489 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Hlidac_budka" createVehicle [3925.0127, 10988.146];
 

  _vehicle_489 = _this;
  _this setDir 630.42322;
  _this setPos [3925.0127, 10988.146];
};

_vehicle_519 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_fort_bagfence_corner" createVehicle [3924.0623, 10985.124, -3.0517578e-005];
 

  _vehicle_519 = _this;
  _this setDir 2.2258055;
  _this setPos [3924.0623, 10985.124, -3.0517578e-005];
};

_vehicle_533 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_CncBlock" createVehicle [3929.6238, 10972.504, 0.0028391904];
 

  _vehicle_533 = _this;
  _this setDir 1.5170435;
  _this setPos [3929.6238, 10972.504, 0.0028391904];
};

_vehicle_535 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_CncBlock" createVehicle [3926.7927, 10972.521];
 

  _vehicle_535 = _this;
  _this setDir 0.96982956;
  _this setPos [3926.7927, 10972.521];
};

_vehicle_538 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3943.1277, 11087.921];
 

  _vehicle_538 = _this;
  _this setDir 160.80922;
  _this setPos [3943.1277, 11087.921];
};

_vehicle_540 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3945.6211, 11085.415];
 

  _vehicle_540 = _this;
  _this setDir 222.69772;
  _this setPos [3945.6211, 11085.415];
};

_vehicle_543 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3881.3391, 11010.534, -3.0517578e-005];
 

  _vehicle_543 = _this;
  _this setDir -128.03677;
  _this setPos [3881.3391, 11010.534, -3.0517578e-005];
};

_vehicle_545 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3877.7144, 11014.983, 6.1035156e-005];
 

  _vehicle_545 = _this;
  _this setDir -128.03677;
  _this setPos [3877.7144, 11014.983, 6.1035156e-005];
};

_vehicle_548 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_Cargo1B_military" createVehicle [3888.2156, 11014.706, 6.1035156e-005];
 

  _vehicle_548 = _this;
  _this setDir 48.268764;
  _this setPos [3888.2156, 11014.706, 6.1035156e-005];
};

_vehicle_550 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_Cargo1B_military" createVehicle [3886.4006, 11016.949, 6.1035156e-005];
 

  _vehicle_550 = _this;
  _this setDir 43.230724;
  _this setPos [3886.4006, 11016.949, 6.1035156e-005];
};

_vehicle_552 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Misc_Cargo1B_military" createVehicle [3888.1787, 11014.727, 2.4500997];
 

  _vehicle_552 = _this;
  _this setDir 228.59882;
  _this setPos [3888.1787, 11014.727, 2.4500997];
};

_vehicle_616 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_misc_amplion_wood" createVehicle [3892.6082, 11033.824, -3.0517578e-005];
 

  _vehicle_616 = _this;
  _this setDir -166.00587;
  _this setPos [3892.6082, 11033.824, -3.0517578e-005];
};

_vehicle_645 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_drevena_bedna" createVehicle [3891.854, 11012.223];
 

  _vehicle_645 = _this;
  _this setDir 141.02856;
  _this setPos [3891.854, 11012.223];
};

_vehicle_647 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3945.6348, 11088.727];
 

  _vehicle_647 = _this;
  _this setDir 231.51234;
  _this setPos [3945.6348, 11088.727];
};

_vehicle_653 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3953.1792, 11040.681, 6.1035156e-005];
 

  _vehicle_653 = _this;
  _this setDir 149.50349;
  _this setPos [3953.1792, 11040.681, 6.1035156e-005];
};

_vehicle_656 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3924.3425, 11070.637, -3.0517578e-005];
 

  _vehicle_656 = _this;
  _this setDir -220.3396;
  _this setPos [3924.3425, 11070.637, -3.0517578e-005];
};

_vehicle_659 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3920.0825, 11066.521, 3.0517578e-005];
 

  _vehicle_659 = _this;
  _this setDir -224.30481;
  _this setPos [3920.0825, 11066.521, 3.0517578e-005];
};

_vehicle_662 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3915.7876, 11062.602, -6.1035156e-005];
 

  _vehicle_662 = _this;
  _this setDir 129.68227;
  _this setPos [3915.7876, 11062.602, -6.1035156e-005];
};

_vehicle_665 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3913.2483, 11062.121, -6.1035156e-005];
 

  _vehicle_665 = _this;
  _this setDir 161.49112;
  _this setPos [3913.2483, 11062.121, -6.1035156e-005];
};

_vehicle_668 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3912.9346, 11060.215];
 

  _vehicle_668 = _this;
  _this setDir 127.98961;
  _this setPos [3912.9346, 11060.215];
};

_vehicle_671 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3912.1348, 11059.161, 6.1035156e-005];
 

  _vehicle_671 = _this;
  _this setDir -224.30481;
  _this setPos [3912.1348, 11059.161, 6.1035156e-005];
};

_vehicle_674 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3908.2458, 11055.146, -6.1035156e-005];
 

  _vehicle_674 = _this;
  _this setDir -224.30481;
  _this setPos [3908.2458, 11055.146, -6.1035156e-005];
};

_vehicle_677 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3903.885, 11051.306, 3.0517578e-005];
 

  _vehicle_677 = _this;
  _this setDir -222.24062;
  _this setPos [3903.885, 11051.306, 3.0517578e-005];
};

_vehicle_682 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3895.4761, 11045.241, 3.0517578e-005];
 

  _vehicle_682 = _this;
  _this setDir -135.36388;
  _this setPos [3895.4761, 11045.241, 3.0517578e-005];
};

_vehicle_685 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3891.5979, 11049.498, 3.0517578e-005];
 

  _vehicle_685 = _this;
  _this setDir -135.36388;
  _this setPos [3891.5979, 11049.498, 3.0517578e-005];
};

_vehicle_688 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3874.1519, 11019.541, 0.00012207031];
 

  _vehicle_688 = _this;
  _this setDir -128.03677;
  _this setPos [3874.1519, 11019.541, 0.00012207031];
};

_vehicle_690 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3870.7314, 11024.147, -6.1035156e-005];
 

  _vehicle_690 = _this;
  _this setDir -128.03677;
  _this setPos [3870.7314, 11024.147, -6.1035156e-005];
};

_vehicle_693 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3887.5137, 11053.805];
 

  _vehicle_693 = _this;
  _this setDir -225.6996;
  _this setPos [3887.5137, 11053.805];
};

_vehicle_695 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3883.4102, 11049.721, -3.0517578e-005];
 

  _vehicle_695 = _this;
  _this setDir -225.6996;
  _this setPos [3883.4102, 11049.721, -3.0517578e-005];
};

_vehicle_697 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3868.0457, 11033.869, 6.1035156e-005];
 

  _vehicle_697 = _this;
  _this setDir -228.25618;
  _this setPos [3868.0457, 11033.869, 6.1035156e-005];
};

_vehicle_700 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3867.0417, 11028.646];
 

  _vehicle_700 = _this;
  _this setDir 65.725311;
  _this setPos [3867.0417, 11028.646];
};

_vehicle_702 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3866.3826, 11029.756, 6.1035156e-005];
 

  _vehicle_702 = _this;
  _this setDir 50.645046;
  _this setPos [3866.3826, 11029.756, 6.1035156e-005];
};

_vehicle_705 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3872.2158, 11037.831, 6.1035156e-005];
 

  _vehicle_705 = _this;
  _this setDir -220.49664;
  _this setPos [3872.2158, 11037.831, 6.1035156e-005];
};

_vehicle_708 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3873.178, 11038.983, 6.1035156e-005];
 

  _vehicle_708 = _this;
  _this setDir -57.472492;
  _this setPos [3873.178, 11038.983, 6.1035156e-005];
};

_vehicle_714 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_tent_east" createVehicle [3940.1069, 11040.798];
 

  _vehicle_714 = _this;
  _this setDir -133.63884;
  _this setPos [3940.1069, 11040.798];
};

_vehicle_728 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3882.0564, 11009.451, 0.00018310547];
 

  _vehicle_728 = _this;
  _this setDir -129.04329;
  _this setPos [3882.0564, 11009.451, 0.00018310547];
};

_vehicle_731 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_tent_east" createVehicle [3930.6797, 11031.964, 0.00012207031];
 

  _vehicle_731 = _this;
  _this setDir -222.19072;
  _this setPos [3930.6797, 11031.964, 0.00012207031];
};

_vehicle_734 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UAZWreck" createVehicle [3882.624, 11037.957, -6.1035156e-005];
 

  _vehicle_734 = _this;
  _this setDir -385.28848;
  _this setPos [3882.624, 11037.957, -6.1035156e-005];
};

_vehicle_740 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Fort_Watchtower" createVehicle [3872.2214, 11029.103, -3.0517578e-005];
 

  _vehicle_740 = _this;
  _this setDir -39.170345;
  _this setPos [3872.2214, 11029.103, -3.0517578e-005];
};

_vehicle_743 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3879.29, 11045.612, 0.00015258789];
 

  _vehicle_743 = _this;
  _this setDir -225.6996;
  _this setPos [3879.29, 11045.612, 0.00015258789];
};

_vehicle_747 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "USMC_WarfareBFieldhHospital" createVehicle [3894.4197, 11021.018, -0.10382144];
 

  _vehicle_747 = _this;
  _this setDir 135.35919;
  _this setPos [3894.4197, 11021.018, -0.10382144];
};

_vehicle_749 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UralWreck" createVehicle [3879.6946, 11027.001, 0.00030517578];
 

  _vehicle_749 = _this;
  _this setDir -473.62155;
  _this setPos [3879.6946, 11027.001, 0.00030517578];
};

_vehicle_753 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Toilet" createVehicle [3914.9788, 11047.928, 3.0517578e-005];
 

  _vehicle_753 = _this;
  _this setDir 233.04257;
  _this setPos [3914.9788, 11047.928, 3.0517578e-005];
};

_vehicle_755 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Toilet" createVehicle [3913.2917, 11049.845, 0.00015258789];
 

  _vehicle_755 = _this;
  _this setDir 223.1655;
  _this setPos [3913.2917, 11049.845, 0.00015258789];
};

_vehicle_757 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Toilet" createVehicle [3911.7246, 11051.477, 0.00021362305];
 

  _vehicle_757 = _this;
  _this setDir 560.56427;
  _this setPos [3911.7246, 11051.477, 0.00021362305];
};

_vehicle_760 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3875.0376, 11041.893, 3.0517578e-005];
 

  _vehicle_760 = _this;
  _this setDir -41.082367;
  _this setPos [3875.0376, 11041.893, 3.0517578e-005];
};

_vehicle_762 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3872.1035, 11041.907, 3.0517578e-005];
 

  _vehicle_762 = _this;
  _this setDir -57.472492;
  _this setPos [3872.1035, 11041.907, 3.0517578e-005];
};

_vehicle_766 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "RU_WarfareBVehicleServicePoint" createVehicle [3876.4482, 11020.225];
 

  _vehicle_766 = _this;
  _this setDir 410.29111;
  _this setPos [3876.4482, 11020.225];
};

_vehicle_779 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier5" createVehicle [3899.6135, 11047.374];
 

  _vehicle_779 = _this;
  _this setDir -224.26852;
  _this setPos [3899.6135, 11047.374];
};

_vehicle_782 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "hiluxWreck" createVehicle [3928.6189, 11000.549, -3.0517578e-005];
 

  _vehicle_782 = _this;
  _this setDir -161.06534;
  _this setPos [3928.6189, 11000.549, -3.0517578e-005];
};

_vehicle_784 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "UralWreck" createVehicle [3937.8904, 11061.807, 3.0517578e-005];
 

  _vehicle_784 = _this;
  _this setDir -389.63504;
  _this setPos [3937.8904, 11061.807, 3.0517578e-005];
};

_vehicle_790 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "BMP2Wreck" createVehicle [3916.8054, 11012.497];
 

  _vehicle_790 = _this;
  _this setDir -99.934082;
  _this setPos [3916.8054, 11012.497];
};

_vehicle_791 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "AmmoCrates_NoInteractive_Large" createVehicle [3914.7695, 11045.874];
 

  _vehicle_791 = _this;
  _this setDir -177.98792;
  _this setPos [3914.7695, 11045.874];
};

_vehicle_792 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "AmmoCrates_NoInteractive_Medium" createVehicle [3913.4922, 11045.173, -6.1035156e-005];
 

  _vehicle_792 = _this;
  _this setDir -44.77565;
  _this setPos [3913.4922, 11045.173, -6.1035156e-005];
};

_vehicle_795 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CamoNetVar_EAST" createVehicle [3914.1277, 11048.075, 0.16966489];
 

  _vehicle_795 = _this;
  _this setDir -131.92813;
  _this setPos [3914.1277, 11048.075, 0.16966489];
};

_vehicle_804 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barbedwire" createVehicle [3934.5552, 11001.538];
 

  _vehicle_804 = _this;
  _this setDir 89.441734;
  _this setPos [3934.5552, 11001.538];
};

_vehicle_810 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barbedwire" createVehicle [3933.9561, 11007.279, -3.0517578e-005];
 

  _vehicle_810 = _this;
  _this setDir 89.441734;
  _this setPos [3933.9561, 11007.279, -3.0517578e-005];
};

_vehicle_813 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barbedwire" createVehicle [3924.6062, 10994.094, 6.1035156e-005];
 

  _vehicle_813 = _this;
  _this setDir 89.441734;
  _this setPos [3924.6062, 10994.094, 6.1035156e-005];
};

_vehicle_816 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barbedwire" createVehicle [3924.5537, 10999.546];
 

  _vehicle_816 = _this;
  _this setDir 89.441734;
  _this setPos [3924.5537, 10999.546];
};

_vehicle_823 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_paleta" createVehicle [3899.5264, 11025.557, 3.0517578e-005];
 

  _vehicle_823 = _this;
  _this setPos [3899.5264, 11025.557, 3.0517578e-005];
};

_vehicle_824 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_ground_garbage_long" createVehicle [3915.1479, 11028.792, -6.1035156e-005];
 

  _vehicle_824 = _this;
  _this setPos [3915.1479, 11028.792, -6.1035156e-005];
};

_vehicle_825 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_ground_garbage_square3" createVehicle [3918.9116, 11026.861, -3.0517578e-005];
 

  _vehicle_825 = _this;
  _this setPos [3918.9116, 11026.861, -3.0517578e-005];
};

_vehicle_826 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_ground_garbage_square5" createVehicle [3919.5437, 11030.021, 0];
 

  _vehicle_826 = _this;
  _this setPos [3919.5437, 11030.021, 0];
};

_vehicle_828 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_paleta" createVehicle [3914.9065, 11044.02, -3.0517578e-005];
 

  _vehicle_828 = _this;
  _this setPos [3914.9065, 11044.02, -3.0517578e-005];
};

_vehicle_830 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_garbage_paleta" createVehicle [3950.6409, 11075.237, -3.0517578e-005];
 

  _vehicle_830 = _this;
  _this setPos [3950.6409, 11075.237, -3.0517578e-005];
};

_vehicle_836 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3904.0269, 11014.88, -3.0517578e-005];
 

  _vehicle_836 = _this;
  _this setDir 6.75213;
  _this setPos [3904.0269, 11014.88, -3.0517578e-005];
};

_vehicle_838 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3901.2493, 11012.736, -3.0517578e-005];
 

  _vehicle_838 = _this;
  _this setDir -43.10437;
  _this setPos [3901.2493, 11012.736, -3.0517578e-005];
};

_vehicle_841 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3900.8831, 11014.613];
 

  _vehicle_841 = _this;
  _this setDir -65.760361;
  _this setPos [3900.8831, 11014.613];
};

_vehicle_848 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_canina2s" createVehicle [3933.5659, 11055.147];
 

  _vehicle_848 = _this;
  _this setPos [3933.5659, 11055.147];
};

_vehicle_849 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus" createVehicle [3935.9395, 11079.084, -3.0517578e-005];
 

  _vehicle_849 = _this;
  _this setPos [3935.9395, 11079.084, -3.0517578e-005];
};

_vehicle_850 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus2s" createVehicle [3910.7593, 11061.272, -6.1035156e-005];
 

  _vehicle_850 = _this;
  _this setPos [3910.7593, 11061.272, -6.1035156e-005];
};

_vehicle_851 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [3918.2095, 11052.664];
 

  _vehicle_851 = _this;
  _this setPos [3918.2095, 11052.664];
};

_vehicle_853 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [3911.4248, 11049.113];
 

  _vehicle_853 = _this;
  _this setPos [3911.4248, 11049.113];
};

_vehicle_856 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [3896.8748, 11025.564];
 

  _vehicle_856 = _this;
  _this setDir -47.956081;
  _this setPos [3896.8748, 11025.564];
};

_vehicle_858 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [3872.6326, 11036.002, -6.1035156e-005];
 

  _vehicle_858 = _this;
  _this setDir -33.796577;
  _this setPos [3872.6326, 11036.002, -6.1035156e-005];
};

_vehicle_861 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus2s" createVehicle [3944.1904, 11090.22];
 

  _vehicle_861 = _this;
  _this setPos [3944.1904, 11090.22];
};

_vehicle_869 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_ruiny_kopa_1Tv" createVehicle [3917.5198, 11043.328, 0.00018310547];
 

  _vehicle_869 = _this;
  _this setPos [3917.5198, 11043.328, 0.00018310547];
};

_vehicle_871 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_ruiny_kopa_1Tv" createVehicle [3963.9709, 11055.825, 0.00018310547];
 

  _vehicle_871 = _this;
  _this setPos [3963.9709, 11055.825, 0.00018310547];
};

_vehicle_873 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_ruiny_kopa_1Tv" createVehicle [3930.6887, 11014.858, 0.00012207031];
 

  _vehicle_873 = _this;
  _this setPos [3930.6887, 11014.858, 0.00012207031];
};

_vehicle_880 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barel7" createVehicle [3883.4114, 11044.823];
 

  _vehicle_880 = _this;
  _this setPos [3883.4114, 11044.823];
};

_vehicle_890 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barels2" createVehicle [3881.4788, 11045.384, 9.1552734e-005];
 

  _vehicle_890 = _this;
  _this setDir 110.23277;
  _this setPos [3881.4788, 11045.384, 9.1552734e-005];
};

_vehicle_891 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barels3" createVehicle [3958.5818, 11070.507];
 

  _vehicle_891 = _this;
  _this setDir 140.82567;
  _this setPos [3958.5818, 11070.507];
};

_vehicle_893 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barel7" createVehicle [3948.1599, 11074.986, 0.00015258789];
 

  _vehicle_893 = _this;
  _this setDir -63.921005;
  _this setPos [3948.1599, 11074.986, 0.00015258789];
};

_vehicle_895 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barel7" createVehicle [3946.729, 11075.162, 6.1035156e-005];
 

  _vehicle_895 = _this;
  _this setPos [3946.729, 11075.162, 6.1035156e-005];
};

_vehicle_897 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barel7" createVehicle [3947.1885, 11074.5, 0.00012207031];
 

  _vehicle_897 = _this;
  _this setDir -156.50668;
  _this setPos [3947.1885, 11074.5, 0.00012207031];
};

_vehicle_899 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barel7" createVehicle [3957.5813, 11068.795, 3.0517578e-005];
 

  _vehicle_899 = _this;
  _this setDir 80.968987;
  _this setPos [3957.5813, 11068.795, 3.0517578e-005];
};

_vehicle_901 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barel7" createVehicle [3956.8694, 11069.556, 0.0002746582];
 

  _vehicle_901 = _this;
  _this setPos [3956.8694, 11069.556, 0.0002746582];
};

_vehicle_904 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barels3" createVehicle [3958.6672, 11070.562, 1.0090218];
 

  _vehicle_904 = _this;
  _this setDir 140.82567;
  _this setPos [3958.6672, 11070.562, 1.0090218];
};

_vehicle_907 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barel7" createVehicle [3963.0327, 11064.512, 3.0517578e-005];
 

  _vehicle_907 = _this;
  _this setDir -18.778915;
  _this setPos [3963.0327, 11064.512, 3.0517578e-005];
};

_vehicle_910 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3924.8154, 11004.533, -3.0517578e-005];
 

  _vehicle_910 = _this;
  _this setDir 117.72036;
  _this setPos [3924.8154, 11004.533, -3.0517578e-005];
};

_vehicle_912 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3935.0447, 10998.551, -3.0517578e-005];
 

  _vehicle_912 = _this;
  _this setDir 82.981972;
  _this setPos [3935.0447, 10998.551, -3.0517578e-005];
};

_vehicle_914 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3933.8813, 10997.015];
 

  _vehicle_914 = _this;
  _this setDir -10.706585;
  _this setPos [3933.8813, 10997.015];
};

_vehicle_917 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3925.9829, 10992.259, 3.0517578e-005];
 

  _vehicle_917 = _this;
  _this setDir -10.706585;
  _this setPos [3925.9829, 10992.259, 3.0517578e-005];
};

_vehicle_919 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_HBarrier1" createVehicle [3935.3247, 10996.939, 9.1552734e-005];
 

  _vehicle_919 = _this;
  _this setDir -9.7976151;
  _this setPos [3935.3247, 10996.939, 9.1552734e-005];
};

_vehicle_921 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "RU_WarfareBFieldhHospital" createVehicle [3905.1602, 11038.551, -0.1152747];
 

  _vehicle_921 = _this;
  _this setDir -46.321651;
  _this setPos [3905.1602, 11038.551, -0.1152747];
};

_vehicle_923 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "RU_WarfareBFieldhHospital" createVehicle [3912.6558, 11031.218, -0.11383289];
 

  _vehicle_923 = _this;
  _this setDir -45.612534;
  _this setPos [3912.6558, 11031.218, -0.11383289];
};

_vehicle_926 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_tent_east" createVehicle [3922.7493, 11023.584, 0.10214803];
 

  _vehicle_926 = _this;
  _this setDir -317.09241;
  _this setPos [3922.7493, 11023.584, 0.10214803];
};

_vehicle_928 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_misc_amplion_wood" createVehicle [3900.8169, 11017.997, 3.0517578e-005];
 

  _vehicle_928 = _this;
  _this setDir -408.19693;
  _this setPos [3900.8169, 11017.997, 3.0517578e-005];
};

_vehicle_931 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [3915.2917, 11041.988];
 

  _vehicle_931 = _this;
  _this setPos [3915.2917, 11041.988];
};

_vehicle_934 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CamoNetVar_EAST" createVehicle [3911.7053, 11019.861];
 

  _vehicle_934 = _this;
  _this setDir -194.55721;
  _this setPos [3911.7053, 11019.861];
};

_vehicle_936 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowGen_Big" createVehicle [3930.0867, 11235.203, -3.0517578e-005];
 

  _vehicle_936 = _this;
  _this setDir -149.44051;
  _this setPos [3930.0867, 11235.203, -3.0517578e-005];
};

_vehicle_941 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_hromada_beden_dekorativniX" createVehicle [3926.1436, 11054.496, 3.0517578e-005];
 

  _vehicle_941 = _this;
  _this setDir -394.20468;
  _this setPos [3926.1436, 11054.496, 3.0517578e-005];
};

_vehicle_943 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_LocalBasicWeapons" createVehicle [3925.1895, 11056.307, 3.0517578e-005];
 

  _vehicle_943 = _this;
  _this setDir -36.406075;
  _this setPos [3925.1895, 11056.307, 3.0517578e-005];
};

_vehicle_952 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barbedwire" createVehicle [3948.3716, 11044.758, -3.0517578e-005];
 

  _vehicle_952 = _this;
  _this setDir 18.56122;
  _this setPos [3948.3716, 11044.758, -3.0517578e-005];
};

_vehicle_959 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowerGenerator" createVehicle [3922.5994, 11059.581, 6.1035156e-005];
 

  _vehicle_959 = _this;
  _this setDir -40.753048;
  _this setPos [3922.5994, 11059.581, 6.1035156e-005];
};

_vehicle_961 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_PowerGenerator" createVehicle [3924.0088, 11057.874, 9.1552734e-005];
 

  _vehicle_961 = _this;
  _this setDir -40.753048;
  _this setPos [3924.0088, 11057.874, 9.1552734e-005];
};

_vehicle_964 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [3935.938, 11057.091];
 

  _vehicle_964 = _this;
  _this setDir 60.405724;
  _this setPos [3935.938, 11057.091];
};

_vehicle_966 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [3896.176, 11042.676, 0.00012207031];
 

  _vehicle_966 = _this;
  _this setPos [3896.176, 11042.676, 0.00012207031];
};

_vehicle_969 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [3887.7498, 11041.682, 6.1035156e-005];
 

  _vehicle_969 = _this;
  _this setDir -33.796577;
  _this setPos [3887.7498, 11041.682, 6.1035156e-005];
};

_vehicle_971 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_craet1" createVehicle [3924.6016, 11003.563, 3.0517578e-005];
 

  _vehicle_971 = _this;
  _this setDir -33.796577;
  _this setPos [3924.6016, 11003.563, 3.0517578e-005];
};

_vehicle_974 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [3918.4031, 11010.768, 6.1035156e-005];
 

  _vehicle_974 = _this;
  _this setPos [3918.4031, 11010.768, 6.1035156e-005];
};

_vehicle_976 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [3932.1389, 11012.96, 0.00018310547];
 

  _vehicle_976 = _this;
  _this setPos [3932.1389, 11012.96, 0.00018310547];
};

_vehicle_979 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barel7" createVehicle [3923.9292, 11046.224, 6.1035156e-005];
 

  _vehicle_979 = _this;
  _this setDir -63.921005;
  _this setPos [3923.9292, 11046.224, 6.1035156e-005];
};

_vehicle_981 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barel7" createVehicle [3899.9604, 11022.378, 6.1035156e-005];
 

  _vehicle_981 = _this;
  _this setDir -74.875923;
  _this setPos [3899.9604, 11022.378, 6.1035156e-005];
};

_vehicle_983 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barel7" createVehicle [3880.3279, 11014.064, 0.00021362305];
 

  _vehicle_983 = _this;
  _this setDir -63.921005;
  _this setPos [3880.3279, 11014.064, 0.00021362305];
};

_vehicle_985 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barel7" createVehicle [3936.4644, 11061.205, 6.1035156e-005];
 

  _vehicle_985 = _this;
  _this setDir 10.531802;
  _this setPos [3936.4644, 11061.205, 6.1035156e-005];
};

_vehicle_988 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_Barbedwire" createVehicle [3943.2668, 11049.723, 6.1035156e-005];
 

  _vehicle_988 = _this;
  _this setDir 76.099854;
  _this setPos [3943.2668, 11049.723, 6.1035156e-005];
};

_vehicle_991 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CamoNetVar_EAST" createVehicle [3930.2402, 11043.725];
 

  _vehicle_991 = _this;
  _this setDir -108.10652;
  _this setPos [3930.2402, 11043.725];
};

_vehicle_994 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_hromada_beden_dekorativniX" createVehicle [3940.1929, 11074.996, 0.00018310547];
 

  _vehicle_994 = _this;
  _this setDir -347.64011;
  _this setPos [3940.1929, 11074.996, 0.00018310547];
};

_vehicle_997 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_canina2s" createVehicle [3953.9509, 11058.038];
 

  _vehicle_997 = _this;
  _this setPos [3953.9509, 11058.038];
};

_vehicle_1000 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [3949.3979, 11051.779, 9.1552734e-005];
 

  _vehicle_1000 = _this;
  _this setDir 1.3066831;
  _this setPos [3949.3979, 11051.779, 9.1552734e-005];
};

_vehicle_1003 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_canina2s" createVehicle [3941.5591, 11069.095];
 

  _vehicle_1003 = _this;
  _this setPos [3941.5591, 11069.095];
};

_vehicle_1005 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_canina2s" createVehicle [3930.106, 11017.404, 3.0517578e-005];
 

  _vehicle_1005 = _this;
  _this setPos [3930.106, 11017.404, 3.0517578e-005];
};

_vehicle_1008 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus2s" createVehicle [3914.071, 11009.081, 0.00039672852];
 

  _vehicle_1008 = _this;
  _this setPos [3914.071, 11009.081, 0.00039672852];
};

_vehicle_1010 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus2s" createVehicle [3924.9402, 11068.505, -9.1552734e-005];
 

  _vehicle_1010 = _this;
  _this setDir -69.078972;
  _this setPos [3924.9402, 11068.505, -9.1552734e-005];
};

_vehicle_1012 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_corylus2s" createVehicle [3948.3203, 11079.558, 9.1552734e-005];
 

  _vehicle_1012 = _this;
  _this setPos [3948.3203, 11079.558, 9.1552734e-005];
};

_vehicle_1014 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "MAP_b_prunus" createVehicle [3940.4402, 11031.171, 3.0517578e-005];
 

  _vehicle_1014 = _this;
  _this setDir 1.3066831;
  _this setPos [3940.4402, 11031.171, 3.0517578e-005];
};

_vehicle_1015 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Misc_deerstand" createVehicle [3887.9919, 11048.234, 0.44055828];
 

  _vehicle_1015 = _this;
  _this setDir 133.44647;
  _this setPos [3887.9919, 11048.234, 0.44055828];
};

_vehicle_1017 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_Misc_deerstand" createVehicle [3919.6902, 11062.58, -6.1035156e-005];
 

  _vehicle_1017 = _this;
  _this setDir 133.44647;
  _this setPos [3919.6902, 11062.58, -6.1035156e-005];
};

_vehicle_1020 = objNull;
if (true) then
{
  // Output from missionParser
  _this = "Land_CamoNetVar_EAST" createVehicle [3880.7075, 11036.651, 3.0517578e-005];
 

  _vehicle_1020 = _this;
  _this setDir -216.4897;
  _this setPos [3880.7075, 11036.651, 3.0517578e-005];
};