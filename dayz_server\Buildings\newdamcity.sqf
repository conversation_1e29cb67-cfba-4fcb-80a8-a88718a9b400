if (isserver) then {
_vehicle_32 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [8050.2612, 14892.34, -1.3426218], [], 0, "CAN_COLLIDE"];
  _vehicle_32 = _this;
  _this setDir 0.52221638;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8050.2612, 14892.34, -1.3426218];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [8010.3735, 14892.581, -2.5713558], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8010.3735, 14892.581, -2.5713558];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [8090.2446, 14892.144, -10.350062], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir -0.14013475;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [8090.2446, 14892.144, -10.350062];
};

_vehicle_38 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pond_big_01", [8022.8877, 14931.026, 0.1045204], [], 0, "CAN_COLLIDE"];
  _vehicle_38 = _this;
  _this setPos [8022.8877, 14931.026, 0.1045204];
};

_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7955.9497, 14755.133, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setDir -91.583382;
  _this setPos [7955.9497, 14755.133, 0.00010681152];
};

_vehicle_45 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7930.9902, 14754.345, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_45 = _this;
  _this setDir -91.8078;
  _this setPos [7930.9902, 14754.345, 0.0001373291];
};

_vehicle_50 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7889.4785, 14751.767, -0.69810486], [], 0, "CAN_COLLIDE"];
  _vehicle_50 = _this;
  _this setDir -96.740547;
  _this setPos [7889.4785, 14751.767, -0.69810486];
};

_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7864.6875, 14748.749, -0.26051331], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setDir -96.964966;
  _this setPos [7864.6875, 14748.749, -0.26051331];
};

_vehicle_56 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7822.9858, 14742.224, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_56 = _this;
  _this setDir 73.27581;
  _this setPos [7822.9858, 14742.224, 0.00024414063];
};

_vehicle_58 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7823.0742, 14742.229, -2.4732513], [], 0, "CAN_COLLIDE"];
  _vehicle_58 = _this;
  _this setDir -109.26765;
  _this setPos [7823.0742, 14742.229, -2.4732513];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7799.7861, 14734.215, -0.43701172], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir -110.82231;
  _this setPos [7799.7861, 14734.215, -0.43701172];
};

_vehicle_66 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7776.5264, 14725.4, -3.7883148], [], 0, "CAN_COLLIDE"];
  _vehicle_66 = _this;
  _this setDir -111.67802;
  _this setPos [7776.5264, 14725.4, -3.7883148];
};

_vehicle_67 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7753.4775, 14716.246, -0.62211609], [], 0, "CAN_COLLIDE"];
  _vehicle_67 = _this;
  _this setDir -113.11969;
  _this setPos [7753.4775, 14716.246, -0.62211609];
};

_vehicle_73 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7730.6621, 14706.536, -5.1781311], [], 0, "CAN_COLLIDE"];
  _vehicle_73 = _this;
  _this setDir -115.33236;
  _this setPos [7730.6621, 14706.536, -5.1781311];
};

_vehicle_74 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7708.2109, 14695.863, -0.8182373], [], 0, "CAN_COLLIDE"];
  _vehicle_74 = _this;
  _this setDir -116.77444;
  _this setPos [7708.2109, 14695.863, -0.8182373];
};

_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7686.0918, 14684.697, -5.3500061], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setDir -117.24496;
  _this setPos [7686.0918, 14684.697, -5.3500061];
};

_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7664.0103, 14673.286, -1.1496735], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setDir -118.68707;
  _this setPos [7664.0103, 14673.286, -1.1496735];
};

_vehicle_88 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7642.4912, 14661.588, -4.2118988], [], 0, "CAN_COLLIDE"];
  _vehicle_88 = _this;
  _this setDir -120.71617;
  _this setPos [7642.4912, 14661.588, -4.2118988];
};

_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7621.1411, 14648.854, -0.1973114], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir -122.15824;
  _this setPos [7621.1411, 14648.854, -0.1973114];
};

_vehicle_94 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7600.1147, 14635.623, -3.43396], [], 0, "CAN_COLLIDE"];
  _vehicle_94 = _this;
  _this setDir -122.06831;
  _this setPos [7600.1147, 14635.623, -3.43396];
};

_vehicle_99 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7579.0503, 14622.421, 0.00061035156], [], 0, "CAN_COLLIDE"];
  _vehicle_99 = _this;
  _this setDir -121.24428;
  _this setPos [7579.0503, 14622.421, 0.00061035156];
};

_vehicle_100 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7563.5068, 14614.742, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_100 = _this;
  _this setDir -110.92697;
  _this setPos [7563.5068, 14614.742, 0.00010681152];
};

_vehicle_101 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7546.9492, 14609.994, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_101 = _this;
  _this setDir -100.18593;
  _this setPos [7546.9492, 14609.994, 4.5776367e-005];
};

_vehicle_103 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7529.7563, 14608.481, -3.5122223], [], 0, "CAN_COLLIDE"];
  _vehicle_103 = _this;
  _this setDir -90.154236;
  _this setPos [7529.7563, 14608.481, -3.5122223];
};

_vehicle_104 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7504.8779, 14608.464, -0.1762085], [], 0, "CAN_COLLIDE"];
  _vehicle_104 = _this;
  _this setDir -89.899818;
  _this setPos [7504.8779, 14608.464, -0.1762085];
};

_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [7460.998, 14604.915, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setDir 68.08226;
  _this setPos [7460.998, 14604.915, 0.00030517578];
};

_vehicle_113 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7461.2324, 14604.929, -4.7444305], [], 0, "CAN_COLLIDE"];
  _vehicle_113 = _this;
  _this setDir -112.41313;
  _this setPos [7461.2324, 14604.929, -4.7444305];
};

_vehicle_114 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7438.2944, 14595.512, -0.27110291], [], 0, "CAN_COLLIDE"];
  _vehicle_114 = _this;
  _this setDir -112.15859;
  _this setPos [7438.2944, 14595.512, -0.27110291];
};

_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7415.3047, 14586.061, -4.6911011], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setDir -112.66285;
  _this setPos [7415.3047, 14586.061, -4.6911011];
};

_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7392.3633, 14576.581, -0.2808075], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir -112.40844;
  _this setPos [7392.3633, 14576.581, -0.2808075];
};

_vehicle_125 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7369.2944, 14567.148, -5.3820343], [], 0, "CAN_COLLIDE"];
  _vehicle_125 = _this;
  _this setDir -112.25301;
  _this setPos [7369.2944, 14567.148, -5.3820343];
};


_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7314.6577, 14519.832, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir -155.55812;
  _this setPos [7314.6577, 14519.832, -0.00015258789];
};

_vehicle_140 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [7319.4937, 14530.992, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_140 = _this;
  _this setDir 22.559639;
  _this setPos [7319.4937, 14530.992, 6.1035156e-005];
};

_vehicle_141 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [7330.126, 14546.971], [], 0, "CAN_COLLIDE"];
  _vehicle_141 = _this;
  _this setDir 45.002804;
  _this setPos [7330.126, 14546.971];
};

_vehicle_142 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_12", [7314.6196, 14519.577, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_142 = _this;
  _this setDir 23.29364;
  _this setPos [7314.6196, 14519.577, 6.1035156e-005];
};

_vehicle_143 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7306.2012, 14504.882, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_143 = _this;
  _this setDir -144.46428;
  _this setPos [7306.2012, 14504.882, -0.00021362305];
};

_vehicle_144 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7295.0146, 14491.906], [], 0, "CAN_COLLIDE"];
  _vehicle_144 = _this;
  _this setDir -132.13283;
  _this setPos [7295.0146, 14491.906];
};

_vehicle_149 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_0_2000", [7906.6577, 14753.588, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_149 = _this;
  _this setDir -96.51889;
  _this setPos [7906.6577, 14753.588, 6.1035156e-005];
};

_vehicle_153 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7281.4277, 14481.61, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_153 = _this;
  _this setDir -121.34624;
  _this setPos [7281.4277, 14481.61, 0.00015258789];
};

_vehicle_154 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7227.7871, 14457.412, 0.00054931641], [], 0, "CAN_COLLIDE"];
  _vehicle_154 = _this;
  _this setDir 58.512379;
  _this setPos [7227.7871, 14457.412, 0.00054931641];
};

_vehicle_155 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7266.1929, 14474.104, 0.00045776367], [], 0, "CAN_COLLIDE"];
  _vehicle_155 = _this;
  _this setDir -111.36203;
  _this setPos [7266.1929, 14474.104, 0.00045776367];
};

_vehicle_156 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7214.0161, 14447.197, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_156 = _this;
  _this setDir 48.633087;
  _this setPos [7214.0161, 14447.197, 0.0002746582];
};

_vehicle_157 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7202.2827, 14434.832, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_157 = _this;
  _this setDir 38.462631;
  _this setPos [7202.2827, 14434.832, 0.0002746582];
};

_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7192.8794, 14420.736, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setDir 28.660656;
  _this setPos [7192.8794, 14420.736, 0.00018310547];
};

_vehicle_159 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7180.7593, 14399.177, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_159 = _this;
  _this setDir 29.344549;
  _this setPos [7180.7593, 14399.177, 6.1035156e-005];
};

_vehicle_160 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7134.9541, 14320.753, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_160 = _this;
  _this setDir -140.01027;
  _this setPos [7134.9541, 14320.753, 3.0517578e-005];
};

_vehicle_161 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7144.6318, 14334.871, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_161 = _this;
  _this setDir -150.54051;
  _this setPos [7144.6318, 14334.871, 0.00021362305];
};

_vehicle_162 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7122.7827, 14308.547, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_162 = _this;
  _this setDir -130.36786;
  _this setPos [7122.7827, 14308.547, -0.00018310547];
};

_vehicle_163 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7168.7471, 14377.794, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_163 = _this;
  _this setDir 29.388966;
  _this setPos [7168.7471, 14377.794, -3.0517578e-005];
};

_vehicle_164 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7156.6416, 14356.419, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_164 = _this;
  _this setDir 29.259317;
  _this setPos [7156.6416, 14356.419, 0.00036621094];
};

_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7144.563, 14334.72, 0.00094604492], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setDir 29.259317;
  _this setPos [7144.563, 14334.72, 0.00094604492];
};

_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7108.7319, 14298.565, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setDir -120.59789;
  _this setPos [7108.7319, 14298.565, 0.00018310547];
};

_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [7093.4312, 14291.215, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setDir -110.37659;
  _this setPos [7093.4312, 14291.215, 6.1035156e-005];
};

_vehicle_170 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7052.7095, 14281.589, 0.102791], [], 0, "CAN_COLLIDE"];
  _vehicle_170 = _this;
  _this setDir 77.946266;
  _this setPos [7052.7095, 14281.589, 0.102791];
};

_vehicle_171 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7052.8486, 14281.609, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_171 = _this;
  _this setDir -101.74741;
  _this setPos [7052.8486, 14281.609, 0.00024414063];
};

_vehicle_172 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7005.2075, 14271.916, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_172 = _this;
  _this setDir -101.19212;
  _this setPos [7005.2075, 14271.916, 6.1035156e-005];
};

_vehicle_173 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [7029.1157, 14276.638, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_173 = _this;
  _this setDir -101.03262;
  _this setPos [7029.1157, 14276.638, 6.1035156e-005];
};

_vehicle_174 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [6981.2114, 14267.208, 0.00045776367], [], 0, "CAN_COLLIDE"];
  _vehicle_174 = _this;
  _this setDir -101.4529;
  _this setPos [6981.2114, 14267.208, 0.00045776367];
};

_vehicle_178 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [6956.9614, 14262.313, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_178 = _this;
  _this setDir -101.41116;
  _this setPos [6956.9614, 14262.313, -6.1035156e-005];
};

_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [6915.873, 14253.117], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setDir 70.635445;
  _this setPos [6915.873, 14253.117];
};

_vehicle_180 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [6900.3691, 14246.168, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_180 = _this;
  _this setDir 60.971085;
  _this setPos [6900.3691, 14246.168, 6.1035156e-005];
};

_vehicle_181 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [6885.9766, 14236.488, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_181 = _this;
  _this setDir 51.072697;
  _this setPos [6885.9766, 14236.488, 3.0517578e-005];
};

_vehicle_182 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [6866.6841, 14221.192, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_182 = _this;
  _this setDir 51.392918;
  _this setPos [6866.6841, 14221.192, 0.00021362305];
};

_vehicle_183 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [6847.2568, 14205.813, 0.00045776367], [], 0, "CAN_COLLIDE"];
  _vehicle_183 = _this;
  _this setDir 51.756519;
  _this setPos [6847.2568, 14205.813, 0.00045776367];
};

_vehicle_184 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [6847.4751, 14206.029], [], 0, "CAN_COLLIDE"];
  _vehicle_184 = _this;
  _this setDir -127.90499;
  _this setPos [6847.4751, 14206.029];
};

_vehicle_185 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [6832.9326, 14196.64], [], 0, "CAN_COLLIDE"];
  _vehicle_185 = _this;
  _this setDir -117.85166;
  _this setPos [6832.9326, 14196.64];
};

_vehicle_186 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [6817.1787, 14190.011, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_186 = _this;
  _this setDir -107.87865;
  _this setPos [6817.1787, 14190.011, -9.1552734e-005];
};

_vehicle_188 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Terrace_K_2_EP1", [7369.8413, 14545.745, -5.1434674], [], 0, "CAN_COLLIDE"];
  _vehicle_188 = _this;
  _this setDir -67.934357;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7369.8413, 14545.745, -5.1434674];
};

_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Build", [7354.71, 14549.563, 0.040480614], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setDir -113.23354;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7354.71, 14549.563, 0.040480614];
};

_vehicle_196 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_spoj", [7361.7578, 14548.782, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_196 = _this;
  _this setDir -22.652597;
  _this setPos [7361.7578, 14548.782, 9.1552734e-005];
};

_vehicle_197 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Feed", [7363.9829, 14551.251, -0.06848006], [], 0, "CAN_COLLIDE"];
  _vehicle_197 = _this;
  _this setDir -113.05981;
  _this setPos [7363.9829, 14551.251, -0.06848006];
};

_vehicle_198 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_FuelStation_Feed", [7364.8511, 14549.126, -0.068126008], [], 0, "CAN_COLLIDE"];
  _vehicle_198 = _this;
  _this setDir -113.07679;
  _this setPos [7364.8511, 14549.126, -0.068126008];
};

_vehicle_199 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_TankSmall2", [7357.9976, 14541.297, -0.13650173], [], 0, "CAN_COLLIDE"];
  _vehicle_199 = _this;
  _this setDir 66.228355;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7357.9976, 14541.297, -0.13650173];
};

_vehicle_200 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [7359.8906, 14552.844, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_200 = _this;
  _this setPos [7359.8906, 14552.844, 3.0517578e-005];
};

_vehicle_201 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [7362.9146, 14547.028, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_201 = _this;
  _this setPos [7362.9146, 14547.028, 0.00012207031];
};

_vehicle_202 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7372.9336, 14544.555, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_202 = _this;
  _this setPos [7372.9336, 14544.555, -6.1035156e-005];
};

_vehicle_203 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7356.6582, 14538.394, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_203 = _this;
  _this setPos [7356.6582, 14538.394, 9.1552734e-005];
};

_vehicle_204 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7366.1084, 14559.852, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_204 = _this;
  _this setPos [7366.1084, 14559.852, 3.0517578e-005];
};

_vehicle_205 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7350.4438, 14553.251, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_205 = _this;
  _this setPos [7350.4438, 14553.251, 9.1552734e-005];
};

_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_phone_box", [7357.4282, 14546.72, -0.06908641], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setDir -115.21781;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7357.4282, 14546.72, -0.06908641];
};

_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [7365.0576, 14549.414, -0.49998054], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setDir 68.114197;
  _this setPos [7365.0576, 14549.414, -0.49998054];
};

_vehicle_222 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [7364.2295, 14551.433, -0.5115127], [], 0, "CAN_COLLIDE"];
  _vehicle_222 = _this;
  _this setDir 66.764893;
  _this setPos [7364.2295, 14551.433, -0.5115127];
};

_vehicle_224 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [7363.6113, 14551.107, -0.52303648], [], 0, "CAN_COLLIDE"];
  _vehicle_224 = _this;
  _this setDir 68.458511;
  _this setPos [7363.6113, 14551.107, -0.52303648];
};

_vehicle_225 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [7364.394, 14549.136, -0.52347171], [], 0, "CAN_COLLIDE"];
  _vehicle_225 = _this;
  _this setDir 68.278328;
  _this setPos [7364.394, 14549.136, -0.52347171];
};

_vehicle_226 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Sign", [7369.7725, 14560.534, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_226 = _this;
  _this setDir -17.85644;
  _this setPos [7369.7725, 14560.534, -9.1552734e-005];
};

_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["Land_deutshe_mini", [7486.1509, 14586.982, 0.44442672], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setDir -70.379593;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7486.1509, 14586.982, 0.44442672];
};

_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7486.0415, 14584.152, -1.2300036], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setDir -70.992386;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7486.0415, 14584.152, -1.2300036];
};

_vehicle_231 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7486.2021, 14584.647, -1.5420442], [], 0, "CAN_COLLIDE"];
  _vehicle_231 = _this;
  _this setDir -70.78727;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7486.2021, 14584.647, -1.5420442];
};

_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7486.3721, 14585.081, -1.8554695], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setDir -71.125069;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7486.3721, 14585.081, -1.8554695];
};

_vehicle_235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_alnus2s", [7470.6118, 14591.567, -0.32875428], [], 0, "CAN_COLLIDE"];
  _vehicle_235 = _this;
  _this setPos [7470.6118, 14591.567, -0.32875428];
};

_vehicle_237 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2f", [7488.6895, 14596.022, -5.459877], [], 0, "CAN_COLLIDE"];
  _vehicle_237 = _this;
  _this setPos [7488.6895, 14596.022, -5.459877];
};

_vehicle_242 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_flower_01", [7485.2593, 14581.378, 0.55608946], [], 0, "CAN_COLLIDE"];
  _vehicle_242 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7485.2593, 14581.378, 0.55608946];
};

_vehicle_243 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_flower_02", [7485.5977, 14580.422, 0.61214846], [], 0, "CAN_COLLIDE"];
  _vehicle_243 = _this;
  _this setDir 13.557828;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7485.5977, 14580.422, 0.61214846];
};

_vehicle_256 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HouseV_1L2", [7537.0352, 14590.159, -0.2521964], [], 0, "CAN_COLLIDE"];
  _vehicle_256 = _this;
  _this setDir 78.284058;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7537.0352, 14590.159, -0.2521964];
};

_vehicle_257 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7535.4951, 14596.923, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_257 = _this;
  _this setDir -2.6085842;
  _this setPos [7535.4951, 14596.923, 0.0001373291];
};

_vehicle_258 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7533.9937, 14596.339, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_258 = _this;
  _this setDir -131.68404;
  _this setPos [7533.9937, 14596.339, 0.00018310547];
};

_vehicle_259 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7532.3979, 14596.385, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_259 = _this;
  _this setDir 26.6488;
  _this setPos [7532.3979, 14596.385, 0.00018310547];
};

_vehicle_260 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7530.7227, 14595.723, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_260 = _this;
  _this setDir -130.56328;
  _this setPos [7530.7227, 14595.723, 0.00012207031];
};

_vehicle_261 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7528.7446, 14595.574, 0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_261 = _this;
  _this setDir -2.6085842;
  _this setPos [7528.7446, 14595.574, 0.00033569336];
};

_vehicle_262 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7538.4409, 14607.153, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_262 = _this;
  _this setDir -189.13231;
  _this setPos [7538.4409, 14607.153, -0.00012207031];
};

_vehicle_263 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7541.1348, 14588.712, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_263 = _this;
  _this setDir -7.628736;
  _this setPos [7541.1348, 14588.712, 7.6293945e-005];
};

_vehicle_264 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [7540.2866, 14594.853, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_264 = _this;
  _this setDir -8.1697969;
  _this setPos [7540.2866, 14594.853, 6.1035156e-005];
};

_vehicle_283 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7532.6548, 14591.797, -1.7866012], [], 0, "CAN_COLLIDE"];
  _vehicle_283 = _this;
  _this setDir -10.332972;
  _this setPos [7532.6548, 14591.797, -1.7866012];
};

_vehicle_287 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7529.7017, 14590.392, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_287 = _this;
  _this setPos [7529.7017, 14590.392, 0.00019836426];
};

_vehicle_288 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7529.3428, 14593.563, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_288 = _this;
  _this setPos [7529.3428, 14593.563, 0.00010681152];
};

_vehicle_289 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7533.9961, 14594.152, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_289 = _this;
  _this setPos [7533.9961, 14594.152, 6.1035156e-005];
};

_vehicle_296 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2W", [7521.2568, 14590.93, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_296 = _this;
  _this setPos [7521.2568, 14590.93, -3.0517578e-005];
};

_vehicle_303 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7495.1812, 14605.291, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_303 = _this;
  _this setDir -174.39345;
  _this setPos [7495.1812, 14605.291, 0.00010681152];
};

_vehicle_304 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7490.2344, 14580.527, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_304 = _this;
  _this setDir 17.611937;
  _this setPos [7490.2344, 14580.527, 3.0517578e-005];
};

_vehicle_307 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_75", [7494.5439, 14599.205, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_307 = _this;
  _this setDir 185.62914;
  _this setPos [7494.5439, 14599.205, -3.0517578e-005];
};

_vehicle_308 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [7502.7651, 14580.189, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_308 = _this;
  _this setPos [7502.7651, 14580.189, 0.00015258789];
};

_vehicle_309 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7510.9204, 14574.566, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_309 = _this;
  _this setPos [7510.9204, 14574.566, 1.5258789e-005];
};

_vehicle_336 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Well_C_EP1", [7497.876, 14574.499, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_336 = _this;
  _this setDir -0.32114667;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7497.876, 14574.499, 0.00015258789];
};

_vehicle_359 = objNull;
if (true) then
{
  _this = createVehicle ["ParkBench_DZ", [7519.938, 14586.599, 0.00047302246], [], 0, "CAN_COLLIDE"];
  _vehicle_359 = _this;
  _this setDir -132.52213;
  _this setPos [7519.938, 14586.599, 0.00047302246];
};

_vehicle_360 = objNull;
if (true) then
{
  _this = createVehicle ["ParkBench_DZ", [7523.7988, 14587.26, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_360 = _this;
  _this setDir -61.928432;
  _this setPos [7523.7988, 14587.26, 0.00012207031];
};

_vehicle_361 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cihlovej_dum_in", [7757.7539, 14758.136, 0.56780654], [], 0, "CAN_COLLIDE"];
  _vehicle_361 = _this;
  _this setDir 89.504898;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7757.7539, 14758.136, 0.56780654];
};

_vehicle_362 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cihlovej_dum_in", [7757.7158, 14764.937, 0.56856042], [], 0, "CAN_COLLIDE"];
  _vehicle_362 = _this;
  _this setDir 89.907051;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7757.7158, 14764.937, 0.56856042];
};

_vehicle_366 = objNull;
if (true) then
{
  _this = createVehicle ["mbg_brickhouse_01_EO", [7759.5918, 14737.21, -2.8027949], [], 0, "CAN_COLLIDE"];
  _vehicle_366 = _this;
  _this setDir 81.747322;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7759.5918, 14737.21, -2.8027949];
};

_vehicle_368 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [7748.9902, 14715.111, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_368 = _this;
  _this setDir -21.039543;
  _this setPos [7748.9902, 14715.111, 4.5776367e-005];
};

_vehicle_369 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7746.7432, 14720.774, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_369 = _this;
  _this setDir -19.568703;
  _this setPos [7746.7432, 14720.774, 0.0001373291];
};

_vehicle_370 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7742.3906, 14737.354, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_370 = _this;
  _this setDir -9.4156694;
  _this setPos [7742.3906, 14737.354, 7.6293945e-005];
};

_vehicle_371 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7741.0791, 14754.682, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_371 = _this;
  _this setDir 0.71080256;
  _this setPos [7741.0791, 14754.682, 4.5776367e-005];
};

_vehicle_372 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7742.8364, 14772.004, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_372 = _this;
  _this setDir 10.897311;
  _this setPos [7742.8364, 14772.004, -0.00010681152];
};

_vehicle_373 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7747.479, 14788.564, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_373 = _this;
  _this setDir 21.711012;
  _this setPos [7747.479, 14788.564, -0.0001373291];
};

_vehicle_374 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7755.2319, 14804.105, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_374 = _this;
  _this setDir 31.864716;
  _this setPos [7755.2319, 14804.105, 0.00015258789];
};

_vehicle_375 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7768.1992, 14825.329, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_375 = _this;
  _this setDir 31.698877;
  _this setPos [7768.1992, 14825.329, -7.6293945e-005];
};

_vehicle_376 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7781.1797, 14846.57, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_376 = _this;
  _this setDir 31.769176;
  _this setPos [7781.1797, 14846.57, -3.0517578e-005];
};

_vehicle_397 = objNull;
if (true) then
{
  _this = createVehicle ["Land_MBG_GER_PUB_2", [7763.3979, 14785.443, 0.96884865], [], 0, "CAN_COLLIDE"];
  _vehicle_397 = _this;
  _this setDir -255.16266;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7763.3979, 14785.443, 0.96884865];
};

_vehicle_401 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_molo_krychle", [7729.4795, 14793.098, -1.6516827], [], 0, "CAN_COLLIDE"];
  _vehicle_401 = _this;
  _this setDir 192.51286;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7729.4795, 14793.098, -1.6516827];
};

_vehicle_420 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_nav_pier_m_end", [7770.6284, 14783.54, -4.4475026], [], 0, "CAN_COLLIDE"];
  _vehicle_420 = _this;
  _this setDir -164.97556;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7770.6284, 14783.54, -4.4475026];
};

_vehicle_424 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7764.9165, 14789.804, -1.2384298], [], 0, "CAN_COLLIDE"];
  _vehicle_424 = _this;
  _this setDir 14.972998;
  _this setVehicleArmor 0.89999998;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7764.9165, 14789.804, -1.2384298];
};

_vehicle_426 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_AirCond_small", [7768.937, 14776.095, 1.6841924], [], 0, "CAN_COLLIDE"];
  _vehicle_426 = _this;
  _this setDir 14.183192;
  _this setPos [7768.937, 14776.095, 1.6841924];
};

_vehicle_427 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_AirCond_big", [7769.0449, 14775.947, 1.9428791], [], 0, "CAN_COLLIDE"];
  _vehicle_427 = _this;
  _this setDir 14.404377;
  _this setPos [7769.0449, 14775.947, 1.9428791];
};

_vehicle_428 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_AirCond_small", [7769.313, 14775.994, 1.7502197], [], 0, "CAN_COLLIDE"];
  _vehicle_428 = _this;
  _this setDir 13.683987;
  _this setPos [7769.313, 14775.994, 1.7502197];
};

_vehicle_437 = objNull;
if (true) then
{
  _this = createVehicle ["Land_deutshe_mini", [7723.0347, 14728.412, 0.51237673], [], 0, "CAN_COLLIDE"];
  _vehicle_437 = _this;
  _this setDir 81.05172;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7723.0347, 14728.412, 0.51237673];
};

_vehicle_440 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_molo_drevo_end", [7730.3477, 14727.754, -1.5846485], [], 0, "CAN_COLLIDE"];
  _vehicle_440 = _this;
  _this setDir 170.88429;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7730.3477, 14727.754, -1.5846485];
};

_vehicle_441 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_molo_drevo_end", [7729.4385, 14733.694, -1.7143164], [], 0, "CAN_COLLIDE"];
  _vehicle_441 = _this;
  _this setDir 171.15576;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7729.4385, 14733.694, -1.7143164];
};

_vehicle_448 = objNull;
if (true) then
{
  _this = createVehicle ["Land_sara_Domek_sedy", [7730.6738, 14761.872, 0.13676015], [], 0, "CAN_COLLIDE"];
  _vehicle_448 = _this;
  _this setDir -82.324982;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7730.6738, 14761.872, 0.13676015];
};

_vehicle_452 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_molo_drevo_end", [7728.9863, 14757.793, -1.8279289], [], 0, "CAN_COLLIDE"];
  _vehicle_452 = _this;
  _this setDir -262.35837;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7728.9863, 14757.793, -1.8279289];
};

_vehicle_461 = objNull;
if (true) then
{
  _this = createVehicle ["Land_dum_mesto2", [7728.4653, 14791.516, 0.61090171], [], 0, "CAN_COLLIDE"];
  _vehicle_461 = _this;
  _this setDir 12.640362;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7728.4653, 14791.516, 0.61090171];
};

_vehicle_484 = objNull;
if (true) then
{
  _this = createVehicle ["RampConcrete", [7731.2329, 14783.837, -0.90894252], [], 0, "CAN_COLLIDE"];
  _vehicle_484 = _this;
  _this setDir -168.84181;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7731.2329, 14783.837, -0.90894252];
};

_vehicle_485 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7734.9312, 14786.773, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_485 = _this;
  _this setPos [7734.9312, 14786.773, 7.6293945e-005];
};

_vehicle_486 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7734.6821, 14785.399, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_486 = _this;
  _this setDir 95.42086;
  _this setPos [7734.6821, 14785.399, 9.1552734e-005];
};

_vehicle_487 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7734.5225, 14783.668, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_487 = _this;
  _this setDir 136.44484;
  _this setPos [7734.5225, 14783.668, -0.0001373291];
};

_vehicle_488 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7736.6504, 14786.385, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_488 = _this;
  _this setPos [7736.6504, 14786.385, 6.1035156e-005];
};

_vehicle_489 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7738.0649, 14787.739, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_489 = _this;
  _this setDir 91.943779;
  _this setPos [7738.0649, 14787.739, 1.5258789e-005];
};

_vehicle_491 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7738.5166, 14789.098, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_491 = _this;
  _this setDir -144.58136;
  _this setPos [7738.5166, 14789.098, -4.5776367e-005];
};

_vehicle_492 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7738.7085, 14791.103, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_492 = _this;
  _this setDir -369.84131;
  _this setPos [7738.7085, 14791.103, -6.1035156e-005];
};

_vehicle_493 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7739.0498, 14792.526, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_493 = _this;
  _this setDir -77.713226;
  _this setPos [7739.0498, 14792.526, -6.1035156e-005];
};

_vehicle_494 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7739.4316, 14794.301, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_494 = _this;
  _this setPos [7739.4316, 14794.301, -4.5776367e-005];
};

_vehicle_495 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7738.4048, 14795.824, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_495 = _this;
  _this setPos [7738.4048, 14795.824, -3.0517578e-005];
};

_vehicle_496 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7736.3262, 14796.122, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_496 = _this;
  _this setPos [7736.3262, 14796.122, -9.1552734e-005];
};

_vehicle_498 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [7737.7925, 14780.436, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_498 = _this;
  _this setPos [7737.7925, 14780.436, 0.00022888184];
};

_vehicle_499 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [7728.0278, 14741.439, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_499 = _this;
  _this setPos [7728.0278, 14741.439, -3.0517578e-005];
};

_vehicle_501 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7710.1367, 14726.346, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_501 = _this;
  _this setPos [7710.1367, 14726.346, 0.00015258789];
};

_vehicle_502 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7726.2056, 14772.378, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_502 = _this;
  _this setPos [7726.2056, 14772.378, 0.0001373291];
};

_vehicle_504 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pyrus2s", [7750.6943, 14771.122, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_504 = _this;
  _this setPos [7750.6943, 14771.122, 0.00010681152];
};

_vehicle_509 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_sorbus2s", [7753.0039, 14727.728, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_509 = _this;
  _this setPos [7753.0039, 14727.728, 6.1035156e-005];
};

_vehicle_510 = objNull;
if (true) then
{
  _this = createVehicle ["Land_t_malus1s", [7748.3491, 14740.079, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_510 = _this;
  _this setPos [7748.3491, 14740.079, 6.1035156e-005];
};

_vehicle_517 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [7753.5151, 14764.604, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_517 = _this;
  _this setDir -40.293343;
  _this setPos [7753.5151, 14764.604, -1.5258789e-005];
};

_vehicle_518 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [7753.6226, 14763.053, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_518 = _this;
  _this setDir -0.21735221;
  _this setPos [7753.6226, 14763.053, -3.0517578e-005];
};

_vehicle_519 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [7753.6768, 14761.222, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_519 = _this;
  _this setPos [7753.6768, 14761.222, -7.6293945e-005];
};

_vehicle_520 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [7753.9512, 14757.319, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_520 = _this;
  _this setPos [7753.9512, 14757.319, 7.6293945e-005];
};

_vehicle_521 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [7753.9219, 14755.454, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_521 = _this;
  _this setPos [7753.9219, 14755.454, -1.5258789e-005];
};

_vehicle_527 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [7732.7974, 14757.598, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_527 = _this;
  _this setPos [7732.7974, 14757.598, -6.1035156e-005];
};

_vehicle_528 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [7733.1143, 14758.714, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_528 = _this;
  _this setDir -162.73325;
  _this setPos [7733.1143, 14758.714, 0.00012207031];
};

_vehicle_529 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [7733.3267, 14760.233, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_529 = _this;
  _this setPos [7733.3267, 14760.233, 3.0517578e-005];
};

_vehicle_530 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [7732.3843, 14761.137, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_530 = _this;
  _this setDir -194.55142;
  _this setPos [7732.3843, 14761.137, -3.0517578e-005];
};

_vehicle_531 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [7731.5713, 14761.037, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_531 = _this;
  _this setPos [7731.5713, 14761.037, 0.00010681152];
};

_vehicle_532 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [7732.5967, 14756.318, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_532 = _this;
  _this setDir -132.94792;
  _this setPos [7732.5967, 14756.318, 0.00010681152];
};

_vehicle_541 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2W", [7774.4849, 14766.619], [], 0, "CAN_COLLIDE"];
  _vehicle_541 = _this;
  _this setPos [7774.4849, 14766.619];
};

_vehicle_542 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2W", [7776.9111, 14737.284, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_542 = _this;
  _this setPos [7776.9111, 14737.284, 0.00010681152];
};

_vehicle_544 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7769.1055, 14749.187, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_544 = _this;
  _this setPos [7769.1055, 14749.187, -9.1552734e-005];
};

_vehicle_545 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7779.207, 14800.337, 0.00028991699], [], 0, "CAN_COLLIDE"];
  _vehicle_545 = _this;
  _this setPos [7779.207, 14800.337, 0.00028991699];
};

_vehicle_546 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7773.0654, 14801.906, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_546 = _this;
  _this setPos [7773.0654, 14801.906, -3.0517578e-005];
};

_vehicle_547 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7767.6431, 14803.253, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_547 = _this;
  _this setPos [7767.6431, 14803.253, 0.00012207031];
};

_vehicle_548 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7762.2456, 14804.355, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_548 = _this;
  _this setPos [7762.2456, 14804.355, 0.00012207031];
};

_vehicle_549 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7717.8613, 14813.236, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_549 = _this;
  _this setPos [7717.8613, 14813.236, 3.0517578e-005];
};

_vehicle_550 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7698.6914, 14786.135, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_550 = _this;
  _this setPos [7698.6914, 14786.135, 6.1035156e-005];
};

_vehicle_551 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_spoj_2", [7758.1216, 14779.539, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_551 = _this;
  _this setDir 17.436314;
  _this setPos [7758.1216, 14779.539, 1.5258789e-005];
};

_vehicle_553 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [7751.4795, 14781.492, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_553 = _this;
  _this setPos [7751.4795, 14781.492, 0.0001373291];
};

_vehicle_558 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [7759.7622, 14777.754, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_558 = _this;
  _this setPos [7759.7622, 14777.754, 6.1035156e-005];
};

_vehicle_561 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7794.2192, 14867.771, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_561 = _this;
  _this setDir 31.460363;
  _this setPos [7794.2192, 14867.771, -0.0001373291];
};

_vehicle_562 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7807.2124, 14889.038, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_562 = _this;
  _this setDir 31.460363;
  _this setPos [7807.2124, 14889.038, 0.0001373291];
};

_vehicle_564 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7832.5024, 14942.271, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_564 = _this;
  _this setDir 191.56084;
  _this setPos [7832.5024, 14942.271, 0.00030517578];
};

_vehicle_565 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7827.7061, 14925.905, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_565 = _this;
  _this setDir 201.06543;
  _this setPos [7827.7061, 14925.905, 0.00019836426];
};

_vehicle_566 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7832.7197, 14976.791, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_566 = _this;
  _this setDir 169.80238;
  _this setPos [7832.7197, 14976.791, 6.1035156e-005];
};

_vehicle_567 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7834.3462, 14959.52, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_567 = _this;
  _this setDir 181.27844;
  _this setPos [7834.3462, 14959.52, 0.00022888184];
};

_vehicle_568 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7810.5181, 15023.116, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_568 = _this;
  _this setDir 140.13989;
  _this setPos [7810.5181, 15023.116, 0.00018310547];
};

_vehicle_569 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7820.4663, 15009.032, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_569 = _this;
  _this setDir 149.71736;
  _this setPos [7820.4663, 15009.032, 1.5258789e-005];
};

_vehicle_570 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7828.0078, 14993.433, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_570 = _this;
  _this setDir 159.64685;
  _this setPos [7828.0078, 14993.433, -3.0517578e-005];
};

_vehicle_571 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [7798.5068, 15035.275, 0.00032043457], [], 0, "CAN_COLLIDE"];
  _vehicle_571 = _this;
  _this setDir 130.80164;
  _this setPos [7798.5068, 15035.275, 0.00032043457];
};

_vehicle_573 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7798.5215, 15035.103, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_573 = _this;
  _this setDir -48.048508;
  _this setPos [7798.5215, 15035.103, 0.0001373291];
};

_vehicle_574 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7780.1777, 15051.464, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_574 = _this;
  _this setDir -46.197231;
  _this setPos [7780.1777, 15051.464, -9.1552734e-005];
};

_vehicle_575 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7762.355, 15068.573, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_575 = _this;
  _this setDir -44.460178;
  _this setPos [7762.355, 15068.573, 3.0517578e-005];
};

_vehicle_576 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7744.8672, 15086.313, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_576 = _this;
  _this setDir -43.489578;
  _this setPos [7744.8672, 15086.313, -6.1035156e-005];
};

_vehicle_577 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7727.707, 15104.279, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_577 = _this;
  _this setDir -41.924625;
  _this setPos [7727.707, 15104.279, 0.00022888184];
};

_vehicle_578 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7711.021, 15122.708, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_578 = _this;
  _this setDir -39.847813;
  _this setPos [7711.021, 15122.708, 0.0001373291];
};

_vehicle_579 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7695.1406, 15141.78, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_579 = _this;
  _this setDir -38.456726;
  _this setPos [7695.1406, 15141.78, 0.0001373291];
};

_vehicle_580 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7679.7021, 15161.157], [], 0, "CAN_COLLIDE"];
  _vehicle_580 = _this;
  _this setDir -37.588367;
  _this setPos [7679.7021, 15161.157];
};

_vehicle_581 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7664.5142, 15180.67, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_581 = _this;
  _this setDir -38.110046;
  _this setPos [7664.5142, 15180.67, 3.0517578e-005];
};

_vehicle_582 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7649.1982, 15199.984, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_582 = _this;
  _this setDir -38.110046;
  _this setPos [7649.1982, 15199.984, 4.5776367e-005];
};

_vehicle_583 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7633.8198, 15219.621, 0.00039672852], [], 0, "CAN_COLLIDE"];
  _vehicle_583 = _this;
  _this setDir -37.802162;
  _this setPos [7633.8198, 15219.621, 0.00039672852];
};

_vehicle_584 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7618.4673, 15239.234, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_584 = _this;
  _this setDir -37.802162;
  _this setPos [7618.4673, 15239.234, -9.1552734e-005];
};

_vehicle_585 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7603.0903, 15258.811, -0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_585 = _this;
  _this setDir -37.802162;
  _this setPos [7603.0903, 15258.811, -0.0001373291];
};

_vehicle_586 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7587.729, 15278.515, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_586 = _this;
  _this setDir -37.802162;
  _this setPos [7587.729, 15278.515, 6.1035156e-005];
};

_vehicle_587 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7572.4233, 15298.133, -0.00054931641], [], 0, "CAN_COLLIDE"];
  _vehicle_587 = _this;
  _this setDir -37.802162;
  _this setPos [7572.4233, 15298.133, -0.00054931641];
};

_vehicle_588 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7557.1343, 15317.772, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_588 = _this;
  _this setDir -37.293839;
  _this setPos [7557.1343, 15317.772, -0.00015258789];
};

_vehicle_589 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [7542.083, 15337.534, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_589 = _this;
  _this setDir -36.657581;
  _this setPos [7542.083, 15337.534, -0.00024414063];
};

_vehicle_595 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7757.519, 14788.361, -0.52332038], [], 0, "CAN_COLLIDE"];
  _vehicle_595 = _this;
  _this setDir -75.43325;
  _this setPos [7757.519, 14788.361, -0.52332038];
};

_vehicle_596 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7756.5356, 14785.022, -0.47019756], [], 0, "CAN_COLLIDE"];
  _vehicle_596 = _this;
  _this setDir -72.59288;
  _this setPos [7756.5356, 14785.022, -0.47019756];
};

_vehicle_597 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7755.5127, 14781.693, -0.47790638], [], 0, "CAN_COLLIDE"];
  _vehicle_597 = _this;
  _this setDir -73.718483;
  _this setPos [7755.5127, 14781.693, -0.47790638];
};

_vehicle_598 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7758.6724, 14776.824, -0.50105631], [], 0, "CAN_COLLIDE"];
  _vehicle_598 = _this;
  _this setDir 16.807783;
  _this setPos [7758.6724, 14776.824, -0.50105631];
};

_vehicle_599 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7762.0269, 14775.813, -0.51462531], [], 0, "CAN_COLLIDE"];
  _vehicle_599 = _this;
  _this setDir 16.732195;
  _this setPos [7762.0269, 14775.813, -0.51462531];
};

_vehicle_600 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7765.3193, 14774.793, -0.50054717], [], 0, "CAN_COLLIDE"];
  _vehicle_600 = _this;
  _this setDir 16.755678;
  _this setPos [7765.3193, 14774.793, -0.50054717];
};

_vehicle_601 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7755.4531, 14777.855, -0.46651697], [], 0, "CAN_COLLIDE"];
  _vehicle_601 = _this;
  _this setDir 18.098728;
  _this setPos [7755.4531, 14777.855, -0.46651697];
};

_vehicle_607 = objNull;
if (true) then
{
  _this = createVehicle ["Land_aif_sara_domek05", [7758.3628, 14856.662, -0.084095441], [], 0, "CAN_COLLIDE"];
  _vehicle_607 = _this;
  _this setDir -55.01162;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7758.3628, 14856.662, -0.084095441];
};

_vehicle_608 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7758.2485, 14854.488, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_608 = _this;
  _this setPos [7758.2485, 14854.488, 0.00019836426];
};

_vehicle_613 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7761.9585, 14854.218, -1.0810745], [], 0, "CAN_COLLIDE"];
  _vehicle_613 = _this;
  _this setDir 35.030556;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7761.9585, 14854.218, -1.0810745];
};

_vehicle_616 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7761.5854, 14853.7, -1.3638982], [], 0, "CAN_COLLIDE"];
  _vehicle_616 = _this;
  _this setDir 35.030735;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7761.5854, 14853.7, -1.3638982];
};

_vehicle_620 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7761.2202, 14853.154, -1.6611851], [], 0, "CAN_COLLIDE"];
  _vehicle_620 = _this;
  _this setDir 35.032253;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7761.2202, 14853.154, -1.6611851];
};

_vehicle_631 = objNull;
if (true) then
{
  _this = createVehicle ["Land_MBG_Outdoortable", [7716.3457, 14763.813], [], 0, "CAN_COLLIDE"];
  _vehicle_631 = _this;
  _this setDir 10.99221;
  _this setPos [7716.3457, 14763.813];
};

_vehicle_639 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_acer2s", [7713.1577, 14764.622, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_639 = _this;
  _this setPos [7713.1577, 14764.622, 6.1035156e-005];
};

_vehicle_641 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7767.4365, 14856.555, -0.090820931], [], 0, "CAN_COLLIDE"];
  _vehicle_641 = _this;
  _this setPos [7767.4365, 14856.555, -0.090820931];
};

_vehicle_642 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7768.4453, 14857.926, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_642 = _this;
  _this setDir -79.05468;
  _this setPos [7768.4453, 14857.926, 6.1035156e-005];
};

_vehicle_643 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7769.4253, 14859.4, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_643 = _this;
  _this setDir -102.20773;
  _this setPos [7769.4253, 14859.4, 1.5258789e-005];
};

_vehicle_644 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [7722.1807, 14826.854, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_644 = _this;
  _this setPos [7722.1807, 14826.854, 9.1552734e-005];
};

_vehicle_645 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [7740.708, 14830.061, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_645 = _this;
  _this setPos [7740.708, 14830.061, 7.6293945e-005];
};

_vehicle_646 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7731.2021, 14825.93, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_646 = _this;
  _this setPos [7731.2021, 14825.93, 0.00016784668];
};

_vehicle_647 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [7733.0791, 14833.241, -4.5462108], [], 0, "CAN_COLLIDE"];
  _vehicle_647 = _this;
  _this setPos [7733.0791, 14833.241, -4.5462108];
};

_vehicle_648 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [7741.6675, 14821.79, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_648 = _this;
  _this setPos [7741.6675, 14821.79, 0.00010681152];
};

_vehicle_649 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea3f", [7735.1509, 14844.062, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_649 = _this;
  _this setPos [7735.1509, 14844.062, 0.00016784668];
};

_vehicle_651 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_malus1s", [7758.8467, 14838.991, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_651 = _this;
  _this setPos [7758.8467, 14838.991, 3.0517578e-005];
};

_vehicle_658 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kulna", [7485.4502, 14569.089, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_658 = _this;
  _this setDir -139.98491;
  _this setPos [7485.4502, 14569.089, 9.1552734e-005];
};

_vehicle_661 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_psi_bouda", [7723.2505, 14792.144, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_661 = _this;
  _this setDir -164.23619;
  _this setPos [7723.2505, 14792.144, 7.6293945e-005];
};

_vehicle_675 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_drevo_hromada", [7722.0762, 14736.176, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_675 = _this;
  _this setDir -100.8974;
  _this setPos [7722.0762, 14736.176, 9.1552734e-005];
};

_vehicle_679 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_WoodPile", [7725.7007, 14796.772, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_679 = _this;
  _this setDir 12.240595;
  _this setPos [7725.7007, 14796.772, 7.6293945e-005];
};

_vehicle_681 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_popelnice", [7724.5166, 14763.386, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_681 = _this;
  _this setPos [7724.5166, 14763.386, -6.1035156e-005];
};

_vehicle_682 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sekyraspalek", [7533.8726, 14583.726, 0.00025939941], [], 0, "CAN_COLLIDE"];
  _vehicle_682 = _this;
  _this setDir 46.598236;
  _this setPos [7533.8726, 14583.726, 0.00025939941];
};

_vehicle_684 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ChickenCoop", [7536.4805, 14584.262, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_684 = _this;
  _this setDir 74.017319;
  _this setPos [7536.4805, 14584.262, 0.0001373291];
};

_vehicle_688 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7534.5825, 14583.771, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_688 = _this;
  _this setPos [7534.5825, 14583.771, 0.00022888184];
};

_vehicle_689 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [7773.1489, 14837.929, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_689 = _this;
  _this setDir -58.45882;
  _this setPos [7773.1489, 14837.929, 0.00012207031];
};

_vehicle_690 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [7773.6274, 14855.104, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_690 = _this;
  _this setDir 59.049183;
  _this setPos [7773.6274, 14855.104, 9.1552734e-005];
};

_vehicle_693 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_60_10", [7768.4307, 14846.697, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_693 = _this;
  _this setDir 1.7898813;
  _this setPos [7768.4307, 14846.697, 0.00016784668];
};

_vehicle_695 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7774.4946, 14848.023, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_695 = _this;
  _this setPos [7774.4946, 14848.023, 9.1552734e-005];
};

_vehicle_696 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2W", [7733.0308, 14812.438, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_696 = _this;
  _this setPos [7733.0308, 14812.438, -3.0517578e-005];
};

_vehicle_699 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fern", [7774.9341, 14849.645, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_699 = _this;
  _this setDir 29.358349;
  _this setPos [7774.9341, 14849.645, -0.00010681152];
};

_vehicle_700 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fern", [7776.0015, 14848.775, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_700 = _this;
  _this setDir -12.885279;
  _this setPos [7776.0015, 14848.775, 3.0517578e-005];
};

_vehicle_701 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fern", [7776.1367, 14847.063, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_701 = _this;
  _this setDir 53.210381;
  _this setPos [7776.1367, 14847.063, 7.6293945e-005];
};

_vehicle_702 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fern", [7773.2993, 14848.664, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_702 = _this;
  _this setDir -15.471898;
  _this setPos [7773.2993, 14848.664, 0.00021362305];
};

_vehicle_703 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fern", [7772.623, 14847.502, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_703 = _this;
  _this setDir 36.500385;
  _this setPos [7772.623, 14847.502, 6.1035156e-005];
};

_vehicle_704 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fern", [7774.2466, 14846.222, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_704 = _this;
  _this setDir -18.619545;
  _this setPos [7774.2466, 14846.222, 9.1552734e-005];
};

_vehicle_705 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [7773.8364, 14847.512, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_705 = _this;
  _this setPos [7773.8364, 14847.512, 0.00012207031];
};

_vehicle_706 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [7774.8521, 14846.366, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_706 = _this;
  _this setPos [7774.8521, 14846.366, 1.5258789e-005];
};

_vehicle_708 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [7775.8242, 14847.461, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_708 = _this;
  _this setPos [7775.8242, 14847.461, 7.6293945e-005];
};

_vehicle_709 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [7775.103, 14848.782, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_709 = _this;
  _this setPos [7775.103, 14848.782, 4.5776367e-005];
};

_vehicle_710 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_fernTall", [7773.8203, 14848.21, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_710 = _this;
  _this setPos [7773.8203, 14848.21, 6.1035156e-005];
};

_vehicle_714 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2s", [7774.7368, 14872.406, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_714 = _this;
  _this setPos [7774.7368, 14872.406, 0.00030517578];
};

_vehicle_715 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_sorbus2s", [7774.4595, 14847.574, -0.43215162], [], 0, "CAN_COLLIDE"];
  _vehicle_715 = _this;
  _this setDir 145.17628;
  _this setPos [7774.4595, 14847.574, -0.43215162];
};

_vehicle_741 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7766.8911, 14853.658, -0.21347412], [], 0, "CAN_COLLIDE"];
  _vehicle_741 = _this;
  _this setPos [7766.8911, 14853.658, -0.21347412];
};

_vehicle_742 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7766.4341, 14852.175, -0.31768975], [], 0, "CAN_COLLIDE"];
  _vehicle_742 = _this;
  _this setDir 139.93069;
  _this setPos [7766.4341, 14852.175, -0.31768975];
};

_vehicle_743 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7765.0396, 14851.139, -0.44303259], [], 0, "CAN_COLLIDE"];
  _vehicle_743 = _this;
  _this setDir 8.8679714;
  _this setPos [7765.0396, 14851.139, -0.44303259];
};

_vehicle_747 = objNull;
if (true) then
{
  _this = createVehicle ["mbg_slum01_EO", [7709.7612, 14734.044, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_747 = _this;
  _this setDir 276.61838;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7709.7612, 14734.044, 6.1035156e-005];
};

_vehicle_749 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7745.125, 14724.366, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_749 = _this;
  _this setDir -101.22126;
  _this setPos [7745.125, 14724.366, -3.0517578e-005];
};

_vehicle_753 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6", [7732.8652, 14721.971, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_753 = _this;
  _this setDir 78.742226;
  _this setPos [7732.8652, 14721.971, -4.5776367e-005];
};

_vehicle_755 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7726.7974, 14720.787, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_755 = _this;
  _this setDir 78.689629;
  _this setPos [7726.7974, 14720.787, 3.0517578e-005];
};

_vehicle_756 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_12", [7741.48, 14776.202, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_756 = _this;
  _this setDir -80.102257;
  _this setPos [7741.48, 14776.202, 0.00016784668];
};

_vehicle_757 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [7723.106, 14779.444, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_757 = _this;
  _this setDir -260.18933;
  _this setPos [7723.106, 14779.444, 0.00010681152];
};

_vehicle_812 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HouseV_1L1", [7624.8086, 14689.293, -0.61037523], [], 0, "CAN_COLLIDE"];
  _vehicle_812 = _this;
  _this setDir 210.36903;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7624.8086, 14689.293, -0.61037523];
};

_vehicle_821 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ConcreteBlock", [7631.019, 14690.142, -1.6989782], [], 0, "CAN_COLLIDE"];
  _vehicle_821 = _this;
  _this setDir -60.512474;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7631.019, 14690.142, -1.6989782];
};

_vehicle_822 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7628.8984, 14687.423, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_822 = _this;
  _this setPos [7628.8984, 14687.423, -4.5776367e-005];
};

_vehicle_823 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7636.5508, 14661.75, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_823 = _this;
  _this setDir -36.073643;
  _this setPos [7636.5508, 14661.75, 0.00010681152];
};

_vehicle_824 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [7620.9873, 14679.449, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_824 = _this;
  _this setDir 131.83871;
  _this setPos [7620.9873, 14679.449, 0.00030517578];
};

_vehicle_825 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7616.3159, 14683.536, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_825 = _this;
  _this setDir 131.19034;
  _this setPos [7616.3159, 14683.536, 7.6293945e-005];
};

_vehicle_829 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2f", [7642.8975, 14697.678, -7.6581268], [], 0, "CAN_COLLIDE"];
  _vehicle_829 = _this;
  _this setDir -0.2033639;
  _this setPos [7642.8975, 14697.678, -7.6581268];
};

_vehicle_830 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [7640.3931, 14673.799, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_830 = _this;
  _this setPos [7640.3931, 14673.799, 0.00024414063];
};

_vehicle_831 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2W", [7616.0215, 14672.932, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_831 = _this;
  _this setPos [7616.0215, 14672.932, -4.5776367e-005];
};

_vehicle_833 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [7631.8765, 14696.028, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_833 = _this;
  _this setDir -47.010941;
  _this setPos [7631.8765, 14696.028, 6.1035156e-005];
};

_vehicle_835 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Barrel_water", [7624.6367, 14694.927, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_835 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7624.6367, 14694.927, 0.00018310547];
};

_vehicle_836 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Wheel_cart_EP1", [7616.8564, 14687.345, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_836 = _this;
  _this setDir -49.031006;
  _this setPos [7616.8564, 14687.345, 3.0517578e-005];
};

_vehicle_872 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruiny_kopa_1m", [7614.6499, 14688.259, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_872 = _this;
  _this setPos [7614.6499, 14688.259, -4.5776367e-005];
};

_vehicle_876 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sara_domek_podhradi_1", [7482.3833, 14628.439, -0.14803836], [], 0, "CAN_COLLIDE"];
  _vehicle_876 = _this;
  _this setDir -89.431831;
  _this setVehicleInit "this setVectorUp [0,0,1]";
  _this setPos [7482.3833, 14628.439, -0.14803836];
};

_vehicle_881 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_spoj", [7485.8594, 14622.795, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_881 = _this;
  _this setPos [7485.8594, 14622.795, 0.00022888184];
};

_vehicle_885 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [7487.2251, 14625.262, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_885 = _this;
  _this setPos [7487.2251, 14625.262, -9.1552734e-005];
};

_vehicle_886 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [7483.7095, 14619.341, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_886 = _this;
  _this setPos [7483.7095, 14619.341, 9.1552734e-005];
};

_vehicle_887 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [7487.2559, 14619.294, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_887 = _this;
  _this setPos [7487.2559, 14619.294, 0.00019836426];
};

_vehicle_888 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [7495.0366, 14631.039, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_888 = _this;
  _this setPos [7495.0366, 14631.039, -0.00021362305];
};

_vehicle_891 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7480.8369, 14620.96, -0.51255894], [], 0, "CAN_COLLIDE"];
  _vehicle_891 = _this;
  _this setDir 0.073974587;
  _this setPos [7480.8369, 14620.96, -0.51255894];
};

_vehicle_893 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7483.9004, 14620.951, -0.48861927], [], 0, "CAN_COLLIDE"];
  _vehicle_893 = _this;
  _this setDir -0.10565592;
  _this setPos [7483.9004, 14620.951, -0.48861927];
};

_vehicle_895 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7477.6201, 14620.957, -0.46664679], [], 0, "CAN_COLLIDE"];
  _vehicle_895 = _this;
  _this setDir 0.099497117;
  _this setPos [7477.6201, 14620.957, -0.46664679];
};

_vehicle_897 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7486.5405, 14627.559, -0.47780627], [], 0, "CAN_COLLIDE"];
  _vehicle_897 = _this;
  _this setDir -88.584633;
  _this setPos [7486.5405, 14627.559, -0.47780627];
};

_vehicle_899 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7486.541, 14631.014, -0.48915669], [], 0, "CAN_COLLIDE"];
  _vehicle_899 = _this;
  _this setDir -91.00251;
  _this setPos [7486.541, 14631.014, -0.48915669];
};

_vehicle_901 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock", [7486.4854, 14624.057, -0.50081575], [], 0, "CAN_COLLIDE"];
  _vehicle_901 = _this;
  _this setDir -90.375336;
  _this setPos [7486.4854, 14624.057, -0.50081575];
};

_vehicle_904 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_sorbus2s", [7493.5615, 14635.247, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_904 = _this;
  _this setPos [7493.5615, 14635.247, -6.1035156e-005];
};

_vehicle_909 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fraxinus2s", [7511.4604, 14625.96, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_909 = _this;
  _this setPos [7511.4604, 14625.96, -9.1552734e-005];
};

_vehicle_958 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7475.5801, 14619.499, -0.14740998], [], 0, "CAN_COLLIDE"];
  _vehicle_958 = _this;
  _this setDir -119.56161;
  _this setPos [7475.5801, 14619.499, -0.14740998];
};

_vehicle_959 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7475.2505, 14617.111, -0.2578254], [], 0, "CAN_COLLIDE"];
  _vehicle_959 = _this;
  _this setDir -57.522194;
  _this setPos [7475.2505, 14617.111, -0.2578254];
};

_vehicle_960 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7475.2275, 14614.929, -0.22750394], [], 0, "CAN_COLLIDE"];
  _vehicle_960 = _this;
  _this setPos [7475.2275, 14614.929, -0.22750394];
};

_vehicle_961 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7487.9619, 14633.278, -0.23555775], [], 0, "CAN_COLLIDE"];
  _vehicle_961 = _this;
  _this setPos [7487.9619, 14633.278, -0.23555775];
};

_vehicle_962 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7490.4575, 14633.361, -0.31844887], [], 0, "CAN_COLLIDE"];
  _vehicle_962 = _this;
  _this setDir 45.235294;
  _this setPos [7490.4575, 14633.361, -0.31844887];
};

_vehicle_963 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7493.2461, 14633.187, -0.25876412], [], 0, "CAN_COLLIDE"];
  _vehicle_963 = _this;
  _this setDir -180.13431;
  _this setPos [7493.2461, 14633.187, -0.25876412];
};

_vehicle_964 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7495.5688, 14632.942, -0.2960161], [], 0, "CAN_COLLIDE"];
  _vehicle_964 = _this;
  _this setPos [7495.5688, 14632.942, -0.2960161];
};

_vehicle_965 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7496.3687, 14630.417, -0.23710012], [], 0, "CAN_COLLIDE"];
  _vehicle_965 = _this;
  _this setDir 108.14881;
  _this setPos [7496.3687, 14630.417, -0.23710012];
};

_vehicle_966 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7496.0713, 14627.669, -0.40361354], [], 0, "CAN_COLLIDE"];
  _vehicle_966 = _this;
  _this setDir -68.459251;
  _this setPos [7496.0713, 14627.669, -0.40361354];
};

_vehicle_967 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7496.4409, 14625.26, -0.3045238], [], 0, "CAN_COLLIDE"];
  _vehicle_967 = _this;
  _this setDir 133.34077;
  _this setPos [7496.4409, 14625.26, -0.3045238];
};

_vehicle_968 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7496.2231, 14622.381, -0.28316325], [], 0, "CAN_COLLIDE"];
  _vehicle_968 = _this;
  _this setDir -71.257149;
  _this setPos [7496.2231, 14622.381, -0.28316325];
};

_vehicle_969 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7496.3506, 14619.607, -0.31709015], [], 0, "CAN_COLLIDE"];
  _vehicle_969 = _this;
  _this setDir -126.38601;
  _this setPos [7496.3506, 14619.607, -0.31709015];
};

_vehicle_970 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7496.1948, 14617.094, -0.17020577], [], 0, "CAN_COLLIDE"];
  _vehicle_970 = _this;
  _this setDir -59.531792;
  _this setPos [7496.1948, 14617.094, -0.17020577];
};

_vehicle_971 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_betulaHumilis", [7496.397, 14614.682, -0.2277934], [], 0, "CAN_COLLIDE"];
  _vehicle_971 = _this;
  _this setDir 106.62646;
  _this setPos [7496.397, 14614.682, -0.2277934];
};
_vehicle_972 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7228.437, 14455.514, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_972 = _this;
  _this setDir 156.60007;
  _this setPos [7228.437, 14455.514, -6.1035156e-005];
};

_vehicle_974 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7262.0776, 14355.362, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_974 = _this;
  _this setDir -13.097034;
  _this setPos [7262.0776, 14355.362, 0.00021362305];
};

_vehicle_975 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7256.4238, 14379.586, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_975 = _this;
  _this setDir -17.460312;
  _this setPos [7256.4238, 14379.586, -0.00024414063];
};

_vehicle_976 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7248.9775, 14403.32, 0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_976 = _this;
  _this setDir -20.418653;
  _this setPos [7248.9775, 14403.32, 0.00033569336];
};

_vehicle_977 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7240.3315, 14426.692, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_977 = _this;
  _this setDir -22.201645;
  _this setPos [7240.3315, 14426.692, 9.1552734e-005];
};

_vehicle_979 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7309.207, 14161.974, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_979 = _this;
  _this setDir -13.704126;
  _this setPos [7309.207, 14161.974, 0.00018310547];
};

_vehicle_980 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7303.2896, 14186.152, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_980 = _this;
  _this setDir -13.704126;
  _this setPos [7303.2896, 14186.152, 0.00036621094];
};

_vehicle_981 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7297.4087, 14210.252, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_981 = _this;
  _this setDir -13.704126;
  _this setPos [7297.4087, 14210.252, 0.00018310547];
};

_vehicle_982 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7291.5508, 14234.321, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_982 = _this;
  _this setDir -13.704126;
  _this setPos [7291.5508, 14234.321, 9.1552734e-005];
};

_vehicle_983 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7285.6797, 14258.51, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_983 = _this;
  _this setDir -13.704126;
  _this setPos [7285.6797, 14258.51, 0.00036621094];
};

_vehicle_984 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7279.7051, 14282.775, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_984 = _this;
  _this setDir -13.704126;
  _this setPos [7279.7051, 14282.775, 0.00030517578];
};

_vehicle_985 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7273.8647, 14307.015, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_985 = _this;
  _this setDir -13.704126;
  _this setPos [7273.8647, 14307.015, -3.0517578e-005];
};

_vehicle_986 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7267.9951, 14331.106, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_986 = _this;
  _this setDir -13.704126;
  _this setPos [7267.9951, 14331.106, 9.1552734e-005];
};

_vehicle_987 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_30_25", [7315.0815, 14150.575, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_987 = _this;
  _this setDir -42.210976;
  _this setPos [7315.0815, 14150.575, 0.0002746582];
};

_vehicle_988 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_30_25", [7325.8813, 14143.667, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_988 = _this;
  _this setDir -72.217445;
  _this setPos [7325.8813, 14143.667, 0.00036621094];
};

_vehicle_989 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_30_25", [7338.7729, 14142.772, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_989 = _this;
  _this setDir -101.27359;
  _this setPos [7338.7729, 14142.772, -0.00018310547];
};

_vehicle_990 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7461.1729, 14165.971, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_990 = _this;
  _this setDir -100.74703;
  _this setPos [7461.1729, 14165.971, -0.00015258789];
};

_vehicle_991 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7436.6943, 14161.311], [], 0, "CAN_COLLIDE"];
  _vehicle_991 = _this;
  _this setDir -100.74703;
  _this setPos [7436.6943, 14161.311];
};

_vehicle_992 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7412.2778, 14156.681, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_992 = _this;
  _this setDir -100.74703;
  _this setPos [7412.2778, 14156.681, -9.1552734e-005];
};

_vehicle_993 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7387.7505, 14152.021, 0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_993 = _this;
  _this setDir -100.74703;
  _this setPos [7387.7505, 14152.021, 0.00033569336];
};

_vehicle_994 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7363.1865, 14147.385, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_994 = _this;
  _this setDir -100.74703;
  _this setPos [7363.1865, 14147.385, 0.00024414063];
};

_vehicle_995 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_22_50", [7461.0117, 14165.95, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_995 = _this;
  _this setDir 80.04467;
  _this setPos [7461.0117, 14165.95, 0.00015258789];
};

_vehicle_996 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7553.4019, 14148.979, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_996 = _this;
  _this setDir -77.201775;
  _this setPos [7553.4019, 14148.979, -0.00015258789];
};

_vehicle_997 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7529.1416, 14154.424, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_997 = _this;
  _this setDir -77.201775;
  _this setPos [7529.1416, 14154.424, -6.1035156e-005];
};

_vehicle_998 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7504.8584, 14159.996, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_998 = _this;
  _this setDir -77.201775;
  _this setPos [7504.8584, 14159.996, -9.1552734e-005];
};

_vehicle_999 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [7577.5771, 14142.862, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_999 = _this;
  _this setDir -75.773666;
  _this setPos [7577.5771, 14142.862, 9.1552734e-005];
};

_vehicle_1000 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [7583.5972, 14141.291, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1000 = _this;
  _this setDir -75.507561;
  _this setPos [7583.5972, 14141.291, 3.0517578e-005];
};
};