private ["_fee","_query3","_nameVK","_name","_item","_player","_playerID","_clientID","_able","_query","_result","_return","_playerUID","_class","_price","_query1","_query2"];

_player = _this select 0;
_item = _this select 1;
_playerID = getPlayerUID _player;
_clientID = owner _player;
_name = name _player;
_able = false;

_query = format["SELECT PlayerUID, Classname, Cost, Type, PlayerName FROM customTrade WHERE ID='%1'",_item];
_result = [_query, 2, true] call fn_asyncCall;

waitUntil {!isNil "_result"};

if (count _result > 0) then {
	//diag_log format["[O9 Custom Trading]: _result %1", _result];
	_able = true;
	_return = _result select 0;
	_playerUID = _return select 0;
	_class = _return select 1;
	_price = _return select 2;
	_nameVK = _return select 4;

	_query1 = format["DELETE FROM customTrade WHERE ID='%1'",_item];
	[_query1, 1, true] call fn_asyncCall;

	diag_log format["[O9 Custom Trading]: Player %4 (%1) bought %2 from %5 (%6) for %3 from the marketplace!",_playerID,_class,[_price] call BIS_fnc_numberText,_name,_nameVK,_playerUID];

	_fee = round(_price/10);
	//_query3 = format["UPDATE customValues SET CustomTradeFee = CustomTradeFee + '%1' WHERE type = 1",_fee];
	//[_query3, 1, false] call fn_asyncCall;

	_query2 = format["UPDATE player_data SET ClaimCoins = ClaimCoins + '%1' WHERE PlayerUID = '%2'",(_price - _fee), _playerUID];
	[_query2, 1, true] call fn_asyncCall;

	// Sigma Player Notify upon purchasing items
    _query3 = format["INSERT INTO player_notifications (player_uid, message) VALUES ('%1','%2 has purchased your %3 from the market for %4!')",_playerUID,_name,_class,[_price] call BIS_fnc_numberText];
    [_query3, 1, true] call fn_asyncCall;

} else {
	diag_log format["[O9 Custom Trading]: ID: %1 not found in Database.",_item];
};

PVDZE_buyItemResult = _able;

if (!isNull _player) then {
	_clientID publicVariableClient "PVDZE_buyItemResult";
};
