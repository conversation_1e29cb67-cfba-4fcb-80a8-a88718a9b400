if (isServer) then {
	_vehicle_1 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_fort_watchtower", [8344.3359, 3021.7349, 1.9073486e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_1 = _this;
	  _this setDir 131.16173;
	  _this setPos [8344.3359, 3021.7349, 1.9073486e-005];
	};

	_vehicle_3 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_fort_watchtower", [8357.6582, 2975.5134, -6.1988831e-006], [], 0, "CAN_COLLIDE"];
	  _vehicle_3 = _this;
	  _this setDir -47.344959;
	  _this setPos [8357.6582, 2975.5134, -6.1988831e-006];
	};

	_vehicle_6 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8344.2432, 3026.5344, -0.056892607], [], 0, "CAN_COLLIDE"];
	  _vehicle_6 = _this;
	  _this setDir -46.233116;
	  _this setPos [8344.2432, 3026.5344, -0.056892607];
	};

	_vehicle_7 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier3", [8337.9971, 3019.4565, -4.2915344e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_7 = _this;
	  _this setDir -47.628845;
	  _this setPos [8337.9971, 3019.4565, -4.2915344e-005];
	};

	_vehicle_9 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier3", [8327.9424, 3008.1248, 4.1484833e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_9 = _this;
	  _this setDir -47.628845;
	  _this setPos [8327.9424, 3008.1248, 4.1484833e-005];
	};

	_vehicle_12 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8324.0176, 3003.9453, 2.3841858e-006], [], 0, "CAN_COLLIDE"];
	  _vehicle_12 = _this;
	  _this setDir -46.233116;
	  _this setPos [8324.0176, 3003.9453, 2.3841858e-006];
	};

	_vehicle_14 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8320.1104, 2999.761, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
	  _vehicle_14 = _this;
	  _this setDir -46.233116;
	  _this setPos [8320.1104, 2999.761, -2.8610229e-006];
	};

	_vehicle_17 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier3", [8362.4277, 2975.9771, 5.9127808e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_17 = _this;
	  _this setDir -47.628845;
	  _this setPos [8362.4277, 2975.9771, 5.9127808e-005];
	};

	_vehicle_20 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8321.3105, 2999.0774, 5.1498413e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_20 = _this;
	  _this setDir 43.912319;
	  _this setPos [8321.3105, 2999.0774, 5.1498413e-005];
	};

	_vehicle_23 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8325.4619, 2995.1714, -1.8119812e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_23 = _this;
	  _this setDir 43.912319;
	  _this setPos [8325.4619, 2995.1714, -1.8119812e-005];
	};

	_vehicle_25 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8329.6543, 2991.2383, 4.863739e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_25 = _this;
	  _this setDir 43.912319;
	  _this setPos [8329.6543, 2991.2383, 4.863739e-005];
	};

	_vehicle_27 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8333.8291, 2987.2805, -7.0571899e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_27 = _this;
	  _this setDir 43.912319;
	  _this setPos [8333.8291, 2987.2805, -7.0571899e-005];
	};

	_vehicle_29 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8337.9805, 2983.3394, 3.528595e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_29 = _this;
	  _this setDir 43.912319;
	  _this setPos [8337.9805, 2983.3394, 3.528595e-005];
	};

	_vehicle_31 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8342.1279, 2979.4167, -1.6689301e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_31 = _this;
	  _this setDir 43.912319;
	  _this setPos [8342.1279, 2979.4167, -1.6689301e-005];
	};

	_vehicle_33 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8346.2725, 2975.4517, 1.3828278e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_33 = _this;
	  _this setDir 43.912319;
	  _this setPos [8346.2725, 2975.4517, 1.3828278e-005];
	};

	_vehicle_35 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8350.4424, 2971.5256, 0.00012254715], [], 0, "CAN_COLLIDE"];
	  _vehicle_35 = _this;
	  _this setDir 43.912319;
	  _this setPos [8350.4424, 2971.5256, 0.00012254715];
	};

	_vehicle_37 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8357.6709, 2970.5972, 9.059906e-006], [], 0, "CAN_COLLIDE"];
	  _vehicle_37 = _this;
	  _this setDir 134.35381;
	  _this setPos [8357.6709, 2970.5972, 9.059906e-006];
	};

	_vehicle_47 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8377.1729, 3001.2388, 0.081973545], [], 0, "CAN_COLLIDE"];
	  _vehicle_47 = _this;
	  _this setDir 43.912319;
	  _this setPos [8377.1729, 3001.2388, 0.081973545];
	};

	_vehicle_48 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8373.0439, 3005.2163, 0.016249796], [], 0, "CAN_COLLIDE"];
	  _vehicle_48 = _this;
	  _this setDir 43.912319;
	  _this setPos [8373.0439, 3005.2163, 0.016249796];
	};

	_vehicle_49 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8368.8711, 3009.1431, 0.038164839], [], 0, "CAN_COLLIDE"];
	  _vehicle_49 = _this;
	  _this setDir 43.912319;
	  _this setPos [8368.8711, 3009.1431, 0.038164839];
	};

	_vehicle_50 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8364.7275, 3013.0466, 0.090636298], [], 0, "CAN_COLLIDE"];
	  _vehicle_50 = _this;
	  _this setDir 43.912319;
	  _this setPos [8364.7275, 3013.0466, 0.090636298];
	};

	_vehicle_51 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8360.6328, 3017.0171, 0.16956258], [], 0, "CAN_COLLIDE"];
	  _vehicle_51 = _this;
	  _this setDir 43.912319;
	  _this setPos [8360.6328, 3017.0171, 0.16956258];
	};

	_vehicle_52 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8356.5127, 3020.9629, 0.19734702], [], 0, "CAN_COLLIDE"];
	  _vehicle_52 = _this;
	  _this setDir 43.912319;
	  _this setPos [8356.5127, 3020.9629, 0.19734702];
	};

	_vehicle_53 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8352.3984, 3024.8652, 0.20059307], [], 0, "CAN_COLLIDE"];
	  _vehicle_53 = _this;
	  _this setDir 43.912319;
	  _this setPos [8352.3984, 3024.8652, 0.20059307];
	};

	_vehicle_54 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8348.2734, 3028.7832, 0.17545108], [], 0, "CAN_COLLIDE"];
	  _vehicle_54 = _this;
	  _this setDir 43.912319;
	  _this setPos [8348.2734, 3028.7832, 0.17545108];
	};

	_vehicle_66 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier3", [8374.7471, 2989.6204, 0.068293869], [], 0, "CAN_COLLIDE"];
	  _vehicle_66 = _this;
	  _this setDir 130.9832;
	  _this setPos [8374.7471, 2989.6204, 0.068293869];
	};

	_vehicle_67 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8377.792, 2992.9729, 0.049855076], [], 0, "CAN_COLLIDE"];
	  _vehicle_67 = _this;
	  _this setDir 132.37894;
	  _this setPos [8377.792, 2992.9729, 0.049855076];
	};

	_vehicle_68 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_HBarrier5", [8381.4805, 2997.2642, 0.037555691], [], 0, "CAN_COLLIDE"];
	  _vehicle_68 = _this;
	  _this setDir 132.37894;
	  _this setPos [8381.4805, 2997.2642, 0.037555691];
	};

	_vehicle_72 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Sandbag1_DZ", [8334.21, 3014.3191, -1.001358e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_72 = _this;
	  _this setDir 43.088852;
	  _this setPos [8334.21, 3014.3191, -1.001358e-005];
	};

	_vehicle_74 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Sandbag1_DZ", [8336.416, 3012.2842, -2.3841858e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_74 = _this;
	  _this setDir 42.811665;
	  _this setPos [8336.416, 3012.2842, -2.3841858e-005];
	};

	_vehicle_81 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Sandbag1_DZ", [8366.3242, 2985.0764, 6.2942505e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_81 = _this;
	  _this setDir 42.811665;
	  _this setPos [8366.3242, 2985.0764, 6.2942505e-005];
	};

	_vehicle_83 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Sandbag1_DZ", [8368.6064, 2983.0801, 0.001625061], [], 0, "CAN_COLLIDE"];
	  _vehicle_83 = _this;
	  _this setDir 41.942276;
	  _this setPos [8368.6064, 2983.0801, 0.001625061];
	};

	_vehicle_89 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_GuardShed", [8330.7432, 3006.7676, 5.4359436e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_89 = _this;
	  _this setDir -138.5722;
	  _this setPos [8330.7432, 3006.7676, 5.4359436e-005];
	};

	_vehicle_91 = objNull;
	if (true) then
	{
	  _this = createVehicle ["MAP_GuardShed", [8371.7734, 2990.7434, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
	  _vehicle_91 = _this;
	  _this setDir 40.720768;
	  _this setPos [8371.7734, 2990.7434, 2.8610229e-006];
	};

	_vehicle_94 = objNull;
	if (true) then
	{
	  _this = createVehicle ["ZavoraAnim", [8332.9736, 3005.1975, -7.7724457e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_94 = _this;
	  _this setDir 130.99139;
	  _this setPos [8332.9736, 3005.1975, -7.7724457e-005];
	};

	_vehicle_97 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Sandbag1_DZ", [8331.5156, 3008.6423, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
	  _vehicle_97 = _this;
	  _this setDir 42.811665;
	  _this setPos [8331.5156, 3008.6423, -3.8146973e-006];
	};

	_vehicle_101 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_BagFenceCorner", [8333.4932, 3006.8225, -6.0081482e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_101 = _this;
	  _this setDir 43.287682;
	  _this setPos [8333.4932, 3006.8225, -6.0081482e-005];
	};

	_vehicle_105 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Sandbag1_DZ", [8371.1006, 2989.033, -1.3828278e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_105 = _this;
	  _this setDir -138.73334;
	  _this setPos [8371.1006, 2989.033, -1.3828278e-005];
	};

	_vehicle_106 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_BagFenceCorner", [8369.2148, 2990.7385, -0.00060367584], [], 0, "CAN_COLLIDE"];
	  _vehicle_106 = _this;
	  _this setDir -138.25731;
	  _this setPos [8369.2148, 2990.7385, -0.00060367584];
	};

	_vehicle_107 = objNull;
	if (true) then
	{
	  _this = createVehicle ["ZavoraAnim", [8369.7881, 2992.1731, 0.00072288513], [], 0, "CAN_COLLIDE"];
	  _vehicle_107 = _this;
	  _this setDir -50.553558;
	  _this setPos [8369.7881, 2992.1731, 0.00072288513];
	};

	_vehicle_111 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Sign_Checkpoint_EP1", [8384.7695, 2976.616, 3.2424927e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_111 = _this;
	  _this setDir -45.663235;
	  _this setPos [8384.7695, 2976.616, 3.2424927e-005];
	};

	_vehicle_113 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Sign_Checkpoint_EP1", [8318.1436, 3019.4731, 2.4795532e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_113 = _this;
	  _this setDir -45.663235;
	  _this setPos [8318.1436, 3019.4731, 2.4795532e-005];
	};

	_vehicle_116 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_ConcreteRamp2_Destructible", [8363.6309, 2983.3279, -1.4032081], [], 0, "CAN_COLLIDE"];
	  _vehicle_116 = _this;
	  _this setDir -46.968224;
	  _this setPos [8363.6309, 2983.3279, -1.4032081];
	};

	_vehicle_118 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_ConcreteRamp2_Destructible", [8339.8652, 3013.0964, -1.4344786], [], 0, "CAN_COLLIDE"];
	  _vehicle_118 = _this;
	  _this setDir 132.67992;
	  _this setPos [8339.8652, 3013.0964, -1.4344786];
	};

	_vehicle_120 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8375.541, 2985.7717, 6.6757202e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_120 = _this;
	  _this setPos [8375.541, 2985.7717, 6.6757202e-005];
	};

	_vehicle_123 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8378.2803, 2983.1785, 6.7234039e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_123 = _this;
	  _this setPos [8378.2803, 2983.1785, 6.7234039e-005];
	};

	_vehicle_125 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8380.5596, 2981.0054, 2.2411346e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_125 = _this;
	  _this setPos [8380.5596, 2981.0054, 2.2411346e-005];
	};

	_vehicle_127 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8382.8193, 2978.7522, 1.9550323e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_127 = _this;
	  _this setPos [8382.8193, 2978.7522, 1.9550323e-005];
	};

	_vehicle_133 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8373.7178, 2969.136, -2.3841858e-006], [], 0, "CAN_COLLIDE"];
	  _vehicle_133 = _this;
	  _this setDir -1.293418;
	  _this setPos [8373.7178, 2969.136, -2.3841858e-006];
	};

	_vehicle_134 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8371.4063, 2971.3396, 0.0037584305], [], 0, "CAN_COLLIDE"];
	  _vehicle_134 = _this;
	  _this setDir -1.293418;
	  _this setPos [8371.4063, 2971.3396, 0.0037584305];
	};

	_vehicle_135 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8369.0674, 2973.4614, 0.0074534416], [], 0, "CAN_COLLIDE"];
	  _vehicle_135 = _this;
	  _this setDir -1.293418;
	  _this setPos [8369.0674, 2973.4614, 0.0074534416];
	};

	_vehicle_136 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8366.2695, 2975.9902, 0.0091018677], [], 0, "CAN_COLLIDE"];
	  _vehicle_136 = _this;
	  _this setDir -1.293418;
	  _this setPos [8366.2695, 2975.9902, 0.0091018677];
	};

	_vehicle_141 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8327.5986, 3010.9971, 1.335144e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_141 = _this;
	  _this setDir -2.7475257;
	  _this setPos [8327.5986, 3010.9971, 1.335144e-005];
	};

	_vehicle_142 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8325.2227, 3013.134, -0.00055742264], [], 0, "CAN_COLLIDE"];
	  _vehicle_142 = _this;
	  _this setDir -2.7475257;
	  _this setPos [8325.2227, 3013.134, -0.00055742264];
	};

	_vehicle_143 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8322.8408, 3015.2, -0.0011482239], [], 0, "CAN_COLLIDE"];
	  _vehicle_143 = _this;
	  _this setDir -2.7475257;
	  _this setPos [8322.8408, 3015.2, -0.0011482239];
	};

	_vehicle_144 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8319.9678, 3017.6553, -0.002114296], [], 0, "CAN_COLLIDE"];
	  _vehicle_144 = _this;
	  _this setDir -2.7475257;
	  _this setPos [8319.9678, 3017.6553, -0.002114296];
	};

	_vehicle_149 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8336.6172, 3021.261, -2.5272369e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_149 = _this;
	  _this setDir -1.7745272;
	  _this setPos [8336.6172, 3021.261, -2.5272369e-005];
	};

	_vehicle_150 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8334.2803, 3023.4414, -0.022787571], [], 0, "CAN_COLLIDE"];
	  _vehicle_150 = _this;
	  _this setDir -1.7745272;
	  _this setPos [8334.2803, 3023.4414, -0.022787571];
	};

	_vehicle_151 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8331.9443, 3025.5442, -0.049402714], [], 0, "CAN_COLLIDE"];
	  _vehicle_151 = _this;
	  _this setDir -1.7745272;
	  _this setPos [8331.9443, 3025.5442, -0.049402714];
	};

	_vehicle_152 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Hedgehog_DZ", [8329.1162, 3028.0518, -0.12585115], [], 0, "CAN_COLLIDE"];
	  _vehicle_152 = _this;
	  _this setDir -1.7745272;
	  _this setPos [8329.1162, 3028.0518, -0.12585115];
	};

	_vehicle_159 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_CamoNetVar_NATO", [8328.9385, 2999.0493, 8.1062317e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_159 = _this;
	  _this setDir 41.934593;
	  _this setPos [8328.9385, 2999.0493, 8.1062317e-005];
	};

	_vehicle_161 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_CamoNetVar_NATO", [8372.8584, 2997.0549, 8.5830688e-006], [], 0, "CAN_COLLIDE"];
	  _vehicle_161 = _this;
	  _this setDir 41.934593;
	  _this setPos [8372.8584, 2997.0549, 8.5830688e-006];
	};

	_vehicle_163 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Misc_Cargo1Bo_military", [8369.6338, 3003.4526, 3.528595e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_163 = _this;
	  _this setDir -51.326473;
	  _this setPos [8369.6338, 3003.4526, 3.528595e-005];
	};

	_vehicle_164 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_Misc_Cargo1Eo_EP1", [8363.665, 3009.6011, 5.531311e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_164 = _this;
	  _this setDir -35.091797;
	  _this setPos [8363.665, 3009.6011, 5.531311e-005];
	};

	_vehicle_165 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Misc_cargo_cont_small", [8363.1797, 3003.8894, 3.194809e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_165 = _this;
	  _this setDir -6.3768458;
	  _this setPos [8363.1797, 3003.8894, 3.194809e-005];
	};

	_vehicle_167 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_A_tent", [8324.873, 3001.1101, 2.5749207e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_167 = _this;
	  _this setDir 276.04337;
	  _this setPos [8324.873, 3001.1101, 2.5749207e-005];
	};

	_vehicle_169 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_A_tent", [8326.0879, 2998.3994, 5.7220459e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_169 = _this;
	  _this setDir 221.97527;
	  _this setPos [8326.0879, 2998.3994, 5.7220459e-005];
	};

	_vehicle_171 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_A_tent", [8328.9404, 2995.3965, 4.2915344e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_171 = _this;
	  _this setDir 176.42053;
	  _this setPos [8328.9404, 2995.3965, 4.2915344e-005];
	};

	_vehicle_173 = objNull;
	if (true) then
	{
	  _this = createVehicle ["Land_tent_east", [8350.4805, 2983.6055, 4.6253204e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_173 = _this;
	  _this setDir 133.79227;
	  _this setPos [8350.4805, 2983.6055, 4.6253204e-005];
	};

	_vehicle_176 = objNull;
	if (true) then
	{
	  _this = createVehicle ["HMMWVWreck", [8375.2832, 2997.5847, 6.3419342e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_176 = _this;
	  _this setDir -259.9071;
	  _this setPos [8375.2832, 2997.5847, 6.3419342e-005];
	};

	_vehicle_184 = objNull;
	if (true) then
	{
	  _this = createVehicle ["TK_GUE_WarfareBVehicleServicePoint_EP1", [8340.4707, 2991.4854, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
	  _vehicle_184 = _this;
	  _this setDir -318.38672;
	  _this setPos [8340.4707, 2991.4854, -2.8610229e-006];
	};

	_vehicle_186 = objNull;
	if (true) then
	{
	  _this = createVehicle ["HeliHCivil", [8353.1338, 3013.2515, 4.1484833e-005], [], 0, "CAN_COLLIDE"];
	  _vehicle_186 = _this;
	  _this setDir 40.463921;
	  _this setPos [8353.1338, 3013.2515, 4.1484833e-005];
	};
};