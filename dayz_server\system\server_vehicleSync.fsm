/*%FSM<COMPILE "scriptedFSM.cfg, DayZ Server Vehicle Sync">*/
/*%FSM<HEAD>*/
/*
item0[] = {"init",0,250,-75.000000,-425.000000,25.000000,-375.000000,0.000000,"init"};
item1[] = {"true",8,218,-75.000000,-200.000000,25.000000,-150.000000,0.000000,"true"};
item2[] = {"waiting",2,250,-75.000000,-125.000000,25.000000,-75.000000,0.000000,"waiting"};
item3[] = {"true",8,218,-75.000000,250.000000,25.000000,300.000000,0.000000,"true"};
item4[] = {"",7,210,-204.000015,271.000000,-195.999985,279.000031,0.000000,""};
item5[] = {"",7,210,-204.000015,-104.000000,-196.000000,-96.000000,0.000000,""};
item6[] = {"initialized",4,218,-75.000000,-350.000000,25.000000,-300.000000,0.000000,"initialized"};
item7[] = {"prepare",2,4346,-75.000000,-275.000000,25.000000,-225.000000,0.000000,"prepare"};
item8[] = {"update_objects",2,250,-75.220673,77.428261,24.779324,127.428261,0.000000,"update objects"};
item9[] = {"Objects_need_upd",4,218,-75.220726,-1.214130,24.779320,48.785873,1.000000,"Objects" \n "need update"};
link0[] = {0,6};
link1[] = {1,2};
link2[] = {2,9};
link3[] = {3,4};
link4[] = {4,5};
link5[] = {5,2};
link6[] = {6,7};
link7[] = {7,1};
link8[] = {8,3};
link9[] = {9,8};
globals[] = {0.000000,0,0,0,0,640,480,1,15,6316128,1,-285.818726,406.559204,464.760406,-344.050812,779,910,1};
window[] = {2,-1,-1,-1,-1,858,130,1459,130,3,797};
*//*%FSM</HEAD>*/
class FSM
{
  fsmName = "DayZ Server Vehicle Sync";
  class States
  {
    /*%FSM<STATE "init">*/
    class init
    {
      name = "init";
      init = /*%FSM<STATEINIT""">*/""/*%FSM</STATEINIT""">*/;
      precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
      class Links
      {
        /*%FSM<LINK "initialized">*/
        class initialized
        {
          priority = 0.000000;
          to="prepare";
          precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
          condition=/*%FSM<CONDITION""">*/"!isnil ""bis_fnc_init"""/*%FSM</CONDITION""">*/;
          action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
        };
        /*%FSM</LINK>*/
      };
    };
    /*%FSM</STATE>*/
    /*%FSM<STATE "waiting">*/
    class waiting
    {
      name = "waiting";
      init = /*%FSM<STATEINIT""">*/""/*%FSM</STATEINIT""">*/;
      precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
      class Links
      {
        /*%FSM<LINK "Objects_need_upd">*/
        class Objects_need_upd
        {
          priority = 1.000000;
          to="update_objects";
          precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
          condition=/*%FSM<CONDITION""">*/"(( (count needUpdate_objects) > 0) && (diag_tickTime -_lastVehicleUpdate> 5) && (!isNil ""sm_done""))"/*%FSM</CONDITION""">*/;
          action=/*%FSM<ACTION""">*/"_lastNeedUpdate = diag_tickTime;"/*%FSM</ACTION""">*/;
        };
        /*%FSM</LINK>*/
      };
    };
    /*%FSM</STATE>*/
    /*%FSM<STATE "prepare">*/
    class prepare
    {
      name = "prepare";
      init = /*%FSM<STATEINIT""">*/"diag_log (""CLEANUP: INITIALIZING Vehicle SCRIPT"");" \n
       "" \n
       "_lastVehicleUpdate = diag_tickTime;" \n
       "" \n
       "" \n
       ""/*%FSM</STATEINIT""">*/;
      precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
      class Links
      {
        /*%FSM<LINK "true">*/
        class true
        {
          priority = 0.000000;
          to="waiting";
          precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
          condition=/*%FSM<CONDITION""">*/"true"/*%FSM</CONDITION""">*/;
          action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
        };
        /*%FSM</LINK>*/
      };
    };
    /*%FSM</STATE>*/
    /*%FSM<STATE "update_objects">*/
    class update_objects
    {
      name = "update_objects";
      init = /*%FSM<STATEINIT""">*/"if (object_debug) then {" \n
       "	diag_log format[""INFO: needUpdate_objects=%1"",needUpdate_objects];" \n
       "};" \n
       "" \n
       "{" \n
       "	needUpdate_objects = needUpdate_objects - [_x];" \n
       "	[_x,""damage"",true] call server_updateObject;" \n
       "} forEach needUpdate_objects;" \n
       ""/*%FSM</STATEINIT""">*/;
      precondition = /*%FSM<STATEPRECONDITION""">*/""/*%FSM</STATEPRECONDITION""">*/;
      class Links
      {
        /*%FSM<LINK "true">*/
        class true
        {
          priority = 0.000000;
          to="waiting";
          precondition = /*%FSM<CONDPRECONDITION""">*/""/*%FSM</CONDPRECONDITION""">*/;
          condition=/*%FSM<CONDITION""">*/"true"/*%FSM</CONDITION""">*/;
          action=/*%FSM<ACTION""">*/""/*%FSM</ACTION""">*/;
        };
        /*%FSM</LINK>*/
      };
    };
    /*%FSM</STATE>*/
  };
  initState="init";
  finalStates[] =
  {
  };
};
/*%FSM</COMPILE>*/