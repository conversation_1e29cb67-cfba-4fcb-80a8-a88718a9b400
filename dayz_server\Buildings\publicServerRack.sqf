 if (isServer) then {

// Stary Trader
_serverRack1 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [6309.85, 7795.99, 0.00146484], [], 0, "CAN_COLLIDE"];
  _serverRack1 = _this;
  _this setDir 35;
  _this setPos [6309.85, 7795.99, 0.00146484];
};

// Bash Trader
_serverRack2 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [4045.83, 11666.9, 0.00131226], [], 0, "CAN_COLLIDE"];
  _serverRack2 = _this;
  _this setDir 202;
  _this setPos [4045.83, 11666.9, 0.00131226];
};

// Staroye Trader
_serverRack3 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [9908.36, 5433.83, 0.00143433], [], 0, "CAN_COLLIDE"];
  _serverRack3 = _this;
  _this setDir 280;
  _this setPos [9908.36, 5433.83, 0.00143433];
};

// Kozlovka Trader
_serverRack4 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [4722.88, 4693.13, 0.00137329], [], 0, "CAN_COLLIDE"];
  _serverRack4 = _this;
  _this setDir 90;
  _this setPos [4722.88, 4693.13, 0.00137329];
};

// Klen Trader
_serverRack5 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [11451.2, 11352.1, 0.00146484], [], 0, "CAN_COLLIDE"];
  _serverRack5 = _this;
  _this setDir 310;
  _this setPos [11451.2, 11352.1, 0.00146484];
};

// Northwest Airfield Trader
_serverRack6 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [5039.7, 9706.2, 0.00143433], [], 0, "CAN_COLLIDE"];
  _serverRack6 = _this;
  _this setDir 140;
  _this setPos [5039.7, 9706.2, 0.00143433];
};

// Northeast Airfield Trader
_serverRack7 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [12059.4, 12637, 0.0584259], [], 0, "CAN_COLLIDE"];
  _serverRack7 = _this;
  _this setDir 200;
  _this setPos [12059.4, 12637, 0.0584259];
};

// Western Hero Trader
_serverRack8 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [1624.81, 7799.7, 0.00137329], [], 0, "CAN_COLLIDE"];
  _serverRack8 = _this;
  _this setDir 106;
  _this setPos [1624.81, 7799.7, 0.00137329];
};

// Eastern Hero Trader
_serverRack9 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [12943.8, 12767.3, 0.00161743], [], 0, "CAN_COLLIDE"];
  _serverRack9 = _this;
  _this setDir 360;
  _this setPos [12943.8, 12767.3, 0.00161743];
};

// Super Hero Trader
_serverRack10 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [2134.81, 14929.9, 0.0012207], [], 0, "CAN_COLLIDE"];
  _serverRack10 = _this;
  _this setDir 195;
  _this setPos [2134.81, 14929.9, 0.0012207];
};

// Bunny's Trader
_serverRack11 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [4631.38, 2511.74, 0.00143623], [], 0, "CAN_COLLIDE"];
  _serverRack11 = _this;
  _this setDir 210;
  _this setPos [4631.38, 2511.74, 0.00143623];
};

// Th3-Hunter333's Creations
_serverRack12 = objNull;
if (true) then
{
  _this = createVehicle ["Server_Rack_DZ", [9302.78, 8884.32, 0.00140381], [], 0, "CAN_COLLIDE"];
  _serverRack12 = _this;
  _this setDir 143;
  _this setPos [9302.78, 8884.32, 0.00140381];
};

};