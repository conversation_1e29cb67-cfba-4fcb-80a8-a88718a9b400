if (isServer) then {

_vehicle_2 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Tovarna2", [4679.3755, 10685.335, 0.3680104], [], 0, "CAN_COLLIDE"];
  _vehicle_2 = _this;
  _this setDir -485.71207;
  _this setPos [4679.3755, 10685.335, 0.3680104];
};

_vehicle_3 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4635.3262, 10494.984], [], 0, "CAN_COLLIDE"];
  _vehicle_3 = _this;
  _this setDir 58.469273;
  _this setPos [4635.3262, 10494.984];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4642.998, 10499.51], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setDir 58.816505;
  _this setPos [4642.998, 10499.51];
};

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_L", [4578.6309, 10650.158, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setDir -30.042166;
  _this setPos [4578.6309, 10650.158, 3.0517578e-005];
};

_vehicle_8 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [4616.686, 10641.499, 0.048289966], [], 0, "CAN_COLLIDE"];
  _vehicle_8 = _this;
  _this setDir 145.31482;
  _this setPos [4616.686, 10641.499, 0.048289966];
};

_vehicle_10 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [4596.0137, 10663.552, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_10 = _this;
  _this setDir -30.343496;
  _this setPos [4596.0137, 10663.552, 3.0517578e-005];
};

_vehicle_13 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_1_1000", [4633.0464, 10669.689, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_13 = _this;
  _this setDir 56.812824;
  _this setPos [4633.0464, 10669.689, 6.1035156e-005];
};

_vehicle_14 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [4653.0117, 10682.374], [], 0, "CAN_COLLIDE"];
  _vehicle_14 = _this;
  _this setDir -121.79391;
  _this setPos [4653.0117, 10682.374];
};

_vehicle_16 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_1_1000", [4618.4927, 10660.053, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_16 = _this;
  _this setDir 55.943302;
  _this setPos [4618.4927, 10660.053, -3.0517578e-005];
};

_vehicle_19 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_1_1000", [4604.1504, 10650.17], [], 0, "CAN_COLLIDE"];
  _vehicle_19 = _this;
  _this setDir 55.11821;
  _this setPos [4604.1504, 10650.17];
};

_vehicle_20 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_1_1000", [4589.9922, 10639.998, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_20 = _this;
  _this setDir 54.031441;
  _this setPos [4589.9922, 10639.998, 3.0517578e-005];
};

_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_1_1000", [4575.7954, 10629.92, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir 54.067898;
  _this setPos [4575.7954, 10629.92, -6.1035156e-005];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_50", [4575.79, 10629.982, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir -126.09648;
  _this setPos [4575.79, 10629.982, -3.0517578e-005];
};

_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [4562.6875, 10622.807], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir 63.83165;
  _this setPos [4562.6875, 10622.807];
};

_vehicle_24 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Guardhouse", [4682.3184, 10722.175, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_24 = _this;
  _this setDir -124.49391;
  _this setPos [4682.3184, 10722.175, 6.1035156e-005];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_House", [4613.457, 10692.161], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir 60.641354;
  _this setPos [4613.457, 10692.161];
};

_vehicle_30 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder_half_EP1", [4612.8516, 10680.497, 1.0996909], [], 0, "CAN_COLLIDE"];
  _vehicle_30 = _this;
  _this setDir -29.988358;
  _this setPos [4612.8516, 10680.497, 1.0996909];
};

_vehicle_32 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder_half_EP1", [4608.4834, 10689.402, 5.3464332], [], 0, "CAN_COLLIDE"];
  _vehicle_32 = _this;
  _this setDir -30.571529;
  _this setPos [4608.4834, 10689.402, 5.3464332];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4592.9575, 10636.606], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir -212.69406;
  _this setPos [4592.9575, 10636.606];
};

_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4599.7695, 10641.156, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir -212.69406;
  _this setPos [4599.7695, 10641.156, -3.0517578e-005];
};

_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4603.1328, 10643.278], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setDir -212.69406;
  _this setPos [4603.1328, 10643.278];
};

_vehicle_41 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4606.4956, 10645.419], [], 0, "CAN_COLLIDE"];
  _vehicle_41 = _this;
  _this setDir -212.69406;
  _this setPos [4606.4956, 10645.419];
};

_vehicle_43 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4609.8433, 10647.55, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_43 = _this;
  _this setDir -212.69406;
  _this setPos [4609.8433, 10647.55, 3.0517578e-005];
};

_vehicle_45 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4613.2036, 10649.703], [], 0, "CAN_COLLIDE"];
  _vehicle_45 = _this;
  _this setDir -212.69406;
  _this setPos [4613.2036, 10649.703];
};

_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4569.6714, 10647.563, -0.10266466], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setDir -300.19858;
  _this setPos [4569.6714, 10647.563, -0.10266466];
};

_vehicle_50 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4568.8457, 10651.212, -0.10204525], [], 0, "CAN_COLLIDE"];
  _vehicle_50 = _this;
  _this setDir -265.25601;
  _this setPos [4568.8457, 10651.212, -0.10204525];
};

_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4571.644, 10644.104, -0.068259574], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setDir -300.19858;
  _this setPos [4571.644, 10644.104, -0.068259574];
};

_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4573.6465, 10640.662, -0.10264716], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setDir -300.19858;
  _this setPos [4573.6465, 10640.662, -0.10264716];
};

_vehicle_58 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4576.5332, 10638.414, -0.093092673], [], 0, "CAN_COLLIDE"];
  _vehicle_58 = _this;
  _this setDir -344.5278;
  _this setPos [4576.5332, 10638.414, -0.093092673];
};

_vehicle_60 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4580.0537, 10638.967, -0.091012985], [], 0, "CAN_COLLIDE"];
  _vehicle_60 = _this;
  _this setDir -393.92703;
  _this setPos [4580.0537, 10638.967, -0.091012985];
};

_vehicle_67 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4599.8608, 10652.648, -0.056642875], [], 0, "CAN_COLLIDE"];
  _vehicle_67 = _this;
  _this setDir -394.90887;
  _this setPos [4599.8608, 10652.648, -0.056642875];
};

_vehicle_68 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4596.5815, 10650.408, -0.051973686], [], 0, "CAN_COLLIDE"];
  _vehicle_68 = _this;
  _this setDir -394.90887;
  _this setPos [4596.5815, 10650.408, -0.051973686];
};

_vehicle_69 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4593.3179, 10648.136, -0.050508842], [], 0, "CAN_COLLIDE"];
  _vehicle_69 = _this;
  _this setDir -394.90887;
  _this setPos [4593.3179, 10648.136, -0.050508842];
};

_vehicle_70 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4590.0376, 10645.88, -0.050478324], [], 0, "CAN_COLLIDE"];
  _vehicle_70 = _this;
  _this setDir -394.90887;
  _this setPos [4590.0376, 10645.88, -0.050478324];
};

_vehicle_71 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4586.7632, 10643.603, -0.051454887], [], 0, "CAN_COLLIDE"];
  _vehicle_71 = _this;
  _this setDir -394.90887;
  _this setPos [4586.7632, 10643.603, -0.051454887];
};

_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4616.2568, 10663.945, -0.044608735], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setDir -394.90887;
  _this setPos [4616.2568, 10663.945, -0.044608735];
};

_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4612.9775, 10661.704, -0.040488865], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setDir -394.90887;
  _this setPos [4612.9775, 10661.704, -0.040488865];
};

_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4609.7139, 10659.43, -0.040977146], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setDir -394.90887;
  _this setPos [4609.7139, 10659.43, -0.040977146];
};

_vehicle_85 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4606.4336, 10657.176, -0.038016941], [], 0, "CAN_COLLIDE"];
  _vehicle_85 = _this;
  _this setDir -394.90887;
  _this setPos [4606.4336, 10657.176, -0.038016941];
};

_vehicle_86 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4603.1592, 10654.898, -0.037864354], [], 0, "CAN_COLLIDE"];
  _vehicle_86 = _this;
  _this setDir -394.90887;
  _this setPos [4603.1592, 10654.898, -0.037864354];
};

_vehicle_104 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4637.9219, 10678.986, -0.099304199], [], 0, "CAN_COLLIDE"];
  _vehicle_104 = _this;
  _this setDir -394.90887;
  _this setPos [4637.9219, 10678.986, -0.099304199];
};

_vehicle_105 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4634.6426, 10676.748, -0.10147095], [], 0, "CAN_COLLIDE"];
  _vehicle_105 = _this;
  _this setDir -394.90887;
  _this setPos [4634.6426, 10676.748, -0.10147095];
};

_vehicle_106 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4631.3789, 10674.479, -0.10568237], [], 0, "CAN_COLLIDE"];
  _vehicle_106 = _this;
  _this setDir -394.90887;
  _this setPos [4631.3789, 10674.479, -0.10568237];
};

_vehicle_107 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4628.0986, 10672.222, -0.10321045], [], 0, "CAN_COLLIDE"];
  _vehicle_107 = _this;
  _this setDir -394.90887;
  _this setPos [4628.0986, 10672.222, -0.10321045];
};

_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4624.8242, 10669.94, -0.10412598], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setDir -394.90887;
  _this setPos [4624.8242, 10669.94, -0.10412598];
};

_vehicle_113 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4641.2202, 10681.236, -0.080535889], [], 0, "CAN_COLLIDE"];
  _vehicle_113 = _this;
  _this setDir -394.90887;
  _this setPos [4641.2202, 10681.236, -0.080535889];
};

_vehicle_131 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4616.541, 10651.868, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_131 = _this;
  _this setDir -212.69406;
  _this setPos [4616.541, 10651.868, 3.0517578e-005];
};

_vehicle_132 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4623.353, 10656.418, 0.0007019043], [], 0, "CAN_COLLIDE"];
  _vehicle_132 = _this;
  _this setDir -212.69406;
  _this setPos [4623.353, 10656.418, 0.0007019043];
};

_vehicle_133 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4626.7163, 10658.54, 0.0062561035], [], 0, "CAN_COLLIDE"];
  _vehicle_133 = _this;
  _this setDir -212.69406;
  _this setPos [4626.7163, 10658.54, 0.0062561035];
};

_vehicle_134 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4630.3794, 10659.536, 0.00061035156], [], 0, "CAN_COLLIDE"];
  _vehicle_134 = _this;
  _this setDir -177.29172;
  _this setPos [4630.3794, 10659.536, 0.00061035156];
};

_vehicle_144 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4633.5234, 10657.872, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_144 = _this;
  _this setDir -126.90449;
  _this setPos [4633.5234, 10657.872, 3.0517578e-005];
};

_vehicle_147 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4635.9053, 10654.688, -0.023081258], [], 0, "CAN_COLLIDE"];
  _vehicle_147 = _this;
  _this setDir -126.90449;
  _this setPos [4635.9053, 10654.688, -0.023081258];
};

_vehicle_149 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4638.2793, 10651.507, -0.058385246], [], 0, "CAN_COLLIDE"];
  _vehicle_149 = _this;
  _this setDir -126.90449;
  _this setPos [4638.2793, 10651.507, -0.058385246];
};

_vehicle_152 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4638.8877, 10648.042, -0.056378052], [], 0, "CAN_COLLIDE"];
  _vehicle_152 = _this;
  _this setDir -73.435677;
  _this setPos [4638.8877, 10648.042, -0.056378052];
};

_vehicle_155 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4637.7476, 10644.212, -0.033880785], [], 0, "CAN_COLLIDE"];
  _vehicle_155 = _this;
  _this setDir -73.435677;
  _this setPos [4637.7476, 10644.212, -0.033880785];
};

_vehicle_157 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4636.5889, 10640.409, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_157 = _this;
  _this setDir -73.435677;
  _this setPos [4636.5889, 10640.409, -3.0517578e-005];
};

_vehicle_159 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4634.3501, 10637.405, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_159 = _this;
  _this setDir -34.009525;
  _this setPos [4634.3501, 10637.405, -3.0517578e-005];
};

_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4631.0391, 10635.159, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setDir -34.009525;
  _this setPos [4631.0391, 10635.159, 3.0517578e-005];
};

_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4641.7363, 10683.973, -0.0888981], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setDir -483.32059;
  _this setPos [4641.7363, 10683.973, -0.0888981];
};

_vehicle_170 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4_2", [4639.4795, 10687.26, -0.10139208], [], 0, "CAN_COLLIDE"];
  _vehicle_170 = _this;
  _this setDir -125.81089;
  _this setPos [4639.4795, 10687.26, -0.10139208];
};

_vehicle_174 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4637.2354, 10690.552, -0.092050329], [], 0, "CAN_COLLIDE"];
  _vehicle_174 = _this;
  _this setDir -483.32059;
  _this setPos [4637.2354, 10690.552, -0.092050329];
};

_vehicle_183 = objNull;
if (true) then
{
  _this = createVehicle ["Land_a_stationhouse", [4647.4092, 10718.758, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_183 = _this;
  _this setDir 56.651588;
  _this setPos [4647.4092, 10718.758, -3.0517578e-005];
};

_vehicle_187 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Vysypka", [4688.6553, 10625.627], [], 0, "CAN_COLLIDE"];
  _vehicle_187 = _this;
  _this setDir -192.43735;
  _this setPos [4688.6553, 10625.627];
};

_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4658.0166, 10687.804], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setDir -651.08875;
  _this setPos [4658.0166, 10687.804];
};

_vehicle_192 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4659.4385, 10684.085], [], 0, "CAN_COLLIDE"];
  _vehicle_192 = _this;
  _this setDir -651.08875;
  _this setPos [4659.4385, 10684.085];
};

_vehicle_194 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4658.9238, 10690.821], [], 0, "CAN_COLLIDE"];
  _vehicle_194 = _this;
  _this setDir -575.38196;
  _this setPos [4658.9238, 10690.821];
};

_vehicle_197 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4662.1733, 10693.133, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_197 = _this;
  _this setDir -575.38196;
  _this setPos [4662.1733, 10693.133, 3.0517578e-005];
};

_vehicle_200 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4660.8428, 10680.373, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_200 = _this;
  _this setDir -651.08875;
  _this setPos [4660.8428, 10680.373, -3.0517578e-005];
};

_vehicle_203 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4662.2578, 10676.675, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_203 = _this;
  _this setDir -651.08875;
  _this setPos [4662.2578, 10676.675, -3.0517578e-005];
};

_vehicle_205 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4663.6807, 10672.967, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_205 = _this;
  _this setDir -651.08875;
  _this setPos [4663.6807, 10672.967, -3.0517578e-005];
};

_vehicle_208 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4652.4771, 10697.197], [], 0, "CAN_COLLIDE"];
  _vehicle_208 = _this;
  _this setDir -659.14758;
  _this setPos [4652.4771, 10697.197];
};

_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4655.0967, 10696.538, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setDir -753.3031;
  _this setPos [4655.0967, 10696.538, -3.0517578e-005];
};

_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4658.3477, 10698.84, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setDir -758.13507;
  _this setPos [4658.3477, 10698.84, 3.0517578e-005];
};

_vehicle_218 = objNull;
if (true) then
{
  _this = createVehicle ["Land_pumpa", [4614.8457, 10672.701, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_218 = _this;
  _this setDir 109.81995;
  _this setPos [4614.8457, 10672.701, -9.1552734e-005];
};

_vehicle_220 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_BoardsPack1", [4644.2622, 10684.398, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_220 = _this;
  _this setDir 56.093327;
  _this setPos [4644.2622, 10684.398, -3.0517578e-005];
};

_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_BoardsPack2", [4642.2627, 10687.259], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setDir 57.405113;
  _this setPos [4642.2627, 10687.259];
};

_vehicle_223 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_BoardsPack1", [4643.2969, 10685.773, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_223 = _this;
  _this setDir 56.093327;
  _this setPos [4643.2969, 10685.773, 6.1035156e-005];
};

_vehicle_226 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_BoardsPack2", [4641.3057, 10688.731, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_226 = _this;
  _this setDir 57.405113;
  _this setPos [4641.3057, 10688.731, 3.0517578e-005];
};

_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_BoardsPack2", [4640.0381, 10690.473, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setDir 42.692558;
  _this setPos [4640.0381, 10690.473, -3.0517578e-005];
};

_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_Timbers", [4661.415, 10609.518, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setDir -10.082105;
  _this setPos [4661.415, 10609.518, -3.0517578e-005];
};

_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_Timbers", [4651.6284, 10620.257, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setDir -51.324551;
  _this setPos [4651.6284, 10620.257, -3.0517578e-005];
};

_vehicle_237 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4667.2798, 10663.669, -0.10195228], [], 0, "CAN_COLLIDE"];
  _vehicle_237 = _this;
  _this setDir -651.08875;
  _this setPos [4667.2798, 10663.669, -0.10195228];
};

_vehicle_240 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4668.7119, 10659.979, -0.12376884], [], 0, "CAN_COLLIDE"];
  _vehicle_240 = _this;
  _this setDir -651.08875;
  _this setPos [4668.7119, 10659.979, -0.12376884];
};

_vehicle_243 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4671.2334, 10658.864, -0.11432333], [], 0, "CAN_COLLIDE"];
  _vehicle_243 = _this;
  _this setDir -741.33099;
  _this setPos [4671.2334, 10658.864, -0.11432333];
};

_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4674.9805, 10660.296, -0.089680158], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setDir -741.33099;
  _this setPos [4674.9805, 10660.296, -0.089680158];
};

_vehicle_249 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4678.6958, 10661.737, -0.07603801], [], 0, "CAN_COLLIDE"];
  _vehicle_249 = _this;
  _this setDir -741.33099;
  _this setPos [4678.6958, 10661.737, -0.07603801];
};

_vehicle_252 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4682.4194, 10663.193, -0.09126167], [], 0, "CAN_COLLIDE"];
  _vehicle_252 = _this;
  _this setDir -741.33099;
  _this setPos [4682.4194, 10663.193, -0.09126167];
};

_vehicle_255 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4686.147, 10664.607, -0.081741206], [], 0, "CAN_COLLIDE"];
  _vehicle_255 = _this;
  _this setDir -741.33099;
  _this setPos [4686.147, 10664.607, -0.081741206];
};

_vehicle_259 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel1", [4603.0234, 10670.118, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_259 = _this;
  _this setPos [4603.0234, 10670.118, -3.0517578e-005];
};

_vehicle_260 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_cargo_cont_net1", [4620.5767, 10696.55, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_260 = _this;
  _this setDir -29.44343;
  _this setPos [4620.5767, 10696.55, 6.1035156e-005];
};

_vehicle_261 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_cargo_cont_net2", [4638.2388, 10683.247, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_261 = _this;
  _this setPos [4638.2388, 10683.247, -6.1035156e-005];
};

_vehicle_262 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_concrete", [4636.5464, 10649.894, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_262 = _this;
  _this setDir -124.53413;
  _this setPos [4636.5464, 10649.894, -3.0517578e-005];
};

_vehicle_264 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_concrete", [4634.9531, 10651.647], [], 0, "CAN_COLLIDE"];
  _vehicle_264 = _this;
  _this setDir -128.86902;
  _this setPos [4634.9531, 10651.647];
};

_vehicle_267 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_concrete", [4633.3804, 10653.925], [], 0, "CAN_COLLIDE"];
  _vehicle_267 = _this;
  _this setDir -296.5744;
  _this setPos [4633.3804, 10653.925];
};

_vehicle_270 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_concrete", [4634.9941, 10642.481], [], 0, "CAN_COLLIDE"];
  _vehicle_270 = _this;
  _this setDir -252.79703;
  _this setPos [4634.9941, 10642.481];
};

_vehicle_273 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_concrete", [4635.9883, 10644.991, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_273 = _this;
  _this setDir -431.71265;
  _this setPos [4635.9883, 10644.991, -3.0517578e-005];
};

_vehicle_275 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_G_Pipes", [4675.7124, 10647.873], [], 0, "CAN_COLLIDE"];
  _vehicle_275 = _this;
  _this setDir -15.548475;
  _this setPos [4675.7124, 10647.873];
};

_vehicle_276 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_palletsfoiled", [4606.5254, 10664.903], [], 0, "CAN_COLLIDE"];
  _vehicle_276 = _this;
  _this setDir -23.693153;
  _this setPos [4606.5254, 10664.903];
};

_vehicle_278 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4630.0405, 10712.187, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_278 = _this;
  _this setDir -483.32059;
  _this setPos [4630.0405, 10712.187, 3.0517578e-005];
};

_vehicle_281 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4627.8706, 10715.493], [], 0, "CAN_COLLIDE"];
  _vehicle_281 = _this;
  _this setDir -483.32059;
  _this setPos [4627.8706, 10715.493];
};

_vehicle_283 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4625.7017, 10718.839], [], 0, "CAN_COLLIDE"];
  _vehicle_283 = _this;
  _this setDir -483.32059;
  _this setPos [4625.7017, 10718.839];
};

_vehicle_285 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4623.5532, 10722.176], [], 0, "CAN_COLLIDE"];
  _vehicle_285 = _this;
  _this setDir -483.32059;
  _this setPos [4623.5532, 10722.176];
};

_vehicle_288 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4620.7734, 10722.86, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_288 = _this;
  _this setDir -569.2309;
  _this setPos [4620.7734, 10722.86, 3.0517578e-005];
};

_vehicle_306 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4617.2598, 10720.933, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_306 = _this;
  _this setDir -569.2309;
  _this setPos [4617.2598, 10720.933, 9.1552734e-005];
};

_vehicle_309 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4613.7739, 10718.984, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_309 = _this;
  _this setDir -569.2309;
  _this setPos [4613.7739, 10718.984, 6.1035156e-005];
};

_vehicle_311 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4610.3091, 10717.019, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_311 = _this;
  _this setDir -569.2309;
  _this setPos [4610.3091, 10717.019, 3.0517578e-005];
};

_vehicle_314 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4661.48, 10701.29, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_314 = _this;
  _this setDir -758.13507;
  _this setPos [4661.48, 10701.29, 6.1035156e-005];
};

_vehicle_318 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4623.5562, 10483.729, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_318 = _this;
  _this setDir -301.64572;
  _this setPos [4623.5562, 10483.729, 3.0517578e-005];
};

_vehicle_321 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4621.4634, 10487.117], [], 0, "CAN_COLLIDE"];
  _vehicle_321 = _this;
  _this setDir -301.64572;
  _this setPos [4621.4634, 10487.117];
};

_vehicle_323 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4619.3833, 10490.516, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_323 = _this;
  _this setDir -301.64572;
  _this setPos [4619.3833, 10490.516, 6.1035156e-005];
};

_vehicle_325 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4617.2939, 10493.893], [], 0, "CAN_COLLIDE"];
  _vehicle_325 = _this;
  _this setDir -301.64572;
  _this setPos [4617.2939, 10493.893];
};

_vehicle_327 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4625.6479, 10480.349, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_327 = _this;
  _this setDir -301.64572;
  _this setPos [4625.6479, 10480.349, 6.1035156e-005];
};

_vehicle_329 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4627.7173, 10476.94, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_329 = _this;
  _this setDir -301.64572;
  _this setPos [4627.7173, 10476.94, -3.0517578e-005];
};

_vehicle_331 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4629.7886, 10473.575, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_331 = _this;
  _this setDir -301.64572;
  _this setPos [4629.7886, 10473.575, 3.0517578e-005];
};

_vehicle_333 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4636.8149, 10462.611, -0.068130545], [], 0, "CAN_COLLIDE"];
  _vehicle_333 = _this;
  _this setDir -301.64572;
  _this setPos [4636.8149, 10462.611, -0.068130545];
};

_vehicle_336 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4615.2256, 10497.294, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_336 = _this;
  _this setDir -301.64572;
  _this setPos [4615.2256, 10497.294, 6.1035156e-005];
};

_vehicle_339 = objNull;
if (true) then
{
  _this = createVehicle ["Land_pumpa", [4645.5293, 10476.292, -0.010895503], [], 0, "CAN_COLLIDE"];
  _vehicle_339 = _this;
  _this setDir 191.93433;
  _this setPos [4645.5293, 10476.292, -0.010895503];
};

_vehicle_342 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4615.8765, 10499.987, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_342 = _this;
  _this setDir -211.80679;
  _this setPos [4615.8765, 10499.987, -3.0517578e-005];
};

_vehicle_345 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4619.2515, 10502.106, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_345 = _this;
  _this setDir -211.80679;
  _this setPos [4619.2515, 10502.106, 6.1035156e-005];
};

_vehicle_348 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4622.6382, 10504.188], [], 0, "CAN_COLLIDE"];
  _vehicle_348 = _this;
  _this setDir -211.80679;
  _this setPos [4622.6382, 10504.188];
};

_vehicle_350 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4626.0229, 10506.273, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_350 = _this;
  _this setDir -211.80679;
  _this setPos [4626.0229, 10506.273, -3.0517578e-005];
};

_vehicle_352 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4629.3994, 10508.359], [], 0, "CAN_COLLIDE"];
  _vehicle_352 = _this;
  _this setDir -211.80679;
  _this setPos [4629.3994, 10508.359];
};

_vehicle_358 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hangar_2", [4711.9272, 10484.162, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_358 = _this;
  _this setDir 78.379639;
  _this setPos [4711.9272, 10484.162, -6.1035156e-005];
};

_vehicle_367 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hangar_2", [4718.3838, 10452.79, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_367 = _this;
  _this setDir 78.379639;
  _this setPos [4718.3838, 10452.79, -9.1552734e-005];
};

_vehicle_373 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pond_small_02", [4749.7505, 10537.585, 1.8804621], [], 0, "CAN_COLLIDE"];
  _vehicle_373 = _this;
  _this setDir 10.646494;
  _this setPos [4749.7505, 10537.585, 1.8804621];
};

_vehicle_374 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4881.9302, 10252.483], [], 0, "CAN_COLLIDE"];
  _vehicle_374 = _this;
  _this setDir -118.11313;
  _this setPos [4881.9302, 10252.483];
};

_vehicle_375 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [4823.0693, 10253.397, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_375 = _this;
  _this setDir 62.585556;
  _this setPos [4823.0693, 10253.397, 3.0517578e-005];
};

_vehicle_377 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4873.9214, 10248.258], [], 0, "CAN_COLLIDE"];
  _vehicle_377 = _this;
  _this setDir -118.11313;
  _this setPos [4873.9214, 10248.258];
};

_vehicle_379 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4743.9941, 10287.564, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_379 = _this;
  _this setDir -209.95535;
  _this setPos [4743.9941, 10287.564, 9.1552734e-005];
};

_vehicle_382 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hangar_2", [4120.7324, 10689.081, -0.14030418], [], 0, "CAN_COLLIDE"];
  _vehicle_382 = _this;
  _this setDir -119.96626;
  _this setPos [4120.7324, 10689.081, -0.14030418];
};

_vehicle_389 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_01", [4622.6382, 10715.805, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_389 = _this;
  _this setDir -210.45317;
  _this setPos [4622.6382, 10715.805, 0.00015258789];
};

_vehicle_398 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Shed_W4", [4733.94,10365.8], [], 0, "CAN_COLLIDE"];
  _vehicle_398 = _this;
  _this setDir -147;
  _this setPos [4733.94,10365.8];
};

_vehicle_400 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Shed_W4", [4739.86,10362], [], 0, "CAN_COLLIDE"];
  _vehicle_400 = _this;
  _this setDir -147;
  _this setPos [4739.86,10362];
};

_vehicle_422 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [4735.5015, 10291.644, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_422 = _this;
  _this setDir 60.149197;
  _this setPos [4735.5015, 10291.644, -3.0517578e-005];
};

_vehicle_424 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [4730.1138, 10288.555, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_424 = _this;
  _this setDir 60.67318;
  _this setPos [4730.1138, 10288.555, 3.0517578e-005];
};

_vehicle_427 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [4750.6426, 10300.307, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_427 = _this;
  _this setDir 60.522022;
  _this setPos [4750.6426, 10300.307, 3.0517578e-005];
};

_vehicle_429 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [4765.812, 10308.837], [], 0, "CAN_COLLIDE"];
  _vehicle_429 = _this;
  _this setDir 60.821842;
  _this setPos [4765.812, 10308.837];
};

_vehicle_433 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [4781.0747, 10317.272, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_433 = _this;
  _this setDir 61.321163;
  _this setPos [4781.0747, 10317.272, 3.0517578e-005];
};

_vehicle_441 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [4814.3086, 10332.487], [], 0, "CAN_COLLIDE"];
  _vehicle_441 = _this;
  _this setDir -107.79837;
  _this setPos [4814.3086, 10332.487];
};

_vehicle_448 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [4796.2788, 10325.688], [], 0, "CAN_COLLIDE"];
  _vehicle_448 = _this;
  _this setDir 62.565144;
  _this setPos [4796.2788, 10325.688];
};

_vehicle_464 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hangar_2", [4829.8145, 10305.468, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_464 = _this;
  _this setDir 212.98021;
  _this setPos [4829.8145, 10305.468, -6.1035156e-005];
};

_vehicle_466 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [5017.979, 10012.595, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_466 = _this;
  _this setDir -33.757992;
  _this setPos [5017.979, 10012.595, 9.1552734e-005];
};

_vehicle_480 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton", [4624.145, 10038.418, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_480 = _this;
  _this setDir -30.087746;
  _this setPos [4624.145, 10038.418, -3.0517578e-005];
};

_vehicle_482 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton", [4589.5933, 10018.353, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_482 = _this;
  _this setDir -30.07173;
  _this setPos [4589.5933, 10018.353, -6.1035156e-005];
};

_vehicle_483 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton", [4571.9497, 10008.302, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_483 = _this;
  _this setDir -30.129417;
  _this setPos [4571.9497, 10008.302, 6.1035156e-005];
};

_vehicle_484 = objNull;
if (true) then
{
  _this = createVehicle ["Land_SS_hangar", [4579.9712, 9985.5088], [], 0, "CAN_COLLIDE"];
  _vehicle_484 = _this;
  _this setDir 150.2103;
  _this setPos [4579.9712, 9985.5088];
};

_vehicle_486 = objNull;
if (true) then
{
  _this = createVehicle ["Land_SS_hangar", [4607.396, 10001.268], [], 0, "CAN_COLLIDE"];
  _vehicle_486 = _this;
  _this setDir 150.04298;
  _this setPos [4607.396, 10001.268];
};

_vehicle_489 = objNull;
if (true) then
{
  _this = createVehicle ["Land_SS_hangar", [4516.3369, 10095.944, -0.031210242], [], 0, "CAN_COLLIDE"];
  _vehicle_489 = _this;
  _this setDir -29.976051;
  _this setPos [4516.3369, 10095.944, -0.031210242];
};

_vehicle_492 = objNull;
if (true) then
{
  _this = createVehicle ["Land_SS_hangar", [4543.666, 10111.726, -0.023035198], [], 0, "CAN_COLLIDE"];
  _vehicle_492 = _this;
  _this setDir -30.01807;
  _this setPos [4543.666, 10111.726, -0.023035198];
};

_vehicle_493 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_01", [4138.3867, 10673.971], [], 0, "CAN_COLLIDE"];
  _vehicle_493 = _this;
  _this setDir 150.13649;
  _this setPos [4138.3867, 10673.971];
};

_vehicle_494 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_02", [4126.7603, 10668.889, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_494 = _this;
  _this setDir 60.349434;
  _this setPos [4126.7603, 10668.889, -6.1035156e-005];
};

_vehicle_495 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_03", [4512.2314, 10845.49, -0.1343606], [], 0, "CAN_COLLIDE"];
  _vehicle_495 = _this;
  _this setDir -79.333931;
  _this setPos [4512.2314, 10845.49, -0.1343606];
};

_vehicle_496 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [4534.7681, 10840.906, -0.10186195], [], 0, "CAN_COLLIDE"];
  _vehicle_496 = _this;
  _this setDir -81.178139;
  _this setPos [4534.7681, 10840.906, -0.10186195];
};

_vehicle_500 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_L", [4788.9028, 10311.821, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_500 = _this;
  _this setDir 60.074379;
  _this setPos [4788.9028, 10311.821, -6.1035156e-005];
};

_vehicle_501 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_04", [4771.2598, 10304.384, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_501 = _this;
  _this setDir 59.872063;
  _this setPos [4771.2598, 10304.384, 3.0517578e-005];
};

_vehicle_505 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_02", [4603.1626, 9563.123], [], 0, "CAN_COLLIDE"];
  _vehicle_505 = _this;
  _this setDir -56.980755;
  _this setPos [4603.1626, 9563.123];
};

_vehicle_507 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_L", [4239.6045, 10414.711, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_507 = _this;
  _this setDir 54.670052;
  _this setPos [4239.6045, 10414.711, 3.0517578e-005];
};

_vehicle_508 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Guardhouse", [4231.1919, 10382.849, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_508 = _this;
  _this setDir 233.317;
  _this setPos [4231.1919, 10382.849, 9.1552734e-005];
};

_vehicle_510 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [4342.8477, 10264.835, 0.049547877], [], 0, "CAN_COLLIDE"];
  _vehicle_510 = _this;
  _this setDir -211.59222;
  _this setPos [4342.8477, 10264.835, 0.049547877];
};

_vehicle_511 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [4336.1309, 10275.91, 0.080557674], [], 0, "CAN_COLLIDE"];
  _vehicle_511 = _this;
  _this setDir 148.28304;
  _this setPos [4336.1309, 10275.91, 0.080557674];
};

_vehicle_513 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [4349.5898, 10253.841, 0.13961621], [], 0, "CAN_COLLIDE"];
  _vehicle_513 = _this;
  _this setDir -211.59222;
  _this setPos [4349.5898, 10253.841, 0.13961621];
};

_vehicle_515 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [4356.1636, 10243.039, 0.10190263], [], 0, "CAN_COLLIDE"];
  _vehicle_515 = _this;
  _this setDir -211.59222;
  _this setPos [4356.1636, 10243.039, 0.10190263];
};

_vehicle_519 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4256.9702, 10386.42, -0.045523938], [], 0, "CAN_COLLIDE"];
  _vehicle_519 = _this;
  _this setDir -392.77069;
  _this setPos [4256.9702, 10386.42, -0.045523938];
};

_vehicle_522 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4260.3423, 10388.572, -0.047429755], [], 0, "CAN_COLLIDE"];
  _vehicle_522 = _this;
  _this setDir -392.77069;
  _this setPos [4260.3423, 10388.572, -0.047429755];
};

_vehicle_524 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4263.6572, 10390.716, -0.063976139], [], 0, "CAN_COLLIDE"];
  _vehicle_524 = _this;
  _this setDir -392.77069;
  _this setPos [4263.6572, 10390.716, -0.063976139];
};

_vehicle_526 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4264.1924, 10393.391, -0.04523826], [], 0, "CAN_COLLIDE"];
  _vehicle_526 = _this;
  _this setDir -483.94299;
  _this setPos [4264.1924, 10393.391, -0.04523826];
};

_vehicle_540 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4261.9888, 10396.716, -0.033756327], [], 0, "CAN_COLLIDE"];
  _vehicle_540 = _this;
  _this setDir -483.94299;
  _this setPos [4261.9888, 10396.716, -0.033756327];
};

_vehicle_543 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4259.7734, 10399.999, -0.022806721], [], 0, "CAN_COLLIDE"];
  _vehicle_543 = _this;
  _this setDir -483.94299;
  _this setPos [4259.7734, 10399.999, -0.022806721];
};

_vehicle_546 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4257.5664, 10403.273, -0.012313923], [], 0, "CAN_COLLIDE"];
  _vehicle_546 = _this;
  _this setDir -483.94299;
  _this setPos [4257.5664, 10403.273, -0.012313923];
};

_vehicle_549 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4255.3516, 10406.554, -0.020075334], [], 0, "CAN_COLLIDE"];
  _vehicle_549 = _this;
  _this setDir -483.94299;
  _this setPos [4255.3516, 10406.554, -0.020075334];
};

_vehicle_551 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4144.8208, 10667.94, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_551 = _this;
  _this setDir -480.07462;
  _this setPos [4144.8208, 10667.94, -3.0517578e-005];
};

_vehicle_554 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4144.1006, 10665.291, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_554 = _this;
  _this setDir -389.87555;
  _this setPos [4144.1006, 10665.291, 3.0517578e-005];
};

_vehicle_557 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4140.6675, 10663.303, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_557 = _this;
  _this setDir -389.87555;
  _this setPos [4140.6675, 10663.303, 6.1035156e-005];
};

_vehicle_559 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4137.2505, 10661.326, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_559 = _this;
  _this setDir -389.87555;
  _this setPos [4137.2505, 10661.326, -3.0517578e-005];
};

_vehicle_561 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4133.812, 10659.362], [], 0, "CAN_COLLIDE"];
  _vehicle_561 = _this;
  _this setDir -389.87555;
  _this setPos [4133.812, 10659.362];
};

_vehicle_564 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4368.4092, 10244.172, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_564 = _this;
  _this setDir -481.69836;
  _this setPos [4368.4092, 10244.172, -0.00012207031];
};

_vehicle_567 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4366.3242, 10247.567, 0.024336465], [], 0, "CAN_COLLIDE"];
  _vehicle_567 = _this;
  _this setDir -481.69836;
  _this setPos [4366.3242, 10247.567, 0.024336465];
};

_vehicle_570 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4364.2344, 10250.962, 0.021087531], [], 0, "CAN_COLLIDE"];
  _vehicle_570 = _this;
  _this setDir -481.69836;
  _this setPos [4364.2344, 10250.962, 0.021087531];
};

_vehicle_572 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4362.1489, 10254.34, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_572 = _this;
  _this setDir -481.69836;
  _this setPos [4362.1489, 10254.34, 3.0517578e-005];
};

_vehicle_574 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4360.5864, 10257.945], [], 0, "CAN_COLLIDE"];
  _vehicle_574 = _this;
  _this setDir -465.12555;
  _this setPos [4360.5864, 10257.945];
};

_vehicle_576 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4358.0215, 10261.081, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_576 = _this;
  _this setDir -481.69836;
  _this setPos [4358.0215, 10261.081, -3.0517578e-005];
};

_vehicle_578 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4355.9463, 10264.456], [], 0, "CAN_COLLIDE"];
  _vehicle_578 = _this;
  _this setDir -481.69836;
  _this setPos [4355.9463, 10264.456];
};

_vehicle_580 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4353.8916, 10267.802, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_580 = _this;
  _this setDir -481.69836;
  _this setPos [4353.8916, 10267.802, -3.0517578e-005];
};

_vehicle_582 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4351.8105, 10271.19, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_582 = _this;
  _this setDir -481.69836;
  _this setPos [4351.8105, 10271.19, 6.1035156e-005];
};

_vehicle_584 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4349.749, 10274.572, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_584 = _this;
  _this setDir -481.69836;
  _this setPos [4349.749, 10274.572, -3.0517578e-005];
};

_vehicle_586 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4347.6694, 10277.933, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_586 = _this;
  _this setDir -481.69836;
  _this setPos [4347.6694, 10277.933, 3.0517578e-005];
};

_vehicle_588 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4345.5786, 10281.299, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_588 = _this;
  _this setDir -481.69836;
  _this setPos [4345.5786, 10281.299, 3.0517578e-005];
};

_vehicle_590 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4343.5083, 10284.682, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_590 = _this;
  _this setDir -481.69836;
  _this setPos [4343.5083, 10284.682, 3.0517578e-005];
};

_vehicle_592 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4341.4351, 10288.063], [], 0, "CAN_COLLIDE"];
  _vehicle_592 = _this;
  _this setDir -481.69836;
  _this setPos [4341.4351, 10288.063];
};

_vehicle_594 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4338.728, 10288.731], [], 0, "CAN_COLLIDE"];
  _vehicle_594 = _this;
  _this setDir -570.95441;
  _this setPos [4338.728, 10288.731];
};

_vehicle_597 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4335.2974, 10286.687], [], 0, "CAN_COLLIDE"];
  _vehicle_597 = _this;
  _this setDir -570.95441;
  _this setPos [4335.2974, 10286.687];
};

_vehicle_600 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4331.5859, 10285.602, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_600 = _this;
  _this setDir -542.17236;
  _this setPos [4331.5859, 10285.602, 3.0517578e-005];
};

_vehicle_602 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4328.4399, 10282.609], [], 0, "CAN_COLLIDE"];
  _vehicle_602 = _this;
  _this setDir -570.95441;
  _this setPos [4328.4399, 10282.609];
};

_vehicle_604 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4325.0078, 10280.577], [], 0, "CAN_COLLIDE"];
  _vehicle_604 = _this;
  _this setDir -570.95441;
  _this setPos [4325.0078, 10280.577];
};

_vehicle_607 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4367.8125, 10241.445, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_607 = _this;
  _this setDir -393.38107;
  _this setPos [4367.8125, 10241.445, -3.0517578e-005];
};

_vehicle_610 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4364.5151, 10239.238, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_610 = _this;
  _this setDir -393.38107;
  _this setPos [4364.5151, 10239.238, -3.0517578e-005];
};

_vehicle_612 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4361.1802, 10237.186], [], 0, "CAN_COLLIDE"];
  _vehicle_612 = _this;
  _this setDir -390.10089;
  _this setPos [4361.1802, 10237.186];
};

_vehicle_615 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4357.7534, 10235.195, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_615 = _this;
  _this setDir -390.10089;
  _this setPos [4357.7534, 10235.195, -6.1035156e-005];
};

_vehicle_617 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4354.3237, 10233.217, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_617 = _this;
  _this setDir -390.10089;
  _this setPos [4354.3237, 10233.217, 3.0517578e-005];
};

_vehicle_620 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rail_najazdovarampa", [4567.5928, 10048.695, 0.99264729], [], 0, "CAN_COLLIDE"];
  _vehicle_620 = _this;
  _this setDir 60.539673;
  _this setPos [4567.5928, 10048.695, 0.99264729];
};

_vehicle_621 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_Crane_02b", [4566.7422, 10050.314, 0.9121272], [], 0, "CAN_COLLIDE"];
  _vehicle_621 = _this;
  _this setDir -207.47026;
  _this setPos [4566.7422, 10050.314, 0.9121272];
};

_vehicle_622 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_Crane_02a", [4569.7793, 10049.25, 1.0111105], [], 0, "CAN_COLLIDE"];
  _vehicle_622 = _this;
  _this setDir -118.9706;
  _this setPos [4569.7793, 10049.25, 1.0111105];
};

_vehicle_623 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4632.3052, 10050.851, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_623 = _this;
  _this setDir 58.544704;
  _this setPos [4632.3052, 10050.851, 9.1552734e-005];
};

_vehicle_631 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4624.3828, 10062.194], [], 0, "CAN_COLLIDE"];
  _vehicle_631 = _this;
  _this setDir 58.544704;
  _this setPos [4624.3828, 10062.194];
};

_vehicle_633 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4617.0044, 10071.867], [], 0, "CAN_COLLIDE"];
  _vehicle_633 = _this;
  _this setDir 58.544704;
  _this setPos [4617.0044, 10071.867];
};

_vehicle_635 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4610.6753, 10068.543], [], 0, "CAN_COLLIDE"];
  _vehicle_635 = _this;
  _this setDir 58.544704;
  _this setPos [4610.6753, 10068.543];
};

_vehicle_637 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4618.459, 10058.311], [], 0, "CAN_COLLIDE"];
  _vehicle_637 = _this;
  _this setDir 58.544704;
  _this setPos [4618.459, 10058.311];
};

_vehicle_639 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4625.3896, 10047.238], [], 0, "CAN_COLLIDE"];
  _vehicle_639 = _this;
  _this setDir 58.544704;
  _this setPos [4625.3896, 10047.238];
};

_vehicle_641 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4603.3599, 10064.688], [], 0, "CAN_COLLIDE"];
  _vehicle_641 = _this;
  _this setDir 58.544704;
  _this setPos [4603.3599, 10064.688];
};

_vehicle_643 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4611.5493, 10054.541], [], 0, "CAN_COLLIDE"];
  _vehicle_643 = _this;
  _this setDir 58.544704;
  _this setPos [4611.5493, 10054.541];
};

_vehicle_645 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4618.9272, 10043.082], [], 0, "CAN_COLLIDE"];
  _vehicle_645 = _this;
  _this setDir 58.544704;
  _this setPos [4618.9272, 10043.082];
};

_vehicle_665 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4611.9502, 10039.248], [], 0, "CAN_COLLIDE"];
  _vehicle_665 = _this;
  _this setDir 58.544704;
  _this setPos [4611.9502, 10039.248];
};

_vehicle_666 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4604.2861, 10050.85, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_666 = _this;
  _this setDir 58.544704;
  _this setPos [4604.2861, 10050.85, -9.1552734e-005];
};

_vehicle_667 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4596.9077, 10060.523, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_667 = _this;
  _this setDir 58.544704;
  _this setPos [4596.9077, 10060.523, -9.1552734e-005];
};

_vehicle_668 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4590.5786, 10057.2, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_668 = _this;
  _this setDir 58.544704;
  _this setPos [4590.5786, 10057.2, -9.1552734e-005];
};

_vehicle_669 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4598.3623, 10046.966, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_669 = _this;
  _this setDir 58.544704;
  _this setPos [4598.3623, 10046.966, -9.1552734e-005];
};

_vehicle_670 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4605.0347, 10035.635, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_670 = _this;
  _this setDir 58.544704;
  _this setPos [4605.0347, 10035.635, -9.1552734e-005];
};

_vehicle_671 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4583.2632, 10053.343, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_671 = _this;
  _this setDir 58.544704;
  _this setPos [4583.2632, 10053.343, -9.1552734e-005];
};

_vehicle_672 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4591.4526, 10043.196, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_672 = _this;
  _this setDir 58.544704;
  _this setPos [4591.4526, 10043.196, -9.1552734e-005];
};

_vehicle_673 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4598.5723, 10031.478, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_673 = _this;
  _this setDir 58.544704;
  _this setPos [4598.5723, 10031.478, -9.1552734e-005];
};

_vehicle_683 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4592.7651, 10028.303], [], 0, "CAN_COLLIDE"];
  _vehicle_683 = _this;
  _this setDir 58.544704;
  _this setPos [4592.7651, 10028.303];
};

_vehicle_684 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4584.8428, 10039.649, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_684 = _this;
  _this setDir 58.544704;
  _this setPos [4584.8428, 10039.649, -9.1552734e-005];
};

_vehicle_685 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4577.4644, 10049.322, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_685 = _this;
  _this setDir 58.544704;
  _this setPos [4577.4644, 10049.322, -9.1552734e-005];
};

_vehicle_686 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4571.1353, 10045.998, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_686 = _this;
  _this setDir 58.544704;
  _this setPos [4571.1353, 10045.998, -9.1552734e-005];
};

_vehicle_687 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4578.9189, 10035.767, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_687 = _this;
  _this setDir 58.544704;
  _this setPos [4578.9189, 10035.767, -9.1552734e-005];
};

_vehicle_688 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4585.8496, 10024.693, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_688 = _this;
  _this setDir 58.544704;
  _this setPos [4585.8496, 10024.693, -9.1552734e-005];
};

_vehicle_689 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4563.8198, 10042.144, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_689 = _this;
  _this setDir 58.544704;
  _this setPos [4563.8198, 10042.144, -9.1552734e-005];
};

_vehicle_690 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4572.0093, 10031.996, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_690 = _this;
  _this setDir 58.544704;
  _this setPos [4572.0093, 10031.996, -9.1552734e-005];
};

_vehicle_691 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4579.3872, 10020.537, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_691 = _this;
  _this setDir 58.544704;
  _this setPos [4579.3872, 10020.537, -9.1552734e-005];
};

_vehicle_701 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4573.3218, 10017.863], [], 0, "CAN_COLLIDE"];
  _vehicle_701 = _this;
  _this setDir 58.544704;
  _this setPos [4573.3218, 10017.863];
};

_vehicle_702 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4565.3994, 10029.205, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_702 = _this;
  _this setDir 58.544704;
  _this setPos [4565.3994, 10029.205, -9.1552734e-005];
};

_vehicle_703 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4558.021, 10038.878, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_703 = _this;
  _this setDir 58.544704;
  _this setPos [4558.021, 10038.878, -9.1552734e-005];
};

_vehicle_704 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4551.6919, 10035.554, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_704 = _this;
  _this setDir 58.544704;
  _this setPos [4551.6919, 10035.554, -9.1552734e-005];
};

_vehicle_705 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4559.4756, 10025.321, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_705 = _this;
  _this setDir 58.544704;
  _this setPos [4559.4756, 10025.321, -9.1552734e-005];
};

_vehicle_706 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4566.4063, 10014.25, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_706 = _this;
  _this setDir 58.544704;
  _this setPos [4566.4063, 10014.25, -9.1552734e-005];
};

_vehicle_707 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4543.4956, 10031.194, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_707 = _this;
  _this setDir 58.544704;
  _this setPos [4543.4956, 10031.194, -9.1552734e-005];
};

_vehicle_708 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4551.2441, 10020.985, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_708 = _this;
  _this setDir 58.544704;
  _this setPos [4551.2441, 10020.985, -9.1552734e-005];
};

_vehicle_709 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4559.0625, 10009.65, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_709 = _this;
  _this setDir 58.544704;
  _this setPos [4559.0625, 10009.65, -9.1552734e-005];
};

_vehicle_755 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4610.626, 10081.823], [], 0, "CAN_COLLIDE"];
  _vehicle_755 = _this;
  _this setDir 58.544704;
  _this setPos [4610.626, 10081.823];
};

_vehicle_756 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4603.5166, 10093.371, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_756 = _this;
  _this setDir 58.544704;
  _this setPos [4603.5166, 10093.371, -9.1552734e-005];
};

_vehicle_757 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4596.0371, 10102.943, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_757 = _this;
  _this setDir 58.544704;
  _this setPos [4596.0371, 10102.943, -9.1552734e-005];
};

_vehicle_758 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4588.6909, 10099.007, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_758 = _this;
  _this setDir 58.544704;
  _this setPos [4588.6909, 10099.007, -9.1552734e-005];
};

_vehicle_759 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4596.4746, 10088.774, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_759 = _this;
  _this setDir 58.544704;
  _this setPos [4596.4746, 10088.774, -9.1552734e-005];
};

_vehicle_760 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4603.4053, 10077.702, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_760 = _this;
  _this setDir 58.544704;
  _this setPos [4603.4053, 10077.702, -9.1552734e-005];
};

_vehicle_761 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4581.3755, 10095.153, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_761 = _this;
  _this setDir 58.544704;
  _this setPos [4581.3755, 10095.153, -9.1552734e-005];
};

_vehicle_762 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4589.5649, 10085.005, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_762 = _this;
  _this setDir 58.544704;
  _this setPos [4589.5649, 10085.005, -9.1552734e-005];
};

_vehicle_763 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4596.9429, 10073.546, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_763 = _this;
  _this setDir 58.544704;
  _this setPos [4596.9429, 10073.546, -9.1552734e-005];
};

_vehicle_764 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4582.3018, 10081.314, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_764 = _this;
  _this setDir 58.544704;
  _this setPos [4582.3018, 10081.314, -0.00018310547];
};

_vehicle_765 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4574.9233, 10090.987, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_765 = _this;
  _this setDir 58.544704;
  _this setPos [4574.9233, 10090.987, -0.00018310547];
};

_vehicle_766 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4568.5942, 10087.664, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_766 = _this;
  _this setDir 58.544704;
  _this setPos [4568.5942, 10087.664, -0.00018310547];
};

_vehicle_767 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4576.3779, 10077.429, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_767 = _this;
  _this setDir 58.544704;
  _this setPos [4576.3779, 10077.429, -0.00018310547];
};

_vehicle_768 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4569.4683, 10073.66, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_768 = _this;
  _this setDir 58.544704;
  _this setPos [4569.4683, 10073.66, -0.00018310547];
};

_vehicle_769 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4556.9346, 10066.231, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_769 = _this;
  _this setDir 58.544704;
  _this setPos [4556.9346, 10066.231, -0.00018310547];
};

_vehicle_770 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4541.8354, 10072.606, 0.0057678223], [], 0, "CAN_COLLIDE"];
  _vehicle_770 = _this;
  _this setDir 58.544704;
  _this setPos [4541.8354, 10072.606, 0.0057678223];
};

_vehicle_771 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4550.0249, 10062.461, 0.008392334], [], 0, "CAN_COLLIDE"];
  _vehicle_771 = _this;
  _this setDir 58.544704;
  _this setPos [4550.0249, 10062.461, 0.008392334];
};

_vehicle_772 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4557.4028, 10051, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_772 = _this;
  _this setDir 58.544704;
  _this setPos [4557.4028, 10051, -0.00018310547];
};

_vehicle_773 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4551.3374, 10048.327, 0.0078125], [], 0, "CAN_COLLIDE"];
  _vehicle_773 = _this;
  _this setDir 58.544704;
  _this setPos [4551.3374, 10048.327, 0.0078125];
};

_vehicle_774 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4543.415, 10059.668, 0.010925293], [], 0, "CAN_COLLIDE"];
  _vehicle_774 = _this;
  _this setDir 58.544704;
  _this setPos [4543.415, 10059.668, 0.010925293];
};

_vehicle_775 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4536.0366, 10069.342, 0.0098266602], [], 0, "CAN_COLLIDE"];
  _vehicle_775 = _this;
  _this setDir 58.544704;
  _this setPos [4536.0366, 10069.342, 0.0098266602];
};

_vehicle_776 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4529.7075, 10066.017, 0.019805908], [], 0, "CAN_COLLIDE"];
  _vehicle_776 = _this;
  _this setDir 58.544704;
  _this setPos [4529.7075, 10066.017, 0.019805908];
};

_vehicle_777 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4537.4912, 10055.785, 0.019805908], [], 0, "CAN_COLLIDE"];
  _vehicle_777 = _this;
  _this setDir 58.544704;
  _this setPos [4537.4912, 10055.785, 0.019805908];
};

_vehicle_778 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4521.5112, 10061.659, 0.019805908], [], 0, "CAN_COLLIDE"];
  _vehicle_778 = _this;
  _this setDir 58.544704;
  _this setPos [4521.5112, 10061.659, 0.019805908];
};

_vehicle_779 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4529.2598, 10051.449, 0.019805908], [], 0, "CAN_COLLIDE"];
  _vehicle_779 = _this;
  _this setDir 58.544704;
  _this setPos [4529.2598, 10051.449, 0.019805908];
};

_vehicle_780 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4537.0781, 10040.114, 0.019805908], [], 0, "CAN_COLLIDE"];
  _vehicle_780 = _this;
  _this setDir 58.544704;
  _this setPos [4537.0781, 10040.114, 0.019805908];
};

_vehicle_781 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4544.4219, 10044.714, 0.019500732], [], 0, "CAN_COLLIDE"];
  _vehicle_781 = _this;
  _this setDir 58.544704;
  _this setPos [4544.4219, 10044.714, 0.019500732];
};

_vehicle_782 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4562.8584, 10070.114, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_782 = _this;
  _this setDir 58.544704;
  _this setPos [4562.8584, 10070.114, -0.00018310547];
};

_vehicle_783 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4561.2788, 10083.807, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_783 = _this;
  _this setDir 58.544704;
  _this setPos [4561.2788, 10083.807, -0.00018310547];
};

_vehicle_784 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4555.48, 10079.786, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_784 = _this;
  _this setDir 58.544704;
  _this setPos [4555.48, 10079.786, -0.00018310547];
};

_vehicle_785 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4549.1509, 10076.463, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_785 = _this;
  _this setDir 58.544704;
  _this setPos [4549.1509, 10076.463, -0.00018310547];
};

_vehicle_786 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4563.8652, 10055.157, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_786 = _this;
  _this setDir 58.544704;
  _this setPos [4563.8652, 10055.157, -0.00018310547];
};

_vehicle_787 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4570.7808, 10058.767, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_787 = _this;
  _this setDir 58.544704;
  _this setPos [4570.7808, 10058.767, -9.1552734e-005];
};

_vehicle_788 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4576.5879, 10061.941, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_788 = _this;
  _this setDir 58.544704;
  _this setPos [4576.5879, 10061.941, -0.00018310547];
};

_vehicle_789 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4583.0503, 10066.099, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_789 = _this;
  _this setDir 58.544704;
  _this setPos [4583.0503, 10066.099, -0.00018310547];
};

_vehicle_790 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4589.9658, 10069.713, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_790 = _this;
  _this setDir 58.544704;
  _this setPos [4589.9658, 10069.713, -9.1552734e-005];
};

_vehicle_802 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4570.8481, 10102.32, -0.079591453], [], 0, "CAN_COLLIDE"];
  _vehicle_802 = _this;
  _this setDir -479.81509;
  _this setPos [4570.8481, 10102.32, -0.079591453];
};

_vehicle_803 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4568.9131, 10105.776, -0.082368553], [], 0, "CAN_COLLIDE"];
  _vehicle_803 = _this;
  _this setDir -479.63474;
  _this setPos [4568.9131, 10105.776, -0.082368553];
};

_vehicle_804 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4566.9443, 10109.201, -0.08316201], [], 0, "CAN_COLLIDE"];
  _vehicle_804 = _this;
  _this setDir -479.81509;
  _this setPos [4566.9443, 10109.201, -0.08316201];
};

_vehicle_805 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4564.981, 10112.639, -0.084840469], [], 0, "CAN_COLLIDE"];
  _vehicle_805 = _this;
  _this setDir -479.81509;
  _this setPos [4564.981, 10112.639, -0.084840469];
};

_vehicle_806 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4563.0381, 10116.052, -0.086610489], [], 0, "CAN_COLLIDE"];
  _vehicle_806 = _this;
  _this setDir -479.81509;
  _this setPos [4563.0381, 10116.052, -0.086610489];
};

_vehicle_807 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4561.0688, 10119.5, -0.088258438], [], 0, "CAN_COLLIDE"];
  _vehicle_807 = _this;
  _this setDir -479.81509;
  _this setPos [4561.0688, 10119.5, -0.088258438];
};

_vehicle_808 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4559.1172, 10122.953, -0.090089485], [], 0, "CAN_COLLIDE"];
  _vehicle_808 = _this;
  _this setDir -479.81509;
  _this setPos [4559.1172, 10122.953, -0.090089485];
};

_vehicle_809 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4557.1504, 10126.379, -0.091737442], [], 0, "CAN_COLLIDE"];
  _vehicle_809 = _this;
  _this setDir -479.81509;
  _this setPos [4557.1504, 10126.379, -0.091737442];
};

_vehicle_810 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4555.1719, 10129.813, -0.093476936], [], 0, "CAN_COLLIDE"];
  _vehicle_810 = _this;
  _this setDir -479.81509;
  _this setPos [4555.1719, 10129.813, -0.093476936];
};

_vehicle_811 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4553.2148, 10133.26, -0.092286758], [], 0, "CAN_COLLIDE"];
  _vehicle_811 = _this;
  _this setDir -479.81509;
  _this setPos [4553.2148, 10133.26, -0.092286758];
};

_vehicle_812 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4551.252, 10136.715, -0.090974502], [], 0, "CAN_COLLIDE"];
  _vehicle_812 = _this;
  _this setDir -479.81509;
  _this setPos [4551.252, 10136.715, -0.090974502];
};

_vehicle_824 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4632.2397, 9995.1631, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_824 = _this;
  _this setDir -479.44955;
  _this setPos [4632.2397, 9995.1631, -9.1552734e-005];
};

_vehicle_825 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4630.8174, 9998.8311, -0.0028686523], [], 0, "CAN_COLLIDE"];
  _vehicle_825 = _this;
  _this setDir -462.87674;
  _this setPos [4630.8174, 9998.8311, -0.0028686523];
};

_vehicle_826 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4628.3696, 10002.067, -0.0036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_826 = _this;
  _this setDir -479.44955;
  _this setPos [4628.3696, 10002.067, -0.0036621094];
};

_vehicle_827 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4626.4414, 10005.509, -0.0053405762], [], 0, "CAN_COLLIDE"];
  _vehicle_827 = _this;
  _this setDir -479.44955;
  _this setPos [4626.4414, 10005.509, -0.0053405762];
};

_vehicle_828 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4624.5171, 10008.955, -0.0071105957], [], 0, "CAN_COLLIDE"];
  _vehicle_828 = _this;
  _this setDir -479.44955;
  _this setPos [4624.5171, 10008.955, -0.0071105957];
};

_vehicle_829 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4622.5674, 10012.403, -0.0087585449], [], 0, "CAN_COLLIDE"];
  _vehicle_829 = _this;
  _this setDir -479.44955;
  _this setPos [4622.5674, 10012.403, -0.0087585449];
};

_vehicle_830 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4620.6387, 10015.87, -0.0105896], [], 0, "CAN_COLLIDE"];
  _vehicle_830 = _this;
  _this setDir -479.44955;
  _this setPos [4620.6387, 10015.87, -0.0105896];
};

_vehicle_831 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4618.6865, 10019.307, -0.012237549], [], 0, "CAN_COLLIDE"];
  _vehicle_831 = _this;
  _this setDir -479.44955;
  _this setPos [4618.6865, 10019.307, -0.012237549];
};

_vehicle_832 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4616.7427, 10022.751, -0.013977051], [], 0, "CAN_COLLIDE"];
  _vehicle_832 = _this;
  _this setDir -479.44955;
  _this setPos [4616.7427, 10022.751, -0.013977051];
};

_vehicle_833 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4614.8052, 10026.214, -0.012786865], [], 0, "CAN_COLLIDE"];
  _vehicle_833 = _this;
  _this setDir -479.44955;
  _this setPos [4614.8052, 10026.214, -0.012786865];
};

_vehicle_834 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4612.8594, 10029.668, -0.011474609], [], 0, "CAN_COLLIDE"];
  _vehicle_834 = _this;
  _this setDir -479.44955;
  _this setPos [4612.8594, 10029.668, -0.011474609];
};

_vehicle_847 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4634.2188, 9991.7158, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_847 = _this;
  _this setDir -479.44955;
  _this setPos [4634.2188, 9991.7158, 3.0517578e-005];
};

_vehicle_849 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4636.1465, 9988.2598], [], 0, "CAN_COLLIDE"];
  _vehicle_849 = _this;
  _this setDir -479.44955;
  _this setPos [4636.1465, 9988.2598];
};

_vehicle_851 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4549.3018, 10140.141, -0.068298087], [], 0, "CAN_COLLIDE"];
  _vehicle_851 = _this;
  _this setDir -479.44955;
  _this setPos [4549.3018, 10140.141, -0.068298087];
};

_vehicle_854 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4547.3726, 10143.564, -0.051947467], [], 0, "CAN_COLLIDE"];
  _vehicle_854 = _this;
  _this setDir -479.44955;
  _this setPos [4547.3726, 10143.564, -0.051947467];
};

_vehicle_861 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4621.6084, 9977.9355], [], 0, "CAN_COLLIDE"];
  _vehicle_861 = _this;
  _this setDir -387.61035;
  _this setPos [4621.6084, 9977.9355];
};

_vehicle_862 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4625.1294, 9979.7471, -0.0027160645], [], 0, "CAN_COLLIDE"];
  _vehicle_862 = _this;
  _this setDir -387.35861;
  _this setPos [4625.1294, 9979.7471, -0.0027160645];
};

_vehicle_863 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4628.6514, 9981.5723, 0.017120361], [], 0, "CAN_COLLIDE"];
  _vehicle_863 = _this;
  _this setDir -387.35861;
  _this setPos [4628.6514, 9981.5723, 0.017120361];
};

_vehicle_864 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4632.0757, 9983.457, 0.042572021], [], 0, "CAN_COLLIDE"];
  _vehicle_864 = _this;
  _this setDir -390.63879;
  _this setPos [4632.0757, 9983.457, 0.042572021];
};

_vehicle_865 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4635.4761, 9985.5049, 0.031463623], [], 0, "CAN_COLLIDE"];
  _vehicle_865 = _this;
  _this setDir -390.63879;
  _this setPos [4635.4761, 9985.5049, 0.031463623];
};

_vehicle_871 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4604.5659, 9967.8574], [], 0, "CAN_COLLIDE"];
  _vehicle_871 = _this;
  _this setDir -389.25784;
  _this setPos [4604.5659, 9967.8574];
};

_vehicle_872 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4608.0225, 9969.792, -0.0027160645], [], 0, "CAN_COLLIDE"];
  _vehicle_872 = _this;
  _this setDir -389.25784;
  _this setPos [4608.0225, 9969.792, -0.0027160645];
};

_vehicle_873 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4611.4795, 9971.7275, 0.017120361], [], 0, "CAN_COLLIDE"];
  _vehicle_873 = _this;
  _this setDir -389.25784;
  _this setPos [4611.4795, 9971.7275, 0.017120361];
};

_vehicle_874 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4614.8462, 9973.7285, 0.042572021], [], 0, "CAN_COLLIDE"];
  _vehicle_874 = _this;
  _this setDir -392.53802;
  _this setPos [4614.8462, 9973.7285, 0.042572021];
};

_vehicle_875 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4618.1738, 9975.8887, 0.031463623], [], 0, "CAN_COLLIDE"];
  _vehicle_875 = _this;
  _this setDir -392.53802;
  _this setPos [4618.1738, 9975.8887, 0.031463623];
};

_vehicle_881 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4587.2573, 9958.3125, -0.033149034], [], 0, "CAN_COLLIDE"];
  _vehicle_881 = _this;
  _this setDir -387.40695;
  _this setPos [4587.2573, 9958.3125, -0.033149034];
};

_vehicle_882 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4590.7769, 9960.1367, -0.035865091], [], 0, "CAN_COLLIDE"];
  _vehicle_882 = _this;
  _this setDir -387.40695;
  _this setPos [4590.7769, 9960.1367, -0.035865091];
};

_vehicle_883 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4594.2969, 9961.9619, -0.016028672], [], 0, "CAN_COLLIDE"];
  _vehicle_883 = _this;
  _this setDir -387.40695;
  _this setPos [4594.2969, 9961.9619, -0.016028672];
};

_vehicle_884 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4597.7212, 9963.8486, 0.009422984], [], 0, "CAN_COLLIDE"];
  _vehicle_884 = _this;
  _this setDir -390.68713;
  _this setPos [4597.7212, 9963.8486, 0.009422984];
};

_vehicle_885 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4601.1182, 9965.9043, -0.0016854657], [], 0, "CAN_COLLIDE"];
  _vehicle_885 = _this;
  _this setDir -390.68713;
  _this setPos [4601.1182, 9965.9043, -0.0016854657];
};

_vehicle_892 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4583.7441, 9956.498, -0.022730559], [], 0, "CAN_COLLIDE"];
  _vehicle_892 = _this;
  _this setDir -387.40695;
  _this setPos [4583.7441, 9956.498, -0.022730559];
};

_vehicle_910 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4507.3472, 10121.84, -0.047156587], [], 0, "CAN_COLLIDE"];
  _vehicle_910 = _this;
  _this setDir -569.06024;
  _this setPos [4507.3472, 10121.84, -0.047156587];
};

_vehicle_911 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4503.8745, 10119.944, -0.049872652], [], 0, "CAN_COLLIDE"];
  _vehicle_911 = _this;
  _this setDir -568.80847;
  _this setPos [4503.8745, 10119.944, -0.049872652];
};

_vehicle_912 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4500.4043, 10118.024, -0.030036302], [], 0, "CAN_COLLIDE"];
  _vehicle_912 = _this;
  _this setDir -568.80847;
  _this setPos [4500.4043, 10118.024, -0.030036302];
};

_vehicle_913 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4497.0249, 10116.048, -0.0045846496], [], 0, "CAN_COLLIDE"];
  _vehicle_913 = _this;
  _this setDir -572.08868;
  _this setPos [4497.0249, 10116.048, -0.0045846496];
};

_vehicle_914 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4524.1357, 10132.348, -0.047156587], [], 0, "CAN_COLLIDE"];
  _vehicle_914 = _this;
  _this setDir -570.7077;
  _this setPos [4524.1357, 10132.348, -0.047156587];
};

_vehicle_915 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4520.7314, 10130.332, -0.049872652], [], 0, "CAN_COLLIDE"];
  _vehicle_915 = _this;
  _this setDir -570.7077;
  _this setPos [4520.7314, 10130.332, -0.049872652];
};

_vehicle_916 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4517.3193, 10128.308, -0.030036302], [], 0, "CAN_COLLIDE"];
  _vehicle_916 = _this;
  _this setDir -570.7077;
  _this setPos [4517.3193, 10128.308, -0.030036302];
};

_vehicle_917 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4514.0088, 10126.222, -0.0045846496], [], 0, "CAN_COLLIDE"];
  _vehicle_917 = _this;
  _this setDir -573.98792;
  _this setPos [4514.0088, 10126.222, -0.0045846496];
};

_vehicle_918 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4510.73, 10123.981, -0.015693061], [], 0, "CAN_COLLIDE"];
  _vehicle_918 = _this;
  _this setDir -573.98792;
  _this setPos [4510.73, 10123.981, -0.015693061];
};

_vehicle_919 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4541.1953, 10142.336, -0.080298699], [], 0, "CAN_COLLIDE"];
  _vehicle_919 = _this;
  _this setDir -568.85681;
  _this setPos [4541.1953, 10142.336, -0.080298699];
};

_vehicle_920 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4537.7144, 10140.423, -0.083014749], [], 0, "CAN_COLLIDE"];
  _vehicle_920 = _this;
  _this setDir -568.85681;
  _this setPos [4537.7144, 10140.423, -0.083014749];
};

_vehicle_921 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4534.2515, 10138.491, -0.063178308], [], 0, "CAN_COLLIDE"];
  _vehicle_921 = _this;
  _this setDir -568.85681;
  _this setPos [4534.2515, 10138.491, -0.063178308];
};

_vehicle_922 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4530.8687, 10136.524, -0.037726659], [], 0, "CAN_COLLIDE"];
  _vehicle_922 = _this;
  _this setDir -572.13702;
  _this setPos [4530.8687, 10136.524, -0.037726659];
};

_vehicle_923 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4527.5308, 10134.387, -0.048835054], [], 0, "CAN_COLLIDE"];
  _vehicle_923 = _this;
  _this setDir -572.13702;
  _this setPos [4527.5308, 10134.387, -0.048835054];
};

_vehicle_924 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4544.6538, 10144.238, -0.069892153], [], 0, "CAN_COLLIDE"];
  _vehicle_924 = _this;
  _this setDir -568.85681;
  _this setPos [4544.6538, 10144.238, -0.069892153];
};

_vehicle_925 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4493.6748, 10113.935, -0.015693061], [], 0, "CAN_COLLIDE"];
  _vehicle_925 = _this;
  _this setDir -572.08868;
  _this setPos [4493.6748, 10113.935, -0.015693061];
};

_vehicle_942 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [4589.4414, 9682.1172, 0.068236627], [], 0, "CAN_COLLIDE"];
  _vehicle_942 = _this;
  _this setDir 10.812428;
  _this setPos [4589.4414, 9682.1172, 0.068236627];
};

_vehicle_943 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_L", [4598.7173, 9708.3633], [], 0, "CAN_COLLIDE"];
  _vehicle_943 = _this;
  _this setDir -79.596931;
  _this setPos [4598.7173, 9708.3633];
};

_vehicle_944 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_L", [4596.1797, 9727.7666, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_944 = _this;
  _this setDir 10.7363;
  _this setPos [4596.1797, 9727.7666, -3.0517578e-005];
};

_vehicle_945 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Guardhouse", [4546.5146, 9896.6035], [], 0, "CAN_COLLIDE"];
  _vehicle_945 = _this;
  _this setDir -2.8851719;
  _this setPos [4546.5146, 9896.6035];
};

_vehicle_947 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4638.7783, 9711.2285, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_947 = _this;
  _this setDir 100.53413;
  _this setPos [4638.7783, 9711.2285, 6.1035156e-005];
};

_vehicle_949 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4651.1948, 9708.7959], [], 0, "CAN_COLLIDE"];
  _vehicle_949 = _this;
  _this setDir 100.28057;
  _this setPos [4651.1948, 9708.7959];
};

_vehicle_982 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4659.9619, 9584.4766], [], 0, "CAN_COLLIDE"];
  _vehicle_982 = _this;
  _this setDir -174.04019;
  _this setPos [4659.9619, 9584.4766];
};

_vehicle_983 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_L", [4618.2754, 9603.1826, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_983 = _this;
  _this setDir -56.72715;
  _this setPos [4618.2754, 9603.1826, -3.0517578e-005];
};

_vehicle_984 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Guardhouse", [4141.0981, 11166.364, 0.056618586], [], 0, "CAN_COLLIDE"];
  _vehicle_984 = _this;
  _this setDir -49.554932;
  _this setPos [4141.0981, 11166.364, 0.056618586];
};

_vehicle_986 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Guardhouse", [5115.4868, 9765.1094, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_986 = _this;
  _this setDir 83.91272;
  _this setPos [5115.4868, 9765.1094, -3.0517578e-005];
};

_vehicle_989 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5_Pole", [4653.1714, 9567.1016, -0.13181444], [], 0, "CAN_COLLIDE"];
  _vehicle_989 = _this;
  _this setPos [4653.1714, 9567.1016, -0.13181444];
};

_vehicle_995 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5_Pole", [4640.8164, 9558.9609, -0.19524129], [], 0, "CAN_COLLIDE"];
  _vehicle_995 = _this;
  _this setPos [4640.8164, 9558.9609, -0.19524129];
};

_vehicle_997 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5", [4634.1167, 9558.4297, -0.22359362], [], 0, "CAN_COLLIDE"];
  _vehicle_997 = _this;
  _this setDir -161.74666;
  _this setPos [4634.1167, 9558.4297, -0.22359362];
};

_vehicle_999 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5_Pole", [4636.4941, 9557.6631, -0.21487038], [], 0, "CAN_COLLIDE"];
  _vehicle_999 = _this;
  _this setPos [4636.4941, 9557.6631, -0.21487038];
};

_vehicle_1002 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5_Hole", [4647.9521, 9563.8164, -0.26164973], [], 0, "CAN_COLLIDE"];
  _vehicle_1002 = _this;
  _this setDir -29.544668;
  _this setPos [4647.9521, 9563.8164, -0.26164973];
};

_vehicle_1004 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5_Pole", [4646.2109, 9562.0713], [], 0, "CAN_COLLIDE"];
  _vehicle_1004 = _this;
  _this setPos [4646.2109, 9562.0713];
};

_vehicle_1006 = objNull;
if (true) then
{
  _this = createVehicle ["Land_a_stationhouse", [4580.1001, 9823.7422, 0.21882124], [], 0, "CAN_COLLIDE"];
  _vehicle_1006 = _this;
  _this setDir -116.18122;
  _this setPos [4580.1001, 9823.7422, 0.21882124];
};

_vehicle_1014 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4672.7578, 9705.9375, -0.010665676], [], 0, "CAN_COLLIDE"];
  _vehicle_1014 = _this;
  _this setDir 63.573978;
  _this setPos [4672.7578, 9705.9375, -0.010665676];
};

_vehicle_1023 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4670.0161, 9711.3672], [], 0, "CAN_COLLIDE"];
  _vehicle_1023 = _this;
  _this setDir 63.573978;
  _this setPos [4670.0161, 9711.3672];
};

_vehicle_1026 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4667.2368, 9716.9424, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1026 = _this;
  _this setDir 63.573978;
  _this setPos [4667.2368, 9716.9424, 3.0517578e-005];
};

_vehicle_1028 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4664.5244, 9722.4365, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1028 = _this;
  _this setDir 63.573978;
  _this setPos [4664.5244, 9722.4365, -3.0517578e-005];
};

_vehicle_1030 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4658.5513, 9723.6768, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1030 = _this;
  _this setDir 12.782376;
  _this setPos [4658.5513, 9723.6768, 3.0517578e-005];
};

_vehicle_1035 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4652.6133, 9725.0186], [], 0, "CAN_COLLIDE"];
  _vehicle_1035 = _this;
  _this setDir 12.782376;
  _this setPos [4652.6133, 9725.0186];
};

_vehicle_1038 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4646.6968, 9726.3457, 0.0070742429], [], 0, "CAN_COLLIDE"];
  _vehicle_1038 = _this;
  _this setDir 12.782376;
  _this setPos [4646.6968, 9726.3457, 0.0070742429];
};

_vehicle_1040 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4640.835, 9727.6523], [], 0, "CAN_COLLIDE"];
  _vehicle_1040 = _this;
  _this setDir 12.782376;
  _this setPos [4640.835, 9727.6523];
};

_vehicle_1042 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4634.9922, 9728.9766, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1042 = _this;
  _this setDir 12.782376;
  _this setPos [4634.9922, 9728.9766, 3.0517578e-005];
};

_vehicle_1044 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4629.0806, 9730.3086], [], 0, "CAN_COLLIDE"];
  _vehicle_1044 = _this;
  _this setDir 12.782376;
  _this setPos [4629.0806, 9730.3086];
};

_vehicle_1046 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4623.1895, 9731.6699, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1046 = _this;
  _this setDir 12.782376;
  _this setPos [4623.1895, 9731.6699, 0.00012207031];
};

_vehicle_1048 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4617.3945, 9732.9902, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1048 = _this;
  _this setDir 12.782376;
  _this setPos [4617.3945, 9732.9902, 0.00015258789];
};

_vehicle_1050 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [5003.2817, 9998.0137, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1050 = _this;
  _this setDir 57.028179;
  _this setPos [5003.2817, 9998.0137, -3.0517578e-005];
};

_vehicle_1053 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4999.9121, 10003.143], [], 0, "CAN_COLLIDE"];
  _vehicle_1053 = _this;
  _this setDir 56.855038;
  _this setPos [4999.9121, 10003.143];
};

_vehicle_1056 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [4996.5859, 10008.343, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1056 = _this;
  _this setDir 57.580814;
  _this setPos [4996.5859, 10008.343, -3.0517578e-005];
};

_vehicle_1068 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [5006.6064, 9992.916, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1068 = _this;
  _this setDir 57.028179;
  _this setPos [5006.6064, 9992.916, 3.0517578e-005];
};

_vehicle_1070 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [5009.896, 9987.791, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1070 = _this;
  _this setDir 57.028179;
  _this setPos [5009.896, 9987.791, 3.0517578e-005];
};

_vehicle_1072 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [5013.2163, 9982.6436, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1072 = _this;
  _this setDir 57.028179;
  _this setPos [5013.2163, 9982.6436, 9.1552734e-005];
};

_vehicle_1098 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [5033.292, 9952.2334], [], 0, "CAN_COLLIDE"];
  _vehicle_1098 = _this;
  _this setDir -122.98872;
  _this setPos [5033.292, 9952.2334];
};

_vehicle_1099 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [5036.6558, 9947.1006, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1099 = _this;
  _this setDir -123.16188;
  _this setPos [5036.6558, 9947.1006, 3.0517578e-005];
};

_vehicle_1100 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [5041.8184, 9950.1396], [], 0, "CAN_COLLIDE"];
  _vehicle_1100 = _this;
  _this setDir -210.78317;
  _this setPos [5041.8184, 9950.1396];
};

_vehicle_1101 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [5029.9595, 9957.3447, -0.0071105957], [], 0, "CAN_COLLIDE"];
  _vehicle_1101 = _this;
  _this setDir -122.98872;
  _this setPos [5029.9595, 9957.3447, -0.0071105957];
};

_vehicle_1102 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [5026.6729, 9962.4609, -0.013916016], [], 0, "CAN_COLLIDE"];
  _vehicle_1102 = _this;
  _this setDir -122.98872;
  _this setPos [5026.6729, 9962.4609, -0.013916016];
};

_vehicle_1103 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed2_civil", [5023.3438, 9967.6172, -0.016448975], [], 0, "CAN_COLLIDE"];
  _vehicle_1103 = _this;
  _this setDir -122.98872;
  _this setPos [5023.3438, 9967.6172, -0.016448975];
};

_vehicle_1111 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Fen1_5_Pole", [5041.8994, 9950.1084, -0.25634545], [], 0, "CAN_COLLIDE"];
  _vehicle_1111 = _this;
  _this setPos [5041.8994, 9950.1084, -0.25634545];
};

_vehicle_1115 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_L", [5055.0103, 9994.2686], [], 0, "CAN_COLLIDE"];
  _vehicle_1115 = _this;
  _this setDir 104.55592;
  _this setPos [5055.0103, 9994.2686];
};

_vehicle_1117 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_L", [5045.3926, 10020.411, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1117 = _this;
  _this setDir 415.86081;
  _this setPos [5045.3926, 10020.411, 0.00015258789];
};

_vehicle_1119 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [5035.4077, 10036.342, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1119 = _this;
  _this setDir 145.23053;
  _this setPos [5035.4077, 10036.342, 6.1035156e-005];
};

_vehicle_1122 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [5013.1289, 10027.024, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1122 = _this;
  _this setPos [5013.1289, 10027.024, -3.0517578e-005];
};

_vehicle_1124 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [5016.1494, 10058.672], [], 0, "CAN_COLLIDE"];
  _vehicle_1124 = _this;
  _this setPos [5016.1494, 10058.672];
};

_vehicle_1126 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4994.2324, 10057.19], [], 0, "CAN_COLLIDE"];
  _vehicle_1126 = _this;
  _this setDir -86.616966;
  _this setPos [4994.2324, 10057.19];
};

_vehicle_1128 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4999.4697, 10078.915, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1128 = _this;
  _this setPos [4999.4697, 10078.915, -9.1552734e-005];
};

_vehicle_1130 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [5001.8369, 10039.544, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1130 = _this;
  _this setPos [5001.8369, 10039.544, 3.0517578e-005];
};

_vehicle_1132 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4982.2739, 10074.267, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1132 = _this;
  _this setPos [4982.2739, 10074.267, 3.0517578e-005];
};

_vehicle_1134 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4967.1655, 10096.556, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1134 = _this;
  _this setPos [4967.1655, 10096.556, -3.0517578e-005];
};

_vehicle_1136 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4984.6821, 10106.627, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1136 = _this;
  _this setPos [4984.6821, 10106.627, 3.0517578e-005];
};

_vehicle_1138 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4986.563, 10110.75], [], 0, "CAN_COLLIDE"];
  _vehicle_1138 = _this;
  _this setDir -75.486214;
  _this setPos [4986.563, 10110.75];
};

_vehicle_1140 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4992.6221, 10111.502, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1140 = _this;
  _this setPos [4992.6221, 10111.502, 6.1035156e-005];
};

_vehicle_1142 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4993.293, 10104.33, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1142 = _this;
  _this setDir 94.330193;
  _this setPos [4993.293, 10104.33, -3.0517578e-005];
};

_vehicle_1144 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4956.3315, 10119.366, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1144 = _this;
  _this setPos [4956.3315, 10119.366, 6.1035156e-005];
};

_vehicle_1146 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4953.062, 10114.609, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1146 = _this;
  _this setPos [4953.062, 10114.609, 0];
};

_vehicle_1148 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4948.4272, 10125.248, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1148 = _this;
  _this setPos [4948.4272, 10125.248, -3.0517578e-005];
};

_vehicle_1150 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4940.8403, 10140.92, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1150 = _this;
  _this setPos [4940.8403, 10140.92, 3.0517578e-005];
};

_vehicle_1152 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4935.5044, 10141.21, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1152 = _this;
  _this setPos [4935.5044, 10141.21, -3.0517578e-005];
};

_vehicle_1154 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4966.3843, 10157.325, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1154 = _this;
  _this setPos [4966.3843, 10157.325, 3.0517578e-005];
};

_vehicle_1156 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4965.0508, 10154.441, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1156 = _this;
  _this setPos [4965.0508, 10154.441, 0];
};

_vehicle_1158 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4945.4814, 10180.958, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1158 = _this;
  _this setPos [4945.4814, 10180.958, -3.0517578e-005];
};

_vehicle_1160 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4962.3916, 10179.852, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1160 = _this;
  _this setPos [4962.3916, 10179.852, 3.0517578e-005];
};

_vehicle_1162 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4955.623, 10184.212, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1162 = _this;
  _this setPos [4955.623, 10184.212, -9.1552734e-005];
};

_vehicle_1164 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4970.6001, 10193.035, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1164 = _this;
  _this setPos [4970.6001, 10193.035, 3.0517578e-005];
};

_vehicle_1166 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4935.8838, 10194.1, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1166 = _this;
  _this setPos [4935.8838, 10194.1, 0];
};

_vehicle_1168 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4914.1587, 10193.798, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1168 = _this;
  _this setPos [4914.1587, 10193.798, 6.1035156e-005];
};

_vehicle_1170 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4906.9438, 10193.979, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1170 = _this;
  _this setPos [4906.9438, 10193.979, -3.0517578e-005];
};

_vehicle_1172 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4923.9155, 10209.48, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1172 = _this;
  _this setPos [4923.9155, 10209.48, 3.0517578e-005];
};

_vehicle_1174 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4908.9429, 10197.641], [], 0, "CAN_COLLIDE"];
  _vehicle_1174 = _this;
  _this setPos [4908.9429, 10197.641];
};

_vehicle_1176 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4926.6221, 10174.616, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1176 = _this;
  _this setPos [4926.6221, 10174.616, 3.0517578e-005];
};

_vehicle_1178 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4936.3267, 10188.444, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1178 = _this;
  _this setPos [4936.3267, 10188.444, -3.0517578e-005];
};

_vehicle_1180 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4963.9844, 10143.287, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1180 = _this;
  _this setPos [4963.9844, 10143.287, 9.1552734e-005];
};

_vehicle_1182 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4931.6646, 10216.99, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1182 = _this;
  _this setPos [4931.6646, 10216.99, 3.0517578e-005];
};

_vehicle_1184 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4966.3931, 10192.293, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1184 = _this;
  _this setPos [4966.3931, 10192.293, 0];
};

_vehicle_1186 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4964.5024, 10197.315, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1186 = _this;
  _this setPos [4964.5024, 10197.315, 0.00015258789];
};

_vehicle_1188 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4952.4746, 10191.332, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1188 = _this;
  _this setPos [4952.4746, 10191.332, 0];
};

_vehicle_1190 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4950.8071, 10121.386, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1190 = _this;
  _this setPos [4950.8071, 10121.386, 6.1035156e-005];
};

_vehicle_1192 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4961.3994, 10094.037, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1192 = _this;
  _this setPos [4961.3994, 10094.037, 6.1035156e-005];
};

_vehicle_1194 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4956.3936, 10095.597, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1194 = _this;
  _this setPos [4956.3936, 10095.597, -3.0517578e-005];
};

_vehicle_1196 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4975.103, 10087.048, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1196 = _this;
  _this setPos [4975.103, 10087.048, 6.1035156e-005];
};

_vehicle_1198 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4985.0591, 10116.631, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1198 = _this;
  _this setPos [4985.0591, 10116.631, 3.0517578e-005];
};

_vehicle_1200 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [5015.1719, 10063.637, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1200 = _this;
  _this setPos [5015.1719, 10063.637, -3.0517578e-005];
};

_vehicle_1202 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4997.3804, 10042.004, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1202 = _this;
  _this setPos [4997.3804, 10042.004, 6.1035156e-005];
};

_vehicle_1204 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4997.9863, 10055.021, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1204 = _this;
  _this setPos [4997.9863, 10055.021, -9.1552734e-005];
};

_vehicle_1206 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [5036.2036, 10058.479, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1206 = _this;
  _this setPos [5036.2036, 10058.479, -6.1035156e-005];
};

_vehicle_1208 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [5046.2813, 10045.524, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1208 = _this;
  _this setPos [5046.2813, 10045.524, -3.0517578e-005];
};

_vehicle_1210 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [5038.7891, 10064.628, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1210 = _this;
  _this setPos [5038.7891, 10064.628, 6.1035156e-005];
};

_vehicle_1212 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [5050.4063, 10061.127, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_1212 = _this;
  _this setPos [5050.4063, 10061.127, 0.00012207031];
};

_vehicle_1214 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [5028.769, 10085.951], [], 0, "CAN_COLLIDE"];
  _vehicle_1214 = _this;
  _this setPos [5028.769, 10085.951];
};

_vehicle_1216 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4918.8564, 10262.474, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1216 = _this;
  _this setPos [4918.8564, 10262.474, 6.1035156e-005];
};

_vehicle_1218 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4894.8062, 10271.382, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1218 = _this;
  _this setPos [4894.8062, 10271.382, 0];
};

_vehicle_1220 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4898.1851, 10272.203, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1220 = _this;
  _this setPos [4898.1851, 10272.203, 9.1552734e-005];
};

_vehicle_1222 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4898.5806, 10266.196, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_1222 = _this;
  _this setPos [4898.5806, 10266.196, 0];
};

_vehicle_1224 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4902.9712, 10262.473, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1224 = _this;
  _this setPos [4902.9712, 10262.473, -3.0517578e-005];
};

_vehicle_1226 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4883.8745, 10281.749, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1226 = _this;
  _this setPos [4883.8745, 10281.749, 3.0517578e-005];
};

_vehicle_1228 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4868.2236, 10239.382, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1228 = _this;
  _this setPos [4868.2236, 10239.382, -3.0517578e-005];
};

_vehicle_1230 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4880.979, 10226.558, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1230 = _this;
  _this setPos [4880.979, 10226.558, 3.0517578e-005];
};

_vehicle_1233 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4879.3169, 10211.184], [], 0, "CAN_COLLIDE"];
  _vehicle_1233 = _this;
  _this setPos [4879.3169, 10211.184];
};

_vehicle_1235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4799.6406, 10289.008, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1235 = _this;
  _this setPos [4799.6406, 10289.008, -6.1035156e-005];
};

_vehicle_1237 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4805.2783, 10292.693, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1237 = _this;
  _this setPos [4805.2783, 10292.693, 6.1035156e-005];
};

_vehicle_1239 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4806.5439, 10286.571, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1239 = _this;
  _this setPos [4806.5439, 10286.571, 3.0517578e-005];
};

_vehicle_1242 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Guardhouse", [5008.2676, 9978.6602, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1242 = _this;
  _this setDir -124.04932;
  _this setPos [5008.2676, 9978.6602, -6.1035156e-005];
};

_vehicle_1245 = objNull;
if (true) then
{
  _this = createVehicle ["Land_pumpa", [4862.5425, 10249.516], [], 0, "CAN_COLLIDE"];
  _vehicle_1245 = _this;
  _this setDir 214.12486;
  _this setPos [4862.5425, 10249.516];
};

_vehicle_1247 = objNull;
if (true) then
{
  _this = createVehicle ["Land_pumpa", [5017.2358, 9999.4219, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1247 = _this;
  _this setDir 300.26321;
  _this setPos [5017.2358, 9999.4219, -3.0517578e-005];
};

_vehicle_1250 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder_half_EP1", [4634.2144, 9669.0645, 1.0511863], [], 0, "CAN_COLLIDE"];
  _vehicle_1250 = _this;
  _this setDir -170.90509;
  _this setPos [4634.2144, 9669.0645, 1.0511863];
};

_vehicle_1252 = objNull;
if (true) then
{
  _this = createVehicle ["Land_ladder_half_EP1", [4630.9995, 9659.7939, 5.2803249], [], 0, "CAN_COLLIDE"];
  _vehicle_1252 = _this;
  _this setDir -170.87192;
  _this setPos [4630.9995, 9659.7939, 5.2803249];
};

};
