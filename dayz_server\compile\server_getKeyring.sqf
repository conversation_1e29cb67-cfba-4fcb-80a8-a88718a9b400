	private ["_player", "_playerUID", "_clientID", "_query", "_keyRing"];
	_player = _this;
	if (isNull _player || !(isPlayer _player)) exitWith {
    	diag_log format ["[SigmaKeyRing] Invalid player object: %1", _player];
	};
	_playerUID = getPlayerUID _player;
	_query = format ["SELECT Sigma_KeyRing FROM player_data WHERE PlayerUID = '%1'",_playerUID];
	_keyRing = [_query, 2, false] call fn_asyncCall;
	PVDZE_Sigma_KeyRing = _keyRing;
	_clientID = owner _player;
	_clientID publicVariableClient "PVDZE_Sigma_KeyRing";