/*
  CHERNARUS ENHANCEMENTS - Balota
  --------------------------------------------------------------
    Redeveloped Balota Airstrip by <PERSON>, blackwiddow
    Email: <EMAIL>
    Steam: blackwiddow20
*/

if (isServer) then {

_vehicle_1 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_main", [4891.1533, 2406.1135], [], 0, "CAN_COLLIDE"];
  _vehicle_1 = _this;
  _this setDir -60.061348;
  _this setPos [4891.1533, 2406.1135];
};

_vehicle_2 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_end15", [4682.9492, 2524.8835, 6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_2 = _this;
  _this setDir -240.39731;
  _this setPos [4682.9492, 2524.8835, 6.6757202e-006];
};

_vehicle_3 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_dirt_1", [4682.8643, 2524.8965, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_3 = _this;
  _this setDir 119.53316;
  _this setPos [4682.8643, 2524.8965, -5.7220459e-006];
};

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_dirt_3", [4752.4111, 2485.5469, 6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir 119.6379;
  _this setPos [4752.4111, 2485.5469, 6.8664551e-005];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_main", [4960.4111, 2366.2317], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setDir -60.061348;
  _this setPos [4960.4111, 2366.2317];
};

_vehicle_6 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_main", [5029.6553, 2326.3408, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_6 = _this;
  _this setDir -60.061348;
  _this setPos [5029.6553, 2326.3408, -3.8146973e-006];
};

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_dirt_3", [5133.3252, 2266.2605], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setDir -59.906937;
  _this setPos [5133.3252, 2266.2605];
};

_vehicle_8 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_dirt_1", [5202.5542, 2226.3604, 0.065215111], [], 0, "CAN_COLLIDE"];
  _vehicle_8 = _this;
  _this setDir -60.011696;
  _this setPos [5202.5542, 2226.3604, 0.065215111];
};

_vehicle_9 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_end15", [5202.3315, 2226.5452, 0.06505394], [], 0, "CAN_COLLIDE"];
  _vehicle_9 = _this;
  _this setDir -419.94223;
  _this setPos [5202.3315, 2226.5452, 0.06505394];
};

_vehicle_10 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_main_40", [5047.7617, 2315.593], [], 0, "CAN_COLLIDE"];
  _vehicle_10 = _this;
  _this setDir -59.926441;
  _this setPos [5047.7617, 2315.593];
};

_vehicle_11 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4642.8857, 2562.7258, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_11 = _this;
  _this setPos [4642.8857, 2562.7258, -1.9073486e-006];
};

_vehicle_12 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4637.0894, 2550.7429, 2.0980835e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_12 = _this;
  _this setPos [4637.0894, 2550.7429, 2.0980835e-005];
};

_vehicle_13 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4632.0957, 2538.5977, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_13 = _this;
  _this setPos [4632.0957, 2538.5977, -1.9073486e-006];
};

_vehicle_14 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4657.6191, 2553.8306, -4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_14 = _this;
  _this setPos [4657.6191, 2553.8306, -4.7683716e-006];
};

_vehicle_15 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4649.9771, 2543.6638, 0.033114433], [], 0, "CAN_COLLIDE"];
  _vehicle_15 = _this;
  _this setPos [4649.9771, 2543.6638, 0.033114433];
};

_vehicle_16 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4646.8291, 2529.7024, -0.081367493], [], 0, "CAN_COLLIDE"];
  _vehicle_16 = _this;
  _this setPos [4646.8291, 2529.7024, -0.081367493];
};

_vehicle_17 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4673.5269, 2514.4014, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_17 = _this;
  _this setPos [4673.5269, 2514.4014, -3.8146973e-006];
};

_vehicle_18 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4676.6748, 2528.3628, 0.14497089], [], 0, "CAN_COLLIDE"];
  _vehicle_18 = _this;
  _this setPos [4676.6748, 2528.3628, 0.14497089];
};

_vehicle_19 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4684.3169, 2538.5295, 0.080039978], [], 0, "CAN_COLLIDE"];
  _vehicle_19 = _this;
  _this setPos [4684.3169, 2538.5295, 0.080039978];
};

_vehicle_20 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4658.7935, 2523.2966, 0.088572502], [], 0, "CAN_COLLIDE"];
  _vehicle_20 = _this;
  _this setPos [4658.7935, 2523.2966, 0.088572502];
};

_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4663.7871, 2535.4419, 0.10070515], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setPos [4663.7871, 2535.4419, 0.10070515];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4669.5835, 2547.4248, 0.1281414], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setPos [4669.5835, 2547.4248, 0.1281414];
};

_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4725.2349, 2515.8345, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setPos [4725.2349, 2515.8345, -3.8146973e-006];
};

_vehicle_24 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4719.4385, 2503.8516, -0.022405624], [], 0, "CAN_COLLIDE"];
  _vehicle_24 = _this;
  _this setPos [4719.4385, 2503.8516, -0.022405624];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4714.4448, 2491.7063, -0.10077858], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setPos [4714.4448, 2491.7063, -0.10077858];
};

_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4739.9683, 2506.9392, -0.04810524], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setPos [4739.9683, 2506.9392, -0.04810524];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4732.3262, 2496.7725, -0.0039863586], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setPos [4732.3262, 2496.7725, -0.0039863586];
};

_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4729.1782, 2482.811, -0.16720009], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setPos [4729.1782, 2482.811, -0.16720009];
};

_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4702.4805, 2498.1121, -0.28844357], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setPos [4702.4805, 2498.1121, -0.28844357];
};

_vehicle_30 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4705.6284, 2512.0735, -0.046580315], [], 0, "CAN_COLLIDE"];
  _vehicle_30 = _this;
  _this setPos [4705.6284, 2512.0735, -0.046580315];
};

_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4713.2705, 2522.2402, -0.17547989], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setPos [4713.2705, 2522.2402, -0.17547989];
};

_vehicle_32 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4687.7471, 2507.0073, -0.15936375], [], 0, "CAN_COLLIDE"];
  _vehicle_32 = _this;
  _this setPos [4687.7471, 2507.0073, -0.15936375];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4692.7407, 2519.1526, -0.11456871], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setPos [4692.7407, 2519.1526, -0.11456871];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4698.5371, 2531.1355, -0.12814713], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setPos [4698.5371, 2531.1355, -0.12814713];
};

_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4775.541, 2487.2742, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setPos [4775.541, 2487.2742, -9.5367432e-007];
};

_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4769.7446, 2475.2913, -0.037713051], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setPos [4769.7446, 2475.2913, -0.037713051];
};

_vehicle_37 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4764.751, 2463.146, -0.10077572], [], 0, "CAN_COLLIDE"];
  _vehicle_37 = _this;
  _this setPos [4764.751, 2463.146, -0.10077572];
};

_vehicle_38 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4790.2744, 2478.3789, -0.0067901611], [], 0, "CAN_COLLIDE"];
  _vehicle_38 = _this;
  _this setPos [4790.2744, 2478.3789, -0.0067901611];
};

_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4782.6323, 2468.2122, -0.015995979], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setPos [4782.6323, 2468.2122, -0.015995979];
};

_vehicle_40 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4779.4844, 2454.2507, -0.16719723], [], 0, "CAN_COLLIDE"];
  _vehicle_40 = _this;
  _this setPos [4779.4844, 2454.2507, -0.16719723];
};

_vehicle_41 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4752.7866, 2469.5518, -0.29660416], [], 0, "CAN_COLLIDE"];
  _vehicle_41 = _this;
  _this setPos [4752.7866, 2469.5518, -0.29660416];
};

_vehicle_42 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4755.9346, 2483.5132, -0.11054611], [], 0, "CAN_COLLIDE"];
  _vehicle_42 = _this;
  _this setPos [4755.9346, 2483.5132, -0.11054611];
};

_vehicle_43 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4763.5767, 2493.6799, -0.17547703], [], 0, "CAN_COLLIDE"];
  _vehicle_43 = _this;
  _this setPos [4763.5767, 2493.6799, -0.17547703];
};

_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4738.0532, 2478.447, -0.18942356], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setPos [4738.0532, 2478.447, -0.18942356];
};

_vehicle_45 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4743.0469, 2490.5923, -0.15598392], [], 0, "CAN_COLLIDE"];
  _vehicle_45 = _this;
  _this setPos [4743.0469, 2490.5923, -0.15598392];
};

_vehicle_46 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4748.8433, 2502.5752, -0.12814426], [], 0, "CAN_COLLIDE"];
  _vehicle_46 = _this;
  _this setPos [4748.8433, 2502.5752, -0.12814426];
};

_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4830.2549, 2457.3396, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setPos [4830.2549, 2457.3396, -1.9073486e-006];
};

_vehicle_48 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4824.4585, 2445.3567, -0.037714005], [], 0, "CAN_COLLIDE"];
  _vehicle_48 = _this;
  _this setPos [4824.4585, 2445.3567, -0.037714005];
};

_vehicle_49 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4819.4648, 2433.2114, -0.076863289], [], 0, "CAN_COLLIDE"];
  _vehicle_49 = _this;
  _this setPos [4819.4648, 2433.2114, -0.076863289];
};

_vehicle_50 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4844.709, 2448.0327, -0.048103333], [], 0, "CAN_COLLIDE"];
  _vehicle_50 = _this;
  _this setPos [4844.709, 2448.0327, -0.048103333];
};

_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4837.0669, 2437.866, -0.015996933], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setPos [4837.0669, 2437.866, -0.015996933];
};

_vehicle_52 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4833.9189, 2423.9045, -0.16544056], [], 0, "CAN_COLLIDE"];
  _vehicle_52 = _this;
  _this setPos [4833.9189, 2423.9045, -0.16544056];
};

_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4807.2212, 2439.2056, -0.29660511], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setPos [4807.2212, 2439.2056, -0.29660511];
};

_vehicle_54 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4810.3691, 2453.167, -0.082529068], [], 0, "CAN_COLLIDE"];
  _vehicle_54 = _this;
  _this setPos [4810.3691, 2453.167, -0.082529068];
};

_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4818.0112, 2463.3337, -0.12727833], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setPos [4818.0112, 2463.3337, -0.12727833];
};

_vehicle_56 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4792.4878, 2448.1008, -0.18942451], [], 0, "CAN_COLLIDE"];
  _vehicle_56 = _this;
  _this setPos [4792.4878, 2448.1008, -0.18942451];
};

_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4797.4814, 2460.2461, -0.13357925], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setPos [4797.4814, 2460.2461, -0.13357925];
};

_vehicle_58 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4803.2778, 2472.229, -0.065678596], [], 0, "CAN_COLLIDE"];
  _vehicle_58 = _this;
  _this setPos [4803.2778, 2472.229, -0.065678596];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5005.5776, 2356.2805, -1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir 0.30644917;
  _this setPos [5005.5776, 2356.2805, -1.9073486e-005];
};

_vehicle_60 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4999.7173, 2344.3289, -0.068756104], [], 0, "CAN_COLLIDE"];
  _vehicle_60 = _this;
  _this setDir 0.30644917;
  _this setPos [4999.7173, 2344.3289, -0.068756104];
};

_vehicle_61 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4994.6587, 2332.2104, -0.12131786], [], 0, "CAN_COLLIDE"];
  _vehicle_61 = _this;
  _this setDir 0.30644917;
  _this setPos [4994.6587, 2332.2104, -0.12131786];
};

_vehicle_62 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5020.2632, 2347.3066, -0.046826363], [], 0, "CAN_COLLIDE"];
  _vehicle_62 = _this;
  _this setDir 0.30644917;
  _this setPos [5020.2632, 2347.3066, -0.046826363];
};

_vehicle_63 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5012.5669, 2337.1809, -0.023318291], [], 0, "CAN_COLLIDE"];
  _vehicle_63 = _this;
  _this setDir 0.30644917;
  _this setPos [5012.5669, 2337.1809, -0.023318291];
};

_vehicle_64 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5009.3442, 2323.2366, -0.22849846], [], 0, "CAN_COLLIDE"];
  _vehicle_64 = _this;
  _this setDir 0.30644917;
  _this setPos [5009.3442, 2323.2366, -0.22849846];
};

_vehicle_65 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5035.96, 2307.7927, -0.097333908], [], 0, "CAN_COLLIDE"];
  _vehicle_65 = _this;
  _this setDir 0.30644917;
  _this setPos [5035.96, 2307.7927, -0.097333908];
};

_vehicle_66 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5039.1826, 2321.7373, 0.051216125], [], 0, "CAN_COLLIDE"];
  _vehicle_66 = _this;
  _this setDir 0.30644917;
  _this setPos [5039.1826, 2321.7373, 0.051216125];
};

_vehicle_67 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5046.8789, 2331.863, 0.044149399], [], 0, "CAN_COLLIDE"];
  _vehicle_67 = _this;
  _this setDir 0.30644917;
  _this setPos [5046.8789, 2331.863, 0.044149399];
};

_vehicle_68 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5021.5557, 2317.177, -0.011109352], [], 0, "CAN_COLLIDE"];
  _vehicle_68 = _this;
  _this setDir 0.30644917;
  _this setPos [5021.5557, 2317.177, -0.011109352];
};

_vehicle_69 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5026.6143, 2329.2954, 0.030392647], [], 0, "CAN_COLLIDE"];
  _vehicle_69 = _this;
  _this setDir 0.30644917;
  _this setPos [5026.6143, 2329.2954, 0.030392647];
};

_vehicle_70 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5032.4746, 2341.2471, 0.11859322], [], 0, "CAN_COLLIDE"];
  _vehicle_70 = _this;
  _this setDir 0.30644917;
  _this setPos [5032.4746, 2341.2471, 0.11859322];
};

_vehicle_71 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4951.3062, 2386.9175, 0.013132095], [], 0, "CAN_COLLIDE"];
  _vehicle_71 = _this;
  _this setDir 0.30644917;
  _this setPos [4951.3062, 2386.9175, 0.013132095];
};

_vehicle_72 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4945.4458, 2374.9656, -0.087877274], [], 0, "CAN_COLLIDE"];
  _vehicle_72 = _this;
  _this setDir 0.30644917;
  _this setPos [4945.4458, 2374.9656, -0.087877274];
};

_vehicle_73 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4940.3872, 2362.8474, -0.028909683], [], 0, "CAN_COLLIDE"];
  _vehicle_73 = _this;
  _this setDir 0.30644917;
  _this setPos [4940.3872, 2362.8474, -0.028909683];
};

_vehicle_74 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4965.9917, 2377.9434, -0.017801285], [], 0, "CAN_COLLIDE"];
  _vehicle_74 = _this;
  _this setDir 0.30644917;
  _this setPos [4965.9917, 2377.9434, -0.017801285];
};

_vehicle_75 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4958.2954, 2367.8176, -0.042439461], [], 0, "CAN_COLLIDE"];
  _vehicle_75 = _this;
  _this setDir 0.30644917;
  _this setPos [4958.2954, 2367.8176, -0.042439461];
};

_vehicle_76 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4955.0728, 2353.8733, -0.21091938], [], 0, "CAN_COLLIDE"];
  _vehicle_76 = _this;
  _this setDir 0.30644917;
  _this setPos [4955.0728, 2353.8733, -0.21091938];
};

_vehicle_77 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4981.6885, 2338.4297, -0.099090576], [], 0, "CAN_COLLIDE"];
  _vehicle_77 = _this;
  _this setDir 0.30644917;
  _this setPos [4981.6885, 2338.4297, -0.099090576];
};

_vehicle_78 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4984.9111, 2352.374, 0.052110672], [], 0, "CAN_COLLIDE"];
  _vehicle_78 = _this;
  _this setDir 0.30644917;
  _this setPos [4984.9111, 2352.374, 0.052110672];
};

_vehicle_79 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4992.6074, 2362.4998, 0.088956833], [], 0, "CAN_COLLIDE"];
  _vehicle_79 = _this;
  _this setDir 0.30644917;
  _this setPos [4992.6074, 2362.4998, 0.088956833];
};

_vehicle_80 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4967.0029, 2347.4036, 0.0077819824], [], 0, "CAN_COLLIDE"];
  _vehicle_80 = _this;
  _this setDir 0.30644917;
  _this setPos [4967.0029, 2347.4036, 0.0077819824];
};

_vehicle_81 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4972.0615, 2359.522, 0.0303936], [], 0, "CAN_COLLIDE"];
  _vehicle_81 = _this;
  _this setDir 0.30644917;
  _this setPos [4972.0615, 2359.522, 0.0303936];
};

_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4977.9219, 2371.4739, 0.14298058], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setDir 0.30644917;
  _this setPos [4977.9219, 2371.4739, 0.14298058];
};

_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4901.1533, 2415.7463, -0.060040474], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setDir 0.30644917;
  _this setPos [4901.1533, 2415.7463, -0.060040474];
};

_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4895.293, 2403.7947, -0.087880135], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setDir 0.30644917;
  _this setPos [4895.293, 2403.7947, -0.087880135];
};

_vehicle_85 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4890.2344, 2391.6763, -0.038965225], [], 0, "CAN_COLLIDE"];
  _vehicle_85 = _this;
  _this setDir 0.30644917;
  _this setPos [4890.2344, 2391.6763, -0.038965225];
};

_vehicle_86 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4915.8389, 2406.7725, -0.10737324], [], 0, "CAN_COLLIDE"];
  _vehicle_86 = _this;
  _this setDir 0.30644917;
  _this setPos [4915.8389, 2406.7725, -0.10737324];
};

_vehicle_87 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4908.1426, 2396.6467, -0.042442322], [], 0, "CAN_COLLIDE"];
  _vehicle_87 = _this;
  _this setDir 0.30644917;
  _this setPos [4908.1426, 2396.6467, -0.042442322];
};

_vehicle_88 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4904.9199, 2382.7024, -0.12849998], [], 0, "CAN_COLLIDE"];
  _vehicle_88 = _this;
  _this setDir 0.30644917;
  _this setPos [4904.9199, 2382.7024, -0.12849998];
};

_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4931.5356, 2367.2588, -0.043743134], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir 0.30644917;
  _this setPos [4931.5356, 2367.2588, -0.043743134];
};

_vehicle_90 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4934.7583, 2381.2031, 0.052107811], [], 0, "CAN_COLLIDE"];
  _vehicle_90 = _this;
  _this setDir 0.30644917;
  _this setPos [4934.7583, 2381.2031, 0.052107811];
};

_vehicle_91 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4942.4546, 2391.3289, 0.086361885], [], 0, "CAN_COLLIDE"];
  _vehicle_91 = _this;
  _this setDir 0.30644917;
  _this setPos [4942.4546, 2391.3289, 0.086361885];
};

_vehicle_92 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4916.8501, 2376.2327, 0.0040740967], [], 0, "CAN_COLLIDE"];
  _vehicle_92 = _this;
  _this setDir 0.30644917;
  _this setPos [4916.8501, 2376.2327, 0.0040740967];
};

_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4921.9087, 2388.3511, 0.030390739], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setDir 0.30644917;
  _this setPos [4921.9087, 2388.3511, 0.030390739];
};

_vehicle_94 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4927.769, 2400.3027, 0.10328674], [], 0, "CAN_COLLIDE"];
  _vehicle_94 = _this;
  _this setDir 0.30644917;
  _this setPos [4927.769, 2400.3027, 0.10328674];
};

_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4872.2876, 2432.1902, 0.19624805], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setDir 0.30644917;
  _this setPos [4872.2876, 2432.1902, 0.19624805];
};

_vehicle_96 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4866.4268, 2420.2385, 0.15853596], [], 0, "CAN_COLLIDE"];
  _vehicle_96 = _this;
  _this setDir 0.30644917;
  _this setPos [4866.4268, 2420.2385, 0.15853596];
};

_vehicle_97 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4861.3687, 2408.1201, 0.14426136], [], 0, "CAN_COLLIDE"];
  _vehicle_97 = _this;
  _this setDir 0.30644917;
  _this setPos [4861.3687, 2408.1201, 0.14426136];
};

_vehicle_98 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4886.9731, 2423.2163, 0.14814663], [], 0, "CAN_COLLIDE"];
  _vehicle_98 = _this;
  _this setDir 0.30644917;
  _this setPos [4886.9731, 2423.2163, 0.14814663];
};

_vehicle_99 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4879.2769, 2413.0906, 0.18025303], [], 0, "CAN_COLLIDE"];
  _vehicle_99 = _this;
  _this setDir 0.30644917;
  _this setPos [4879.2769, 2413.0906, 0.18025303];
};

_vehicle_100 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4876.0542, 2399.1462, 0.10241985], [], 0, "CAN_COLLIDE"];
  _vehicle_100 = _this;
  _this setDir 0.30644917;
  _this setPos [4876.0542, 2399.1462, 0.10241985];
};

_vehicle_101 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4849.4385, 2414.5898, -0.030488014], [], 0, "CAN_COLLIDE"];
  _vehicle_101 = _this;
  _this setDir 0.30644917;
  _this setPos [4849.4385, 2414.5898, -0.030488014];
};

_vehicle_102 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4852.6611, 2428.5342, 0.085702896], [], 0, "CAN_COLLIDE"];
  _vehicle_102 = _this;
  _this setDir 0.30644917;
  _this setPos [4852.6611, 2428.5342, 0.085702896];
};

_vehicle_103 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4860.3574, 2438.6599, 0.02077198], [], 0, "CAN_COLLIDE"];
  _vehicle_103 = _this;
  _this setDir 0.30644917;
  _this setPos [4860.3574, 2438.6599, 0.02077198];
};

_vehicle_104 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4834.7529, 2423.5637, 0.0068254471], [], 0, "CAN_COLLIDE"];
  _vehicle_104 = _this;
  _this setDir 0.30644917;
  _this setPos [4834.7529, 2423.5637, 0.0068254471];
};

_vehicle_105 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4839.8115, 2435.6821, 0.040265083], [], 0, "CAN_COLLIDE"];
  _vehicle_105 = _this;
  _this setDir 0.30644917;
  _this setPos [4839.8115, 2435.6821, 0.040265083];
};

_vehicle_106 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4845.6719, 2447.6338, 0.068104744], [], 0, "CAN_COLLIDE"];
  _vehicle_106 = _this;
  _this setDir 0.30644917;
  _this setPos [4845.6719, 2447.6338, 0.068104744];
};

_vehicle_107 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4718.9146, 2482.5435, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_107 = _this;
  _this setPos [4718.9146, 2482.5435, 3.0517578e-005];
};

_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4743.623, 2470, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setPos [4743.623, 2470, -1.9073486e-006];
};

_vehicle_109 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4797.8599, 2439.8938, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_109 = _this;
  _this setPos [4797.8599, 2439.8938, 9.5367432e-007];
};

_vehicle_110 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4832.8022, 2420.0276, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_110 = _this;
  _this setPos [4832.8022, 2420.0276, -1.9073486e-006];
};

_vehicle_111 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4970.6519, 2370.6138, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_111 = _this;
  _this setPos [4970.6519, 2370.6138, -2.2888184e-005];
};

_vehicle_112 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4888.9229, 2413.0286, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_112 = _this;
  _this setPos [4888.9229, 2413.0286, -2.8610229e-006];
};

_vehicle_113 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4814.063, 2472.5493, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_113 = _this;
  _this setPos [4814.063, 2472.5493, -9.5367432e-007];
};

_vehicle_114 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4846.0981, 2452.7268, -2.0027161e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_114 = _this;
  _this setPos [4846.0981, 2452.7268, -2.0027161e-005];
};

_vehicle_115 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4856.6094, 2447.3547, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_115 = _this;
  _this setPos [4856.6094, 2447.3547, 1.9073486e-006];
};

_vehicle_116 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4870.0137, 2430.9939, -2.6702881e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_116 = _this;
  _this setPos [4870.0137, 2430.9939, -2.6702881e-005];
};

_vehicle_117 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4882.2935, 2432.5923, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_117 = _this;
  _this setPos [4882.2935, 2432.5923, 9.5367432e-007];
};

_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4896.9277, 2424.4512, 1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setPos [4896.9277, 2424.4512, 1.0490417e-005];
};

_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4911.3174, 2415.676, 1.4305115e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setPos [4911.3174, 2415.676, 1.4305115e-005];
};

_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4938.1309, 2400.3354, -6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setPos [4938.1309, 2400.3354, -6.8664551e-005];
};

_vehicle_121 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4960.6421, 2386.7222, -2.0027161e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_121 = _this;
  _this setPos [4960.6421, 2386.7222, -2.0027161e-005];
};

_vehicle_122 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4989.3521, 2371.0938, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_122 = _this;
  _this setPos [4989.3521, 2371.0938, -5.7220459e-006];
};

_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5015.2749, 2355.186, -1.7166138e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setPos [5015.2749, 2355.186, -1.7166138e-005];
};

_vehicle_124 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5031.166, 2339.6118, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_124 = _this;
  _this setPos [5031.166, 2339.6118, 0];
};

_vehicle_125 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5041.9644, 2341.0037, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_125 = _this;
  _this setPos [5041.9644, 2341.0037, -5.7220459e-006];
};

_vehicle_126 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5057.9844, 2330.9253, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_126 = _this;
  _this setPos [5057.9844, 2330.9253, 3.8146973e-006];
};

_vehicle_127 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5052.5923, 2322.1165, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_127 = _this;
  _this setPos [5052.5923, 2322.1165, 9.5367432e-007];
};

_vehicle_128 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5050.2207, 2312.6072, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_128 = _this;
  _this setPos [5050.2207, 2312.6072, 7.6293945e-006];
};

_vehicle_129 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5047.311, 2301.4412, 6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_129 = _this;
  _this setPos [5047.311, 2301.4412, 6.6757202e-006];
};

_vehicle_130 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4972.1523, 2337.489, -8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_130 = _this;
  _this setPos [4972.1523, 2337.489, -8.5830688e-006];
};

_vehicle_131 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4945.4712, 2353.1567, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_131 = _this;
  _this setPos [4945.4712, 2353.1567, 3.8146973e-006];
};

_vehicle_132 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4922.6548, 2366.8511, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_132 = _this;
  _this setPos [4922.6548, 2366.8511, -2.8610229e-006];
};

_vehicle_133 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4895.3271, 2382.2813, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_133 = _this;
  _this setPos [4895.3271, 2382.2813, 7.6293945e-006];
};

_vehicle_134 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4866.8604, 2399.6335, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_134 = _this;
  _this setPos [4866.8604, 2399.6335, -2.8610229e-006];
};

_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5060.1274, 2289.28, 1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setPos [5060.1274, 2289.28, 1.2397766e-005];
};

_vehicle_136 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5064.4976, 2299.5, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_136 = _this;
  _this setPos [5064.4976, 2299.5, -9.5367432e-007];
};

_vehicle_137 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5066.7954, 2311.6899, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_137 = _this;
  _this setPos [5066.7954, 2311.6899, 9.5367432e-006];
};

_vehicle_138 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5070.1768, 2321.5242, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_138 = _this;
  _this setPos [5070.1768, 2321.5242, 0];
};

_vehicle_139 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5090.4541, 2310.9702, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_139 = _this;
  _this setPos [5090.4541, 2310.9702, 9.5367432e-006];
};

_vehicle_140 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5082.5835, 2314.6616, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_140 = _this;
  _this setPos [5082.5835, 2314.6616, 2.8610229e-006];
};

_vehicle_141 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5081.0503, 2303.2971, 2.1934509e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_141 = _this;
  _this setPos [5081.0503, 2303.2971, 2.1934509e-005];
};

_vehicle_142 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5074.9551, 2286.7356, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_142 = _this;
  _this setPos [5074.9551, 2286.7356, 0];
};

_vehicle_143 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5083.8955, 2275.043, -1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_143 = _this;
  _this setPos [5083.8955, 2275.043, -1.2397766e-005];
};

_vehicle_144 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5090.3491, 2285.9636, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_144 = _this;
  _this setPos [5090.3491, 2285.9636, 2.8610229e-006];
};

_vehicle_145 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5094.6211, 2295.8196, -9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_145 = _this;
  _this setPos [5094.6211, 2295.8196, -9.5367432e-006];
};

_vehicle_146 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5100.5386, 2304.4778, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_146 = _this;
  _this setPos [5100.5386, 2304.4778, -2.8610229e-006];
};

_vehicle_147 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5102.1401, 2266.7334, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_147 = _this;
  _this setPos [5102.1401, 2266.7334, -5.7220459e-006];
};

_vehicle_148 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5107.25, 2274.7114, 1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_148 = _this;
  _this setPos [5107.25, 2274.7114, 1.2397766e-005];
};

_vehicle_149 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5096.4497, 2275.188, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_149 = _this;
  _this setPos [5096.4497, 2275.188, -1.9073486e-006];
};

_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5104.9878, 2290.6646, 8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setPos [5104.9878, 2290.6646, 8.5830688e-006];
};

_vehicle_151 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5113.6777, 2296.5879, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_151 = _this;
  _this setPos [5113.6777, 2296.5879, 0];
};

_vehicle_152 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5130.6343, 2288.3118, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_152 = _this;
  _this setPos [5130.6343, 2288.3118, -2.8610229e-006];
};

_vehicle_153 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5124.1099, 2278.7263, -4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_153 = _this;
  _this setPos [5124.1099, 2278.7263, -4.7683716e-006];
};

_vehicle_154 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5114.8691, 2286.2, -1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_154 = _this;
  _this setPos [5114.8691, 2286.2, -1.0490417e-005];
};

_vehicle_155 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5125.835, 2267.4199, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_155 = _this;
  _this setPos [5125.835, 2267.4199, -1.1444092e-005];
};

_vehicle_156 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5116.7505, 2258.5112, -1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_156 = _this;
  _this setPos [5116.7505, 2258.5112, -1.2397766e-005];
};

_vehicle_157 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5131.3184, 2248.4146, -8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_157 = _this;
  _this setPos [5131.3184, 2248.4146, -8.5830688e-006];
};

_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5138.4121, 2258.0869, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setPos [5138.4121, 2258.0869, 0];
};

_vehicle_159 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5144.4292, 2267.8833, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_159 = _this;
  _this setPos [5144.4292, 2267.8833, 0];
};

_vehicle_160 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5138.4048, 2278.2134, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_160 = _this;
  _this setPos [5138.4048, 2278.2134, 9.5367432e-007];
};

_vehicle_161 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5154.2339, 2274.8047, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_161 = _this;
  _this setPos [5154.2339, 2274.8047, 9.5367432e-007];
};

_vehicle_162 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5176.8682, 2261.8662, 1.6212463e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_162 = _this;
  _this setPos [5176.8682, 2261.8662, 1.6212463e-005];
};

_vehicle_163 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5164.6104, 2268.8962, 1.4305115e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_163 = _this;
  _this setPos [5164.6104, 2268.8962, 1.4305115e-005];
};

_vehicle_164 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5158.7363, 2260.8279, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_164 = _this;
  _this setPos [5158.7363, 2260.8279, -5.7220459e-006];
};

_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5154.0391, 2249.9929, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setPos [5154.0391, 2249.9929, 0];
};

_vehicle_166 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5145.5386, 2243.7283, 4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_166 = _this;
  _this setPos [5145.5386, 2243.7283, 4.7683716e-006];
};

_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5158.3174, 2231.5029, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setPos [5158.3174, 2231.5029, -2.8610229e-006];
};

_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5169.8647, 2241.6384, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setPos [5169.8647, 2241.6384, 0];
};

_vehicle_169 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5167.7983, 2251.2271, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_169 = _this;
  _this setPos [5167.7983, 2251.2271, 1.9073486e-006];
};

_vehicle_170 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5188.606, 2253.981, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_170 = _this;
  _this setPos [5188.606, 2253.981, 9.5367432e-007];
};

_vehicle_171 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5181.8198, 2246.3928, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_171 = _this;
  _this setPos [5181.8198, 2246.3928, -5.7220459e-006];
};

_vehicle_172 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5178.6782, 2230.366, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_172 = _this;
  _this setPos [5178.6782, 2230.366, -9.5367432e-007];
};

_vehicle_173 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5168.9116, 2228.9956, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_173 = _this;
  _this setPos [5168.9116, 2228.9956, 2.8610229e-006];
};

_vehicle_174 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5184.3823, 2218.8574, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_174 = _this;
  _this setPos [5184.3823, 2218.8574, 3.8146973e-006];
};

_vehicle_175 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5197.8569, 2210.5203, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_175 = _this;
  _this setPos [5197.8569, 2210.5203, -9.5367432e-007];
};

_vehicle_176 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5189.3696, 2233.4358, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_176 = _this;
  _this setPos [5189.3696, 2233.4358, 5.7220459e-006];
};

_vehicle_177 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5200.7769, 2225.3901, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_177 = _this;
  _this setPos [5200.7769, 2225.3901, 9.5367432e-007];
};

_vehicle_178 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5218.3804, 2213.4812, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_178 = _this;
  _this setPos [5218.3804, 2213.4812, 5.7220459e-006];
};

_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5194.3398, 2240.5491, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setPos [5194.3398, 2240.5491, 3.8146973e-006];
};

_vehicle_180 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5206.7808, 2232.6741, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_180 = _this;
  _this setPos [5206.7808, 2232.6741, 1.9073486e-006];
};

_vehicle_181 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5225.1636, 2221.2188, -1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_181 = _this;
  _this setPos [5225.1636, 2221.2188, -1.9073486e-005];
};

_vehicle_182 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5200.6152, 2246.583, 1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_182 = _this;
  _this setPos [5200.6152, 2246.583, 1.0490417e-005];
};

_vehicle_183 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5211.6406, 2239.2214, -4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_183 = _this;
  _this setPos [5211.6406, 2239.2214, -4.7683716e-006];
};

_vehicle_184 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5226.5957, 2229.738, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_184 = _this;
  _this setPos [5226.5957, 2229.738, -1.9073486e-006];
};

_vehicle_185 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5213.8745, 2223.1663, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_185 = _this;
  _this setPos [5213.8745, 2223.1663, 5.7220459e-006];
};

_vehicle_186 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5209.3975, 2205.5049, -1.4305115e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_186 = _this;
  _this setPos [5209.3975, 2205.5049, -1.4305115e-005];
};

_vehicle_187 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5220.7852, 2196.7537, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_187 = _this;
  _this setPos [5220.7852, 2196.7537, 1.9073486e-006];
};

_vehicle_188 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5228.5156, 2206.5078, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_188 = _this;
  _this setPos [5228.5156, 2206.5078, -2.8610229e-006];
};

_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5236.3457, 2215.5715, -1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setPos [5236.3457, 2215.5715, -1.0490417e-005];
};

_vehicle_190 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5241.251, 2222.8284, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_190 = _this;
  _this setPos [5241.251, 2222.8284, 1.9073486e-006];
};

_vehicle_191 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5254.7437, 2213.2578, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_191 = _this;
  _this setPos [5254.7437, 2213.2578, -9.5367432e-007];
};

_vehicle_192 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5247.7314, 2201.9497, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_192 = _this;
  _this setPos [5247.7314, 2201.9497, 5.7220459e-006];
};

_vehicle_193 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5241.3135, 2189.7827, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_193 = _this;
  _this setPos [5241.3135, 2189.7827, -1.9073486e-006];
};

_vehicle_194 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5234.2832, 2196.001, -9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_194 = _this;
  _this setPos [5234.2832, 2196.001, -9.5367432e-006];
};

_vehicle_195 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [5027.251, 2309.7944, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_195 = _this;
  _this setPos [5027.251, 2309.7944, 0];
};

_vehicle_196 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4679.7139, 2547.6938, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_196 = _this;
  _this setPos [4679.7139, 2547.6938, 3.8146973e-006];
};

_vehicle_197 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4664.8096, 2516.7432, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_197 = _this;
  _this setPos [4664.8096, 2516.7432, -1.9073486e-006];
};

_vehicle_198 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4724.792, 2548.1597, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_198 = _this;
  _this setDir -59.970627;
  _this setPos [4724.792, 2548.1597, 9.5367432e-006];
};

_vehicle_199 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_T_1", [4705.3838, 2544.6514], [], 0, "CAN_COLLIDE"];
  _vehicle_199 = _this;
  _this setDir -239.94124;
  _this setPos [4705.3838, 2544.6514];
};

_vehicle_200 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [4690.6157, 2563.8503, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_200 = _this;
  _this setDir 89.810715;
  _this setPos [4690.6157, 2563.8503, 7.6293945e-006];
};

_vehicle_201 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [4690.6348, 2563.7649, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_201 = _this;
  _this setDir 270.22238;
  _this setPos [4690.6348, 2563.7649, 7.6293945e-006];
};

_vehicle_202 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4678.3555, 2567.0042, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_202 = _this;
  _this setDir -63.786205;
  _this setPos [4678.3555, 2567.0042, -2.8610229e-006];
};

_vehicle_203 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [4643.2544, 2579.9519, 5.7220459e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_203 = _this;
  _this setDir 84.046516;
  _this setPos [4643.2544, 2579.9519, 5.7220459e-005];
};

_vehicle_204 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [4631.876, 2574.2263, 1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_204 = _this;
  _this setDir 48.591778;
  _this setPos [4631.876, 2574.2263, 1.9073486e-005];
};

_vehicle_205 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4620.9146, 2558.3772, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_205 = _this;
  _this setDir 27.368814;
  _this setPos [4620.9146, 2558.3772, -1.1444092e-005];
};

_vehicle_206 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4614.3623, 2539.9373, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_206 = _this;
  _this setDir 12.077979;
  _this setPos [4614.3623, 2539.9373, 3.8146973e-006];
};

_vehicle_207 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton", [4733.0259, 2450.7659, 1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_207 = _this;
  _this setDir -60.382332;
  _this setPos [4733.0259, 2450.7659, 1.1444092e-005];
};

_vehicle_208 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton_end1", [4754.1743, 2450.24, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_208 = _this;
  _this setDir -60.298275;
  _this setPos [4754.1743, 2450.24, 1.5258789e-005];
};

_vehicle_209 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_beton_end2", [4655.0596, 2506.5249, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_209 = _this;
  _this setDir -60.309937;
  _this setPos [4655.0596, 2506.5249, 2.2888184e-005];
};

_vehicle_210 = objNull;
if (true) then
{
  _this = createVehicle ["Land_a_stationhouse", [4666.9121, 2460.749, 0.1141279], [], 0, "CAN_COLLIDE"];
  _vehicle_210 = _this;
  _this setDir -151.14546;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4666.9121, 2460.749, 0.1141279];
};

_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4631.1475, 2527.3289, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setDir 0.30644917;
  _this setPos [4631.1475, 2527.3289, 9.5367432e-007];
};

_vehicle_212 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4642.5039, 2516.27, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_212 = _this;
  _this setDir 0.30644917;
  _this setPos [4642.5039, 2516.27, -9.5367432e-007];
};

_vehicle_213 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4641.9395, 2501.5779, -6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_213 = _this;
  _this setDir 0.30644917;
  _this setPos [4641.9395, 2501.5779, -6.6757202e-006];
};

_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4657.5757, 2511.4846, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setDir 0.30644917;
  _this setPos [4657.5757, 2511.4846, 0];
};

_vehicle_215 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4652.4893, 2498.135, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_215 = _this;
  _this setDir 0.30644917;
  _this setPos [4652.4893, 2498.135, -1.1444092e-005];
};

_vehicle_216 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4650.9683, 2487.1079, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_216 = _this;
  _this setDir 0.30644917;
  _this setPos [4650.9683, 2487.1079, 0];
};

_vehicle_217 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4668.5527, 2502.1575, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_217 = _this;
  _this setDir 0.30644917;
  _this setPos [4668.5527, 2502.1575, 5.7220459e-006];
};

_vehicle_218 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4664.7949, 2491.2378, -1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_218 = _this;
  _this setDir 0.30644917;
  _this setPos [4664.7949, 2491.2378, -1.335144e-005];
};

_vehicle_219 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4658.7329, 2475.1501, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_219 = _this;
  _this setDir 0.30644917;
  _this setPos [4658.7329, 2475.1501, 3.8146973e-006];
};

_vehicle_220 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4670.9243, 2467.9539, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_220 = _this;
  _this setDir 0.30644917;
  _this setPos [4670.9243, 2467.9539, 1.5258789e-005];
};

_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4676.54, 2476.688, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setDir 0.30644917;
  _this setPos [4676.54, 2476.688, -2.8610229e-006];
};

_vehicle_222 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4682.8433, 2494.6531, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_222 = _this;
  _this setDir 0.30644917;
  _this setPos [4682.8433, 2494.6531, 1.9073486e-006];
};

_vehicle_223 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4694.8862, 2490.5657, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_223 = _this;
  _this setDir 0.30644917;
  _this setPos [4694.8862, 2490.5657, -5.7220459e-006];
};

_vehicle_224 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4707.7666, 2482.2664, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_224 = _this;
  _this setDir 0.30644917;
  _this setPos [4707.7666, 2482.2664, 0];
};

_vehicle_225 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4690.9907, 2479.5964, -6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_225 = _this;
  _this setDir 0.30644917;
  _this setPos [4690.9907, 2479.5964, -6.6757202e-006];
};

_vehicle_226 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4684.3271, 2463.9641, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_226 = _this;
  _this setDir 0.30644917;
  _this setPos [4684.3271, 2463.9641, 3.8146973e-006];
};

_vehicle_227 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4695.0239, 2454.9968, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_227 = _this;
  _this setDir 0.30644917;
  _this setPos [4695.0239, 2454.9968, 9.5367432e-007];
};

_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4709.2803, 2446.561, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setDir 0.30644917;
  _this setPos [4709.2803, 2446.561, 9.5367432e-006];
};

_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4724.0093, 2438.2129, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setDir 0.30644917;
  _this setPos [4724.0093, 2438.2129, -3.8146973e-006];
};

_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4697.1001, 2468.5601, 2.3841858e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setDir 0.30644917;
  _this setPos [4697.1001, 2468.5601, 2.3841858e-005];
};

_vehicle_231 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4707.2842, 2463.5413, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_231 = _this;
  _this setDir 0.30644917;
  _this setPos [4707.2842, 2463.5413, -7.6293945e-006];
};

_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4715.3101, 2473.0361, 6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setDir 0.30644917;
  _this setPos [4715.3101, 2473.0361, 6.6757202e-006];
};

_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4725.6372, 2467.1602, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setDir 0.30644917;
  _this setPos [4725.6372, 2467.1602, 0];
};

_vehicle_234 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4739.0054, 2460.988, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_234 = _this;
  _this setDir 0.30644917;
  _this setPos [4739.0054, 2460.988, 0];
};

_vehicle_235 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4713.9307, 2454.9036, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_235 = _this;
  _this setDir 0.30644917;
  _this setPos [4713.9307, 2454.9036, -1.9073486e-006];
};

_vehicle_236 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4723.3799, 2450.438, 4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_236 = _this;
  _this setDir 0.30644917;
  _this setPos [4723.3799, 2450.438, 4.7683716e-006];
};

_vehicle_237 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4733.8823, 2445.2156, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_237 = _this;
  _this setDir 0.30644917;
  _this setPos [4733.8823, 2445.2156, -1.5258789e-005];
};

_vehicle_238 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4744.3481, 2439.7126, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_238 = _this;
  _this setDir 0.30644917;
  _this setPos [4744.3481, 2439.7126, 7.6293945e-006];
};

_vehicle_239 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4734.8472, 2440.7224, 1.4305115e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_239 = _this;
  _this setDir 0.30644917;
  _this setPos [4734.8472, 2440.7224, 1.4305115e-005];
};

_vehicle_240 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4755.1826, 2449.0447, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_240 = _this;
  _this setDir 0.30644917;
  _this setPos [4755.1826, 2449.0447, -9.5367432e-007];
};

_vehicle_241 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4767.0981, 2449.7209, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_241 = _this;
  _this setDir 0.30644917;
  _this setPos [4767.0981, 2449.7209, 5.7220459e-006];
};

_vehicle_242 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4777.877, 2444.5283, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_242 = _this;
  _this setDir 0.30644917;
  _this setPos [4777.877, 2444.5283, 0];
};

_vehicle_243 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4755.1143, 2439.8899, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_243 = _this;
  _this setDir 0.30644917;
  _this setPos [4755.1143, 2439.8899, 9.5367432e-007];
};

_vehicle_244 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4787.23, 2441.3538, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_244 = _this;
  _this setDir 0.30644917;
  _this setPos [4787.23, 2441.3538, 0];
};

_vehicle_245 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4694.5884, 2551.4937, -1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_245 = _this;
  _this setDir 0.30644917;
  _this setPos [4694.5884, 2551.4937, -1.2397766e-005];
};

_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4706.4126, 2548.98, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setDir 0.30644917;
  _this setPos [4706.4126, 2548.98, 1.9073486e-006];
};

_vehicle_247 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4711.146, 2538.6179, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_247 = _this;
  _this setDir 0.30644917;
  _this setPos [4711.146, 2538.6179, 3.8146973e-006];
};

_vehicle_248 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4804.6509, 2435.509, 1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_248 = _this;
  _this setPos [4804.6509, 2435.509, 1.335144e-005];
};

_vehicle_249 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4648.248, 2564.9172, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_249 = _this;
  _this setPos [4648.248, 2564.9172, 1.9073486e-006];
};

_vehicle_250 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4665.7539, 2554.9724, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_250 = _this;
  _this setPos [4665.7539, 2554.9724, 2.8610229e-006];
};

_vehicle_251 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4735.1011, 2515.6284, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_251 = _this;
  _this setPos [4735.1011, 2515.6284, 9.5367432e-006];
};

_vehicle_252 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4752.6055, 2505.6792, 1.6212463e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_252 = _this;
  _this setPos [4752.6055, 2505.6792, 1.6212463e-005];
};

_vehicle_253 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4769.8916, 2495.9382, 1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_253 = _this;
  _this setPos [4769.8916, 2495.9382, 1.0490417e-005];
};

_vehicle_254 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4787.3564, 2486.0193, -8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_254 = _this;
  _this setPos [4787.3564, 2486.0193, -8.5830688e-006];
};

_vehicle_255 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4804.6055, 2476.1509, -3.6239624e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_255 = _this;
  _this setPos [4804.6055, 2476.1509, -3.6239624e-005];
};

_vehicle_256 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4822.1621, 2466.2031, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_256 = _this;
  _this setPos [4822.1621, 2466.2031, -2.8610229e-006];
};

_vehicle_257 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4839.1001, 2456.4443, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_257 = _this;
  _this setPos [4839.1001, 2456.4443, 9.5367432e-007];
};

_vehicle_258 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4856.541, 2446.4482, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_258 = _this;
  _this setPos [4856.541, 2446.4482, -2.2888184e-005];
};

_vehicle_259 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4873.8794, 2436.4663, 6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_259 = _this;
  _this setPos [4873.8794, 2436.4663, 6.6757202e-006];
};

_vehicle_260 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4891.2188, 2426.4392, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_260 = _this;
  _this setPos [4891.2188, 2426.4392, 2.8610229e-006];
};

_vehicle_261 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4908.417, 2416.5767, 3.7193298e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_261 = _this;
  _this setPos [4908.417, 2416.5767, 3.7193298e-005];
};

_vehicle_262 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4925.8638, 2406.5325, 8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_262 = _this;
  _this setPos [4925.8638, 2406.5325, 8.5830688e-006];
};

_vehicle_263 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4943.0444, 2396.6167, 6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_263 = _this;
  _this setPos [4943.0444, 2396.6167, 6.6757202e-006];
};

_vehicle_264 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4960.5005, 2386.6057, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_264 = _this;
  _this setPos [4960.5005, 2386.6057, -5.7220459e-006];
};

_vehicle_265 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4977.6724, 2376.6538, -8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_265 = _this;
  _this setPos [4977.6724, 2376.6538, -8.5830688e-006];
};

_vehicle_266 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4995.0845, 2366.6272, -1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_266 = _this;
  _this setPos [4995.0845, 2366.6272, -1.0490417e-005];
};

_vehicle_267 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5012.3169, 2356.7214, -5.1498413e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_267 = _this;
  _this setPos [5012.3169, 2356.7214, -5.1498413e-005];
};

_vehicle_268 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5029.7319, 2346.7, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_268 = _this;
  _this setPos [5029.7319, 2346.7, 0];
};

_vehicle_269 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5046.9155, 2336.7642, -1.6212463e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_269 = _this;
  _this setPos [5046.9155, 2336.7642, -1.6212463e-005];
};

_vehicle_270 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5064.3242, 2326.6875, 4.3869019e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_270 = _this;
  _this setPos [5064.3242, 2326.6875, 4.3869019e-005];
};

_vehicle_271 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5081.5034, 2316.7275, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_271 = _this;
  _this setPos [5081.5034, 2316.7275, 5.7220459e-006];
};

_vehicle_272 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5098.8672, 2306.6509, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_272 = _this;
  _this setPos [5098.8672, 2306.6509, -5.7220459e-006];
};

_vehicle_273 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5116.0381, 2296.6829, 2.0980835e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_273 = _this;
  _this setPos [5116.0381, 2296.6829, 2.0980835e-005];
};

_vehicle_274 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5133.4385, 2286.6072, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_274 = _this;
  _this setPos [5133.4385, 2286.6072, -5.7220459e-006];
};

_vehicle_275 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5150.6157, 2276.7222, -8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_275 = _this;
  _this setPos [5150.6157, 2276.7222, -8.5830688e-006];
};

_vehicle_276 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5168.0479, 2266.6848, -1.4305115e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_276 = _this;
  _this setPos [5168.0479, 2266.6848, -1.4305115e-005];
};

_vehicle_277 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5185.2529, 2256.7566, 4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_277 = _this;
  _this setPos [5185.2529, 2256.7566, 4.7683716e-006];
};

_vehicle_278 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5202.709, 2246.6851, 2.8610229e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_278 = _this;
  _this setPos [5202.709, 2246.6851, 2.8610229e-005];
};

_vehicle_279 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5219.7231, 2236.8435, 1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_279 = _this;
  _this setPos [5219.7231, 2236.8435, 1.2397766e-005];
};

_vehicle_280 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5236.9922, 2226.8618, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_280 = _this;
  _this setPos [5236.9922, 2226.8618, -3.8146973e-006];
};

_vehicle_281 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5254.3809, 2216.8142, 1.8119812e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_281 = _this;
  _this setPos [5254.3809, 2216.8142, 1.8119812e-005];
};

_vehicle_282 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5236.6997, 2186.2639, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_282 = _this;
  _this setPos [5236.6997, 2186.2639, 3.8146973e-006];
};

_vehicle_283 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5219.2954, 2196.3633, -1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_283 = _this;
  _this setPos [5219.2954, 2196.3633, -1.2397766e-005];
};

_vehicle_284 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5202.0391, 2206.3308, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_284 = _this;
  _this setPos [5202.0391, 2206.3308, -1.9073486e-006];
};

_vehicle_285 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5185.0088, 2216.1001, 2.4795532e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_285 = _this;
  _this setPos [5185.0088, 2216.1001, 2.4795532e-005];
};

_vehicle_286 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5167.6128, 2226.1516, 1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_286 = _this;
  _this setPos [5167.6128, 2226.1516, 1.9073486e-005];
};

_vehicle_287 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5150.4004, 2236.0908, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_287 = _this;
  _this setPos [5150.4004, 2236.0908, 1.9073486e-006];
};

_vehicle_288 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5132.9219, 2246.1296, 4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_288 = _this;
  _this setPos [5132.9219, 2246.1296, 4.7683716e-006];
};

_vehicle_289 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5115.7495, 2256.0457, 8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_289 = _this;
  _this setPos [5115.7495, 2256.0457, 8.5830688e-006];
};

_vehicle_290 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5098.3379, 2266.1047, 1.4305115e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_290 = _this;
  _this setPos [5098.3379, 2266.1047, 1.4305115e-005];
};

_vehicle_291 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5081.1514, 2276.0952, 1.4305115e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_291 = _this;
  _this setPos [5081.1514, 2276.0952, 1.4305115e-005];
};

_vehicle_292 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5063.7349, 2286.1479, 2.0980835e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_292 = _this;
  _this setPos [5063.7349, 2286.1479, 2.0980835e-005];
};

_vehicle_293 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5046.6147, 2296.1086, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_293 = _this;
  _this setPos [5046.6147, 2296.1086, -1.9073486e-006];
};

_vehicle_294 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5029.2368, 2306.1826, 4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_294 = _this;
  _this setPos [5029.2368, 2306.1826, 4.7683716e-006];
};

_vehicle_295 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [5012.1426, 2316.0593, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_295 = _this;
  _this setPos [5012.1426, 2316.0593, 1.9073486e-006];
};

_vehicle_296 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4994.6772, 2326.094, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_296 = _this;
  _this setPos [4994.6772, 2326.094, -7.6293945e-006];
};

_vehicle_297 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4977.4287, 2336.075, 2.4795532e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_297 = _this;
  _this setPos [4977.4287, 2336.075, 2.4795532e-005];
};

_vehicle_298 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4960.0234, 2346.083, 2.0027161e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_298 = _this;
  _this setPos [4960.0234, 2346.083, 2.0027161e-005];
};

_vehicle_299 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4942.8579, 2355.967, 2.3841858e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_299 = _this;
  _this setPos [4942.8579, 2355.967, 2.3841858e-005];
};

_vehicle_300 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4925.4355, 2365.9817, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_300 = _this;
  _this setPos [4925.4355, 2365.9817, 9.5367432e-006];
};

_vehicle_301 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4908.2021, 2375.8894, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_301 = _this;
  _this setPos [4908.2021, 2375.8894, 9.5367432e-007];
};

_vehicle_302 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4890.7305, 2385.9304, 1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_302 = _this;
  _this setPos [4890.7305, 2385.9304, 1.9073486e-005];
};

_vehicle_303 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4873.6035, 2395.8398, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_303 = _this;
  _this setPos [4873.6035, 2395.8398, 9.5367432e-007];
};

_vehicle_304 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4856.2192, 2405.8635, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_304 = _this;
  _this setPos [4856.2192, 2405.8635, 4.5776367e-005];
};

_vehicle_305 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4838.9185, 2415.804, 1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_305 = _this;
  _this setPos [4838.9185, 2415.804, 1.2397766e-005];
};

_vehicle_306 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [4821.4756, 2425.866, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_306 = _this;
  _this setPos [4821.4756, 2425.866, 9.5367432e-007];
};

_vehicle_307 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4639.6289, 2569.8027, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_307 = _this;
  _this setPos [4639.6289, 2569.8027, 9.5367432e-006];
};

_vehicle_308 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4634.2544, 2560.4785, 1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_308 = _this;
  _this setPos [4634.2544, 2560.4785, 1.1444092e-005];
};

_vehicle_309 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4627.4414, 2548.5134, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_309 = _this;
  _this setPos [4627.4414, 2548.5134, -2.2888184e-005];
};

_vehicle_310 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4622.1055, 2539.0896, 1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_310 = _this;
  _this setPos [4622.1055, 2539.0896, 1.9073486e-005];
};

_vehicle_311 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5245.3843, 2181.2454, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_311 = _this;
  _this setPos [5245.3843, 2181.2454, 1.9073486e-006];
};

_vehicle_312 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5250.7432, 2190.5732, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_312 = _this;
  _this setPos [5250.7432, 2190.5732, -2.8610229e-006];
};

_vehicle_313 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5257.7305, 2202.531, 4.6730042e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_313 = _this;
  _this setPos [5257.7305, 2202.531, 4.6730042e-005];
};

_vehicle_314 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5263.0454, 2211.8699, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_314 = _this;
  _this setPos [5263.0454, 2211.8699, -3.8146973e-006];
};

_vehicle_315 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5253.019, 2194.425, 2.3841858e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_315 = _this;
  _this setPos [5253.019, 2194.425, 2.3841858e-005];
};

_vehicle_316 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5255.5303, 2198.7024, 1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_316 = _this;
  _this setPos [5255.5303, 2198.7024, 1.1444092e-005];
};

_vehicle_317 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5261.1719, 2195.3787, 0.057181221], [], 0, "CAN_COLLIDE"];
  _vehicle_317 = _this;
  _this setPos [5261.1719, 2195.3787, 0.057181221];
};

_vehicle_318 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5263.4243, 2199.1953, 0.03041596], [], 0, "CAN_COLLIDE"];
  _vehicle_318 = _this;
  _this setPos [5263.4243, 2199.1953, 0.03041596];
};

_vehicle_319 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5258.7744, 2191.01, 0.057360239], [], 0, "CAN_COLLIDE"];
  _vehicle_319 = _this;
  _this setPos [5258.7744, 2191.01, 0.057360239];
};

_vehicle_320 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5256.5322, 2187.2273, 0.046162885], [], 0, "CAN_COLLIDE"];
  _vehicle_320 = _this;
  _this setPos [5256.5322, 2187.2273, 0.046162885];
};

_vehicle_321 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5257.9976, 2197.229, 0.12285397], [], 0, "CAN_COLLIDE"];
  _vehicle_321 = _this;
  _this setPos [5257.9976, 2197.229, 0.12285397];
};

_vehicle_322 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5260.1978, 2201.0576, 0.02139977], [], 0, "CAN_COLLIDE"];
  _vehicle_322 = _this;
  _this setPos [5260.1978, 2201.0576, 0.02139977];
};

_vehicle_323 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5255.4863, 2192.9517, 0.04538966], [], 0, "CAN_COLLIDE"];
  _vehicle_323 = _this;
  _this setPos [5255.4863, 2192.9517, 0.04538966];
};

_vehicle_324 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5253.249, 2189.0933, 0.024699736], [], 0, "CAN_COLLIDE"];
  _vehicle_324 = _this;
  _this setPos [5253.249, 2189.0933, 0.024699736];
};

_vehicle_325 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5264.9834, 2193.1436, 1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_325 = _this;
  _this setPos [5264.9834, 2193.1436, 1.9073486e-005];
};

_vehicle_326 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5267.2358, 2196.9602, 0.043926001], [], 0, "CAN_COLLIDE"];
  _vehicle_326 = _this;
  _this setPos [5267.2358, 2196.9602, 0.043926001];
};

_vehicle_327 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5262.5859, 2188.7749, 0.023461001], [], 0, "CAN_COLLIDE"];
  _vehicle_327 = _this;
  _this setPos [5262.5859, 2188.7749, 0.023461001];
};

_vehicle_328 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5260.3438, 2184.9922, 0.049841225], [], 0, "CAN_COLLIDE"];
  _vehicle_328 = _this;
  _this setPos [5260.3438, 2184.9922, 0.049841225];
};

_vehicle_329 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5269.7061, 2190.3635, 0.057077836], [], 0, "CAN_COLLIDE"];
  _vehicle_329 = _this;
  _this setPos [5269.7061, 2190.3635, 0.057077836];
};

_vehicle_330 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5271.9585, 2194.1802, 0.051629812], [], 0, "CAN_COLLIDE"];
  _vehicle_330 = _this;
  _this setPos [5271.9585, 2194.1802, 0.051629812];
};

_vehicle_331 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5267.3086, 2185.9949, 0.12080258], [], 0, "CAN_COLLIDE"];
  _vehicle_331 = _this;
  _this setPos [5267.3086, 2185.9949, 0.12080258];
};

_vehicle_332 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5265.0664, 2182.2122, 0.059818901], [], 0, "CAN_COLLIDE"];
  _vehicle_332 = _this;
  _this setPos [5265.0664, 2182.2122, 0.059818901];
};

_vehicle_333 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5273.167, 2182.5723, 0.019093785], [], 0, "CAN_COLLIDE"];
  _vehicle_333 = _this;
  _this setPos [5273.167, 2182.5723, 0.019093785];
};

_vehicle_334 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5275.5645, 2186.9409, 0.037713744], [], 0, "CAN_COLLIDE"];
  _vehicle_334 = _this;
  _this setPos [5275.5645, 2186.9409, 0.037713744];
};

_vehicle_335 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5280.8906, 2177.9656, 5.3882599e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_335 = _this;
  _this setPos [5280.8906, 2177.9656, 5.3882599e-005];
};

_vehicle_336 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5283.2881, 2182.3342, 0.029972486], [], 0, "CAN_COLLIDE"];
  _vehicle_336 = _this;
  _this setPos [5283.2881, 2182.3342, 0.029972486];
};

_vehicle_337 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5288.8442, 2173.2417, 2.0980835e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_337 = _this;
  _this setPos [5288.8442, 2173.2417, 2.0980835e-005];
};

_vehicle_338 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [5291.2417, 2177.6104, 0.04552665], [], 0, "CAN_COLLIDE"];
  _vehicle_338 = _this;
  _this setPos [5291.2417, 2177.6104, 0.04552665];
};

_vehicle_339 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4630.8896, 2562.4644, 0.049248405], [], 0, "CAN_COLLIDE"];
  _vehicle_339 = _this;
  _this setPos [4630.8896, 2562.4644, 0.049248405];
};

_vehicle_340 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4624.0767, 2550.4993, 0.049214095], [], 0, "CAN_COLLIDE"];
  _vehicle_340 = _this;
  _this setPos [4624.0767, 2550.4993, 0.049214095];
};

_vehicle_341 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4627.7041, 2564.2913, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_341 = _this;
  _this setPos [4627.7041, 2564.2913, 7.6293945e-006];
};

_vehicle_342 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4620.8911, 2552.3262, -2.6702881e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_342 = _this;
  _this setPos [4620.8911, 2552.3262, -2.6702881e-005];
};

_vehicle_343 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4622.8662, 2567.0593, 9.3460083e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_343 = _this;
  _this setPos [4622.8662, 2567.0593, 9.3460083e-005];
};

_vehicle_344 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4616.0532, 2555.0942, 0.02477169], [], 0, "CAN_COLLIDE"];
  _vehicle_344 = _this;
  _this setPos [4616.0532, 2555.0942, 0.02477169];
};

_vehicle_345 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4824.877, 2496.626, 0.00011539459], [], 0, "CAN_COLLIDE"];
  _vehicle_345 = _this;
  _this setDir 0.30644917;
  _this setPos [4824.877, 2496.626, 0.00011539459];
};

_vehicle_346 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4819.2998, 2507.9434, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_346 = _this;
  _this setDir 0.30644917;
  _this setPos [4819.2998, 2507.9434, 3.8146973e-005];
};

_vehicle_347 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4811.8203, 2504.4172, 0.00015926361], [], 0, "CAN_COLLIDE"];
  _vehicle_347 = _this;
  _this setDir 0.30644917;
  _this setPos [4811.8203, 2504.4172, 0.00015926361];
};

_vehicle_348 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4802.8066, 2510.7959, 8.4877014e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_348 = _this;
  _this setDir 0.30644917;
  _this setPos [4802.8066, 2510.7959, 8.4877014e-005];
};

_vehicle_349 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4815.9536, 2517.5327, 0.00015735626], [], 0, "CAN_COLLIDE"];
  _vehicle_349 = _this;
  _this setDir 0.30644917;
  _this setPos [4815.9536, 2517.5327, 0.00015735626];
};

_vehicle_350 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4810.0674, 2524.9746, 0.00015068054], [], 0, "CAN_COLLIDE"];
  _vehicle_350 = _this;
  _this setDir 0.30644917;
  _this setPos [4810.0674, 2524.9746, 0.00015068054];
};

_vehicle_351 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4804.8252, 2532.7905, 0.00011062622], [], 0, "CAN_COLLIDE"];
  _vehicle_351 = _this;
  _this setDir 0.30644917;
  _this setPos [4804.8252, 2532.7905, 0.00011062622];
};

_vehicle_352 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4801.1113, 2545.9038, 0.00013446808], [], 0, "CAN_COLLIDE"];
  _vehicle_352 = _this;
  _this setDir 0.30644917;
  _this setPos [4801.1113, 2545.9038, 0.00013446808];
};

_vehicle_353 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4803.8237, 2563.415, 3.3378601e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_353 = _this;
  _this setDir 0.30644917;
  _this setPos [4803.8237, 2563.415, 3.3378601e-005];
};

_vehicle_354 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4810.0249, 2574.511, 9.727478e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_354 = _this;
  _this setDir 0.30644917;
  _this setPos [4810.0249, 2574.511, 9.727478e-005];
};

_vehicle_355 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4820.1367, 2584.1826, 0.00015830994], [], 0, "CAN_COLLIDE"];
  _vehicle_355 = _this;
  _this setDir 0.30644917;
  _this setPos [4820.1367, 2584.1826, 0.00015830994];
};

_vehicle_356 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_01", [4820.1064, 2543.9216, 0.019056534], [], 0, "CAN_COLLIDE"];
  _vehicle_356 = _this;
  _this setDir 25.487146;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4820.1064, 2543.9216, 0.019056534];
};

_vehicle_357 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Workshop01_01", [4826.8477, 2560.3892, -0.011618275], [], 0, "CAN_COLLIDE"];
  _vehicle_357 = _this;
  _this setDir -62.97633;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4826.8477, 2560.3892, -0.011618275];
};

_vehicle_358 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_House", [4687.8306, 2615.9187, 0.16897713], [], 0, "CAN_COLLIDE"];
  _vehicle_358 = _this;
  _this setDir 209.33881;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4687.8306, 2615.9187, 0.16897713];
};

_vehicle_359 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [4673.8857, 2589.5818, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_359 = _this;
  _this setDir -4.5184212;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4673.8857, 2589.5818, 1.9073486e-006];
};

_vehicle_360 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [4641.9614, 2616.4309, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_360 = _this;
  _this setDir -61.565468;
  _this setPos [4641.9614, 2616.4309, 3.8146973e-006];
};

_vehicle_361 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [4637.3599, 2612.4768, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_361 = _this;
  _this setDir 1.3107949;
  _this setPos [4637.3599, 2612.4768, 3.8146973e-006];
};

_vehicle_362 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [4634.5454, 2599.7454, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_362 = _this;
  _this setDir -87.233414;
  _this setPos [4634.5454, 2599.7454, 3.0517578e-005];
};

_vehicle_364 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [4629.0337, 2612.6045, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_364 = _this;
  _this setDir 3.6935856;
  _this setPos [4629.0337, 2612.6045, 1.9073486e-006];
};

_vehicle_365 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [4629.0781, 2596.7107, 1.7166138e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_365 = _this;
  _this setDir 4.2873306;
  _this setPos [4629.0781, 2596.7107, 1.7166138e-005];
};

_vehicle_366 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [4624.4438, 2604.4133, 0.2028859], [], 0, "CAN_COLLIDE"];
  _vehicle_366 = _this;
  _this setDir 88.27224;
  _this setPos [4624.4438, 2604.4133, 0.2028859];
};

_vehicle_367 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_D", [4630.6509, 2604.04, 2.3841858e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_367 = _this;
  _this setPos [4630.6509, 2604.04, 2.3841858e-005];
};

_vehicle_368 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_D", [4626.8994, 2604.1394, 3.2424927e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_368 = _this;
  _this setDir -177.16432;
  _this setPos [4626.8994, 2604.1394, 3.2424927e-005];
};

_vehicle_369 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small", [4623.4248, 2599.9373, 2.8610229e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_369 = _this;
  _this setDir 89.511276;
  _this setPos [4623.4248, 2599.9373, 2.8610229e-005];
};

_vehicle_370 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [4623.5991, 2602.7961, -8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_370 = _this;
  _this setDir -89.289299;
  _this setPos [4623.5991, 2602.7961, -8.5830688e-006];
};

_vehicle_371 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [4614.3301, 2540.0051, -2.0980835e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_371 = _this;
  _this setDir -168.1801;
  _this setPos [4614.3301, 2540.0051, -2.0980835e-005];
};

_vehicle_372 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4606.707, 2522.0869, 4.1007996e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_372 = _this;
  _this setDir 214.61226;
  _this setPos [4606.707, 2522.0869, 4.1007996e-005];
};

_vehicle_373 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [4586.5815, 2496.7429, -1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_373 = _this;
  _this setDir 18.270689;
  _this setPos [4586.5815, 2496.7429, -1.9073486e-005];
};

_vehicle_374 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4583.1641, 2478.074, -1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_374 = _this;
  _this setDir 363.64832;
  _this setPos [4583.1641, 2478.074, -1.9073486e-005];
};

_vehicle_375 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [4583.248, 2478.5361, -4.1007996e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_375 = _this;
  _this setDir 188.77171;
  _this setPos [4583.248, 2478.5361, -4.1007996e-005];
};

_vehicle_376 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4571.5107, 2453.229, -4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_376 = _this;
  _this setDir 380.26639;
  _this setPos [4571.5107, 2453.229, -4.7683716e-006];
};

_vehicle_377 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4568.0591, 2439.8171, 1.001358e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_377 = _this;
  _this setDir 369.16595;
  _this setPos [4568.0591, 2439.8171, 1.001358e-005];
};

_vehicle_378 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [4567.4146, 2434.5872, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_378 = _this;
  _this setDir 9.2840776;
  _this setPos [4567.4146, 2434.5872, -7.6293945e-006];
};

_vehicle_379 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [4604.8916, 2427.2783, 2.6226044e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_379 = _this;
  _this setDir 183.20041;
  _this setPos [4604.8916, 2427.2783, 2.6226044e-005];
};

_vehicle_380 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4602.002, 2396.3616, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_380 = _this;
  _this setDir 5.9292059;
  _this setPos [4602.002, 2396.3616, 9.5367432e-007];
};

_vehicle_381 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4599.458, 2371.5332, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_381 = _this;
  _this setDir 5.9292059;
  _this setPos [4599.458, 2371.5332, 9.5367432e-006];
};

_vehicle_382 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [4598.8364, 2365.3643, -3.3378601e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_382 = _this;
  _this setDir 6.1234426;
  _this setPos [4598.8364, 2365.3643, -3.3378601e-006];
};

_vehicle_383 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_L_1", [4599.2944, 2393.6042, -4.7683716e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_383 = _this;
  _this setDir 5.7971959;
  _this setPos [4599.2944, 2393.6042, -4.7683716e-007];
};

_vehicle_384 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_GeneralStore_01a", [4584.0195, 2385.7747, -0.037808102], [], 0, "CAN_COLLIDE"];
  _vehicle_384 = _this;
  _this setDir 95.880661;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4584.0195, 2385.7747, -0.037808102];
};

_vehicle_385 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4598.9697, 2407.8489, -4.7683716e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_385 = _this;
  _this setDir 0.30644917;
  _this setPos [4598.9697, 2407.8489, -4.7683716e-007];
};

_vehicle_386 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4596.7593, 2392.4761, 6.2465668e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_386 = _this;
  _this setDir 0.30644917;
  _this setPos [4596.7593, 2392.4761, 6.2465668e-005];
};

_vehicle_387 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4594.1221, 2379.7786, 3.3378601e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_387 = _this;
  _this setDir 0.30644917;
  _this setPos [4594.1221, 2379.7786, 3.3378601e-006];
};

_vehicle_388 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4587.9102, 2399.2415, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_388 = _this;
  _this setDir 0.30644917;
  _this setPos [4587.9102, 2399.2415, 0];
};

_vehicle_389 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Obstacle_saddle", [4593.4897, 2377.2876, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_389 = _this;
  _this setDir -84.552254;
  _this setPos [4593.4897, 2377.2876, 9.5367432e-007];
};

_vehicle_390 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Obstacle_saddle", [4594.0874, 2382.3682], [], 0, "CAN_COLLIDE"];
  _vehicle_390 = _this;
  _this setDir -84.552254;
  _this setPos [4594.0874, 2382.3682];
};

_vehicle_391 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Obstacle_saddle", [4594.5322, 2387.1812, -7.1525574e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_391 = _this;
  _this setDir -84.255379;
  _this setPos [4594.5322, 2387.1812, -7.1525574e-006];
};

_vehicle_392 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Obstacle_saddle", [4595.0952, 2392.2627, -4.0531158e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_392 = _this;
  _this setDir -84.552254;
  _this setPos [4595.0952, 2392.2627, -4.0531158e-005];
};

_vehicle_393 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Obstacle_saddle", [4595.5576, 2397.073], [], 0, "CAN_COLLIDE"];
  _vehicle_393 = _this;
  _this setDir -84.278221;
  _this setPos [4595.5576, 2397.073];
};

_vehicle_394 = objNull;
if (true) then
{
  _this = createVehicle ["LADAWreck", [4595.5483, 2389.6599, -1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_394 = _this;
  _this setDir -94.381966;
  _this setPos [4595.5483, 2389.6599, -1.0490417e-005];
};

_vehicle_395 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHSSidl2", [4596.8579, 2386.9858, 1.6689301e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_395 = _this;
  _this setDir 96.107697;
  _this setPos [4596.8579, 2386.9858, 1.6689301e-005];
};

_vehicle_396 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHSInd", [4625.394, 2604.0957, 4.6730042e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_396 = _this;
  _this setDir -270.28214;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4625.394, 2604.0957, 4.6730042e-005];
};

_vehicle_397 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVSidl1", [4670.2461, 2464.968, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_397 = _this;
  _this setDir 119.10101;
  _this setPos [4670.2461, 2464.968, -5.7220459e-006];
};

_vehicle_398 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4632.3315, 2585.5876, 0.039593846], [], 0, "CAN_COLLIDE"];
  _vehicle_398 = _this;
  _this setDir -63.30283;
  _this setPos [4632.3315, 2585.5876, 0.039593846];
};

_vehicle_399 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4626.4272, 2578.8486, 3.7193298e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_399 = _this;
  _this setDir -45.904411;
  _this setPos [4626.4272, 2578.8486, 3.7193298e-005];
};

_vehicle_400 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4620.9614, 2571.6843, -3.7193298e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_400 = _this;
  _this setDir -54.792164;
  _this setPos [4620.9614, 2571.6843, -3.7193298e-005];
};

_vehicle_401 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4616.2817, 2564.04, -0.058746662], [], 0, "CAN_COLLIDE"];
  _vehicle_401 = _this;
  _this setDir -59.26173;
  _this setPos [4616.2817, 2564.04, -0.058746662];
};

_vehicle_402 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4612.5884, 2555.8369, 0.1705516], [], 0, "CAN_COLLIDE"];
  _vehicle_402 = _this;
  _this setDir -67.118813;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4612.5884, 2555.8369, 0.1705516];
};

_vehicle_403 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4610.5698, 2547.0859, -0.40209544], [], 0, "CAN_COLLIDE"];
  _vehicle_403 = _this;
  _this setDir -80.098106;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4610.5698, 2547.0859, -0.40209544];
};

_vehicle_404 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4608.4204, 2538.3979, -0.036561847], [], 0, "CAN_COLLIDE"];
  _vehicle_404 = _this;
  _this setDir -74.251961;
  _this setPos [4608.4204, 2538.3979, -0.036561847];
};

_vehicle_405 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4603.0967, 2525.0879, -8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_405 = _this;
  _this setDir -60.979084;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4603.0967, 2525.0879, -8.5830688e-006];
};

_vehicle_406 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4597.4204, 2518.2051, -0.21058485], [], 0, "CAN_COLLIDE"];
  _vehicle_406 = _this;
  _this setDir -48.339985;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4597.4204, 2518.2051, -0.21058485];
};

_vehicle_407 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4591.4375, 2511.5959, -0.093248293], [], 0, "CAN_COLLIDE"];
  _vehicle_407 = _this;
  _this setDir -47.424625;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4591.4375, 2511.5959, -0.093248293];
};

_vehicle_408 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4585.9175, 2504.6589, 2.3841858e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_408 = _this;
  _this setDir -52.419502;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4585.9175, 2504.6589, 2.3841858e-005];
};

_vehicle_409 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [4606.3232, 2533.72, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_409 = _this;
  _this setDir -44.674461;
  _this setPos [4606.3232, 2533.72, 1.9073486e-006];
};

_vehicle_410 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4582.6909, 2496.4341, 0.040037841], [], 0, "CAN_COLLIDE"];
  _vehicle_410 = _this;
  _this setDir -72.119614;
  _this setPos [4582.6909, 2496.4341, 0.040037841];
};

_vehicle_411 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4662.3867, 2449.1472, -0.15130992], [], 0, "CAN_COLLIDE"];
  _vehicle_411 = _this;
  _this setDir 29.732466;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4662.3867, 2449.1472, -0.15130992];
};

_vehicle_412 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4657.1138, 2452.2969, -0.2672039], [], 0, "CAN_COLLIDE"];
  _vehicle_412 = _this;
  _this setDir 28.593924;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4657.1138, 2452.2969, -0.2672039];
};

_vehicle_413 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4651.9253, 2455.0676, -0.15260054], [], 0, "CAN_COLLIDE"];
  _vehicle_413 = _this;
  _this setDir 29.947924;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4651.9253, 2455.0676, -0.15260054];
};

_vehicle_414 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4641.6572, 2461.4019, -0.11431293], [], 0, "CAN_COLLIDE"];
  _vehicle_414 = _this;
  _this setDir 32.720455;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4641.6572, 2461.4019, -0.11431293];
};

_vehicle_415 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4646.7813, 2458.1323, -0.085440427], [], 0, "CAN_COLLIDE"];
  _vehicle_415 = _this;
  _this setDir 31.532967;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4646.7813, 2458.1323, -0.085440427];
};

_vehicle_416 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4691.1113, 2451.5049, -0.0381561], [], 0, "CAN_COLLIDE"];
  _vehicle_416 = _this;
  _this setDir 30.326609;
  _this setPos [4691.1113, 2451.5049, -0.0381561];
};

_vehicle_417 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4696.3818, 2448.5085, -0.025528356], [], 0, "CAN_COLLIDE"];
  _vehicle_417 = _this;
  _this setDir 29.139116;
  _this setPos [4696.3818, 2448.5085, -0.025528356];
};

_vehicle_418 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4701.7021, 2445.5222, -2.0980835e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_418 = _this;
  _this setDir 29.403004;
  _this setPos [4701.7021, 2445.5222, -2.0980835e-005];
};

_vehicle_419 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4706.9463, 2442.47, -0.038139883], [], 0, "CAN_COLLIDE"];
  _vehicle_419 = _this;
  _this setDir 30.550638;
  _this setPos [4706.9463, 2442.47, -0.038139883];
};

_vehicle_420 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4712.2666, 2439.4854, -0.13843304], [], 0, "CAN_COLLIDE"];
  _vehicle_420 = _this;
  _this setDir 29.435064;
  _this setPos [4712.2666, 2439.4854, -0.13843304];
};

_vehicle_421 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4717.5229, 2436.4749, -0.17430358], [], 0, "CAN_COLLIDE"];
  _vehicle_421 = _this;
  _this setDir 29.679949;
  _this setPos [4717.5229, 2436.4749, -0.17430358];
};

_vehicle_422 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4637.7881, 2463.8384, 0.01907376], [], 0, "CAN_COLLIDE"];
  _vehicle_422 = _this;
  _this setDir -149.42102;
  _this setPos [4637.7881, 2463.8384, 0.01907376];
};

_vehicle_423 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4630.0195, 2468.3923, 0.076310307], [], 0, "CAN_COLLIDE"];
  _vehicle_423 = _this;
  _this setDir -149.42102;
  _this setPos [4630.0195, 2468.3923, 0.076310307];
};

_vehicle_424 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4622.2671, 2472.9666, -1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_424 = _this;
  _this setDir -149.42102;
  _this setPos [4622.2671, 2472.9666, -1.9073486e-005];
};

_vehicle_425 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4614.4985, 2477.5205, 0.07964325], [], 0, "CAN_COLLIDE"];
  _vehicle_425 = _this;
  _this setDir -149.42102;
  _this setPos [4614.4985, 2477.5205, 0.07964325];
};

_vehicle_426 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4606.7886, 2482.1165, 3.9100647e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_426 = _this;
  _this setDir -149.42102;
  _this setPos [4606.7886, 2482.1165, 3.9100647e-005];
};

_vehicle_427 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4599.02, 2486.6704, 0.074036598], [], 0, "CAN_COLLIDE"];
  _vehicle_427 = _this;
  _this setDir -149.42102;
  _this setPos [4599.02, 2486.6704, 0.074036598];
};

_vehicle_428 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small", [4590.5024, 2477.7708, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_428 = _this;
  _this setDir 12.458302;
  _this setPos [4590.5024, 2477.7708, 1.5258789e-005];
};

_vehicle_429 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_bagfence_long", [4587.7012, 2478.2129, -0.15654278], [], 0, "CAN_COLLIDE"];
  _vehicle_429 = _this;
  _this setDir 8.0099192;
  _this setPos [4587.7012, 2478.2129, -0.15654278];
};

_vehicle_430 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [4587.1792, 2479.1929, 0.33722606], [], 0, "CAN_COLLIDE"];
  _vehicle_430 = _this;
  _this setDir 6.6756806;
  _this setPos [4587.1792, 2479.1929, 0.33722606];
};

_vehicle_431 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [4594.7456, 2482.5491, 0.063853264], [], 0, "CAN_COLLIDE"];
  _vehicle_431 = _this;
  _this setDir -74.196991;
  _this setPos [4594.7456, 2482.5491, 0.063853264];
};

_vehicle_432 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [4592.6787, 2489.0029, 0.059332263], [], 0, "CAN_COLLIDE"];
  _vehicle_432 = _this;
  _this setDir -149.54643;
  _this setPos [4592.6787, 2489.0029, 0.059332263];
};

_vehicle_433 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [4579.2837, 2484.842, -0.0022428706], [], 0, "CAN_COLLIDE"];
  _vehicle_433 = _this;
  _this setDir -83.51001;
  _this setPos [4579.2837, 2484.842, -0.0022428706];
};

_vehicle_434 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [4580.6143, 2493.2649, -0.041859113], [], 0, "CAN_COLLIDE"];
  _vehicle_434 = _this;
  _this setDir -74.643616;
  _this setPos [4580.6143, 2493.2649, -0.041859113];
};

_vehicle_435 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [4722.4561, 2434.0452, -2.4795532e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_435 = _this;
  _this setDir -380.23273;
  _this setPos [4722.4561, 2434.0452, -2.4795532e-005];
};

_vehicle_436 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4729.3677, 2433.9282, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_436 = _this;
  _this setDir -4.3116465;
  _this setPos [4729.3677, 2433.9282, 1.9073486e-006];
};

_vehicle_437 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4735.5435, 2434.416, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_437 = _this;
  _this setDir -4.3116465;
  _this setPos [4735.5435, 2434.416, 7.6293945e-006];
};

_vehicle_438 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4741.4824, 2434.8594, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_438 = _this;
  _this setDir -4.2873821;
  _this setPos [4741.4824, 2434.8594, 7.6293945e-006];
};

_vehicle_439 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4747.5986, 2435.3298, -0.019070897], [], 0, "CAN_COLLIDE"];
  _vehicle_439 = _this;
  _this setDir -4.6382065;
  _this setPos [4747.5986, 2435.3298, -0.019070897];
};

_vehicle_440 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHSInd", [4587.5068, 2480.0315, 2.0027161e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_440 = _this;
  _this setDir -350.63333;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4587.5068, 2480.0315, 2.0027161e-005];
};

_vehicle_441 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4627.2842, 2527.2603], [], 0, "CAN_COLLIDE"];
  _vehicle_441 = _this;
  _this setPos [4627.2842, 2527.2603];
};

_vehicle_442 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4632.6953, 2516.0801, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_442 = _this;
  _this setPos [4632.6953, 2516.0801, 2.8610229e-006];
};

_vehicle_443 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4637.7627, 2505.5818, 1.8119812e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_443 = _this;
  _this setPos [4637.7627, 2505.5818, 1.8119812e-005];
};

_vehicle_444 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4643.2612, 2494.2173, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_444 = _this;
  _this setPos [4643.2612, 2494.2173, -2.8610229e-006];
};

_vehicle_445 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4648.418, 2483.5825, 8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_445 = _this;
  _this setPos [4648.418, 2483.5825, 8.5830688e-006];
};

_vehicle_446 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4759.2119, 2436.4448, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_446 = _this;
  _this setPos [4759.2119, 2436.4448, 1.9073486e-006];
};

_vehicle_447 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4770.9448, 2437.426, -8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_447 = _this;
  _this setPos [4770.9448, 2437.426, -8.5830688e-006];
};

_vehicle_448 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4783.1929, 2438.4578, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_448 = _this;
  _this setPos [4783.1929, 2438.4578, 1.9073486e-006];
};

_vehicle_449 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4723.7729, 2524.6782, 1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_449 = _this;
  _this setPos [4723.7729, 2524.6782, 1.0490417e-005];
};

_vehicle_450 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4719.3257, 2533.6304, 1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_450 = _this;
  _this setPos [4719.3257, 2533.6304, 1.1444092e-005];
};

_vehicle_451 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4718.6855, 2543.269, -1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_451 = _this;
  _this setPos [4718.6855, 2543.269, -1.0490417e-005];
};

_vehicle_452 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4719.3501, 2548.2637, -8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_452 = _this;
  _this setPos [4719.3501, 2548.2637, -8.5830688e-006];
};

_vehicle_453 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4722.0308, 2552.7683, -2.0980835e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_453 = _this;
  _this setPos [4722.0308, 2552.7683, -2.0980835e-005];
};

_vehicle_454 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4701.9819, 2558.2439, 1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_454 = _this;
  _this setPos [4701.9819, 2558.2439, 1.9073486e-005];
};

_vehicle_455 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4704.7407, 2562.7649, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_455 = _this;
  _this setPos [4704.7407, 2562.7649, 5.7220459e-006];
};

_vehicle_456 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4698.126, 2555.2556, 1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_456 = _this;
  _this setPos [4698.126, 2555.2556, 1.335144e-005];
};

_vehicle_457 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4689.3457, 2550.9893, -1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_457 = _this;
  _this setPos [4689.3457, 2550.9893, -1.0490417e-005];
};

_vehicle_458 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4678.3564, 2550.4951, 4.6730042e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_458 = _this;
  _this setPos [4678.3564, 2550.4951, 4.6730042e-005];
};

_vehicle_459 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4624.7256, 2543.791, -4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_459 = _this;
  _this setPos [4624.7256, 2543.791, -4.7683716e-006];
};

_vehicle_460 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4753.2251, 2434.0725, -0.076302662], [], 0, "CAN_COLLIDE"];
  _vehicle_460 = _this;
  _this setDir 29.783426;
  _this setPos [4753.2251, 2434.0725, -0.076302662];
};

_vehicle_461 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4758.5542, 2431.2268, -0.038145609], [], 0, "CAN_COLLIDE"];
  _vehicle_461 = _this;
  _this setDir 26.027151;
  _this setPos [4758.5542, 2431.2268, -0.038145609];
};

_vehicle_462 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4763.9023, 2428.4043, -0.11442156], [], 0, "CAN_COLLIDE"];
  _vehicle_462 = _this;
  _this setDir 29.783426;
  _this setPos [4763.9023, 2428.4043, -0.11442156];
};

_vehicle_463 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4627.3813, 2556.5183, 0.076556161], [], 0, "CAN_COLLIDE"];
  _vehicle_463 = _this;
  _this setPos [4627.3813, 2556.5183, 0.076556161];
};

_vehicle_464 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4624.1958, 2558.3452, 0.027315097], [], 0, "CAN_COLLIDE"];
  _vehicle_464 = _this;
  _this setPos [4624.1958, 2558.3452, 0.027315097];
};

_vehicle_465 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4619.3579, 2561.1133, 0.04273887], [], 0, "CAN_COLLIDE"];
  _vehicle_465 = _this;
  _this setPos [4619.3579, 2561.1133, 0.04273887];
};

_vehicle_466 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [4630.7461, 2554.5325, 0.027318912], [], 0, "CAN_COLLIDE"];
  _vehicle_466 = _this;
  _this setPos [4630.7461, 2554.5325, 0.027318912];
};

_vehicle_467 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4769.166, 2425.448, -0.13344572], [], 0, "CAN_COLLIDE"];
  _vehicle_467 = _this;
  _this setDir 29.783426;
  _this setPos [4769.166, 2425.448, -0.13344572];
};

_vehicle_468 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4774.4775, 2422.4697, -0.15068553], [], 0, "CAN_COLLIDE"];
  _vehicle_468 = _this;
  _this setDir 28.922842;
  _this setPos [4774.4775, 2422.4697, -0.15068553];
};

_vehicle_469 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4758.7251, 2501.4829, 1.7166138e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_469 = _this;
  _this setPos [4758.7251, 2501.4829, 1.7166138e-005];
};

_vehicle_470 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_House", [4807.668, 2405.262, 0.13531551], [], 0, "CAN_COLLIDE"];
  _vehicle_470 = _this;
  _this setDir 391.36713;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4807.668, 2405.262, 0.13531551];
};

_vehicle_471 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4779.7554, 2419.4358, -0.17022461], [], 0, "CAN_COLLIDE"];
  _vehicle_471 = _this;
  _this setDir 29.783426;
  _this setPos [4779.7554, 2419.4358, -0.17022461];
};

_vehicle_472 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4785.0552, 2416.4082, -0.19073565], [], 0, "CAN_COLLIDE"];
  _vehicle_472 = _this;
  _this setDir 30.785374;
  _this setPos [4785.0552, 2416.4082, -0.19073565];
};

_vehicle_473 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4790.3184, 2413.3757, -0.20982757], [], 0, "CAN_COLLIDE"];
  _vehicle_473 = _this;
  _this setDir 28.922842;
  _this setPos [4790.3184, 2413.3757, -0.20982757];
};

_vehicle_474 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_BoardsPack1", [4787.0259, 2418.1208], [], 0, "CAN_COLLIDE"];
  _vehicle_474 = _this;
  _this setDir 109.08772;
  _this setPos [4787.0259, 2418.1208];
};

_vehicle_475 = objNull;
if (true) then
{
  _this = createVehicle ["Barrels", [4784.5728, 2420.6174, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_475 = _this;
  _this setDir 55.660072;
  _this setPos [4784.5728, 2420.6174, -1.9073486e-006];
};

_vehicle_476 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_BoardsPack2", [4796.0908, 2418.6965, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_476 = _this;
  _this setDir 31.953381;
  _this setPos [4796.0908, 2418.6965, -9.5367432e-007];
};

_vehicle_477 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Coil_EP1", [4780.9282, 2422.1409, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_477 = _this;
  _this setPos [4780.9282, 2422.1409, 3.8146973e-006];
};

_vehicle_478 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4827.5117, 2389.9387, 6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_478 = _this;
  _this setDir 30.038223;
  _this setPos [4827.5117, 2389.9387, 6.6757202e-006];
};

_vehicle_479 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4822.3071, 2393.0732, -0.0025568008], [], 0, "CAN_COLLIDE"];
  _vehicle_479 = _this;
  _this setDir 31.900755;
  _this setPos [4822.3071, 2393.0732, -0.0025568008];
};

_vehicle_480 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4862.7832, 2368.2424, -0.060692977], [], 0, "CAN_COLLIDE"];
  _vehicle_480 = _this;
  _this setDir -59.622654;
  _this setPos [4862.7832, 2368.2424, -0.060692977];
};

_vehicle_481 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4832.6572, 2386.8101, -0.034780502], [], 0, "CAN_COLLIDE"];
  _vehicle_481 = _this;
  _this setDir 31.900755;
  _this setPos [4832.6572, 2386.8101, -0.034780502];
};

_vehicle_482 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4858.5854, 2367.0571, -0.078895941], [], 0, "CAN_COLLIDE"];
  _vehicle_482 = _this;
  _this setDir 29.905249;
  _this setPos [4858.5854, 2367.0571, -0.078895941];
};

_vehicle_483 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4866.8252, 2369.2534, -0.20132168], [], 0, "CAN_COLLIDE"];
  _vehicle_483 = _this;
  _this setDir 29.685474;
  _this setPos [4866.8252, 2369.2534, -0.20132168];
};

_vehicle_484 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4872.041, 2366.2686, -0.14540607], [], 0, "CAN_COLLIDE"];
  _vehicle_484 = _this;
  _this setDir 29.920656;
  _this setPos [4872.041, 2366.2686, -0.14540607];
};

_vehicle_485 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4877.2563, 2363.269, -0.17240028], [], 0, "CAN_COLLIDE"];
  _vehicle_485 = _this;
  _this setDir 29.741882;
  _this setPos [4877.2563, 2363.269, -0.17240028];
};

_vehicle_486 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_L", [4848.2363, 2380.2061, -0.22888698], [], 0, "CAN_COLLIDE"];
  _vehicle_486 = _this;
  _this setDir -149.51315;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4848.2363, 2380.2061, -0.22888698];
};

_vehicle_487 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4837.8267, 2383.6499, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_487 = _this;
  _this setDir 31.900755;
  _this setPos [4837.8267, 2383.6499, -1.9073486e-006];
};

_vehicle_488 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4853.4556, 2370.0171, -0.097090274], [], 0, "CAN_COLLIDE"];
  _vehicle_488 = _this;
  _this setDir 29.402615;
  _this setPos [4853.4556, 2370.0171, -0.097090274];
};

_vehicle_489 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [4881.0801, 2361.1638, 8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_489 = _this;
  _this setDir -150.72551;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4881.0801, 2361.1638, 8.5830688e-006];
};

_vehicle_490 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4892.9507, 2354.3179, -0.12864451], [], 0, "CAN_COLLIDE"];
  _vehicle_490 = _this;
  _this setDir 29.741882;
  _this setPos [4892.9507, 2354.3179, -0.12864451];
};

_vehicle_491 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4887.7354, 2357.3174, -0.10504484], [], 0, "CAN_COLLIDE"];
  _vehicle_491 = _this;
  _this setDir 29.920656;
  _this setPos [4887.7354, 2357.3174, -0.10504484];
};

_vehicle_492 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4882.5195, 2360.3022, -0.11839676], [], 0, "CAN_COLLIDE"];
  _vehicle_492 = _this;
  _this setDir 29.685474;
  _this setPos [4882.5195, 2360.3022, -0.11839676];
};

_vehicle_493 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4903.4531, 2348.3586, -0.11659205], [], 0, "CAN_COLLIDE"];
  _vehicle_493 = _this;
  _this setDir 28.808868;
  _this setPos [4903.4531, 2348.3586, -0.11659205];
};

_vehicle_494 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4898.2305, 2351.304, -0.11313885], [], 0, "CAN_COLLIDE"];
  _vehicle_494 = _this;
  _this setDir 29.741882;
  _this setPos [4898.2305, 2351.304, -0.11313885];
};

_vehicle_495 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4919.1001, 2339.3708, -0.065776452], [], 0, "CAN_COLLIDE"];
  _vehicle_495 = _this;
  _this setDir 30.015919;
  _this setPos [4919.1001, 2339.3708, -0.065776452];
};

_vehicle_496 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4913.8994, 2342.3953, -0.066935137], [], 0, "CAN_COLLIDE"];
  _vehicle_496 = _this;
  _this setDir 30.194693;
  _this setPos [4913.8994, 2342.3953, -0.066935137];
};

_vehicle_497 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4908.6978, 2345.405, -0.081459999], [], 0, "CAN_COLLIDE"];
  _vehicle_497 = _this;
  _this setDir 29.959511;
  _this setPos [4908.6978, 2345.405, -0.081459999];
};

_vehicle_498 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4948.1758, 2322.4912, -0.15439807], [], 0, "CAN_COLLIDE"];
  _vehicle_498 = _this;
  _this setDir 30.149549;
  _this setPos [4948.1758, 2322.4912, -0.15439807];
};

_vehicle_499 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4942.9175, 2325.5427, -0.17924392], [], 0, "CAN_COLLIDE"];
  _vehicle_499 = _this;
  _this setDir 30.149549;
  _this setPos [4942.9175, 2325.5427, -0.17924392];
};

_vehicle_500 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4937.7231, 2328.5793, -0.1019185], [], 0, "CAN_COLLIDE"];
  _vehicle_500 = _this;
  _this setDir 30.328323;
  _this setPos [4937.7231, 2328.5793, -0.1019185];
};

_vehicle_501 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4932.5293, 2331.6011, -0.09723404], [], 0, "CAN_COLLIDE"];
  _vehicle_501 = _this;
  _this setDir 30.093142;
  _this setPos [4932.5293, 2331.6011, -0.09723404];
};

_vehicle_502 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [4926.9678, 2335.3828, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_502 = _this;
  _this setDir -713.76526;
  _this setPos [4926.9678, 2335.3828, -3.8146973e-006];
};

_vehicle_503 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [4922.8784, 2335.1235, 2.0980835e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_503 = _this;
  _this setPos [4922.8784, 2335.1235, 2.0980835e-005];
};

_vehicle_504 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [4928.7002, 2333.3887, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_504 = _this;
  _this setPos [4928.7002, 2333.3887, 3.8146973e-006];
};

_vehicle_505 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [4724.522, 2435.3494, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_505 = _this;
  _this setPos [4724.522, 2435.3494, 3.8146973e-006];
};

_vehicle_506 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [4859.3643, 2374.9141, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_506 = _this;
  _this setDir 11.276886;
  _this setPos [4859.3643, 2374.9141, 2.8610229e-006];
};

_vehicle_507 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Glass_Cullet_01", [4860.457, 2373.0156, 6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_507 = _this;
  _this setPos [4860.457, 2373.0156, 6.6757202e-006];
};

_vehicle_508 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4953.4434, 2319.4399, -0.10626616], [], 0, "CAN_COLLIDE"];
  _vehicle_508 = _this;
  _this setDir 29.810282;
  _this setPos [4953.4434, 2319.4399, -0.10626616];
};

_vehicle_509 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4958.6904, 2316.3909, -0.080795422], [], 0, "CAN_COLLIDE"];
  _vehicle_509 = _this;
  _this setDir 30.960924;
  _this setPos [4958.6904, 2316.3909, -0.080795422];
};

_vehicle_510 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4963.8389, 2313.2903, -0.10274287], [], 0, "CAN_COLLIDE"];
  _vehicle_510 = _this;
  _this setDir 31.196106;
  _this setPos [4963.8389, 2313.2903, -0.10274287];
};

_vehicle_511 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4968.9854, 2310.1755, -0.13894227], [], 0, "CAN_COLLIDE"];
  _vehicle_511 = _this;
  _this setDir 31.017332;
  _this setPos [4968.9854, 2310.1755, -0.13894227];
};

_vehicle_512 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4725.7217, 2590.9619, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_512 = _this;
  _this setPos [4725.7217, 2590.9619, 2.2888184e-005];
};

_vehicle_513 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4726.7324, 2597.7578, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_513 = _this;
  _this setPos [4726.7324, 2597.7578, -5.7220459e-006];
};

_vehicle_514 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4785.4575, 2421.7146, -4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_514 = _this;
  _this setPos [4785.4575, 2421.7146, -4.7683716e-006];
};

_vehicle_515 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4974.2007, 2307.0603, -0.093258068], [], 0, "CAN_COLLIDE"];
  _vehicle_515 = _this;
  _this setDir 30.686888;
  _this setPos [4974.2007, 2307.0603, -0.093258068];
};

_vehicle_516 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4979.3828, 2304.0159, -0.093414225], [], 0, "CAN_COLLIDE"];
  _vehicle_516 = _this;
  _this setDir 30.92207;
  _this setPos [4979.3828, 2304.0159, -0.093414225];
};

_vehicle_517 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4984.5449, 2300.9255, -0.16209191], [], 0, "CAN_COLLIDE"];
  _vehicle_517 = _this;
  _this setDir 30.743296;
  _this setPos [4984.5449, 2300.9255, -0.16209191];
};

_vehicle_518 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [4989.771, 2297.8201, -0.1498526], [], 0, "CAN_COLLIDE"];
  _vehicle_518 = _this;
  _this setDir 30.743296;
  _this setPos [4989.771, 2297.8201, -0.1498526];
};

_vehicle_519 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [4995.0039, 2294.7246, -0.11513695], [], 0, "CAN_COLLIDE"];
  _vehicle_519 = _this;
  _this setDir 30.404028;
  _this setPos [4995.0039, 2294.7246, -0.11513695];
};

_vehicle_520 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5000.2881, 2291.7075, -0.09510386], [], 0, "CAN_COLLIDE"];
  _vehicle_520 = _this;
  _this setDir 30.960924;
  _this setPos [5000.2881, 2291.7075, -0.09510386];
};

_vehicle_521 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5005.4238, 2288.5906, -0.099199966], [], 0, "CAN_COLLIDE"];
  _vehicle_521 = _this;
  _this setDir 31.196106;
  _this setPos [5005.4238, 2288.5906, -0.099199966];
};

_vehicle_522 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5020.9536, 2279.1702, -0.045854568], [], 0, "CAN_COLLIDE"];
  _vehicle_522 = _this;
  _this setDir 31.263357;
  _this setPos [5020.9536, 2279.1702, -0.045854568];
};

_vehicle_523 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [5026.147, 2276.0996, -0.055365562], [], 0, "CAN_COLLIDE"];
  _vehicle_523 = _this;
  _this setDir 29.996361;
  _this setPos [5026.147, 2276.0996, -0.055365562];
};

_vehicle_524 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5031.3994, 2273.0396, -0.14288816], [], 0, "CAN_COLLIDE"];
  _vehicle_524 = _this;
  _this setDir 30.553257;
  _this setPos [5031.3994, 2273.0396, -0.14288816];
};

_vehicle_525 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5036.6772, 2269.9565, -0.11919747], [], 0, "CAN_COLLIDE"];
  _vehicle_525 = _this;
  _this setDir 30.788439;
  _this setPos [5036.6772, 2269.9565, -0.11919747];
};

_vehicle_526 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5041.8809, 2266.8674, -0.13355811], [], 0, "CAN_COLLIDE"];
  _vehicle_526 = _this;
  _this setDir 30.609665;
  _this setPos [5041.8809, 2266.8674, -0.13355811];
};

_vehicle_527 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_IronPipes_EP1", [4974.918, 2309.3445, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_527 = _this;
  _this setDir 31.858831;
  _this setPos [4974.918, 2309.3445, -5.7220459e-006];
};

_vehicle_528 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [5035.8203, 2270.4573, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_528 = _this;
  _this setDir -150.72551;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5035.8203, 2270.4573, -2.8610229e-006];
};

_vehicle_529 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5010.5962, 2285.4163, -0.099393561], [], 0, "CAN_COLLIDE"];
  _vehicle_529 = _this;
  _this setDir 31.196106;
  _this setPos [5010.5962, 2285.4163, -0.099393561];
};

_vehicle_530 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5015.7783, 2282.3367, -0.057222228], [], 0, "CAN_COLLIDE"];
  _vehicle_530 = _this;
  _this setDir 31.196106;
  _this setPos [5015.7783, 2282.3367, -0.057222228];
};

_vehicle_531 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5087.166, 2237.8203, -0.055146795], [], 0, "CAN_COLLIDE"];
  _vehicle_531 = _this;
  _this setDir 73.931366;
  _this setPos [5087.166, 2237.8203, -0.055146795];
};

_vehicle_532 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5083.7207, 2242.2424, -0.061284643], [], 0, "CAN_COLLIDE"];
  _vehicle_532 = _this;
  _this setDir 30.306021;
  _this setPos [5083.7207, 2242.2424, -0.061284643];
};

_vehicle_533 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5078.417, 2245.2808, -0.052047867], [], 0, "CAN_COLLIDE"];
  _vehicle_533 = _this;
  _this setDir 30.070839;
  _this setPos [5078.417, 2245.2808, -0.052047867];
};

_vehicle_534 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [5073.1616, 2248.2888, -0.073312581], [], 0, "CAN_COLLIDE"];
  _vehicle_534 = _this;
  _this setDir 29.513943;
  _this setPos [5073.1616, 2248.2888, -0.073312581];
};

_vehicle_535 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5067.9634, 2251.3767, -0.097724609], [], 0, "CAN_COLLIDE"];
  _vehicle_535 = _this;
  _this setDir 30.780939;
  _this setPos [5067.9634, 2251.3767, -0.097724609];
};

_vehicle_536 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5062.707, 2254.4932, -0.090494424], [], 0, "CAN_COLLIDE"];
  _vehicle_536 = _this;
  _this setDir 30.713688;
  _this setPos [5062.707, 2254.4932, -0.090494424];
};

_vehicle_537 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5057.4512, 2257.6277, -0.12065005], [], 0, "CAN_COLLIDE"];
  _vehicle_537 = _this;
  _this setDir 30.713688;
  _this setPos [5057.4512, 2257.6277, -0.12065005];
};

_vehicle_538 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5052.2329, 2260.73, -0.17474546], [], 0, "CAN_COLLIDE"];
  _vehicle_538 = _this;
  _this setDir 30.713688;
  _this setPos [5052.2329, 2260.73, -0.17474546];
};

_vehicle_539 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5047.0713, 2263.8037, -0.11920846], [], 0, "CAN_COLLIDE"];
  _vehicle_539 = _this;
  _this setDir 30.478506;
  _this setPos [5047.0713, 2263.8037, -0.11920846];
};

_vehicle_540 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [5095.3579, 2231.9253, -0.021719085], [], 0, "CAN_COLLIDE"];
  _vehicle_540 = _this;
  _this setDir -60.643906;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5095.3579, 2231.9253, -0.021719085];
};

_vehicle_541 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5086.4438, 2232.3208, -0.13352302], [], 0, "CAN_COLLIDE"];
  _vehicle_541 = _this;
  _this setDir 119.72938;
  _this setPos [5086.4438, 2232.3208, -0.13352302];
};

_vehicle_542 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5083.4434, 2227.0286, -0.12370548], [], 0, "CAN_COLLIDE"];
  _vehicle_542 = _this;
  _this setDir 119.72938;
  _this setPos [5083.4434, 2227.0286, -0.12370548];
};

_vehicle_543 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [5082.7334, 2221.5168, -0.18500984], [], 0, "CAN_COLLIDE"];
  _vehicle_543 = _this;
  _this setDir 74.600433;
  _this setPos [5082.7334, 2221.5168, -0.18500984];
};

_vehicle_544 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5091.5151, 2213.9873, -0.16029879], [], 0, "CAN_COLLIDE"];
  _vehicle_544 = _this;
  _this setDir 30.306021;
  _this setPos [5091.5151, 2213.9873, -0.16029879];
};

_vehicle_545 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5086.2114, 2217.0256, -0.15189788], [], 0, "CAN_COLLIDE"];
  _vehicle_545 = _this;
  _this setDir 30.070839;
  _this setPos [5086.2114, 2217.0256, -0.15189788];
};

_vehicle_546 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_L", [5107.3867, 2213.9172, -0.21272156], [], 0, "CAN_COLLIDE"];
  _vehicle_546 = _this;
  _this setDir -149.51315;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5107.3867, 2213.9172, -0.21272156];
};

_vehicle_547 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2_noRoad", [5101.124, 2239.0437, 0.0095908251], [], 0, "CAN_COLLIDE"];
  _vehicle_547 = _this;
  _this setDir 29.4804;
  _this setPos [5101.124, 2239.0437, 0.0095908251];
};

_vehicle_548 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [5132.6626, 2391.6252, 2.6702881e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_548 = _this;
  _this setDir -150.58792;
  _this setPos [5132.6626, 2391.6252, 2.6702881e-005];
};

_vehicle_549 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Glass_Cullet_01", [5134.3584, 2391.9463, 2.0027161e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_549 = _this;
  _this setPos [5134.3584, 2391.9463, 2.0027161e-005];
};

_vehicle_550 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Glass_Cullet_01", [5132.0078, 2393.3418, 1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_550 = _this;
  _this setPos [5132.0078, 2393.3418, 1.9073486e-005];
};

_vehicle_551 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [5137.6567, 2401.0466, 1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_551 = _this;
  _this setDir -31.129759;
  _this setPos [5137.6567, 2401.0466, 1.0490417e-005];
};

_vehicle_552 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [5121.5522, 2204.5327], [], 0, "CAN_COLLIDE"];
  _vehicle_552 = _this;
  _this setPos [5121.5522, 2204.5327];
};

_vehicle_553 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [5128.2739, 2219.0256, -2.3841858e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_553 = _this;
  _this setDir -781.23846;
  _this setPos [5128.2739, 2219.0256, -2.3841858e-005];
};

_vehicle_554 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [5129.4546, 2221.542], [], 0, "CAN_COLLIDE"];
  _vehicle_554 = _this;
  _this setDir -25.127068;
  _this setPos [5129.4546, 2221.542];
};

_vehicle_555 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [5111.5996, 2201.9622, -0.14407848], [], 0, "CAN_COLLIDE"];
  _vehicle_555 = _this;
  _this setDir 30.451792;
  _this setPos [5111.5996, 2201.9622, -0.14407848];
};

_vehicle_556 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5124.7295, 2194.3503, -0.22283372], [], 0, "CAN_COLLIDE"];
  _vehicle_556 = _this;
  _this setDir 29.519701;
  _this setPos [5124.7295, 2194.3503, -0.22283372];
};

_vehicle_557 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5116.9014, 2198.8599, -0.14279473], [], 0, "CAN_COLLIDE"];
  _vehicle_557 = _this;
  _this setDir 30.713688;
  _this setPos [5116.9014, 2198.8599, -0.14279473];
};

_vehicle_558 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5122.1187, 2195.8542, -0.16308935], [], 0, "CAN_COLLIDE"];
  _vehicle_558 = _this;
  _this setDir 29.522072;
  _this setPos [5122.1187, 2195.8542, -0.16308935];
};

_vehicle_559 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5173.4248, 2178.7891, -0.1146602], [], 0, "CAN_COLLIDE"];
  _vehicle_559 = _this;
  _this setDir 11.011163;
  _this setPos [5173.4248, 2178.7891, -0.1146602];
};

_vehicle_560 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5167.4204, 2179.9072, -0.12835768], [], 0, "CAN_COLLIDE"];
  _vehicle_560 = _this;
  _this setDir 10.775981;
  _this setPos [5167.4204, 2179.9072, -0.12835768];
};

_vehicle_561 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [5161.4409, 2180.9539, -0.16030623], [], 0, "CAN_COLLIDE"];
  _vehicle_561 = _this;
  _this setDir 10.219086;
  _this setPos [5161.4409, 2180.9539, -0.16030623];
};

_vehicle_562 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5155.5601, 2182.042, -0.17621145], [], 0, "CAN_COLLIDE"];
  _vehicle_562 = _this;
  _this setDir 10.892336;
  _this setPos [5155.5601, 2182.042, -0.17621145];
};

_vehicle_563 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5149.7246, 2183.1948, -0.13333388], [], 0, "CAN_COLLIDE"];
  _vehicle_563 = _this;
  _this setDir 11.41883;
  _this setPos [5149.7246, 2183.1948, -0.13333388];
};

_vehicle_564 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5143.8599, 2184.3752, -0.16284262], [], 0, "CAN_COLLIDE"];
  _vehicle_564 = _this;
  _this setDir 11.694565;
  _this setPos [5143.8599, 2184.3752, -0.16284262];
};

_vehicle_565 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_runway_BluelightB", [4624.6963, 2543.7241, 3.7193298e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_565 = _this;
  _this setPos [4624.6963, 2543.7241, 3.7193298e-005];
};

_vehicle_566 = objNull;
if (true) then
{
  _this = createVehicle ["Barrels", [4650.9814, 2465.8159], [], 0, "CAN_COLLIDE"];
  _vehicle_566 = _this;
  _this setDir 27.149609;
  _this setPos [4650.9814, 2465.8159];
};

_vehicle_567 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5229.1011, 2156.6477, -0.09533827], [], 0, "CAN_COLLIDE"];
  _vehicle_567 = _this;
  _this setDir 30.645304;
  _this setPos [5229.1011, 2156.6477, -0.09533827];
};

_vehicle_568 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5223.8306, 2159.7524, -0.011109628], [], 0, "CAN_COLLIDE"];
  _vehicle_568 = _this;
  _this setDir 30.410122;
  _this setPos [5223.8306, 2159.7524, -0.011109628];
};

_vehicle_569 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [5218.603, 2162.7815, -0.090765312], [], 0, "CAN_COLLIDE"];
  _vehicle_569 = _this;
  _this setDir 29.853226;
  _this setPos [5218.603, 2162.7815, -0.090765312];
};

_vehicle_570 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5213.333, 2165.8113, -0.054386564], [], 0, "CAN_COLLIDE"];
  _vehicle_570 = _this;
  _this setDir 31.120222;
  _this setPos [5213.333, 2165.8113, -0.054386564];
};

_vehicle_571 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5208.0952, 2168.9587, -0.089097835], [], 0, "CAN_COLLIDE"];
  _vehicle_571 = _this;
  _this setDir 31.052971;
  _this setPos [5208.0952, 2168.9587, -0.089097835];
};

_vehicle_572 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5202.8579, 2172.1245, -0.13756409], [], 0, "CAN_COLLIDE"];
  _vehicle_572 = _this;
  _this setDir 31.052971;
  _this setPos [5202.8579, 2172.1245, -0.13756409];
};

_vehicle_573 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5179.3828, 2177.666, -0.11299568], [], 0, "CAN_COLLIDE"];
  _vehicle_573 = _this;
  _this setDir 11.41883;
  _this setPos [5179.3828, 2177.666, -0.11299568];
};

_vehicle_574 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5185.3789, 2176.4751, -0.10718895], [], 0, "CAN_COLLIDE"];
  _vehicle_574 = _this;
  _this setDir 11.41883;
  _this setPos [5185.3789, 2176.4751, -0.10718895];
};

_vehicle_575 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5191.3276, 2175.3223, -0.11461351], [], 0, "CAN_COLLIDE"];
  _vehicle_575 = _this;
  _this setDir 10.892336;
  _this setPos [5191.3276, 2175.3223, -0.11461351];
};

_vehicle_576 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [5197.333, 2174.1833, -0.11037091], [], 0, "CAN_COLLIDE"];
  _vehicle_576 = _this;
  _this setDir 10.219086;
  _this setPos [5197.333, 2174.1833, -0.11037091];
};

_vehicle_577 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [5224.0747, 2160.0491, 1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_577 = _this;
  _this setDir -150.72551;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5224.0747, 2160.0491, 1.0490417e-005];
};

_vehicle_578 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [5101.0156, 2226.2678, -6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_578 = _this;
  _this setDir 84.258629;
  _this setPos [5101.0156, 2226.2678, -6.6757202e-006];
};

_vehicle_579 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks", [5137.5146, 2192.6074, 0.041864131], [], 0, "CAN_COLLIDE"];
  _vehicle_579 = _this;
  _this setDir -149.90033;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5137.5146, 2192.6074, 0.041864131];
};

_vehicle_580 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench1", [5123.1899, 2202.2773, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_580 = _this;
  _this setDir -36.392319;
  _this setPos [5123.1899, 2202.2773, 9.5367432e-007];
};

_vehicle_581 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench1", [5117.98, 2202.4744, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_581 = _this;
  _this setDir -124.33583;
  _this setPos [5117.98, 2202.4744, 9.5367432e-007];
};

_vehicle_582 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench1", [5119.5967, 2207.2954, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_582 = _this;
  _this setDir -36.097347;
  _this setPos [5119.5967, 2207.2954, -1.9073486e-006];
};

_vehicle_583 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [5120.1299, 2203.6497, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_583 = _this;
  _this setPos [5120.1299, 2203.6497, 0];
};

_vehicle_584 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [5102.7349, 2240.9956, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_584 = _this;
  _this setPos [5102.7349, 2240.9956, -9.5367432e-007];
};

_vehicle_585 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVOld", [5130.7891, 2200.3047, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_585 = _this;
  _this setDir -57.275528;
  _this setPos [5130.7891, 2200.3047, 9.5367432e-007];
};

_vehicle_586 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVOld", [5100.0322, 2240.2356, -3.4332275e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_586 = _this;
  _this setDir 27.511106;
  _this setPos [5100.0322, 2240.2356, -3.4332275e-005];
};

_vehicle_587 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5234.3257, 2153.5823, -0.14402519], [], 0, "CAN_COLLIDE"];
  _vehicle_587 = _this;
  _this setDir 30.645304;
  _this setPos [5234.3257, 2153.5823, -0.14402519];
};

_vehicle_588 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [5239.5161, 2150.5012, -0.16314943], [], 0, "CAN_COLLIDE"];
  _vehicle_588 = _this;
  _this setDir 30.645304;
  _this setPos [5239.5161, 2150.5012, -0.16314943];
};

_vehicle_589 = objNull;
if (true) then
{
  _this = createVehicle ["BMP2Wreck", [5246.5303, 2147.8467, -0.0024021417], [], 0, "CAN_COLLIDE"];
  _vehicle_589 = _this;
  _this setDir -163.31079;
  _this setPos [5246.5303, 2147.8467, -0.0024021417];
};

_vehicle_590 = objNull;
if (true) then
{
  _this = createVehicle ["T72Wreck", [5258.2817, 2135.9866, -2.1934509e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_590 = _this;
  _this setDir -76.458336;
  _this setPos [5258.2817, 2135.9866, -2.1934509e-005];
};

_vehicle_591 = objNull;
if (true) then
{
  _this = createVehicle ["T72WreckTurret", [5265.7837, 2138.2554, -0.31634033], [], 0, "CAN_COLLIDE"];
  _vehicle_591 = _this;
  _this setDir -202.06233;
  _this setPos [5265.7837, 2138.2554, -0.31634033];
};

_vehicle_592 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [5243.4766, 2148.301, 0.012686316], [], 0, "CAN_COLLIDE"];
  _vehicle_592 = _this;
  _this setDir -150.63895;
  _this setPos [5243.4766, 2148.301, 0.012686316];
};

_vehicle_593 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [5250.9795, 2143.8469, -3.1471252e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_593 = _this;
  _this setDir -157.9136;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5250.9795, 2143.8469, -3.1471252e-005];
};

_vehicle_594 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [5259.9209, 2138.7163, 8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_594 = _this;
  _this setDir -154.13899;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5259.9209, 2138.7163, 8.5830688e-006];
};

_vehicle_595 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5289.5098, 2136.7131, -0.036842667], [], 0, "CAN_COLLIDE"];
  _vehicle_595 = _this;
  _this setDir -241.15494;
  _this setPos [5289.5098, 2136.7131, -0.036842667];
};

_vehicle_596 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [5284.458, 2129.9478, 0.021678779], [], 0, "CAN_COLLIDE"];
  _vehicle_596 = _this;
  _this setDir -22.287531;
  _this setPos [5284.458, 2129.9478, 0.021678779];
};

_vehicle_597 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [5267.7188, 2134.0491, 5.4359436e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_597 = _this;
  _this setDir -154.13899;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5267.7188, 2134.0491, 5.4359436e-005];
};

_vehicle_598 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5280.96, 2126.3267, 0.012410646], [], 0, "CAN_COLLIDE"];
  _vehicle_598 = _this;
  _this setDir -149.92767;
  _this setPos [5280.96, 2126.3267, 0.012410646];
};

_vehicle_599 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [5274.4346, 2130.1973, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_599 = _this;
  _this setDir -154.13899;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5274.4346, 2130.1973, -2.2888184e-005];
};

_vehicle_600 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5293.8477, 2144.53, -0.080167867], [], 0, "CAN_COLLIDE"];
  _vehicle_600 = _this;
  _this setDir -241.15494;
  _this setPos [5293.8477, 2144.53, -0.080167867];
};

_vehicle_601 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5295.7017, 2153.2288, -0.076526985], [], 0, "CAN_COLLIDE"];
  _vehicle_601 = _this;
  _this setDir -261.34995;
  _this setPos [5295.7017, 2153.2288, -0.076526985];
};

_vehicle_602 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5295.8047, 2162.1584, -4.2915344e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_602 = _this;
  _this setDir -270.80695;
  _this setPos [5295.8047, 2162.1584, -4.2915344e-006];
};

_vehicle_603 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5295.6499, 2171.0029, -0.017022986], [], 0, "CAN_COLLIDE"];
  _vehicle_603 = _this;
  _this setDir -270.80695;
  _this setPos [5295.6499, 2171.0029, -0.017022986];
};

_vehicle_604 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5295.5269, 2180.0071, -5.6266785e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_604 = _this;
  _this setDir -270.80695;
  _this setPos [5295.5269, 2180.0071, -5.6266785e-005];
};

_vehicle_605 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5295.3823, 2188.8811, -3.194809e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_605 = _this;
  _this setDir -270.80695;
  _this setPos [5295.3823, 2188.8811, -3.194809e-005];
};

_vehicle_606 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5295.2876, 2197.897, -2.3841858e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_606 = _this;
  _this setDir -270.80695;
  _this setPos [5295.2876, 2197.897, -2.3841858e-005];
};

_vehicle_607 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5295.1738, 2206.8994, -3.3855438e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_607 = _this;
  _this setDir -270.80695;
  _this setPos [5295.1738, 2206.8994, -3.3855438e-005];
};

_vehicle_608 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [5291.3018, 2211.6428, 0.00010919571], [], 0, "CAN_COLLIDE"];
  _vehicle_608 = _this;
  _this setDir -323.41089;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5291.3018, 2211.6428, 0.00010919571];
};

_vehicle_609 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [5282.7974, 2216.9929, 7.5817108e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_609 = _this;
  _this setDir -323.41089;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5282.7974, 2216.9929, 7.5817108e-005];
};

_vehicle_610 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [5268.7563, 2225.8501, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_610 = _this;
  _this setDir -323.41089;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5268.7563, 2225.8501, 3.8146973e-006];
};

_vehicle_611 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5253.1016, 2235.478, -0.019089971], [], 0, "CAN_COLLIDE"];
  _vehicle_611 = _this;
  _this setDir -328.88727;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5253.1016, 2235.478, -0.019089971];
};

_vehicle_612 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [5259.5684, 2231.5784, 0.038137026], [], 0, "CAN_COLLIDE"];
  _vehicle_612 = _this;
  _this setDir -325.44638;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5259.5684, 2231.5784, 0.038137026];
};

_vehicle_613 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5245.4116, 2240.1172, -0.090593673], [], 0, "CAN_COLLIDE"];
  _vehicle_613 = _this;
  _this setDir -328.88727;
  _this setPos [5245.4116, 2240.1172, -0.090593673];
};

_vehicle_614 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5237.7393, 2244.7705, -0.040520292], [], 0, "CAN_COLLIDE"];
  _vehicle_614 = _this;
  _this setDir -328.88727;
  _this setPos [5237.7393, 2244.7705, -0.040520292];
};

_vehicle_615 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5230.0356, 2249.4138, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_615 = _this;
  _this setDir -328.88727;
  _this setPos [5230.0356, 2249.4138, -1.9073486e-006];
};

_vehicle_616 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5222.3726, 2254.0239, -4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_616 = _this;
  _this setDir -328.88727;
  _this setPos [5222.3726, 2254.0239, -4.7683716e-006];
};

_vehicle_617 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5214.6978, 2258.6345, -0.019068988], [], 0, "CAN_COLLIDE"];
  _vehicle_617 = _this;
  _this setDir -328.88727;
  _this setPos [5214.6978, 2258.6345, -0.019068988];
};

_vehicle_618 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5206.9922, 2263.2654, -0.05992797], [], 0, "CAN_COLLIDE"];
  _vehicle_618 = _this;
  _this setDir -328.88727;
  _this setPos [5206.9922, 2263.2654, -0.05992797];
};

_vehicle_619 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5200.5176, 2269.4226, -4.7683716e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_619 = _this;
  _this setDir -314.28687;
  _this setPos [5200.5176, 2269.4226, -4.7683716e-006];
};

_vehicle_620 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5195.8809, 2276.9141, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_620 = _this;
  _this setDir -298.91678;
  _this setPos [5195.8809, 2276.9141, 5.7220459e-006];
};

_vehicle_621 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5187.1377, 2293.7769, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_621 = _this;
  _this setDir -297.01193;
  _this setPos [5187.1377, 2293.7769, 9.5367432e-007];
};

_vehicle_622 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [5191.0557, 2285.8003, 1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_622 = _this;
  _this setDir -78.542336;
  _this setPos [5191.0557, 2285.8003, 1.1444092e-005];
};

_vehicle_623 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [5258.7715, 2231.7703, 2.9563904e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_623 = _this;
  _this setDir -330.83368;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5258.7715, 2231.7703, 2.9563904e-005];
};

_vehicle_624 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5211.7485, 2338.022, 2.5749207e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_624 = _this;
  _this setDir -286.01917;
  _this setPos [5211.7485, 2338.022, 2.5749207e-005];
};

_vehicle_625 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5209.2485, 2346.6523, 1.6212463e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_625 = _this;
  _this setDir -286.01917;
  _this setPos [5209.2485, 2346.6523, 1.6212463e-005];
};

_vehicle_626 = objNull;
if (true) then
{
  _this = createVehicle ["HMMWVWreck", [5207.8198, 2350.1072, 1.8119812e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_626 = _this;
  _this setDir 42.309608;
  _this setPos [5207.8198, 2350.1072, 1.8119812e-005];
};

_vehicle_627 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [5209.6055, 2349.0767, 4.2438507e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_627 = _this;
  _this setDir -64.63253;
  _this setPos [5209.6055, 2349.0767, 4.2438507e-005];
};

_vehicle_628 = objNull;
if (true) then
{
  _this = createVehicle ["C130J_wreck_EP1", [5263.3442, 2161.9167, -0.47412252], [], 0, "CAN_COLLIDE"];
  _vehicle_628 = _this;
  _this setDir -55.270531;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5263.3442, 2161.9167, -0.47412252];
};

_vehicle_629 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [5273.332, 2159.0547, -0.015296653], [], 0, "CAN_COLLIDE"];
  _vehicle_629 = _this;
  _this setDir -25.127068;
  _this setPos [5273.332, 2159.0547, -0.015296653];
};

_vehicle_630 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [5255.7422, 2164.0684, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_630 = _this;
  _this setPos [5255.7422, 2164.0684, 9.5367432e-007];
};

_vehicle_631 = objNull;
if (true) then
{
  _this = createVehicle ["Body1", [5286.8442, 2128.1072, 2.0503998e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_631 = _this;
  _this setPos [5286.8442, 2128.1072, 2.0503998e-005];
};

_vehicle_632 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [5256.7627, 2167.4568, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_632 = _this;
  _this setDir -145.70399;
  _this setPos [5256.7627, 2167.4568, -1.9073486e-006];
};

_vehicle_633 = objNull;
if (true) then
{
  _this = createVehicle ["Body2", [5189.9214, 2283.5759, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_633 = _this;
  _this setDir -25.127068;
  _this setPos [5189.9214, 2283.5759, -1.5258789e-005];
};

_vehicle_634 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_sphere10cm_EP1", [5270.5679, 2162.8811, 2.8854465], [], 0, "CAN_COLLIDE"];
  _vehicle_634 = _this;
  _this setVehicleInit "BIS_Effects_Burn = compile preprocessFile ""\ca\Data\ParticleEffects\SCRIPTS\destruction\burn.sqf""; nul = [this, 4, time, false, false] spawn BIS_Effects_Burn;";
  _this setPos [5270.5679, 2162.8811, 2.8854465];
};

_vehicle_635 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5088.5479, 2360.3726, -1.6212463e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_635 = _this;
  _this setDir -329.83319;
  _this setPos [5088.5479, 2360.3726, -1.6212463e-005];
};

_vehicle_636 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5080.7891, 2364.8601, -4.196167e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_636 = _this;
  _this setDir -329.83319;
  _this setPos [5080.7891, 2364.8601, -4.196167e-005];
};

_vehicle_637 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5073.0591, 2369.3081, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_637 = _this;
  _this setDir -329.83319;
  _this setPos [5073.0591, 2369.3081, 9.5367432e-006];
};

_vehicle_638 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5065.333, 2373.7546, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_638 = _this;
  _this setDir -329.83319;
  _this setPos [5065.333, 2373.7546, 5.7220459e-006];
};

_vehicle_639 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [5095.0444, 2356.6377, 2.4795532e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_639 = _this;
  _this setDir -368.71021;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [5095.0444, 2356.6377, 2.4795532e-005];
};

_vehicle_640 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5057.6411, 2378.1821, -4.863739e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_640 = _this;
  _this setDir -329.83319;
  _this setPos [5057.6411, 2378.1821, -4.863739e-005];
};

_vehicle_641 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5049.8823, 2382.6697, -7.4386597e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_641 = _this;
  _this setDir -329.83319;
  _this setPos [5049.8823, 2382.6697, -7.4386597e-005];
};

_vehicle_642 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5042.1523, 2387.1177, -2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_642 = _this;
  _this setDir -329.83319;
  _this setPos [5042.1523, 2387.1177, -2.2888184e-005];
};

_vehicle_643 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5034.4263, 2391.5642, -2.6702881e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_643 = _this;
  _this setDir -329.83319;
  _this setPos [5034.4263, 2391.5642, -2.6702881e-005];
};

_vehicle_644 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5026.7266, 2395.9854, -4.1007996e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_644 = _this;
  _this setDir -329.83319;
  _this setPos [5026.7266, 2395.9854, -4.1007996e-005];
};

_vehicle_645 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5018.9678, 2400.4729, -0.013973236], [], 0, "CAN_COLLIDE"];
  _vehicle_645 = _this;
  _this setDir -329.83319;
  _this setPos [5018.9678, 2400.4729, -0.013973236];
};

_vehicle_646 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5011.2378, 2404.9209, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_646 = _this;
  _this setDir -329.83319;
  _this setPos [5011.2378, 2404.9209, -1.5258789e-005];
};

_vehicle_647 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [5003.5117, 2409.3674, -0.001414299], [], 0, "CAN_COLLIDE"];
  _vehicle_647 = _this;
  _this setDir -329.83319;
  _this setPos [5003.5117, 2409.3674, -0.001414299];
};

_vehicle_648 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4995.7803, 2413.8328, -9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_648 = _this;
  _this setDir -329.83319;
  _this setPos [4995.7803, 2413.8328, -9.5367432e-006];
};

_vehicle_649 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4988.0542, 2418.2793, -1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_649 = _this;
  _this setDir -329.83319;
  _this setPos [4988.0542, 2418.2793, -1.335144e-005];
};

_vehicle_650 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4980.3623, 2422.7068, -6.7710876e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_650 = _this;
  _this setDir -329.83319;
  _this setPos [4980.3623, 2422.7068, -6.7710876e-005];
};

_vehicle_651 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4972.6035, 2427.1943, -9.3460083e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_651 = _this;
  _this setDir -329.83319;
  _this setPos [4972.6035, 2427.1943, -9.3460083e-005];
};

_vehicle_652 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4964.8735, 2431.6423, -4.196167e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_652 = _this;
  _this setDir -329.83319;
  _this setPos [4964.8735, 2431.6423, -4.196167e-005];
};

_vehicle_653 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4957.1475, 2436.0889, -0.083676338], [], 0, "CAN_COLLIDE"];
  _vehicle_653 = _this;
  _this setDir -329.83319;
  _this setPos [4957.1475, 2436.0889, -0.083676338];
};

_vehicle_654 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4933.959, 2449.4456, -0.033658028], [], 0, "CAN_COLLIDE"];
  _vehicle_654 = _this;
  _this setDir -329.83319;
  _this setPos [4933.959, 2449.4456, -0.033658028];
};

_vehicle_655 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4926.2329, 2453.8921, -0.0014333725], [], 0, "CAN_COLLIDE"];
  _vehicle_655 = _this;
  _this setDir -329.83319;
  _this setPos [4926.2329, 2453.8921, -0.0014333725];
};

_vehicle_656 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4918.5654, 2458.3074, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_656 = _this;
  _this setDir -329.83319;
  _this setPos [4918.5654, 2458.3074, -7.6293945e-006];
};

_vehicle_657 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4910.8394, 2462.7539, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_657 = _this;
  _this setDir -329.83319;
  _this setPos [4910.8394, 2462.7539, -1.1444092e-005];
};

_vehicle_658 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4903.1475, 2467.1814, 0.044776917], [], 0, "CAN_COLLIDE"];
  _vehicle_658 = _this;
  _this setDir -329.83319;
  _this setPos [4903.1475, 2467.1814, 0.044776917];
};

_vehicle_659 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4895.3887, 2471.6689, 0.099908829], [], 0, "CAN_COLLIDE"];
  _vehicle_659 = _this;
  _this setDir -329.83319;
  _this setPos [4895.3887, 2471.6689, 0.099908829];
};

_vehicle_660 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4887.6587, 2476.1169, 0.099960327], [], 0, "CAN_COLLIDE"];
  _vehicle_660 = _this;
  _this setDir -329.83319;
  _this setPos [4887.6587, 2476.1169, 0.099960327];
};

_vehicle_661 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4879.9326, 2480.5635, 0.099956512], [], 0, "CAN_COLLIDE"];
  _vehicle_661 = _this;
  _this setDir -329.83319;
  _this setPos [4879.9326, 2480.5635, 0.099956512];
};

_vehicle_662 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4872.2329, 2484.9846, 0.099942207], [], 0, "CAN_COLLIDE"];
  _vehicle_662 = _this;
  _this setDir -329.83319;
  _this setPos [4872.2329, 2484.9846, 0.099942207];
};

_vehicle_663 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4864.4741, 2489.4722, 0.086009979], [], 0, "CAN_COLLIDE"];
  _vehicle_663 = _this;
  _this setDir -329.83319;
  _this setPos [4864.4741, 2489.4722, 0.086009979];
};

_vehicle_664 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4856.7441, 2493.9202, 0.11920357], [], 0, "CAN_COLLIDE"];
  _vehicle_664 = _this;
  _this setDir -329.83319;
  _this setPos [4856.7441, 2493.9202, 0.11920357];
};

_vehicle_665 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_9", [4849.0181, 2498.3667, 0.15921688], [], 0, "CAN_COLLIDE"];
  _vehicle_665 = _this;
  _this setDir -329.83319;
  _this setPos [4849.0181, 2498.3667, 0.15921688];
};

_vehicle_666 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [4847.4761, 2500.343, 2.0980835e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_666 = _this;
  _this setDir 53.106129;
  _this setPos [4847.4761, 2500.343, 2.0980835e-005];
};

_vehicle_667 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Glass_Cullet_01", [4847.0288, 2498.3696, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_667 = _this;
  _this setPos [4847.0288, 2498.3696, -1.9073486e-006];
};

_vehicle_668 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Glass_Cullet_01", [4845.7417, 2500.6104, -9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_668 = _this;
  _this setPos [4845.7417, 2500.6104, -9.5367432e-007];
};

_vehicle_669 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [4965.2114, 2430.6802, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_669 = _this;
  _this setDir -334.94012;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4965.2114, 2430.6802, 9.5367432e-006];
};

_vehicle_670 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [4784.4009, 2609.2773, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_670 = _this;
  _this setDir -330.6821;
  _this setPos [4784.4009, 2609.2773, -2.8610229e-006];
};

_vehicle_671 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3_D", [4786.8472, 2607.7888, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_671 = _this;
  _this setDir 31.742025;
  _this setPos [4786.8472, 2607.7888, 2.8610229e-006];
};

_vehicle_672 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [4789.4565, 2606.3201, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_672 = _this;
  _this setDir -330.6821;
  _this setPos [4789.4565, 2606.3201, 7.6293945e-006];
};

_vehicle_673 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_Pole", [4790.7808, 2605.5979, 5.531311e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_673 = _this;
  _this setDir -328.20276;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4790.7808, 2605.5979, 5.531311e-005];
};

_vehicle_674 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4746.3901, 2535.6416, 1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_674 = _this;
  _this setDir -59.744499;
  _this setPos [4746.3901, 2535.6416, 1.335144e-005];
};

_vehicle_675 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4768.002, 2523.1428, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_675 = _this;
  _this setDir -59.744499;
  _this setPos [4768.002, 2523.1428, 2.8610229e-006];
};

_vehicle_676 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4789.6133, 2510.6472, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_676 = _this;
  _this setDir -59.744499;
  _this setPos [4789.6133, 2510.6472, 9.5367432e-007];
};

_vehicle_677 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4789.5449, 2510.7102, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_677 = _this;
  _this setDir 122.05942;
  _this setPos [4789.5449, 2510.7102, 2.8610229e-006];
};

_vehicle_678 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [4828.8188, 2477.6807, 1.4305115e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_678 = _this;
  _this setDir -60.542892;
  _this setPos [4828.8188, 2477.6807, 1.4305115e-005];
};

_vehicle_679 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_12", [4813.248, 2489.3816, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_679 = _this;
  _this setDir -43.966801;
  _this setPos [4813.248, 2489.3816, 1.9073486e-006];
};

_vehicle_680 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4851.0073, 2466.6138, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_680 = _this;
  _this setDir -63.421661;
  _this setPos [4851.0073, 2466.6138, -1.9073486e-006];
};

_vehicle_681 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4873.3359, 2455.4883, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_681 = _this;
  _this setDir -63.421661;
  _this setPos [4873.3359, 2455.4883, -3.8146973e-006];
};

_vehicle_682 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4895.6426, 2444.3694, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_682 = _this;
  _this setDir -63.421661;
  _this setPos [4895.6426, 2444.3694, -3.8146973e-006];
};

_vehicle_683 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4917.9888, 2433.259, -2.7656555e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_683 = _this;
  _this setDir -63.421661;
  _this setPos [4917.9888, 2433.259, -2.7656555e-005];
};

_vehicle_684 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4939.4541, 2420.7407, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_684 = _this;
  _this setDir -59.744499;
  _this setPos [4939.4541, 2420.7407, 5.7220459e-006];
};

_vehicle_685 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4961.0049, 2408.2744, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_685 = _this;
  _this setDir -59.744499;
  _this setPos [4961.0049, 2408.2744, -5.7220459e-006];
};

_vehicle_686 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [4982.585, 2395.7441, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_686 = _this;
  _this setDir -59.744499;
  _this setPos [4982.585, 2395.7441, 9.5367432e-006];
};

_vehicle_687 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5004.2427, 2383.2437, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_687 = _this;
  _this setDir -59.744499;
  _this setPos [5004.2427, 2383.2437, 2.8610229e-006];
};

_vehicle_688 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5025.8291, 2370.7556, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_688 = _this;
  _this setDir -59.744499;
  _this setPos [5025.8291, 2370.7556, 9.5367432e-007];
};

_vehicle_689 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5047.3828, 2358.2573, 1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_689 = _this;
  _this setDir -59.744499;
  _this setPos [5047.3828, 2358.2573, 1.335144e-005];
};

_vehicle_690 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [5065.5303, 2350.9487, 1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_690 = _this;
  _this setDir 284.819;
  _this setPos [5065.5303, 2350.9487, 1.2397766e-005];
};

_vehicle_691 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [5089.5425, 2344.3174, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_691 = _this;
  _this setDir -74.460396;
  _this setPos [5089.5425, 2344.3174, 9.5367432e-006];
};

_vehicle_692 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [5089.5288, 2344.4529, 3.4332275e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_692 = _this;
  _this setDir 106.37193;
  _this setPos [5089.5288, 2344.4529, 3.4332275e-005];
};

_vehicle_693 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [5119.7832, 2332.9277], [], 0, "CAN_COLLIDE"];
  _vehicle_693 = _this;
  _this setDir -88.393074;
  _this setPos [5119.7832, 2332.9277];
};

_vehicle_694 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [5128.5166, 2337.5735, -3.4332275e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_694 = _this;
  _this setDir 212.89594;
  _this setPos [5128.5166, 2337.5735, -3.4332275e-005];
};

_vehicle_695 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [5131.8423, 2342.8477, 1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_695 = _this;
  _this setDir -147.12764;
  _this setPos [5131.8423, 2342.8477, 1.335144e-005];
};

_vehicle_696 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_L", [4943.582, 2431.8967, -0.22741216], [], 0, "CAN_COLLIDE"];
  _vehicle_696 = _this;
  _this setDir 29.684711;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4943.582, 2431.8967, -0.22741216];
};

_vehicle_697 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [4954.5537, 2437.5862, 2.4795532e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_697 = _this;
  _this setDir -330.6821;
  _this setPos [4954.5537, 2437.5862, 2.4795532e-005];
};

_vehicle_698 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3_D", [4951.9219, 2439.0239, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_698 = _this;
  _this setDir 29.394724;
  _this setPos [4951.9219, 2439.0239, 9.1552734e-005];
};

_vehicle_699 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [4949.3232, 2440.4951, 4.6730042e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_699 = _this;
  _this setDir -330.6821;
  _this setPos [4949.3232, 2440.4951, 4.6730042e-005];
};

_vehicle_700 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [4946.7207, 2441.9353, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_700 = _this;
  _this setDir -330.6821;
  _this setPos [4946.7207, 2441.9353, 2.2888184e-005];
};

_vehicle_701 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3_D", [4944.1279, 2443.4392, 2.4795532e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_701 = _this;
  _this setDir 31.87965;
  _this setPos [4944.1279, 2443.4392, 2.4795532e-005];
};

_vehicle_702 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndFnc_3", [4941.6934, 2444.9229, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_702 = _this;
  _this setDir -327.88123;
  _this setPos [4941.6934, 2444.9229, 1.5258789e-005];
};

_vehicle_703 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVOld", [5160.3945, 2314.7407, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_703 = _this;
  _this setDir -147.73676;
  _this setPos [5160.3945, 2314.7407, 3.8146973e-006];
};

_vehicle_704 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHSInd", [4821.1895, 2566.9392, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_704 = _this;
  _this setDir -420.23901;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4821.1895, 2566.9392, 9.5367432e-007];
};

_vehicle_705 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHSInd", [4809.2114, 2548.2908, 2.4795532e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_705 = _this;
  _this setDir -509.04178;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4809.2114, 2548.2908, 2.4795532e-005];
};

_vehicle_706 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHSInd", [5150.1948, 2367.4929, 1.7166138e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_706 = _this;
  _this setDir -420.12064;
  _this setPos [5150.1948, 2367.4929, 1.7166138e-005];
};

_vehicle_707 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHSInd", [5127.2964, 2360.3818], [], 0, "CAN_COLLIDE"];
  _vehicle_707 = _this;
  _this setDir -510.24609;
  _this setPos [5127.2964, 2360.3818];
};

_vehicle_708 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVOld", [4943.1768, 2426.1382, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_708 = _this;
  _this setDir -150.24956;
  _this setPos [4943.1768, 2426.1382, -1.9073486e-006];
};

_vehicle_709 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [5103.8413, 2351.9399, -6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_709 = _this;
  _this setDir -45.799458;
  _this setPos [5103.8413, 2351.9399, -6.6757202e-006];
};

_vehicle_710 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1Bo_military", [4983.1982, 2303.6584, 1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_710 = _this;
  _this setDir -60.990494;
  _this setPos [4983.1982, 2303.6584, 1.1444092e-005];
};

_vehicle_711 = objNull;
if (true) then
{
  _this = createVehicle ["Park_bench2_noRoad", [5104.7246, 2218.3237, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_711 = _this;
  _this setDir 29.4804;
  _this setPos [5104.7246, 2218.3237, -7.6293945e-006];
};

_vehicle_712 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [5106.3027, 2220.2637, -0.049932476], [], 0, "CAN_COLLIDE"];
  _vehicle_712 = _this;
  _this setPos [5106.3027, 2220.2637, -0.049932476];
};

_vehicle_713 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Heli_H_rescue", [4854.313, 2544.2979, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_713 = _this;
  _this setPos [4854.313, 2544.2979, 1.9073486e-006];
};

_vehicle_714 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [4799.3643, 2550.4133, 2.0980835e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_714 = _this;
  _this setDir -329.56223;
  _this setPos [4799.3643, 2550.4133, 2.0980835e-005];
};

_vehicle_715 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Bo", [4801.6104, 2541.9822, 1.4305115e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_715 = _this;
  _this setDir -86.373924;
  _this setPos [4801.6104, 2541.9822, 1.4305115e-005];
};

_vehicle_716 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1C", [4801.9482, 2549.5173, 6.3896179e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_716 = _this;
  _this setDir 29.59569;
  _this setPos [4801.9482, 2549.5173, 6.3896179e-005];
};

_vehicle_717 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1F", [4803.9844, 2547.9771, -2.8610229e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_717 = _this;
  _this setDir 29.798082;
  _this setPos [4803.9844, 2547.9771, -2.8610229e-005];
};

_vehicle_718 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_misc", [4800.3994, 2545.0137, 4.7683716e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_718 = _this;
  _this setDir 293.72861;
  _this setPos [4800.3994, 2545.0137, 4.7683716e-005];
};

_vehicle_719 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4805.4766, 2541.738, 5.7220459e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_719 = _this;
  _this setPos [4805.4766, 2541.738, 5.7220459e-005];
};

_vehicle_720 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4798.2104, 2545.7175, 8.9645386e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_720 = _this;
  _this setPos [4798.2104, 2545.7175, 8.9645386e-005];
};

_vehicle_721 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4802.5049, 2544.5479, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_721 = _this;
  _this setPos [4802.5049, 2544.5479, 9.5367432e-006];
};

_vehicle_722 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4797.0459, 2542.4419, 6.6757202e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_722 = _this;
  _this setPos [4797.0459, 2542.4419, 6.6757202e-005];
};

_vehicle_723 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [4801.7046, 2553.8396, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_723 = _this;
  _this setPos [4801.7046, 2553.8396, 4.5776367e-005];
};

_vehicle_724 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ground_garbage_square5", [5172.103, 2345.2866, 1.7166138e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_724 = _this;
  _this setPos [5172.103, 2345.2866, 1.7166138e-005];
};

_vehicle_725 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Bo", [5177.3135, 2344.6956, -0.01906327], [], 0, "CAN_COLLIDE"];
  _vehicle_725 = _this;
  _this setDir -86.373924;
  _this setPos [5177.3135, 2344.6956, -0.01906327];
};

_vehicle_726 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [5177.1211, 2349.5361, -0.076623365], [], 0, "CAN_COLLIDE"];
  _vehicle_726 = _this;
  _this setDir -284.26813;
  _this setPos [5177.1211, 2349.5361, -0.076623365];
};

_vehicle_727 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [5019.8511, 2397.2429, -2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_727 = _this;
  _this setDir -413.69333;
  _this setPos [5019.8511, 2397.2429, -2.8610229e-006];
};

_vehicle_734 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ural_wrecked", [4635.3174, 2497.9453, -0.15491804], [], 0, "CAN_COLLIDE"];
  _vehicle_734 = _this;
  _this setDir -26.592381;
  _this setVehicleInit "this setvectorup [0.1,0,0.01]";
  _this setPos [4635.3174, 2497.9453, -0.15491804];
};

_vehicle_742 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [4610.4624, 2478.4077, 3.1471252e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_742 = _this;
  _this setPos [4610.4624, 2478.4077, 3.1471252e-005];
};

_vehicle_745 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_salix2s", [4624.9067, 2470.5581, 2.8610229e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_745 = _this;
  _this setPos [4624.9067, 2470.5581, 2.8610229e-005];
};

_vehicle_746 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_salix2s", [5054.3516, 2383.9341, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_746 = _this;
  _this setPos [5054.3516, 2383.9341, 5.7220459e-006];
};

_vehicle_753 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4848.6187, 2704.0989, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_753 = _this;
  _this setPos [4848.6187, 2704.0989, -1.5258789e-005];
};

_vehicle_755 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4869.834, 2709.1509], [], 0, "CAN_COLLIDE"];
  _vehicle_755 = _this;
  _this setPos [4869.834, 2709.1509];
};

_vehicle_758 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4892.2471, 2722.416, 4.3869019e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_758 = _this;
  _this setPos [4892.2471, 2722.416, 4.3869019e-005];
};

_vehicle_770 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4640.2197, 2621.2219], [], 0, "CAN_COLLIDE"];
  _vehicle_770 = _this;
  _this setPos [4640.2197, 2621.2219];
};

_vehicle_772 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4691.1582, 2637.8625, 1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_772 = _this;
  _this setPos [4691.1582, 2637.8625, 1.9073486e-005];
};

_vehicle_774 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4762.2603, 2652.2944, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_774 = _this;
  _this setPos [4762.2603, 2652.2944, -7.6293945e-006];
};

_vehicle_776 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4652.5049, 2596.7275], [], 0, "CAN_COLLIDE"];
  _vehicle_776 = _this;
  _this setPos [4652.5049, 2596.7275];
};

_vehicle_778 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4657.1577, 2595.2266], [], 0, "CAN_COLLIDE"];
  _vehicle_778 = _this;
  _this setPos [4657.1577, 2595.2266];
};

_vehicle_780 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea2s", [4757.6846, 2639.1638, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_780 = _this;
  _this setPos [4757.6846, 2639.1638, 3.0517578e-005];
};

_vehicle_781 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4758.1729, 2667.8574, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_781 = _this;
  _this setPos [4758.1729, 2667.8574, -3.8146973e-006];
};

_vehicle_783 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4653.9092, 2621.6216, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_783 = _this;
  _this setPos [4653.9092, 2621.6216, 9.5367432e-006];
};

_vehicle_785 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [5040.6978, 2395.0684, 0.00011730194], [], 0, "CAN_COLLIDE"];
  _vehicle_785 = _this;
  _this setPos [5040.6978, 2395.0684, 0.00011730194];
};

_vehicle_789 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [5055.7324, 2390.0681, 0.00014877319], [], 0, "CAN_COLLIDE"];
  _vehicle_789 = _this;
  _this setPos [5055.7324, 2390.0681, 0.00014877319];
};

_vehicle_795 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4873.1094, 2498.2163, 5.531311e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_795 = _this;
  _this setPos [4873.1094, 2498.2163, 5.531311e-005];
};

_vehicle_797 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4890.9922, 2512.8562, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_797 = _this;
  _this setPos [4890.9922, 2512.8562, 2.2888184e-005];
};

_vehicle_801 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_craet1", [5044.0029, 2391.5823, 0.00016975403], [], 0, "CAN_COLLIDE"];
  _vehicle_801 = _this;
  _this setPos [5044.0029, 2391.5823, 0.00016975403];
};

_vehicle_837 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sara_Domek_sedy", [4577.9741, 2519.6636, -0.21105574], [], 0, "CAN_COLLIDE"];
  _vehicle_837 = _this;
  _this setDir -228.58354;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4577.9741, 2519.6636, -0.21105574];
};

_vehicle_844 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5_2", [4581.1572, 2534.2593, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_844 = _this;
  _this setDir 130.31056;
  _this setPos [4581.1572, 2534.2593, -1.1444092e-005];
};

_vehicle_846 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5_2", [4577.9302, 2530.448, 3.8146973e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_846 = _this;
  _this setDir 130.31056;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4577.9302, 2530.448, 3.8146973e-005];
};

_vehicle_849 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5_2", [4584.6729, 2534.5154, -0.017470757], [], 0, "CAN_COLLIDE"];
  _vehicle_849 = _this;
  _this setDir 221.40939;
  _this setPos [4584.6729, 2534.5154, -0.017470757];
};

_vehicle_852 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Wood1_5_2", [4567.918, 2519.4446, 6.6757202e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_852 = _this;
  _this setDir 133.44518;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4567.918, 2519.4446, 6.6757202e-006];
};

_vehicle_855 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_picea1s", [4658.0986, 2622.2239, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_855 = _this;
  _this setPos [4658.0986, 2622.2239, 9.5367432e-006];
};

_vehicle_859 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_salix2s", [4642.7051, 2622.9622, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_859 = _this;
  _this setPos [4642.7051, 2622.9622, -1.9073486e-006];
};

_vehicle_871 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Checkpoint_US_EP1", [4586.4937, 2476.5964, -1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_871 = _this;
  _this setDir -137.20306;
  _this setPos [4586.4937, 2476.5964, -1.9073486e-005];
};

_vehicle_878 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Checkpoint", [4622.0112, 2603.9922, -1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_878 = _this;
  _this setDir -255.51501;
  _this setPos [4622.0112, 2603.9922, -1.9073486e-005];
};

/*
*****************************************************************************************************************************************************
    Update - 04/08/2014 --- Added Industrial Area
	  a)Default option is MAP_Ind_Pec_03b.
		  This will not cause any errors.
		
		b)Optional building is Land_Ind_Pec_03a. This is enterable and spawns loot.
		  IMPORTANT: Using this option will cause a 'missing file "warehouse" error' on connection.
	               To fix this add "warehouse" to the AddOns section in your mission.sqm to stop the "warehouse" error.
*****************************************************************************************************************************************************
*/

// DEFAULT BUILDING
//
//
_vehicle_880 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_Pec_03b", [4518.3008, 2297.3406, -0.074109785], [], 0, "CAN_COLLIDE"];
  _vehicle_880 = _this;
  _this setDir -66.776543;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4518.3008, 2297.3406, -0.074109785];
};

// OPTIONAL BUILDING
// To use this option delete the above vehicle entry and uncomment the blow one. Remove /* and */
// If you use this one remember to add "warehouse" to your AddOns section in your mission.sqm!
//
/*
_vehicle_881 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Pec_03a", [4511.1724, 2296.4041, 5.3012118], [], 0, "CAN_COLLIDE"];
  _vehicle_881 = _this;
  _this setDir -426.78857;
  _this setVehicleLock "LOCKED";
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4511.1724, 2296.4041, 5.3012118];
};
*/



_vehicle_891 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4463.3418, 2292.3306, -6.6280365e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_891 = _this;
  _this setDir -66.559532;
  _this setPos [4463.3418, 2292.3306, -6.6280365e-005];
};

_vehicle_893 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4462.7808, 2283.9607, -3.2424927e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_893 = _this;
  _this setDir 23.060385;
  _this setPos [4462.7808, 2283.9607, -3.2424927e-005];
};

_vehicle_896 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4468.4966, 2281.5212, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_896 = _this;
  _this setDir 23.060385;
  _this setPos [4468.4966, 2281.5212, 1.9073486e-006];
};

_vehicle_898 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Shed_02_EP1", [4492.0293, 2271.47, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_898 = _this;
  _this setDir -426.93958;
  _this setPos [4492.0293, 2271.47, 9.5367432e-007];
};

_vehicle_899 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_Pole", [4471.3511, 2280.2998, -9.059906e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_899 = _this;
  _this setDir 111.71741;
  _this setPos [4471.3511, 2280.2998, -9.059906e-006];
};

_vehicle_902 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [4510.4766, 2264.5432, 2.3841858e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_902 = _this;
  _this setDir -159.3916;
  _this setPos [4510.4766, 2264.5432, 2.3841858e-005];
};

_vehicle_903 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D_2", [4530.7119, 2273.1738, 3.6239624e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_903 = _this;
  _this setDir 114.0984;
  _this setPos [4530.7119, 2273.1738, 3.6239624e-005];
};

_vehicle_905 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rails_v_LB_RE", [4439.9624, 2291.0649, 0.0027273539], [], 0, "CAN_COLLIDE"];
  _vehicle_905 = _this;
  _this setDir 112.91061;
  _this setPos [4439.9624, 2291.0649, 0.0027273539];
};

_vehicle_906 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rails_25", [4456.1909, 2289.1599, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_906 = _this;
  _this setDir 113.04807;
  _this setPos [4456.1909, 2289.1599, -1.9073486e-006];
};

_vehicle_908 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rails_25", [4479.1353, 2279.3958, 0.0014557391], [], 0, "CAN_COLLIDE"];
  _vehicle_908 = _this;
  _this setDir 113.04807;
  _this setPos [4479.1353, 2279.3958, 0.0014557391];
};

_vehicle_922 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_green_vrat_l", [4459.7681, 2289.7756, 0.18786694], [], 0, "CAN_COLLIDE"];
  _vehicle_922 = _this;
  _this setDir -170.85204;
  _this setPos [4459.7681, 2289.7756, 0.18786694];
};

_vehicle_923 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_green_vrat_r", [4460.3179, 2287.7583, 0.20605321], [], 0, "CAN_COLLIDE"];
  _vehicle_923 = _this;
  _this setDir 90.025909;
  _this setPos [4460.3179, 2287.7583, 0.20605321];
};

_vehicle_926 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4496.7798, 2269.5391, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_926 = _this;
  _this setDir 22.499739;
  _this setPos [4496.7798, 2269.5391, -1.9073486e-006];
};

_vehicle_931 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [4508.3271, 2265.3933, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_931 = _this;
  _this setDir -340.2973;
  _this setPos [4508.3271, 2265.3933, -5.7220459e-006];
};

_vehicle_934 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4502.5518, 2267.3987, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_934 = _this;
  _this setDir 18.94528;
  _this setVehicleInit "this setvectorup [0,0,1]";
  _this setPos [4502.5518, 2267.3987, -7.6293945e-006];
};

_vehicle_937 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4516.2988, 2262.314, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_937 = _this;
  _this setDir -158.53743;
  _this setPos [4516.2988, 2262.314, 1.9073486e-006];
};

_vehicle_940 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4522.0381, 2260.0095, 2.8610229e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_940 = _this;
  _this setDir -158.53743;
  _this setPos [4522.0381, 2260.0095, 2.8610229e-006];
};

_vehicle_943 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4525.958, 2262.1582, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
  _vehicle_943 = _this;
  _this setDir -247.47034;
  _this setPos [4525.958, 2262.1582, 9.5367432e-007];
};

_vehicle_947 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4528.3857, 2267.8657, -1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_947 = _this;
  _this setDir -606.6911;
  _this setPos [4528.3857, 2267.8657, -1.0490417e-005];
};

_vehicle_950 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [4532.104, 2276.1433, 1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_950 = _this;
  _this setDir -245.9868;
  _this setPos [4532.104, 2276.1433, 1.1444092e-005];
};

_vehicle_956 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4465.6128, 2297.5361, -1.9073486e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_956 = _this;
  _this setDir -66.559532;
  _this setPos [4465.6128, 2297.5361, -1.9073486e-006];
};

_vehicle_960 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4467.9067, 2302.8457, -2.3841858e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_960 = _this;
  _this setDir -66.559532;
  _this setPos [4467.9067, 2302.8457, -2.3841858e-006];
};

_vehicle_961 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4470.2275, 2308.1792, -0.0021858215], [], 0, "CAN_COLLIDE"];
  _vehicle_961 = _this;
  _this setDir -66.559532;
  _this setPos [4470.2275, 2308.1792, -0.0021858215];
};

_vehicle_964 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rails_linebreak_iron", [4497.6284, 2271.5325], [], 0, "CAN_COLLIDE"];
  _vehicle_964 = _this;
  _this setDir -66.778526;
  _this setPos [4497.6284, 2271.5325];
};

_vehicle_969 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4492.8257, 2320.8108, -9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_969 = _this;
  _this setDir -156.71313;
  _this setPos [4492.8257, 2320.8108, -9.5367432e-006];
};

_vehicle_970 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4477.0786, 2323.9702, 0.036601067], [], 0, "CAN_COLLIDE"];
  _vehicle_970 = _this;
  _this setDir -66.559532;
  _this setPos [4477.0786, 2323.9702, 0.036601067];
};

_vehicle_971 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4474.8232, 2318.7798, 0.039215088], [], 0, "CAN_COLLIDE"];
  _vehicle_971 = _this;
  _this setDir -66.559532;
  _this setPos [4474.8232, 2318.7798, 0.039215088];
};

_vehicle_976 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_Pole", [4471.5322, 2311.2625, -1.4781952e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_976 = _this;
  _this setDir 111.71741;
  _this setPos [4471.5322, 2311.2625, -1.4781952e-005];
};

_vehicle_978 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_green_vrat_o", [4472.8154, 2313.6675, -1.0967255e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_978 = _this;
  _this setDir 114.24902;
  _this setPos [4472.8154, 2313.6675, -1.0967255e-005];
};

_vehicle_980 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4481.0049, 2325.9924], [], 0, "CAN_COLLIDE"];
  _vehicle_980 = _this;
  _this setDir 23.173553;
  _this setPos [4481.0049, 2325.9924];
};

_vehicle_986 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5", [4498.0884, 2318.5449, -1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_986 = _this;
  _this setDir -156.71313;
  _this setPos [4498.0884, 2318.5449, -1.335144e-005];
};

_vehicle_989 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [4486.2437, 2323.6895, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_989 = _this;
  _this setDir -336.20743;
  _this setPos [4486.2437, 2323.6895, -1.5258789e-005];
};

_vehicle_992 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_IndVar1_5_D", [4487.5776, 2323.0674, -2.8610229e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_992 = _this;
  _this setDir -516.26813;
  _this setPos [4487.5776, 2323.0674, -2.8610229e-005];
};

_vehicle_994 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [4489.7192, 2323.1641, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_994 = _this;
  _this setPos [4489.7192, 2323.1641, 3.8146973e-006];
};

_vehicle_996 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4497.3901, 2333.575, 5.7220459e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_996 = _this;
  _this setPos [4497.3901, 2333.575, 5.7220459e-005];
};

_vehicle_998 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4452.7642, 2304.4443, -9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_998 = _this;
  _this setDir 100.88069;
  _this setPos [4452.7642, 2304.4443, -9.5367432e-006];
};

_vehicle_1006 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4554.6396, 2265.9622, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1006 = _this;
  _this setPos [4554.6396, 2265.9622, -1.5258789e-005];
};

_vehicle_1008 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4554.3154, 2323.5449, -2.3841858e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1008 = _this;
  _this setPos [4554.3154, 2323.5449, -2.3841858e-006];
};

_vehicle_1010 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4615.9043, 2472.3745, -3.9100647e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1010 = _this;
  _this setPos [4615.9043, 2472.3745, -3.9100647e-005];
};

_vehicle_1012 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4584.7632, 2506.6714, -8.1062317e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1012 = _this;
  _this setPos [4584.7632, 2506.6714, -8.1062317e-005];
};

_vehicle_1017 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_prunus", [4552.8594, 2325.3389, -5.2452087e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1017 = _this;
  _this setPos [4552.8594, 2325.3389, -5.2452087e-006];
};

_vehicle_1019 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_salix2s", [4497.9106, 2339.1812, -8.5830688e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1019 = _this;
  _this setPos [4497.9106, 2339.1812, -8.5830688e-006];
};

_vehicle_1020 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_canina2s", [4484.7373, 2312.8967, -4.8160553e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1020 = _this;
  _this setPos [4484.7373, 2312.8967, -4.8160553e-005];
};

_vehicle_1021 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_corylus", [4461.4917, 2292.7886, -1.2397766e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1021 = _this;
  _this setPos [4461.4917, 2292.7886, -1.2397766e-005];
};

_vehicle_1022 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4458.8589, 2285.4688, -1.3828278e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1022 = _this;
  _this setPos [4458.8589, 2285.4688, -1.3828278e-005];
};

_vehicle_1023 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4457.7598, 2285.9976, -1.0490417e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1023 = _this;
  _this setPos [4457.7598, 2285.9976, -1.0490417e-005];
};

_vehicle_1024 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4459.6548, 2285.2085, -2.8133392e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1024 = _this;
  _this setPos [4459.6548, 2285.2085, -2.8133392e-005];
};

_vehicle_1025 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_c_GrassTall", [4461.2109, 2289.7805, -2.0503998e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1025 = _this;
  _this setPos [4461.2109, 2289.7805, -2.0503998e-005];
};

_vehicle_1031 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [4539.0962, 2287.9185, -0.037888717], [], 0, "CAN_COLLIDE"];
  _vehicle_1031 = _this;
  _this setDir 22.616026;
  _this setPos [4539.0962, 2287.9185, -0.037888717];
};

_vehicle_1034 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1C", [4541.5518, 2293.647, -0.018964147], [], 0, "CAN_COLLIDE"];
  _vehicle_1034 = _this;
  _this setDir 22.692955;
  _this setPos [4541.5518, 2293.647, -0.018964147];
};

_vehicle_1035 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_misc", [4542.8066, 2298.1047, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1035 = _this;
  _this setDir 128.61063;
  _this setVehicleInit "this allowDammage false;this enableSimulation false;";
  _this setPos [4542.8066, 2298.1047, -1.1444092e-005];
};

_vehicle_1036 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_paleta", [4541.8735, 2300.6758, -1.3828278e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1036 = _this;
  _this setDir -304.87915;
  _this setVehicleInit "this allowDammage false;this enableSimulation false;";
  _this setPos [4541.8735, 2300.6758, -1.3828278e-005];
};

_vehicle_1037 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1F", [4537.5513, 2302.6511, -0.03789014], [], 0, "CAN_COLLIDE"];
  _vehicle_1037 = _this;
  _this setDir -67.0345;
  _this setPos [4537.5513, 2302.6511, -0.03789014];
};

_vehicle_1038 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo2D", [4497.0625, 2317.1147, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1038 = _this;
  _this setDir -64.305016;
  _this setPos [4497.0625, 2317.1147, -1.1444092e-005];
};

_vehicle_1039 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Bo", [4492.8521, 2292.0667, -0.058520328], [], 0, "CAN_COLLIDE"];
  _vehicle_1039 = _this;
  _this setDir -80.877502;
  _this setPos [4492.8521, 2292.0667, -0.058520328];
};

_vehicle_1069 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [4474.437, 2312.7671, -4.2915344e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1069 = _this;
  _this setDir -67.334381;
  _this setPos [4474.437, 2312.7671, -4.2915344e-006];
};

_vehicle_1070 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [4439.814, 2326.9587, -4.2915344e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1070 = _this;
  _this setDir 112.33579;
  _this setPos [4439.814, 2326.9587, -4.2915344e-006];
};

_vehicle_1071 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_25", [4468.7334, 2315.177, -1.1920929e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1071 = _this;
  _this setDir -67.882332;
  _this setPos [4468.7334, 2315.177, -1.1920929e-005];
};

_vehicle_1075 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [4495.7568, 2314.9744, -9.059906e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1075 = _this;
  _this setDir 113.46397;
  _this setPos [4495.7568, 2314.9744, -9.059906e-006];
};

_vehicle_1077 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_salix2s", [4450.8447, 2302.8101, -5.531311e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1077 = _this;
  _this setPos [4450.8447, 2302.8101, -5.531311e-005];
};

_vehicle_1079 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_salix2s", [4469.5313, 2310.6753, 1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1079 = _this;
  _this setPos [4469.5313, 2310.6753, 1.335144e-005];
};

_vehicle_1081 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_b_salix2s", [4528.1548, 2264.1489, -4.0054321e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1081 = _this;
  _this setPos [4528.1548, 2264.1489, -4.0054321e-005];
};

_vehicle_1084 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_fagus2s", [4464.1255, 2301.429, -5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_1084 = _this;
  _this setDir 36.339016;
  _this setPos [4464.1255, 2301.429, -5.7220459e-006];
};

};
