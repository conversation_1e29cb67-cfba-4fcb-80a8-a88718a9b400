if (isServer) then {

_vehicle_0 = objNull;
if (true) then
{
  _this = createVehicle ["MBG_Warehouse_InEditor", [2295.9194, 14323.787, 0.5466029], [], 0, "CAN_COLLIDE"];
  _vehicle_0 = _this;
  _this setDir 206.22105;
  _this setPos [2295.9194, 14323.787, 0.5466029];
};

_vehicle_2 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [2333.5549, 14337.151, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2 = _this;
  _this setDir 26.613134;
  _this setPos [2333.5549, 14337.151, -6.1035156e-005];
};

_vehicle_3 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2323.0222, 14315.941, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_3 = _this;
  _this setDir 26.205595;
  _this setPos [2323.0222, 14315.941, 6.1035156e-005];
};

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2336.356, 14342.709, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir 26.522287;
  _this setPos [2336.356, 14342.709, 6.1035156e-005];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2344.1797, 14358.316], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setDir 27.087873;
  _this setPos [2344.1797, 14358.316];
};

_vehicle_13 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pond_big_28_01", [2246.9934, 14363.441, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_13 = _this;
  _this setDir 2.7623615;
  _this setPos [2246.9934, 14363.441, 0.00030517578];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Ind_SiloVelke_01", [2657.9377, 14155.816, -1.4415555], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir -163.37111;
  _this setPos [2657.9377, 14155.816, -1.4415555];
};

_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_statue01", [2375.8948, 14366.087, 0.16834272], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir -27.54797;
  _this setPos [2375.8948, 14366.087, 0.16834272];
};

_vehicle_32 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_statue02", [2360.1523, 14357.933, 0.09087123], [], 0, "CAN_COLLIDE"];
  _vehicle_32 = _this;
  _this setDir 154.63835;
  _this setPos [2360.1523, 14357.933, 0.09087123];
};

_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2225.9897, 14381.531, -11.466896], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setDir 130.33887;
  _this setPos [2225.9897, 14381.531, -11.466896];
};

_vehicle_52 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockTower", [2231.574, 14345.686], [], 0, "CAN_COLLIDE"];
  _vehicle_52 = _this;
  _this setDir -105.97131;
  _this setPos [2231.574, 14345.686];
};

_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_Rock1", [2219.2478, 14357.575, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setDir 34.612041;
  _this setPos [2219.2478, 14357.575, -0.00012207031];
};

_vehicle_54 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2229.0261, 14314.84, -0.94624949], [], 0, "CAN_COLLIDE"];
  _vehicle_54 = _this;
  _this setDir 85.64328;
  _this setPos [2229.0261, 14314.84, -0.94624949];
};

_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2245.5139, 14282.495, -4.671865], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setDir 24.408331;
  _this setPos [2245.5139, 14282.495, -4.671865];
};

_vehicle_56 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2251.5256, 14447.671, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_56 = _this;
  _this setDir -268.71417;
  _this setPos [2251.5256, 14447.671, 0.00030517578];
};

_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2268.7454, 14481.414, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setDir -61.552513;
  _this setPos [2268.7454, 14481.414, 0.00024414063];
};

_vehicle_58 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2302.3047, 14520.552, -9.4060221], [], 0, "CAN_COLLIDE"];
  _vehicle_58 = _this;
  _this setDir -216.60974;
  _this setPos [2302.3047, 14520.552, -9.4060221];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2341.6084, 14527.987, -9.9876366], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir -170.80725;
  _this setPos [2341.6084, 14527.987, -9.9876366];
};

_vehicle_60 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2396.1399, 14446.259, -12.650902], [], 0, "CAN_COLLIDE"];
  _vehicle_60 = _this;
  _this setDir -95.893723;
  _this setPos [2396.1399, 14446.259, -12.650902];
};

_vehicle_61 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2384.0801, 14326.154, -10.230657], [], 0, "CAN_COLLIDE"];
  _vehicle_61 = _this;
  _this setDir -37.277905;
  _this setPos [2384.0801, 14326.154, -10.230657];
};

_vehicle_62 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2327.4238, 14270.233, -0.0017089844], [], 0, "CAN_COLLIDE"];
  _vehicle_62 = _this;
  _this setDir -16.319345;
  _this setPos [2327.4238, 14270.233, -0.0017089844];
};

_vehicle_63 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2355.022, 14291.219, -0.00054931641], [], 0, "CAN_COLLIDE"];
  _vehicle_63 = _this;
  _this setDir -73.799889;
  _this setPos [2355.022, 14291.219, -0.00054931641];
};

_vehicle_64 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2423.5193, 14370.813, -10.788845], [], 0, "CAN_COLLIDE"];
  _vehicle_64 = _this;
  _this setDir 35.606098;
  _this setPos [2423.5193, 14370.813, -10.788845];
};

_vehicle_65 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2409.6418, 14405.48, -8.5268297], [], 0, "CAN_COLLIDE"];
  _vehicle_65 = _this;
  _this setDir -101.23235;
  _this setPos [2409.6418, 14405.48, -8.5268297];
};

_vehicle_66 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [2377.2944, 14496.095, -3.7204714], [], 0, "CAN_COLLIDE"];
  _vehicle_66 = _this;
  _this setDir -134.71599;
  _this setPos [2377.2944, 14496.095, -3.7204714];
};

_vehicle_77 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2502.0344, 14249.408, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_77 = _this;
  _this setDir -42.657841;
  _this setPos [2502.0344, 14249.408, 0.00024414063];
};

_vehicle_78 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2490.4529, 14262.088, 0.00039672852], [], 0, "CAN_COLLIDE"];
  _vehicle_78 = _this;
  _this setDir -42.657841;
  _this setPos [2490.4529, 14262.088, 0.00039672852];
};

_vehicle_79 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2478.8735, 14274.825, 0.00042724609], [], 0, "CAN_COLLIDE"];
  _vehicle_79 = _this;
  _this setDir -42.657841;
  _this setPos [2478.8735, 14274.825, 0.00042724609];
};

_vehicle_80 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2467.4041, 14287.416, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_80 = _this;
  _this setDir -42.657841;
  _this setPos [2467.4041, 14287.416, 3.0517578e-005];
};

_vehicle_81 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2455.9238, 14300.135, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_81 = _this;
  _this setDir -42.657841;
  _this setPos [2455.9238, 14300.135, 0.00021362305];
};

_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2444.4802, 14312.573, 0.00039672852], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setDir -42.657841;
  _this setPos [2444.4802, 14312.573, 0.00039672852];
};

_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2432.896, 14325.164, 0.00054931641], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setDir -42.657841;
  _this setPos [2432.896, 14325.164, 0.00054931641];
};

_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2421.3616, 14337.774, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setDir -42.657841;
  _this setPos [2421.3616, 14337.774, 9.1552734e-005];
};

_vehicle_85 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2149.8821, 14339.579, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_85 = _this;
  _this setDir 20.368803;
  _this setPos [2149.8821, 14339.579, 6.1035156e-005];
};

_vehicle_86 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2155.9507, 14355.568, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_86 = _this;
  _this setDir 20.368803;
  _this setPos [2155.9507, 14355.568, 0.00024414063];
};

_vehicle_87 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2161.8618, 14371.521, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_87 = _this;
  _this setDir 20.368803;
  _this setPos [2161.8618, 14371.521, 0.00036621094];
};

_vehicle_88 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2167.8123, 14387.676], [], 0, "CAN_COLLIDE"];
  _vehicle_88 = _this;
  _this setDir 20.368803;
  _this setPos [2167.8123, 14387.676];
};

_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2173.8794, 14403.749, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir 20.368803;
  _this setPos [2173.8794, 14403.749, 0.00015258789];
};

_vehicle_90 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2125.3506, 14357.194, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_90 = _this;
  _this setDir 27.226488;
  _this setPos [2125.3506, 14357.194, 0.00024414063];
};

_vehicle_91 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2133.1177, 14372.521, 0.00057983398], [], 0, "CAN_COLLIDE"];
  _vehicle_91 = _this;
  _this setDir 27.226488;
  _this setPos [2133.1177, 14372.521, 0.00057983398];
};

_vehicle_92 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2140.9363, 14387.888, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_92 = _this;
  _this setDir 27.226488;
  _this setPos [2140.9363, 14387.888, 0.0002746582];
};

_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2148.7104, 14403.154, 0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setDir 27.226488;
  _this setPos [2148.7104, 14403.154, 0.00033569336];
};

_vehicle_94 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2156.5144, 14418.388, 0.00048828125], [], 0, "CAN_COLLIDE"];
  _vehicle_94 = _this;
  _this setDir 27.226488;
  _this setPos [2156.5144, 14418.388, 0.00048828125];
};

_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2164.2498, 14433.699, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setDir 27.226488;
  _this setPos [2164.2498, 14433.699, 3.0517578e-005];
};

_vehicle_96 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1984.8279, 14413.608, 0.00045776367], [], 0, "CAN_COLLIDE"];
  _vehicle_96 = _this;
  _this setDir 77.109444;
  _this setPos [1984.8279, 14413.608, 0.00045776367];
};

_vehicle_97 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2001.5659, 14417.396, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_97 = _this;
  _this setDir 77.109444;
  _this setPos [2001.5659, 14417.396, 9.1552734e-005];
};

_vehicle_98 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2018.2395, 14421.201, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_98 = _this;
  _this setDir 77.109444;
  _this setPos [2018.2395, 14421.201, 0.00024414063];
};

_vehicle_99 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2034.8928, 14425.182, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_99 = _this;
  _this setDir 77.109444;
  _this setPos [2034.8928, 14425.182, 0.00036621094];
};

_vehicle_100 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2051.5181, 14429.05, 0.00057983398], [], 0, "CAN_COLLIDE"];
  _vehicle_100 = _this;
  _this setDir 77.109444;
  _this setPos [2051.5181, 14429.05, 0.00057983398];
};

_vehicle_101 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2068.2437, 14432.85, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_101 = _this;
  _this setDir 77.109444;
  _this setPos [2068.2437, 14432.85, 0.00018310547];
};

_vehicle_102 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2084.926, 14436.778, 0.00039672852], [], 0, "CAN_COLLIDE"];
  _vehicle_102 = _this;
  _this setDir 77.109444;
  _this setPos [2084.926, 14436.778, 0.00039672852];
};

_vehicle_103 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2101.6177, 14440.672, 0.00048828125], [], 0, "CAN_COLLIDE"];
  _vehicle_103 = _this;
  _this setDir 77.109444;
  _this setPos [2101.6177, 14440.672, 0.00048828125];
};

_vehicle_104 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2118.3162, 14444.664, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_104 = _this;
  _this setDir 77.109444;
  _this setPos [2118.3162, 14444.664, 6.1035156e-005];
};

_vehicle_106 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2149.9675, 14339.545, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_106 = _this;
  _this setDir -159.55058;
  _this setPos [2149.9675, 14339.545, -0.00015258789];
};

_vehicle_107 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2142.2893, 14333.116], [], 0, "CAN_COLLIDE"];
  _vehicle_107 = _this;
  _this setDir -99.041939;
  _this setPos [2142.2893, 14333.116];
};

_vehicle_109 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2132.9031, 14336.629, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_109 = _this;
  _this setDir -39.358158;
  _this setPos [2132.9031, 14336.629, -9.1552734e-005];
};

_vehicle_110 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2125.3667, 14347.277, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_110 = _this;
  _this setDir -29.212273;
  _this setPos [2125.3667, 14347.277, -0.00015258789];
};

_vehicle_111 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2171.8164, 14459.223, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_111 = _this;
  _this setDir 147.71155;
  _this setPos [2171.8164, 14459.223, 6.1035156e-005];
};

_vehicle_112 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2162.8923, 14463.785, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_112 = _this;
  _this setDir 87.65564;
  _this setPos [2162.8923, 14463.785, 6.1035156e-005];
};

_vehicle_113 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2149.9729, 14462.097, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_113 = _this;
  _this setDir 77.62104;
  _this setPos [2149.9729, 14462.097, 0.00018310547];
};

_vehicle_115 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2142.623, 14455.274, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_115 = _this;
  _this setDir 17.542702;
  _this setPos [2142.623, 14455.274, 0.00021362305];
};

_vehicle_116 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2142.7048, 14455.258, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_116 = _this;
  _this setDir -162.53932;
  _this setPos [2142.7048, 14455.258, 0.00018310547];
};

_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1901.0886, 14393.427, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setDir 76.462334;
  _this setPos [1901.0886, 14393.427, 9.1552734e-005];
};

_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1917.7728, 14397.4, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setDir 76.462334;
  _this setPos [1917.7728, 14397.4, 0.00012207031];
};

_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1934.437, 14401.442, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir 76.462334;
  _this setPos [1934.437, 14401.442, 0.00012207031];
};

_vehicle_121 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1951.1981, 14405.416, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_121 = _this;
  _this setDir 76.462334;
  _this setPos [1951.1981, 14405.416, 6.1035156e-005];
};

_vehicle_122 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1967.8854, 14409.475, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_122 = _this;
  _this setDir 76.462334;
  _this setPos [1967.8854, 14409.475, 9.1552734e-005];
};

_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [1883.4683, 14385.129, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setDir 53.921104;
  _this setPos [1883.4683, 14385.129, 0.00024414063];
};

_vehicle_124 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [1883.4524, 14385.192, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_124 = _this;
  _this setDir 234.55818;
  _this setPos [1883.4524, 14385.192, 3.0517578e-005];
};

_vehicle_125 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [1859.5614, 14375.892, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_125 = _this;
  _this setDir 77.407173;
  _this setPos [1859.5614, 14375.892, -3.0517578e-005];
};

_vehicle_134 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_Rock1", [1821.5695, 14304.425, -18.729818], [], 0, "CAN_COLLIDE"];
  _vehicle_134 = _this;
  _this setDir -128.39676;
  _this setPos [1821.5695, 14304.425, -18.729818];
};

_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_Rock1", [1823.5232, 14314.602, -24.214712], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir -82.752495;
  _this setPos [1823.5232, 14314.602, -24.214712];
};

_vehicle_136 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_Rock1", [1826.8723, 14328.16, -20.189875], [], 0, "CAN_COLLIDE"];
  _vehicle_136 = _this;
  _this setDir -51.327774;
  _this setPos [1826.8723, 14328.16, -20.189875];
};

_vehicle_137 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_Rock1", [1846.1985, 14337.835, -23.75304], [], 0, "CAN_COLLIDE"];
  _vehicle_137 = _this;
  _this setDir 58.795509;
  _this setPos [1846.1985, 14337.835, -23.75304];
};

_vehicle_140 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockTower", [1836.934, 14334.821, -19.384754], [], 0, "CAN_COLLIDE"];
  _vehicle_140 = _this;
  _this setDir 22.386787;
  _this setPos [1836.934, 14334.821, -19.384754];
};

_vehicle_143 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pond_small_06", [1839.7551, 14315.893, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_143 = _this;
  _this setDir 0.86211473;
  _this setPos [1839.7551, 14315.893, 0.00012207031];
};

_vehicle_145 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_Boulder2", [1829.5109, 14300.687, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_145 = _this;
  _this setPos [1829.5109, 14300.687, 3.0517578e-005];
};

_vehicle_146 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_Boulder1", [1828.682, 14298.994, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_146 = _this;
  _this setDir -115.00209;
  _this setPos [1828.682, 14298.994, -3.0517578e-005];
};

_vehicle_147 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2179.9395, 14420.097], [], 0, "CAN_COLLIDE"];
  _vehicle_147 = _this;
  _this setDir 20.991568;
  _this setPos [2179.9395, 14420.097];
};

_vehicle_148 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2187.6423, 14426.557, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_148 = _this;
  _this setDir 81.144524;
  _this setPos [2187.6423, 14426.557, 0.00024414063];
};

_vehicle_149 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2207.189, 14427.125, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_149 = _this;
  _this setDir 96.072189;
  _this setPos [2207.189, 14427.125, -6.1035156e-005];
};

_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2242.5623, 14416.406], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setDir -69.448647;
  _this setPos [2242.5623, 14416.406];
};

_vehicle_153 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2258.9722, 14410.473], [], 0, "CAN_COLLIDE"];
  _vehicle_153 = _this;
  _this setDir -70.076157;
  _this setPos [2258.9722, 14410.473];
};

_vehicle_154 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [2264.8523, 14408.353, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_154 = _this;
  _this setDir 19.845116;
  _this setPos [2264.8523, 14408.353, 0.00012207031];
};

_vehicle_155 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2266.9321, 14414.187], [], 0, "CAN_COLLIDE"];
  _vehicle_155 = _this;
  _this setDir 20.19722;
  _this setPos [2266.9321, 14414.187];
};

_vehicle_156 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2272.8965, 14430.321, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_156 = _this;
  _this setDir 20.19722;
  _this setPos [2272.8965, 14430.321, 6.1035156e-005];
};

_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_draha", [2313.3179, 14454.98], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setDir 25.347265;
  _this setPos [2313.3179, 14454.98];
};

_vehicle_161 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runway_poj_L_2_end", [2310.5828, 14390.974, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_161 = _this;
  _this setDir -64.363792;
  _this setPos [2310.5828, 14390.974, 0.00012207031];
};

_vehicle_171 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barn_W_02", [1836.3538, 14291.224, -0.27532688], [], 0, "CAN_COLLIDE"];
  _vehicle_171 = _this;
  _this setDir -136.34012;
  _this setPos [1836.3538, 14291.224, -0.27532688];
};

_vehicle_173 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Hangar_2", [2343.054, 14470.28, -0.13706993], [], 0, "CAN_COLLIDE"];
  _vehicle_173 = _this;
  _this setDir 115.49468;
  _this setPos [2343.054, 14470.28, -0.13706993];
};

_vehicle_177 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_water_tank", [2366.0591, 14349.257, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_177 = _this;
  _this setPos [2366.0591, 14349.257, 6.1035156e-005];
};

_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Mil_ControlTower", [2328.2507, 14400.092, 0.057103954], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setDir -64.694679;
  _this setPos [2328.2507, 14400.092, 0.057103954];
};

_vehicle_190 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Mil_Mil_Guardhouse", [2306.6333, 14362.422, 0.37179458], [], 0, "CAN_COLLIDE"];
  _vehicle_190 = _this;
  _this setDir -153.41069;
  _this setPos [2306.6333, 14362.422, 0.37179458];
};

_vehicle_191 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Mil_House", [2330.6174, 14363.603, 0.23799369], [], 0, "CAN_COLLIDE"];
  _vehicle_191 = _this;
  _this setDir 116.27332;
  _this setPos [2330.6174, 14363.603, 0.23799369];
};

_vehicle_193 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2140.6111, 14343.691, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_193 = _this;
  _this setPos [2140.6111, 14343.691, 3.0517578e-005];
};

_vehicle_201 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2130.7617, 14354.205, -0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_201 = _this;
  _this setPos [2130.7617, 14354.205, -0.00033569336];
};

_vehicle_202 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2132.8306, 14345.013, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_202 = _this;
  _this setPos [2132.8306, 14345.013, -0.00012207031];
};

_vehicle_203 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2219.5269, 14410.354, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_203 = _this;
  _this setPos [2219.5269, 14410.354, 6.1035156e-005];
};

_vehicle_204 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2223.0005, 14435.097, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_204 = _this;
  _this setPos [2223.0005, 14435.097, -0.00012207031];
};

_vehicle_205 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2201.1521, 14387.451, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_205 = _this;
  _this setPos [2201.1521, 14387.451, 0];
};

_vehicle_206 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2193.9626, 14417.539, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_206 = _this;
  _this setPos [2193.9626, 14417.539, 0.00012207031];
};

_vehicle_207 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2206.0916, 14406.994, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_207 = _this;
  _this setPos [2206.0916, 14406.994, 0.00012207031];
};

_vehicle_208 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2173.304, 14433.659, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_208 = _this;
  _this setPos [2173.304, 14433.659, 0.0002746582];
};

_vehicle_209 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2167.4919, 14420.3, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_209 = _this;
  _this setPos [2167.4919, 14420.3, 3.0517578e-005];
};

_vehicle_210 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2160.3286, 14403.676, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_210 = _this;
  _this setPos [2160.3286, 14403.676, -6.1035156e-005];
};

_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2162.4126, 14387.678, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setPos [2162.4126, 14387.678, -6.1035156e-005];
};

_vehicle_212 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2154.6216, 14388.577, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_212 = _this;
  _this setPos [2154.6216, 14388.577, -3.0517578e-005];
};

_vehicle_213 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2155.3008, 14369.705, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_213 = _this;
  _this setPos [2155.3008, 14369.705, -6.1035156e-005];
};

_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2150.6455, 14375.96, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setPos [2150.6455, 14375.96, -9.1552734e-005];
};

_vehicle_215 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2139.8899, 14369.283, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_215 = _this;
  _this setPos [2139.8899, 14369.283, -0.00012207031];
};

_vehicle_216 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2146.1455, 14360.208, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_216 = _this;
  _this setPos [2146.1455, 14360.208, 6.1035156e-005];
};

_vehicle_217 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2203.8777, 14437.765, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_217 = _this;
  _this setPos [2203.8777, 14437.765, 0.00018310547];
};

_vehicle_218 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2183.8894, 14438.319, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_218 = _this;
  _this setPos [2183.8894, 14438.319, 6.1035156e-005];
};

_vehicle_219 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2211.1597, 14447.987, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_219 = _this;
  _this setPos [2211.1597, 14447.987, -0.00012207031];
};

_vehicle_220 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2185.0527, 14446.014, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_220 = _this;
  _this setPos [2185.0527, 14446.014, 3.0517578e-005];
};

_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2252.4321, 14395.605, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setPos [2252.4321, 14395.605, -6.1035156e-005];
};

_vehicle_222 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2233.7849, 14410.033, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_222 = _this;
  _this setPos [2233.7849, 14410.033, 0];
};

_vehicle_223 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2210.8496, 14396.008, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_223 = _this;
  _this setPos [2210.8496, 14396.008, 0];
};

_vehicle_224 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2158.8489, 14451.53, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_224 = _this;
  _this setPos [2158.8489, 14451.53, 0.0002746582];
};

_vehicle_225 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2116.738, 14365.915, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_225 = _this;
  _this setPos [2116.738, 14365.915, 6.1035156e-005];
};

_vehicle_226 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2116.6765, 14350.695], [], 0, "CAN_COLLIDE"];
  _vehicle_226 = _this;
  _this setPos [2116.6765, 14350.695];
};

_vehicle_227 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2126.9663, 14332.141], [], 0, "CAN_COLLIDE"];
  _vehicle_227 = _this;
  _this setPos [2126.9663, 14332.141];
};

_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2120.7549, 14340.861, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setPos [2120.7549, 14340.861, 3.0517578e-005];
};

_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2169.5725, 14343.47, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setPos [2169.5725, 14343.47, -0.00018310547];
};

_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2149.0327, 14324.933], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setPos [2149.0327, 14324.933];
};

_vehicle_231 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2143.8938, 14313.788, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_231 = _this;
  _this setPos [2143.8938, 14313.788, -0.00018310547];
};

_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2137.3347, 14304.597, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setPos [2137.3347, 14304.597, 9.1552734e-005];
};

_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2137.7109, 14326.386, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setPos [2137.7109, 14326.386, -0.00012207031];
};

_vehicle_234 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2143.6367, 14352.649, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_234 = _this;
  _this setPos [2143.6367, 14352.649, 0];
};

_vehicle_235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2152.7104, 14382.412, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_235 = _this;
  _this setPos [2152.7104, 14382.412, -9.1552734e-005];
};

_vehicle_236 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2162.1123, 14409.488], [], 0, "CAN_COLLIDE"];
  _vehicle_236 = _this;
  _this setPos [2162.1123, 14409.488];
};

_vehicle_237 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2170.9678, 14427.593, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_237 = _this;
  _this setPos [2170.9678, 14427.593, 0.00018310547];
};

_vehicle_238 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2214.4089, 14402.512, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_238 = _this;
  _this setPos [2214.4089, 14402.512, 0];
};

_vehicle_239 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2222.1951, 14440.719, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_239 = _this;
  _this setPos [2222.1951, 14440.719, 0.00024414063];
};

_vehicle_240 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2139.9165, 14360.409, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_240 = _this;
  _this setPos [2139.9165, 14360.409, -0.00012207031];
};

_vehicle_241 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2160.4519, 14441.689], [], 0, "CAN_COLLIDE"];
  _vehicle_241 = _this;
  _this setPos [2160.4519, 14441.689];
};

_vehicle_242 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2151.4014, 14453.582, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_242 = _this;
  _this setPos [2151.4014, 14453.582, 0.00012207031];
};

_vehicle_243 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2152.6235, 14445.582, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_243 = _this;
  _this setPos [2152.6235, 14445.582, 0.00015258789];
};

_vehicle_244 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2164.032, 14457.537, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_244 = _this;
  _this setPos [2164.032, 14457.537, -3.0517578e-005];
};

_vehicle_245 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2180.719, 14456.004, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_245 = _this;
  _this setPos [2180.719, 14456.004, 0.00015258789];
};

_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2197.8018, 14448.859, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setPos [2197.8018, 14448.859, -0.00012207031];
};

_vehicle_247 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2317.8218, 14352.496], [], 0, "CAN_COLLIDE"];
  _vehicle_247 = _this;
  _this setPos [2317.8218, 14352.496];
};

_vehicle_248 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2247.3652, 14403.441], [], 0, "CAN_COLLIDE"];
  _vehicle_248 = _this;
  _this setPos [2247.3652, 14403.441];
};

_vehicle_250 = objNull;
if (true) then
{
  _this = createVehicle ["76n6ClamShell", [2317.7339, 14411.18, -0.57846045], [], 0, "CAN_COLLIDE"];
  _vehicle_250 = _this;
  _this setDir -25.568237;
  _this setPos [2317.7339, 14411.18, -0.57846045];
};

_vehicle_301 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [2236.3167, 14408.446, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_301 = _this;
  _this setDir 112.61575;
  _this setPos [2236.3167, 14408.446, 0.00018310547];
};

_vehicle_346 = objNull;
if (true) then
{
  _this = createVehicle ["Gold_Vein_DZE", [1851.6099, 13865.483], [], 0, "CAN_COLLIDE"];
  _vehicle_346 = _this;
  _this setDir 48.280102;
  _this setPos [1851.6099, 13865.483];
};

_vehicle_347 = objNull;
if (true) then
{
  _this = createVehicle ["Iron_Vein_DZE", [1844.764, 13867.987, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_347 = _this;
  _this setPos [1844.764, 13867.987, 0.00018310547];
};

_vehicle_348 = objNull;
if (true) then
{
  _this = createVehicle ["Silver_Vein_DZE", [1841.3307, 13863.213, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_348 = _this;
  _this setDir 0.92399275;
  _this setPos [1841.3307, 13863.213, -6.1035156e-005];
};

_vehicle_350 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5_DZ", [2274.1428, 14384.726, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_350 = _this;
  _this setDir -153.62265;
  _this setPos [2274.1428, 14384.726, 6.1035156e-005];
};

_vehicle_351 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2352.1106, 14357.827, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_351 = _this;
  _this setDir -108.60854;
  _this setPos [2352.1106, 14357.827, 3.0517578e-005];
};

_vehicle_352 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2352.7573, 14366.7, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_352 = _this;
  _this setDir -63.118961;
  _this setPos [2352.7573, 14366.7, -6.1035156e-005];
};

_vehicle_353 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2357.2585, 14375.411, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_353 = _this;
  _this setDir -62.269287;
  _this setPos [2357.2585, 14375.411, 6.1035156e-005];
};

_vehicle_354 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2361.4639, 14383.127, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_354 = _this;
  _this setDir -60.009109;
  _this setPos [2361.4639, 14383.127, -6.1035156e-005];
};

_vehicle_355 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2368.7029, 14386.554, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_355 = _this;
  _this setDir 7.5383048;
  _this setPos [2368.7029, 14386.554, 6.1035156e-005];
};

_vehicle_356 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2381.7222, 14373.939, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_356 = _this;
  _this setDir 60.926121;
  _this setPos [2381.7222, 14373.939, 6.1035156e-005];
};

_vehicle_357 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2376.3328, 14382.114, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_357 = _this;
  _this setDir 52.31741;
  _this setPos [2376.3328, 14382.114, 6.1035156e-005];
};

_vehicle_358 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2385.4009, 14364.982, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_358 = _this;
  _this setDir 74.844894;
  _this setPos [2385.4009, 14364.982, 0.00012207031];
};

_vehicle_359 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2383.0945, 14357.252, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_359 = _this;
  _this setDir 138.98724;
  _this setPos [2383.0945, 14357.252, 6.1035156e-005];
};

_vehicle_360 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2377.5813, 14352.571, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_360 = _this;
  _this setDir 138.41257;
  _this setPos [2377.5813, 14352.571, 6.1035156e-005];
};

_vehicle_361 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2370.2009, 14345.945], [], 0, "CAN_COLLIDE"];
  _vehicle_361 = _this;
  _this setDir 138.09105;
  _this setPos [2370.2009, 14345.945];
};

_vehicle_362 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2355.4128, 14348.583], [], 0, "CAN_COLLIDE"];
  _vehicle_362 = _this;
  _this setDir -111.2235;
  _this setPos [2355.4128, 14348.583];
};

_vehicle_363 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fort_rampart", [2361.8435, 14343.449, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_363 = _this;
  _this setDir 188.9287;
  _this setPos [2361.8435, 14343.449, -6.1035156e-005];
};

_vehicle_364 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_Boulder1", [2245.5442, 14386.323, 0.51292461], [], 0, "CAN_COLLIDE"];
  _vehicle_364 = _this;
  _this setDir -16.977482;
  _this setPos [2245.5442, 14386.323, 0.51292461];
};

_vehicle_365 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_Boulder1", [2244.1028, 14388.021, 1.324398], [], 0, "CAN_COLLIDE"];
  _vehicle_365 = _this;
  _this setDir -55.392242;
  _this setPos [2244.1028, 14388.021, 1.324398];
};

_vehicle_372 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2279.7759, 14391.647, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_372 = _this;
  _this setPos [2279.7759, 14391.647, 0];
};

_vehicle_373 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2295.0793, 14387.363, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_373 = _this;
  _this setPos [2295.0793, 14387.363, 0];
};

_vehicle_374 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2313.2651, 14378.726, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_374 = _this;
  _this setPos [2313.2651, 14378.726, 0];
};

_vehicle_375 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2304.4143, 14382.629, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_375 = _this;
  _this setPos [2304.4143, 14382.629, -6.1035156e-005];
};

_vehicle_376 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2287.1958, 14400.684, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_376 = _this;
  _this setPos [2287.1958, 14400.684, 0];
};

_vehicle_378 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2278.7881, 14378.312, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_378 = _this;
  _this setPos [2278.7881, 14378.312, 6.1035156e-005];
};

_vehicle_379 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2270.7266, 14381.523, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_379 = _this;
  _this setPos [2270.7266, 14381.523, -0.00012207031];
};

_vehicle_380 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2290.7148, 14361.837, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_380 = _this;
  _this setPos [2290.7148, 14361.837, -6.1035156e-005];
};

_vehicle_384 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [2245.9099, 14410.934, 0.30761236], [], 0, "CAN_COLLIDE"];
  _vehicle_384 = _this;
  _this setDir 107.47172;
  _this setVehicleLock "UNLOCKED";
  _this setPos [2245.9099, 14410.934, 0.30761236];
};

_vehicle_385 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierPOWMIA_EP1", [2239.729, 14410.59, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_385 = _this;
  _this setPos [2239.729, 14410.59, 6.1035156e-005];
};

_vehicle_387 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2275.3442, 14354.879, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_387 = _this;
  _this setDir -274.88309;
  _this setPos [2275.3442, 14354.879, 0.00012207031];
};

_vehicle_388 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2267.1902, 14349.071], [], 0, "CAN_COLLIDE"];
  _vehicle_388 = _this;
  _this setDir 24.891813;
  _this setPos [2267.1902, 14349.071];
};

_vehicle_389 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2262.7271, 14336.804], [], 0, "CAN_COLLIDE"];
  _vehicle_389 = _this;
  _this setDir 14.972783;
  _this setPos [2262.7271, 14336.804];
};

_vehicle_398 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2268.2249, 14381.929, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_398 = _this;
  _this setDir -70.01017;
  _this setPos [2268.2249, 14381.929, 0.00012207031];
};

_vehicle_400 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2261.7568, 14389.561, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_400 = _this;
  _this setDir -10.210488;
  _this setPos [2261.7568, 14389.561, 6.1035156e-005];
};

_vehicle_401 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2268.2942, 14382.153], [], 0, "CAN_COLLIDE"];
  _vehicle_401 = _this;
  _this setDir -249.8399;
  _this setPos [2268.2942, 14382.153];
};

_vehicle_402 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [2294.5781, 14354.408], [], 0, "CAN_COLLIDE"];
  _vehicle_402 = _this;
  _this setDir 94.491905;
  _this setPos [2294.5781, 14354.408];
};

_vehicle_404 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2290.7927, 14369.645, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_404 = _this;
  _this setDir 124.71646;
  _this setPos [2290.7927, 14369.645, -6.1035156e-005];
};

_vehicle_406 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [2290.8171, 14369.617], [], 0, "CAN_COLLIDE"];
  _vehicle_406 = _this;
  _this setDir -55.552227;
  _this setPos [2290.8171, 14369.617];
};

_vehicle_410 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2300.7551, 14354.042], [], 0, "CAN_COLLIDE"];
  _vehicle_410 = _this;
  _this setDir 94.712677;
  _this setPos [2300.7551, 14354.042];
};

_vehicle_411 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2317.3884, 14343.007], [], 0, "CAN_COLLIDE"];
  _vehicle_411 = _this;
  _this setDir -87.204086;
  _this setPos [2317.3884, 14343.007];
};

_vehicle_412 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2317.356, 14343.052], [], 0, "CAN_COLLIDE"];
  _vehicle_412 = _this;
  _this setDir 92.748207;
  _this setPos [2317.356, 14343.052];
};

_vehicle_413 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2278.9895, 14446.664, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_413 = _this;
  _this setDir 20.711559;
  _this setPos [2278.9895, 14446.664, 6.1035156e-005];
};

_vehicle_414 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2284.5891, 14458.434, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_414 = _this;
  _this setDir 30.917549;
  _this setPos [2284.5891, 14458.434, 6.1035156e-005];
};

_vehicle_415 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2299.8835, 14479.574, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_415 = _this;
  _this setDir -149.18222;
  _this setPos [2299.8835, 14479.574, 6.1035156e-005];
};

_vehicle_416 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2299.8442, 14479.581, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_416 = _this;
  _this setDir 30.681425;
  _this setPos [2299.8442, 14479.581, 0.00012207031];
};

_vehicle_417 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2308.6858, 14494.578, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_417 = _this;
  _this setDir 31.304611;
  _this setPos [2308.6858, 14494.578, 6.1035156e-005];
};

_vehicle_418 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2318.009, 14503.554, 0.00054931641], [], 0, "CAN_COLLIDE"];
  _vehicle_418 = _this;
  _this setDir 61.368561;
  _this setPos [2318.009, 14503.554, 0.00054931641];
};

_vehicle_419 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2330.5833, 14506.676, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_419 = _this;
  _this setDir 91.336151;
  _this setPos [2330.5833, 14506.676, 0.00018310547];
};

_vehicle_420 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2343.0166, 14503.116, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_420 = _this;
  _this setDir 121.03956;
  _this setPos [2343.0166, 14503.116, 6.1035156e-005];
};

_vehicle_421 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2358.3171, 14490.931, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_421 = _this;
  _this setDir 136.14815;
  _this setPos [2358.3171, 14490.931, 6.1035156e-005];
};

_vehicle_422 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2378.4648, 14459.958, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_422 = _this;
  _this setDir -29.419901;
  _this setPos [2378.4648, 14459.958, 0.00018310547];
};

_vehicle_423 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2378.4963, 14460.012, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_423 = _this;
  _this setDir 151.07736;
  _this setPos [2378.4963, 14460.012, -0.00012207031];
};

_vehicle_424 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2352.1846, 14373.801, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_424 = _this;
  _this setDir 27.158556;
  _this setPos [2352.1846, 14373.801, 6.1035156e-005];
};

_vehicle_425 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf3_asf3", [2363.0752, 14394.791, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_425 = _this;
  _this setDir -152.30904;
  _this setPos [2363.0752, 14394.791, -6.1035156e-005];
};

_vehicle_426 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2365.9465, 14400.338, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_426 = _this;
  _this setDir 27.74441;
  _this setPos [2365.9465, 14400.338, -6.1035156e-005];
};

_vehicle_428 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2380.9617, 14434.054], [], 0, "CAN_COLLIDE"];
  _vehicle_428 = _this;
  _this setDir 193.39018;
  _this setPos [2380.9617, 14434.054];
};

_vehicle_429 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [2381.5361, 14450.104, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_429 = _this;
  _this setDir 172.80664;
  _this setPos [2381.5361, 14450.104, 6.1035156e-005];
};

_vehicle_431 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearLong", [2320.7158, 14391.724, -0.04534724], [], 0, "CAN_COLLIDE"];
  _vehicle_431 = _this;
  _this setDir 24.593348;
  _this setPos [2320.7158, 14391.724, -0.04534724];
};

_vehicle_433 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_SidewalkClearMiddle", [2340.478, 14361.747, -0.01225127], [], 0, "CAN_COLLIDE"];
  _vehicle_433 = _this;
  _this setDir -64.583488;
  _this setPos [2340.478, 14361.747, -0.01225127];
};

_vehicle_435 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2338.7053, 14356.799, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_435 = _this;
  _this setPos [2338.7053, 14356.799, -6.1035156e-005];
};

_vehicle_436 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2345.6711, 14370.789], [], 0, "CAN_COLLIDE"];
  _vehicle_436 = _this;
  _this setPos [2345.6711, 14370.789];
};

_vehicle_437 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2343.4265, 14365.73], [], 0, "CAN_COLLIDE"];
  _vehicle_437 = _this;
  _this setPos [2343.4265, 14365.73];
};

_vehicle_438 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2335.3113, 14349.604, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_438 = _this;
  _this setPos [2335.3113, 14349.604, -6.1035156e-005];
};

_vehicle_439 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2342.2935, 14378.693, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_439 = _this;
  _this setPos [2342.2935, 14378.693, 6.1035156e-005];
};

_vehicle_440 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2336.4419, 14381.949, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_440 = _this;
  _this setPos [2336.4419, 14381.949, 6.1035156e-005];
};

_vehicle_441 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2332.9395, 14390.726, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_441 = _this;
  _this setPos [2332.9395, 14390.726, -6.1035156e-005];
};

_vehicle_442 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2336.2063, 14397.836, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_442 = _this;
  _this setPos [2336.2063, 14397.836, 0.00018310547];
};

_vehicle_443 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2315.5068, 14394.896, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_443 = _this;
  _this setPos [2315.5068, 14394.896, -6.1035156e-005];
};

_vehicle_444 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2319.1743, 14403.715, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_444 = _this;
  _this setPos [2319.1743, 14403.715, 6.1035156e-005];
};

_vehicle_445 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2310.4976, 14404.705, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_445 = _this;
  _this setPos [2310.4976, 14404.705, -6.1035156e-005];
};

_vehicle_446 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2344.2944, 14315.915, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_446 = _this;
  _this setPos [2344.2944, 14315.915, 0.00012207031];
};

_vehicle_447 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2298.5046, 14368.99, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_447 = _this;
  _this setPos [2298.5046, 14368.99, 0.00012207031];
};

_vehicle_448 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2254.21, 14383.069, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_448 = _this;
  _this setPos [2254.21, 14383.069, 0.00018310547];
};

_vehicle_449 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2266.8594, 14395.638, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_449 = _this;
  _this setPos [2266.8594, 14395.638, 0.00012207031];
};

_vehicle_450 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2274.6519, 14348.734, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_450 = _this;
  _this setPos [2274.6519, 14348.734, 0.00018310547];
};

_vehicle_451 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2259.1853, 14363.247, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_451 = _this;
  _this setPos [2259.1853, 14363.247, 0.00012207031];
};

_vehicle_452 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2326.1704, 14331.138, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_452 = _this;
  _this setDir 55.74057;
  _this setPos [2326.1704, 14331.138, -6.1035156e-005];
};

_vehicle_453 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2304.29, 14342.404, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_453 = _this;
  _this setPos [2304.29, 14342.404, 6.1035156e-005];
};

_vehicle_454 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2288.7458, 14349.863, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_454 = _this;
  _this setPos [2288.7458, 14349.863, 6.1035156e-005];
};

_vehicle_455 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2296.2327, 14346.358, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_455 = _this;
  _this setPos [2296.2327, 14346.358, 6.1035156e-005];
};

_vehicle_456 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [2322.6741, 14329.771, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_456 = _this;
  _this setDir 21.081322;
  _this setPos [2322.6741, 14329.771, -0.00012207031];
};

_vehicle_457 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [2332.1514, 14325.357, 0.2497462], [], 0, "CAN_COLLIDE"];
  _vehicle_457 = _this;
  _this setDir 27.660156;
  _this setPos [2332.1514, 14325.357, 0.2497462];
};

_vehicle_458 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [2370.7415, 14394.918, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_458 = _this;
  _this setDir -58.13126;
  _this setPos [2370.7415, 14394.918, 0.00012207031];
};

_vehicle_461 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Garb_Heap_EP1", [2279.0747, 14344.54, 0.089680053], [], 0, "CAN_COLLIDE"];
  _vehicle_461 = _this;
  _this setPos [2279.0747, 14344.54, 0.089680053];
};

_vehicle_462 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_concrete_High", [2323.0942, 14331.042, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_462 = _this;
  _this setDir 113.44378;
  _this setPos [2323.0942, 14331.042, 0.00012207031];
};

_vehicle_465 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hlidac_budka", [2373.7524, 14397.807, -0.065531619], [], 0, "CAN_COLLIDE"];
  _vehicle_465 = _this;
  _this setDir 117.81148;
  _this setPos [2373.7524, 14397.807, -0.065531619];
};

_vehicle_466 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hlidac_budka", [2334.55, 14320.389, -0.030388769], [], 0, "CAN_COLLIDE"];
  _vehicle_466 = _this;
  _this setDir -155.51353;
  _this setPos [2334.55, 14320.389, -0.030388769];
};

_vehicle_467 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hlidac_budka", [2241.7395, 14408.531, -0.12361009], [], 0, "CAN_COLLIDE"];
  _vehicle_467 = _this;
  _this setDir -71.579422;
  _this setPos [2241.7395, 14408.531, -0.12361009];
};

_vehicle_472 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_burning", [2244.27, 14408.393, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_472 = _this;
  _this setPos [2244.27, 14408.393, 6.1035156e-005];
};

_vehicle_474 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_burning", [2370.4827, 14396.965, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_474 = _this;
  _this setPos [2370.4827, 14396.965, 0.00030517578];
};

_vehicle_476 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [2282.5205, 14347.394, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_476 = _this;
  _this setDir 204.68188;
  _this setPos [2282.5205, 14347.394, 0.00012207031];
};

_vehicle_477 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [2283.3896, 14349.035], [], 0, "CAN_COLLIDE"];
  _vehicle_477 = _this;
  _this setDir -153.3013;
  _this setPos [2283.3896, 14349.035];
};

_vehicle_482 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2328.3853, 14485.484, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_482 = _this;
  _this setPos [2328.3853, 14485.484, 0.00030517578];
};

_vehicle_483 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2320.2549, 14470.012, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_483 = _this;
  _this setPos [2320.2549, 14470.012, 0.00018310547];
};

_vehicle_484 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2312.6484, 14454.084, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_484 = _this;
  _this setPos [2312.6484, 14454.084, 0];
};

_vehicle_485 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2305.1655, 14438.352, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_485 = _this;
  _this setPos [2305.1655, 14438.352, 0.00024414063];
};

_vehicle_486 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2297.0471, 14420.77, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_486 = _this;
  _this setPos [2297.0471, 14420.77, 0.00018310547];
};

_vehicle_487 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2293.1282, 14410.468, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_487 = _this;
  _this setPos [2293.1282, 14410.468, 0.00030517578];
};

_vehicle_488 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2301.5591, 14398.331, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_488 = _this;
  _this setPos [2301.5591, 14398.331, 0.00018310547];
};

_vehicle_489 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2323.645, 14373.767, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_489 = _this;
  _this setPos [2323.645, 14373.767, 0.00024414063];
};

_vehicle_490 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2337.5215, 14361.237, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_490 = _this;
  _this setPos [2337.5215, 14361.237, 0.00018310547];
};

_vehicle_491 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2328.1199, 14348.155, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_491 = _this;
  _this setPos [2328.1199, 14348.155, 6.1035156e-005];
};

_vehicle_492 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2310.8904, 14373.401, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_492 = _this;
  _this setPos [2310.8904, 14373.401, 0.00018310547];
};

_vehicle_493 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2299.3621, 14409.538, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_493 = _this;
  _this setPos [2299.3621, 14409.538, 0.00012207031];
};

_vehicle_494 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2285.0349, 14413.569, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_494 = _this;
  _this setPos [2285.0349, 14413.569, 0.00036621094];
};

_vehicle_495 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2298.7361, 14431.2, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_495 = _this;
  _this setPos [2298.7361, 14431.2, 6.1035156e-005];
};

_vehicle_496 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2307.5291, 14427.049, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_496 = _this;
  _this setPos [2307.5291, 14427.049, 0.00036621094];
};

_vehicle_497 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2314.5408, 14443.598, 0.00061035156], [], 0, "CAN_COLLIDE"];
  _vehicle_497 = _this;
  _this setPos [2314.5408, 14443.598, 0.00061035156];
};

_vehicle_498 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2304.52, 14448.895, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_498 = _this;
  _this setPos [2304.52, 14448.895, 0.00024414063];
};

_vehicle_499 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2322.1538, 14462.088, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_499 = _this;
  _this setPos [2322.1538, 14462.088, 0.00024414063];
};

_vehicle_500 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2312.4719, 14465.774, 0.00048828125], [], 0, "CAN_COLLIDE"];
  _vehicle_500 = _this;
  _this setPos [2312.4719, 14465.774, 0.00048828125];
};

_vehicle_501 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2321.2502, 14480.385, 0.00042724609], [], 0, "CAN_COLLIDE"];
  _vehicle_501 = _this;
  _this setPos [2321.2502, 14480.385, 0.00042724609];
};

_vehicle_502 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2330.2302, 14473.173, 0.00073242188], [], 0, "CAN_COLLIDE"];
  _vehicle_502 = _this;
  _this setPos [2330.2302, 14473.173, 0.00073242188];
};

_vehicle_503 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2323.9363, 14492.851, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_503 = _this;
  _this setPos [2323.9363, 14492.851, -6.1035156e-005];
};

_vehicle_504 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2317.6177, 14385.073, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_504 = _this;
  _this setPos [2317.6177, 14385.073, -6.1035156e-005];
};

_vehicle_505 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2319.6404, 14394.304, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_505 = _this;
  _this setPos [2319.6404, 14394.304, 0.00030517578];
};

_vehicle_513 = objNull;
if (true) then
{
  _this = createVehicle ["HeliH", [2345.0168, 14388.579, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_513 = _this;
  _this setDir 24.816515;
  _this setPos [2345.0168, 14388.579, 6.1035156e-005];
};

_vehicle_516 = objNull;
if (true) then
{
  _this = createVehicle ["Loudspeakers_EP1", [2316.2568, 14362.857], [], 0, "CAN_COLLIDE"];
  _vehicle_516 = _this;
  _this setDir 116.81097;
  _this setPos [2316.2568, 14362.857];
};

_vehicle_529 = objNull;
if (true) then
{
  _this = createVehicle ["SignM_FARP_Winchester_EP1", [2237.426, 14411.042], [], 0, "CAN_COLLIDE"];
  _vehicle_529 = _this;
  _this setDir 110.7369;
  _this setPos [2237.426, 14411.042];
};

_vehicle_531 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Danger", [2320.7212, 14330.636, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_531 = _this;
  _this setDir 19.852804;
  _this setPos [2320.7212, 14330.636, -0.00012207031];
};

_vehicle_533 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Danger", [2324.8203, 14329.311, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_533 = _this;
  _this setDir 21.215429;
  _this setPos [2324.8203, 14329.311, -6.1035156e-005];
};

_vehicle_539 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_tape_redwhite", [2327.1343, 14348.162], [], 0, "CAN_COLLIDE"];
  _vehicle_539 = _this;
  _this setDir 24.34758;
  _this setPos [2327.1343, 14348.162];
};

_vehicle_543 = objNull;
if (true) then
{
  _this = createVehicle ["RoadBarrier_light", [2265.5803, 14338.545, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_543 = _this;
  _this setDir 13.475735;
  _this setPos [2265.5803, 14338.545, -6.1035156e-005];
};

_vehicle_544 = objNull;
if (true) then
{
  _this = createVehicle ["RoadBarrier_light", [2261.2959, 14340.113, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_544 = _this;
  _this setDir -163.68153;
  _this setPos [2261.2959, 14340.113, 6.1035156e-005];
};

_vehicle_547 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2298.241, 14362.156, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_547 = _this;
  _this setDir 108.5809;
  _this setPos [2298.241, 14362.156, -3.0517578e-005];
};

_vehicle_548 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2298.3003, 14360.005], [], 0, "CAN_COLLIDE"];
  _vehicle_548 = _this;
  _this setDir 111.10218;
  _this setPos [2298.3003, 14360.005];
};

_vehicle_549 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2298.0181, 14357.722, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_549 = _this;
  _this setDir 114.35975;
  _this setPos [2298.0181, 14357.722, -3.0517578e-005];
};

_vehicle_550 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2293.813, 14371.208], [], 0, "CAN_COLLIDE"];
  _vehicle_550 = _this;
  _this setDir 104.54209;
  _this setPos [2293.813, 14371.208];
};

_vehicle_551 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2297.8303, 14364.564, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_551 = _this;
  _this setDir -71.365555;
  _this setPos [2297.8303, 14364.564, 6.1035156e-005];
};

_vehicle_552 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2295.6563, 14369.37, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_552 = _this;
  _this setDir 93.778503;
  _this setPos [2295.6563, 14369.37, 3.0517578e-005];
};

_vehicle_553 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2297.0903, 14366.806, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_553 = _this;
  _this setDir 123.77486;
  _this setPos [2297.0903, 14366.806, 3.0517578e-005];
};

_vehicle_554 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2291.7534, 14372.948, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_554 = _this;
  _this setDir 121.64648;
  _this setPos [2291.7534, 14372.948, 3.0517578e-005];
};

_vehicle_556 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2271.1135, 14384.559, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_556 = _this;
  _this setDir -67.467438;
  _this setPos [2271.1135, 14384.559, 0.00012207031];
};

_vehicle_557 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2274.0554, 14383.29], [], 0, "CAN_COLLIDE"];
  _vehicle_557 = _this;
  _this setDir 114.36503;
  _this setPos [2274.0554, 14383.29];
};

_vehicle_558 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2276.738, 14381.939, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_558 = _this;
  _this setDir 113.5368;
  _this setPos [2276.738, 14381.939, 6.1035156e-005];
};

_vehicle_559 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2279.4141, 14380.511], [], 0, "CAN_COLLIDE"];
  _vehicle_559 = _this;
  _this setDir 118.15238;
  _this setPos [2279.4141, 14380.511];
};

_vehicle_560 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2282.1931, 14379.104], [], 0, "CAN_COLLIDE"];
  _vehicle_560 = _this;
  _this setDir 117.83356;
  _this setPos [2282.1931, 14379.104];
};

_vehicle_561 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2284.7625, 14377.523, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_561 = _this;
  _this setDir 117.36649;
  _this setPos [2284.7625, 14377.523, -6.1035156e-005];
};

_vehicle_562 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2287.0161, 14376.016, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_562 = _this;
  _this setDir 116.8395;
  _this setPos [2287.0161, 14376.016, 6.1035156e-005];
};

_vehicle_563 = objNull;
if (true) then
{
  _this = createVehicle ["Land_coneLight", [2289.4285, 14374.472], [], 0, "CAN_COLLIDE"];
  _vehicle_563 = _this;
  _this setDir 122.72581;
  _this setPos [2289.4285, 14374.472];
};

_vehicle_564 = objNull;
if (true) then
{
  _this = createVehicle ["RoadBarrier_long", [2263.0911, 14337.894, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_564 = _this;
  _this setDir 15.462583;
  _this setPos [2263.0911, 14337.894, 0.00018310547];
};

_vehicle_565 = objNull;
if (true) then
{
  _this = createVehicle ["RoadBarrier_light", [2269.4287, 14387.952, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_565 = _this;
  _this setDir -63.140068;
  _this setPos [2269.4287, 14387.952, 0.00012207031];
};

_vehicle_566 = objNull;
if (true) then
{
  _this = createVehicle ["RoadBarrier_light", [2268.4988, 14386.191, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_566 = _this;
  _this setDir -241.21504;
  _this setPos [2268.4988, 14386.191, 0.00012207031];
};

_vehicle_568 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2273.1223, 14383.02, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_568 = _this;
  _this setPos [2273.1223, 14383.02, 6.1035156e-005];
};

_vehicle_581 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbYELP", [2346.7505, 14392.34, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_581 = _this;
  _this setPos [2346.7505, 14392.34, 0];
};

_vehicle_582 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbYELP", [2340.8345, 14388.175, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_582 = _this;
  _this setPos [2340.8345, 14388.175, 0.00012207031];
};

_vehicle_583 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbYELP", [2347.376, 14385.106, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_583 = _this;
  _this setPos [2347.376, 14385.106, 6.1035156e-005];
};

_vehicle_584 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2342.761, 14389.643, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_584 = _this;
  _this setPos [2342.761, 14389.643, 0];
};

_vehicle_585 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2344.8115, 14391.079, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_585 = _this;
  _this setPos [2344.8115, 14391.079, 6.1035156e-005];
};

_vehicle_586 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2347.0681, 14390.021, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_586 = _this;
  _this setPos [2347.0681, 14390.021, 0];
};

_vehicle_587 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2347.3074, 14387.563, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_587 = _this;
  _this setPos [2347.3074, 14387.563, 0];
};

_vehicle_588 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2345.3218, 14386.014, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_588 = _this;
  _this setPos [2345.3218, 14386.014, 0];
};

_vehicle_589 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2343.0203, 14387.124, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_589 = _this;
  _this setPos [2343.0203, 14387.124, 0];
};

_vehicle_590 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2339.3018, 14387.807, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_590 = _this;
  _this setPos [2339.3018, 14387.807, 0];
};

_vehicle_591 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2347.446, 14393.781, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_591 = _this;
  _this setPos [2347.446, 14393.781, 6.1035156e-005];
};

_vehicle_592 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2348.3147, 14383.833, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_592 = _this;
  _this setPos [2348.3147, 14383.833, 0.00012207031];
};

_vehicle_593 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2342.4717, 14383.386, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_593 = _this;
  _this setPos [2342.4717, 14383.386, 0];
};

_vehicle_594 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2341.7622, 14393.316, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_594 = _this;
  _this setPos [2341.7622, 14393.316, 6.1035156e-005];
};

_vehicle_595 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2350.7747, 14389.109, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_595 = _this;
  _this setPos [2350.7747, 14389.109, 0.00012207031];
};

_vehicle_596 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2330.7273, 14492.186, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_596 = _this;
  _this setPos [2330.7273, 14492.186, 0.00018310547];
};

_vehicle_597 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2321.8286, 14496.377], [], 0, "CAN_COLLIDE"];
  _vehicle_597 = _this;
  _this setPos [2321.8286, 14496.377];
};

_vehicle_598 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2325.9033, 14494.46, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_598 = _this;
  _this setPos [2325.9033, 14494.46, -0.00024414063];
};

_vehicle_599 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2335.3804, 14490.034, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_599 = _this;
  _this setPos [2335.3804, 14490.034, 6.1035156e-005];
};

_vehicle_600 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2317.645, 14487.583], [], 0, "CAN_COLLIDE"];
  _vehicle_600 = _this;
  _this setPos [2317.645, 14487.583];
};

_vehicle_601 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2319.6587, 14491.789, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_601 = _this;
  _this setPos [2319.6587, 14491.789, 0.00024414063];
};

_vehicle_602 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2313.3394, 14478.511, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_602 = _this;
  _this setPos [2313.3394, 14478.511, 6.1035156e-005];
};

_vehicle_603 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2309.0796, 14469.4, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_603 = _this;
  _this setPos [2309.0796, 14469.4, 6.1035156e-005];
};

_vehicle_604 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2304.7883, 14460.303, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_604 = _this;
  _this setPos [2304.7883, 14460.303, 6.1035156e-005];
};

_vehicle_605 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2300.613, 14451.402, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_605 = _this;
  _this setPos [2300.613, 14451.402, -6.1035156e-005];
};

_vehicle_606 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2296.2297, 14442.331, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_606 = _this;
  _this setPos [2296.2297, 14442.331, 0.00012207031];
};

_vehicle_607 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2291.9214, 14433.256, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_607 = _this;
  _this setPos [2291.9214, 14433.256, 0.00012207031];
};

_vehicle_608 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2287.6067, 14424.209, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_608 = _this;
  _this setPos [2287.6067, 14424.209, 0.00018310547];
};

_vehicle_609 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2281.9307, 14412.439, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_609 = _this;
  _this setPos [2281.9307, 14412.439, 6.1035156e-005];
};

_vehicle_610 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2273.2844, 14394.44], [], 0, "CAN_COLLIDE"];
  _vehicle_610 = _this;
  _this setPos [2273.2844, 14394.44];
};

_vehicle_613 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2270.2825, 14388.198, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_613 = _this;
  _this setPos [2270.2825, 14388.198, 6.1035156e-005];
};

_vehicle_614 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2279.302, 14383.904, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_614 = _this;
  _this setPos [2279.302, 14383.904, 0.00012207031];
};

_vehicle_615 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2288.3755, 14379.554, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_615 = _this;
  _this setPos [2288.3755, 14379.554, 0.00012207031];
};

_vehicle_616 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2297.4736, 14375.182, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_616 = _this;
  _this setPos [2297.4736, 14375.182, 6.1035156e-005];
};

_vehicle_617 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2306.3074, 14370.938], [], 0, "CAN_COLLIDE"];
  _vehicle_617 = _this;
  _this setPos [2306.3074, 14370.938];
};

_vehicle_618 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2315.3743, 14366.586, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_618 = _this;
  _this setPos [2315.3743, 14366.586, 6.1035156e-005];
};

_vehicle_619 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2318.9041, 14364.885, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_619 = _this;
  _this setPos [2318.9041, 14364.885, 6.1035156e-005];
};

_vehicle_620 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2310.8811, 14368.76, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_620 = _this;
  _this setPos [2310.8811, 14368.76, -0.00012207031];
};

_vehicle_621 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2301.9563, 14373.045, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_621 = _this;
  _this setPos [2301.9563, 14373.045, 6.1035156e-005];
};

_vehicle_622 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2293.1558, 14377.319, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_622 = _this;
  _this setPos [2293.1558, 14377.319, 6.1035156e-005];
};

_vehicle_623 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2284.136, 14381.579, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_623 = _this;
  _this setPos [2284.136, 14381.579, 0.00012207031];
};

_vehicle_624 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2274.8545, 14386.016, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_624 = _this;
  _this setPos [2274.8545, 14386.016, 0.00012207031];
};

_vehicle_625 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2276.3489, 14400.657, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_625 = _this;
  _this setPos [2276.3489, 14400.657, 0.00018310547];
};

_vehicle_626 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2315.4504, 14482.908, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_626 = _this;
  _this setPos [2315.4504, 14482.908, 0.00012207031];
};

_vehicle_627 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2311.2488, 14474.071, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_627 = _this;
  _this setPos [2311.2488, 14474.071, 0.00012207031];
};

_vehicle_628 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2306.9219, 14464.849, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_628 = _this;
  _this setPos [2306.9219, 14464.849, 0.00012207031];
};

_vehicle_629 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2302.6987, 14455.94], [], 0, "CAN_COLLIDE"];
  _vehicle_629 = _this;
  _this setPos [2302.6987, 14455.94];
};

_vehicle_630 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2298.5066, 14447.051, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_630 = _this;
  _this setPos [2298.5066, 14447.051, 0.00012207031];
};

_vehicle_631 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2294.1843, 14437.961, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_631 = _this;
  _this setPos [2294.1843, 14437.961, 0.00012207031];
};

_vehicle_632 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2289.9282, 14428.999, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_632 = _this;
  _this setPos [2289.9282, 14428.999, 0.00012207031];
};

_vehicle_633 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2285.1812, 14419.194, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_633 = _this;
  _this setPos [2285.1812, 14419.194, 6.1035156e-005];
};

_vehicle_634 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2278.9883, 14406.195, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_634 = _this;
  _this setPos [2278.9883, 14406.195, 6.1035156e-005];
};

_vehicle_635 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2314.1938, 14433.845, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_635 = _this;
  _this setPos [2314.1938, 14433.845, 6.1035156e-005];
};

_vehicle_636 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2309.918, 14424.723, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_636 = _this;
  _this setPos [2309.918, 14424.723, 0.00030517578];
};

_vehicle_637 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2305.5742, 14415.611, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_637 = _this;
  _this setPos [2305.5742, 14415.611, 0.00024414063];
};

_vehicle_638 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2248.0701, 14384.636], [], 0, "CAN_COLLIDE"];
  _vehicle_638 = _this;
  _this setPos [2248.0701, 14384.636];
};

_vehicle_639 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2322.6929, 14451.846, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_639 = _this;
  _this setPos [2322.6929, 14451.846, 0.00036621094];
};

_vehicle_640 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2324.9509, 14456.526, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_640 = _this;
  _this setPos [2324.9509, 14456.526, 0.00012207031];
};

_vehicle_641 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2327.1094, 14460.948], [], 0, "CAN_COLLIDE"];
  _vehicle_641 = _this;
  _this setPos [2327.1094, 14460.948];
};

_vehicle_642 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2320.6555, 14447.326, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_642 = _this;
  _this setPos [2320.6555, 14447.326, 0.00024414063];
};

_vehicle_643 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2316.3367, 14438.408, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_643 = _this;
  _this setPos [2316.3367, 14438.408, 0.00012207031];
};

_vehicle_644 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2312.1013, 14429.382, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_644 = _this;
  _this setPos [2312.1013, 14429.382, 0.00012207031];
};

_vehicle_645 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Yellowlight", [2308.0361, 14420.689, 0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_645 = _this;
  _this setPos [2308.0361, 14420.689, 0.00036621094];
};

_vehicle_646 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2315.0234, 14388.878], [], 0, "CAN_COLLIDE"];
  _vehicle_646 = _this;
  _this setPos [2315.0234, 14388.878];
};

_vehicle_647 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2306.7014, 14401.053, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_647 = _this;
  _this setPos [2306.7014, 14401.053, 0.00012207031];
};

_vehicle_648 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2305.3594, 14410.551, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_648 = _this;
  _this setPos [2305.3594, 14410.551, 6.1035156e-005];
};

_vehicle_649 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2305.9712, 14405.619, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_649 = _this;
  _this setPos [2305.9712, 14405.619, 6.1035156e-005];
};

_vehicle_650 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2309.313, 14396.38, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_650 = _this;
  _this setPos [2309.313, 14396.38, 0.00018310547];
};

_vehicle_651 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2311.6489, 14392.649, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_651 = _this;
  _this setPos [2311.6489, 14392.649, 6.1035156e-005];
};

_vehicle_652 = objNull;
if (true) then
{
  _this = createVehicle ["LAND_ASC_runway_Bluelight", [2320.6743, 14386.154, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_652 = _this;
  _this setPos [2320.6743, 14386.154, 0.00012207031];
};

_vehicle_661 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_R", [2172.5776, 14463.575, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_661 = _this;
  _this setDir 48.295013;
  _this setPos [2172.5776, 14463.575, -6.1035156e-005];
};

_vehicle_662 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_R", [2170.0759, 14465.536, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_662 = _this;
  _this setDir 32.357952;
  _this setPos [2170.0759, 14465.536, 0.00021362305];
};

_vehicle_663 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_R", [2166.7163, 14466.759, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_663 = _this;
  _this setDir 16.593849;
  _this setPos [2166.7163, 14466.759, 9.1552734e-005];
};

_vehicle_664 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_R", [2162.5107, 14467.004, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_664 = _this;
  _this setPos [2162.5107, 14467.004, 3.0517578e-005];
};

_vehicle_665 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_R", [2159.0972, 14466.923, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_665 = _this;
  _this setPos [2159.0972, 14466.923, 0.00012207031];
};

_vehicle_666 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_R", [2174.8945, 14460.492, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_666 = _this;
  _this setDir 60.817539;
  _this setPos [2174.8945, 14460.492, 6.1035156e-005];
};

_vehicle_667 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_R", [2176.5762, 14456.114, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_667 = _this;
  _this setDir 80.431984;
  _this setPos [2176.5762, 14456.114, 3.0517578e-005];
};

_vehicle_668 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_L", [2119.876, 14351.264, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_668 = _this;
  _this setDir -110.43669;
  _this setPos [2119.876, 14351.264, 3.0517578e-005];
};

_vehicle_670 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_L", [2121.1921, 14347.817, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_670 = _this;
  _this setDir -114.85053;
  _this setPos [2121.1921, 14347.817, 3.0517578e-005];
};

_vehicle_671 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_L", [2123.7507, 14343.071, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_671 = _this;
  _this setDir -124.09546;
  _this setPos [2123.7507, 14343.071, -3.0517578e-005];
};

_vehicle_672 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_L", [2126.4478, 14338.559, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_672 = _this;
  _this setDir -127.67683;
  _this setPos [2126.4478, 14338.559, 3.0517578e-005];
};

_vehicle_673 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_L", [2130.0974, 14334.186, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_673 = _this;
  _this setDir -138.06316;
  _this setPos [2130.0974, 14334.186, 9.1552734e-005];
};

_vehicle_674 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_L", [2133.8196, 14331.553, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_674 = _this;
  _this setDir -152.79567;
  _this setPos [2133.8196, 14331.553, -0.00012207031];
};

_vehicle_675 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_L", [2138.9141, 14329.838], [], 0, "CAN_COLLIDE"];
  _vehicle_675 = _this;
  _this setDir -173.91533;
  _this setPos [2138.9141, 14329.838];
};

_vehicle_676 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_L", [2144.584, 14330.288, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_676 = _this;
  _this setDir -198.41682;
  _this setPos [2144.584, 14330.288, -3.0517578e-005];
};

_vehicle_679 = objNull;
if (true) then
{
  _this = createVehicle ["Land_arrows_desk_R", [2176.6455, 14452.073, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_679 = _this;
  _this setDir -259.21298;
  _this setPos [2176.6455, 14452.073, 0.00021362305];
};

_vehicle_705 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [2275.2393, 14384.196, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_705 = _this;
  _this setDir 27.296522;
  _this setPos [2275.2393, 14384.196, 6.1035156e-005];
};

_vehicle_706 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5_Curved", [2299.1685, 14371.283, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_706 = _this;
  _this setDir -137.94336;
  _this setPos [2299.1685, 14371.283, 0.00018310547];
};

_vehicle_707 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [2280.3198, 14381.627, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_707 = _this;
  _this setDir 26.666761;
  _this setPos [2280.3198, 14381.627, 0.00018310547];
};

_vehicle_708 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [2290.4558, 14376.494, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_708 = _this;
  _this setDir 27.200409;
  _this setPos [2290.4558, 14376.494, 0.00012207031];
};

_vehicle_709 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5", [2285.4055, 14379.089], [], 0, "CAN_COLLIDE"];
  _vehicle_709 = _this;
  _this setDir 26.647507;
  _this setPos [2285.4055, 14379.089];
};

_vehicle_710 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier5_Curved", [2303.2351, 14367.245, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_710 = _this;
  _this setDir -125.75434;
  _this setPos [2303.2351, 14367.245, 6.1035156e-005];
};

_vehicle_711 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2381.1772, 14388.95], [], 0, "CAN_COLLIDE"];
  _vehicle_711 = _this;
  _this setDir 267.90125;
  _this setPos [2381.1772, 14388.95];
};

_vehicle_712 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2381.1589, 14389.024, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_712 = _this;
  _this setDir 88.100166;
  _this setPos [2381.1589, 14389.024, -0.00012207031];
};

_vehicle_713 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2390.0327, 14384.365], [], 0, "CAN_COLLIDE"];
  _vehicle_713 = _this;
  _this setDir 148.59572;
  _this setPos [2390.0327, 14384.365];
};

_vehicle_714 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2401.7649, 14361.025], [], 0, "CAN_COLLIDE"];
  _vehicle_714 = _this;
  _this setDir -31.360945;
  _this setPos [2401.7649, 14361.025];
};

_vehicle_715 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2409.5015, 14350.54], [], 0, "CAN_COLLIDE"];
  _vehicle_715 = _this;
  _this setDir -41.203224;
  _this setPos [2409.5015, 14350.54];
};

_vehicle_719 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2350.7, 14313.15, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_719 = _this;
  _this setDir -154.54332;
  _this setPos [2350.7, 14313.15, -0.00012207031];
};

_vehicle_720 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2342.0166, 14316.851, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_720 = _this;
  _this setDir 26.552116;
  _this setPos [2342.0166, 14316.851, 6.1035156e-005];
};

_vehicle_721 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2378.2588, 14396.813, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_721 = _this;
  _this setDir -59.754543;
  _this setPos [2378.2588, 14396.813, 6.1035156e-005];
};

_vehicle_722 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2377.8638, 14405.486, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_722 = _this;
  _this setDir -62.379719;
  _this setPos [2377.8638, 14405.486, 0.00030517578];
};

_vehicle_723 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2382.481, 14414.122, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_723 = _this;
  _this setDir -61.785324;
  _this setPos [2382.481, 14414.122, 0.00012207031];
};

_vehicle_724 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2387.1282, 14423.542, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_724 = _this;
  _this setDir -61.189487;
  _this setPos [2387.1282, 14423.542, 6.1035156e-005];
};

_vehicle_725 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2332.0803, 14316.731, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_725 = _this;
  _this setDir 26.057552;
  _this setPos [2332.0803, 14316.731, -0.00012207031];
};

_vehicle_732 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2380.7607, 14405.302, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_732 = _this;
  _this setPos [2380.7607, 14405.302, 0.00018310547];
};

_vehicle_733 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2385.8438, 14415.642], [], 0, "CAN_COLLIDE"];
  _vehicle_733 = _this;
  _this setPos [2385.8438, 14415.642];
};

_vehicle_734 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2392.8813, 14415.906, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_734 = _this;
  _this setPos [2392.8813, 14415.906, 0.00012207031];
};

_vehicle_735 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2271.3459, 14447.893], [], 0, "CAN_COLLIDE"];
  _vehicle_735 = _this;
  _this setPos [2271.3459, 14447.893];
};

_vehicle_736 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2302.3464, 14499.467, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_736 = _this;
  _this setPos [2302.3464, 14499.467, -0.00018310547];
};

_vehicle_737 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2352.2585, 14488.861, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_737 = _this;
  _this setPos [2352.2585, 14488.861, 6.1035156e-005];
};

_vehicle_738 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2326.417, 14514.858], [], 0, "CAN_COLLIDE"];
  _vehicle_738 = _this;
  _this setPos [2326.417, 14514.858];
};

_vehicle_739 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2274.73, 14457.047], [], 0, "CAN_COLLIDE"];
  _vehicle_739 = _this;
  _this setPos [2274.73, 14457.047];
};

_vehicle_740 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2262.2588, 14419.053, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_740 = _this;
  _this setPos [2262.2588, 14419.053, -6.1035156e-005];
};

_vehicle_741 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2329.3948, 14498.086, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_741 = _this;
  _this setPos [2329.3948, 14498.086, 0.00018310547];
};

_vehicle_742 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2338.1487, 14313.303, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_742 = _this;
  _this setPos [2338.1487, 14313.303, 0.00012207031];
};

_vehicle_743 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN1s", [2059.7532, 14416.569], [], 0, "CAN_COLLIDE"];
  _vehicle_743 = _this;
  _this setPos [2059.7532, 14416.569];
};

_vehicle_746 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_dira_civil", [2267.689, 14392.408], [], 0, "CAN_COLLIDE"];
  _vehicle_746 = _this;
  _this setDir -64.680145;
  _this setPos [2267.689, 14392.408];
};

_vehicle_750 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo1", [2299.344, 14357.255, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_750 = _this;
  _this setDir -64.635033;
  _this setPos [2299.344, 14357.255, 6.1035156e-005];
};

_vehicle_751 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo2", [2313.9124, 14352.073], [], 0, "CAN_COLLIDE"];
  _vehicle_751 = _this;
  _this setDir -155.56631;
  _this setPos [2313.9124, 14352.073];
};

_vehicle_752 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo3", [2309.1008, 14354.262], [], 0, "CAN_COLLIDE"];
  _vehicle_752 = _this;
  _this setDir -155.58176;
  _this setPos [2309.1008, 14354.262];
};

_vehicle_754 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo1_branka", [2315.9006, 14355.421], [], 0, "CAN_COLLIDE"];
  _vehicle_754 = _this;
  _this setDir -64.929947;
  _this setPos [2315.9006, 14355.421];
};

_vehicle_755 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo1", [2305.6697, 14370.226], [], 0, "CAN_COLLIDE"];
  _vehicle_755 = _this;
  _this setDir 118.35701;
  _this setPos [2305.6697, 14370.226];
};

_vehicle_759 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo1", [2309.1152, 14368.91, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_759 = _this;
  _this setDir -153.77187;
  _this setPos [2309.1152, 14368.91, -6.1035156e-005];
};

_vehicle_760 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo1", [2312.5938, 14367.292, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_760 = _this;
  _this setDir -154.47961;
  _this setPos [2312.5938, 14367.292, 6.1035156e-005];
};

_vehicle_761 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo1", [2316.0527, 14365.651, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_761 = _this;
  _this setDir -154.70586;
  _this setPos [2316.0527, 14365.651, 6.1035156e-005];
};

_vehicle_762 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo1", [2319.5, 14363.994, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_762 = _this;
  _this setDir -154.14592;
  _this setPos [2319.5, 14363.994, 6.1035156e-005];
};

_vehicle_763 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo1", [2318.2795, 14360.499, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_763 = _this;
  _this setDir -63.845497;
  _this setPos [2318.2795, 14360.499, 6.1035156e-005];
};

_vehicle_764 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo1", [2315.5244, 14355.132, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_764 = _this;
  _this setDir 114.70341;
  _this setPos [2315.5244, 14355.132, 0.00012207031];
};

_vehicle_765 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_plot_zed_drevo1", [2302.6299, 14356.767], [], 0, "CAN_COLLIDE"];
  _vehicle_765 = _this;
  _this setDir -174.38127;
  _this setPos [2302.6299, 14356.767];
};

_vehicle_766 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2270.416, 14397.833], [], 0, "CAN_COLLIDE"];
  _vehicle_766 = _this;
  _this setDir -64.128593;
  _this setPos [2270.416, 14397.833];
};

_vehicle_767 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2273.0906, 14403.302], [], 0, "CAN_COLLIDE"];
  _vehicle_767 = _this;
  _this setDir -64.050591;
  _this setPos [2273.0906, 14403.302];
};

_vehicle_768 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2275.731, 14408.781, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_768 = _this;
  _this setDir -64.40696;
  _this setPos [2275.731, 14408.781, -6.1035156e-005];
};

_vehicle_769 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2278.4026, 14414.322, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_769 = _this;
  _this setDir -64.262199;
  _this setPos [2278.4026, 14414.322, 6.1035156e-005];
};

_vehicle_770 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2281.0811, 14419.83, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_770 = _this;
  _this setDir -64.466988;
  _this setPos [2281.0811, 14419.83, 6.1035156e-005];
};

_vehicle_771 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2283.7476, 14425.299, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_771 = _this;
  _this setDir -64.074501;
  _this setPos [2283.7476, 14425.299, 6.1035156e-005];
};

_vehicle_772 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [2286.3857, 14430.805, -0.04536707], [], 0, "CAN_COLLIDE"];
  _vehicle_772 = _this;
  _this setDir -64.716003;
  _this setPos [2286.3857, 14430.805, -0.04536707];
};

_vehicle_773 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2288.9788, 14436.228, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_773 = _this;
  _this setDir -64.696228;
  _this setPos [2288.9788, 14436.228, 0.00012207031];
};

_vehicle_774 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2291.606, 14441.73, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_774 = _this;
  _this setDir -64.588951;
  _this setPos [2291.606, 14441.73, 6.1035156e-005];
};

_vehicle_775 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2294.2637, 14447.247, -0.032090992], [], 0, "CAN_COLLIDE"];
  _vehicle_775 = _this;
  _this setDir -64.283607;
  _this setPos [2294.2637, 14447.247, -0.032090992];
};

_vehicle_776 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2296.9063, 14452.719, -0.067015536], [], 0, "CAN_COLLIDE"];
  _vehicle_776 = _this;
  _this setDir -64.751968;
  _this setPos [2296.9063, 14452.719, -0.067015536];
};

_vehicle_777 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2299.5144, 14458.23, -0.089757115], [], 0, "CAN_COLLIDE"];
  _vehicle_777 = _this;
  _this setDir -64.699707;
  _this setPos [2299.5144, 14458.23, -0.089757115];
};

_vehicle_778 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2302.1223, 14463.734, -0.14934757], [], 0, "CAN_COLLIDE"];
  _vehicle_778 = _this;
  _this setDir -64.591728;
  _this setPos [2302.1223, 14463.734, -0.14934757];
};

_vehicle_779 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_civil", [2304.7239, 14469.219, -0.21815644], [], 0, "CAN_COLLIDE"];
  _vehicle_779 = _this;
  _this setDir -64.803825;
  _this setPos [2304.7239, 14469.219, -0.21815644];
};

_vehicle_780 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_zed_podplaz_civil", [2307.3289, 14474.686, -0.070232585], [], 0, "CAN_COLLIDE"];
  _vehicle_780 = _this;
  _this setDir -64.380562;
  _this setPos [2307.3289, 14474.686, -0.070232585];
};

_vehicle_800 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2315.5981, 14300.244], [], 0, "CAN_COLLIDE"];
  _vehicle_800 = _this;
  _this setDir 25.115301;
  _this setPos [2315.5981, 14300.244];
};

_vehicle_801 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2315.6538, 14300.243, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_801 = _this;
  _this setDir -154.6225;
  _this setPos [2315.6538, 14300.243, -0.00012207031];
};

_vehicle_802 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [2307.3333, 14290.37], [], 0, "CAN_COLLIDE"];
  _vehicle_802 = _this;
  _this setDir -125.67838;
  _this setPos [2307.3333, 14290.37];
};

_vehicle_803 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2284.8232, 14270.321, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_803 = _this;
  _this setDir 24.845482;
  _this setPos [2284.8232, 14270.321, -0.00024414063];
};

_vehicle_804 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2250.3074, 14214.885, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_804 = _this;
  _this setDir 31.436796;
  _this setPos [2250.3074, 14214.885, -0.00024414063];
};

_vehicle_805 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2259.458, 14229.722, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_805 = _this;
  _this setDir 32.682861;
  _this setPos [2259.458, 14229.722, -6.1035156e-005];
};

_vehicle_806 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2268.8252, 14244.398], [], 0, "CAN_COLLIDE"];
  _vehicle_806 = _this;
  _this setDir 33.559494;
  _this setPos [2268.8252, 14244.398];
};

_vehicle_807 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2284.9097, 14270.306, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_807 = _this;
  _this setDir -155.45677;
  _this setPos [2284.9097, 14270.306, -6.1035156e-005];
};

_vehicle_808 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2250.3567, 14214.885, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_808 = _this;
  _this setDir -148.89389;
  _this setPos [2250.3567, 14214.885, -6.1035156e-005];
};

_vehicle_809 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2231.7141, 14196.935, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_809 = _this;
  _this setDir 30.997953;
  _this setPos [2231.7141, 14196.935, -0.00012207031];
};

_vehicle_810 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2169.2249, 14094.014, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_810 = _this;
  _this setDir -328.59384;
  _this setPos [2169.2249, 14094.014, -0.00012207031];
};

_vehicle_811 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2178.2466, 14108.751, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_811 = _this;
  _this setDir -328.59384;
  _this setPos [2178.2466, 14108.751, -0.00024414063];
};

_vehicle_812 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2187.1665, 14123.434], [], 0, "CAN_COLLIDE"];
  _vehicle_812 = _this;
  _this setDir -328.59384;
  _this setPos [2187.1665, 14123.434];
};

_vehicle_813 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2196.1377, 14138.122, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_813 = _this;
  _this setDir -328.59384;
  _this setPos [2196.1377, 14138.122, -0.00012207031];
};

_vehicle_814 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2204.9595, 14152.722, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_814 = _this;
  _this setDir -328.59384;
  _this setPos [2204.9595, 14152.722, -6.1035156e-005];
};

_vehicle_815 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2213.77, 14167.563, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_815 = _this;
  _this setDir -328.59384;
  _this setPos [2213.77, 14167.563, -6.1035156e-005];
};

_vehicle_816 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2222.6567, 14182.044, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_816 = _this;
  _this setDir -328.59384;
  _this setPos [2222.6567, 14182.044, -0.00012207031];
};

_vehicle_817 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2165.7747, 14081.661, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_817 = _this;
  _this setDir 0.85163587;
  _this setPos [2165.7747, 14081.661, -6.1035156e-005];
};

_vehicle_818 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2165.8665, 14081.755, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_818 = _this;
  _this setDir -179.269;
  _this setPos [2165.8665, 14081.755, -6.1035156e-005];
};

_vehicle_819 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2162.436, 14069.34, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_819 = _this;
  _this setDir -149.3838;
  _this setPos [2162.436, 14069.34, -6.1035156e-005];
};

_vehicle_820 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2153.2554, 14060.24, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_820 = _this;
  _this setDir -119.15745;
  _this setPos [2153.2554, 14060.24, 0.00030517578];
};

_vehicle_821 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2116.8398, 14045.896, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_821 = _this;
  _this setDir 61.240204;
  _this setPos [2116.8398, 14045.896, -6.1035156e-005];
};

_vehicle_822 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2101.6277, 14037.429, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_822 = _this;
  _this setDir 60.829945;
  _this setPos [2101.6277, 14037.429, 3.0517578e-005];
};

_vehicle_823 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2086.5117, 14028.764, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_823 = _this;
  _this setDir 60.054405;
  _this setPos [2086.5117, 14028.764, -9.1552734e-005];
};

_vehicle_824 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2071.5317, 14019.95], [], 0, "CAN_COLLIDE"];
  _vehicle_824 = _this;
  _this setDir 59.500763;
  _this setPos [2071.5317, 14019.95];
};

_vehicle_825 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2056.1611, 14007.881, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_825 = _this;
  _this setDir 44.562965;
  _this setPos [2056.1611, 14007.881, 3.0517578e-005];
};

_vehicle_826 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2056.2358, 14007.851, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_826 = _this;
  _this setDir -135.31465;
  _this setPos [2056.2358, 14007.851, 3.0517578e-005];
};

_vehicle_827 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2040.7813, 13995.876, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_827 = _this;
  _this setDir -120.61475;
  _this setPos [2040.7813, 13995.876, 0.00012207031];
};

_vehicle_828 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2026.0175, 13987.118, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_828 = _this;
  _this setDir -120.61475;
  _this setPos [2026.0175, 13987.118, 3.0517578e-005];
};

_vehicle_829 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2011.2057, 13978.407], [], 0, "CAN_COLLIDE"];
  _vehicle_829 = _this;
  _this setDir -120.61475;
  _this setPos [2011.2057, 13978.407];
};

_vehicle_830 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1996.1851, 13969.548, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_830 = _this;
  _this setDir -119.80544;
  _this setPos [1996.1851, 13969.548, 3.0517578e-005];
};

_vehicle_831 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1981.2708, 13961.087, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_831 = _this;
  _this setDir -119.80544;
  _this setPos [1981.2708, 13961.087, -6.1035156e-005];
};

_vehicle_832 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1966.3358, 13952.516], [], 0, "CAN_COLLIDE"];
  _vehicle_832 = _this;
  _this setDir -119.80544;
  _this setPos [1966.3358, 13952.516];
};

_vehicle_833 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1951.4957, 13943.979, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_833 = _this;
  _this setDir -119.80544;
  _this setPos [1951.4957, 13943.979, 0.00015258789];
};

_vehicle_834 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1936.6367, 13935.433, 0.00045776367], [], 0, "CAN_COLLIDE"];
  _vehicle_834 = _this;
  _this setDir -119.80544;
  _this setPos [1936.6367, 13935.433, 0.00045776367];
};

_vehicle_835 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [1921.6915, 13926.932, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_835 = _this;
  _this setDir -119.80544;
  _this setPos [1921.6915, 13926.932, 0.00012207031];
};

_vehicle_836 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [1901.1057, 13915.289, -0.00033569336], [], 0, "CAN_COLLIDE"];
  _vehicle_836 = _this;
  _this setDir 60.727715;
  _this setPos [1901.1057, 13915.289, -0.00033569336];
};

_vehicle_837 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2512.957, 14242.493, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_837 = _this;
  _this setDir -72.534912;
  _this setPos [2512.957, 14242.493, -0.00018310547];
};

_vehicle_838 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2525.9087, 14242.016, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_838 = _this;
  _this setDir -102.64327;
  _this setPos [2525.9087, 14242.016, -0.00012207031];
};

_vehicle_839 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2525.8635, 14242.155, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_839 = _this;
  _this setDir 77.554497;
  _this setPos [2525.8635, 14242.155, 0.00018310547];
};

_vehicle_840 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_15_75", [2580.2019, 14242.565, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_840 = _this;
  _this setDir 92.066444;
  _this setPos [2580.2019, 14242.565, -0.00012207031];
};

_vehicle_841 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2562.7842, 14243.068, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_841 = _this;
  _this setDir -87.593979;
  _this setPos [2562.7842, 14243.068, 0.00012207031];
};

_vehicle_842 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2580.2122, 14242.497, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_842 = _this;
  _this setDir -88.199364;
  _this setPos [2580.2122, 14242.497, 6.1035156e-005];
};

_vehicle_843 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2616.1648, 14234.227, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_843 = _this;
  _this setDir -73.139885;
  _this setPos [2616.1648, 14234.227, -0.00012207031];
};

_vehicle_844 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2632.856, 14229.134], [], 0, "CAN_COLLIDE"];
  _vehicle_844 = _this;
  _this setDir -73.296463;
  _this setPos [2632.856, 14229.134];
};

_vehicle_845 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2632.8193, 14229.182], [], 0, "CAN_COLLIDE"];
  _vehicle_845 = _this;
  _this setDir 106.94796;
  _this setPos [2632.8193, 14229.182];
};

_vehicle_846 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_30_25", [2649.4946, 14224.074, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_846 = _this;
  _this setDir 107.37334;
  _this setPos [2649.4946, 14224.074, 0.00018310547];
};

_vehicle_847 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2660.4089, 14217.16], [], 0, "CAN_COLLIDE"];
  _vehicle_847 = _this;
  _this setDir 137.24454;
  _this setPos [2660.4089, 14217.16];
};

_vehicle_848 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2657.8672, 14190.646], [], 0, "CAN_COLLIDE"];
  _vehicle_848 = _this;
  _this setDir 15.766823;
  _this setPos [2657.8672, 14190.646];
};

_vehicle_849 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2653.5337, 14173.783, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_849 = _this;
  _this setDir 14.40517;
  _this setPos [2653.5337, 14173.783, -0.00012207031];
};

_vehicle_850 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [2651.9534, 14167.771], [], 0, "CAN_COLLIDE"];
  _vehicle_850 = _this;
  _this setDir 14.41361;
  _this setPos [2651.9534, 14167.771];
};

_vehicle_852 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [2261.0957, 14330.772], [], 0, "CAN_COLLIDE"];
  _vehicle_852 = _this;
  _this setDir 14.981194;
  _this setPos [2261.0957, 14330.772];
};

_vehicle_853 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2357.6951, 14384.708, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_853 = _this;
  _this setPos [2357.6951, 14384.708, 0];
};

_vehicle_854 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2270.8767, 14380.735, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_854 = _this;
  _this setPos [2270.8767, 14380.735, 6.1035156e-005];
};

_vehicle_855 = objNull;
if (true) then
{
  _this = createVehicle ["Notebook", [2305.6919, 14366.449, 1.3866345], [], 0, "CAN_COLLIDE"];
  _vehicle_855 = _this;
  _this setDir -64.916512;
  _this setPos [2305.6919, 14366.449, 1.3866345];
};

_vehicle_856 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Videoprojektor", [2284.2314, 14349.096, 13.656371], [], 0, "CAN_COLLIDE"];
  _vehicle_856 = _this;
  _this setDir -201.59926;
  _this setPos [2284.2314, 14349.096, 13.656371];
};

_vehicle_857 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Videoprojektor", [2325.0603, 14408.193, 8.7646112], [], 0, "CAN_COLLIDE"];
  _vehicle_857 = _this;
  _this setDir 163.88226;
  _this setPos [2325.0603, 14408.193, 8.7646112];
};

_vehicle_858 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Videoprojektor", [2373.8625, 14394.317, 2.8272424], [], 0, "CAN_COLLIDE"];
  _vehicle_858 = _this;
  _this setDir -61.412926;
  _this setPos [2373.8625, 14394.317, 2.8272424];
};

_vehicle_859 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Videoprojektor", [2324.0308, 14366.709, 9.6026669], [], 0, "CAN_COLLIDE"];
  _vehicle_859 = _this;
  _this setDir 77.507553;
  _this setPos [2324.0308, 14366.709, 9.6026669];
};

_vehicle_860 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Videoprojektor", [2331.1936, 14320.03, 2.8762426], [], 0, "CAN_COLLIDE"];
  _vehicle_860 = _this;
  _this setDir 24.690472;
  _this setPos [2331.1936, 14320.03, 2.8762426];
};

_vehicle_861 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Videoprojektor", [2240.9158, 14412.118, 2.697561], [], 0, "CAN_COLLIDE"];
  _vehicle_861 = _this;
  _this setDir 106.83801;
  _this setPos [2240.9158, 14412.118, 2.697561];
};

_vehicle_862 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2326.5283, 14322.398, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_862 = _this;
  _this setPos [2326.5283, 14322.398, 0];
};

_vehicle_863 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2321.0808, 14311.994, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_863 = _this;
  _this setPos [2321.0808, 14311.994, 0];
};

_vehicle_864 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2314.2202, 14299.508, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_864 = _this;
  _this setPos [2314.2202, 14299.508, -6.1035156e-005];
};

_vehicle_865 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2307.0803, 14291.214, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_865 = _this;
  _this setPos [2307.0803, 14291.214, -0.00018310547];
};

_vehicle_866 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2295.8279, 14283.445, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_866 = _this;
  _this setPos [2295.8279, 14283.445, -6.1035156e-005];
};

_vehicle_867 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2287.4275, 14276.364, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_867 = _this;
  _this setPos [2287.4275, 14276.364, -0.00012207031];
};

_vehicle_868 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2332.792, 14337.508, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_868 = _this;
  _this setPos [2332.792, 14337.508, -0.00012207031];
};

_vehicle_869 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2311.9683, 14345.021, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_869 = _this;
  _this setPos [2311.9683, 14345.021, 0];
};

_vehicle_870 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2321.8601, 14341.751, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_870 = _this;
  _this setPos [2321.8601, 14341.751, 0];
};

_vehicle_871 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2294.1909, 14354.517, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_871 = _this;
  _this setPos [2294.1909, 14354.517, 0];
};

_vehicle_872 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2303.7637, 14352.434, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_872 = _this;
  _this setPos [2303.7637, 14352.434, 0];
};

_vehicle_873 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2264.4106, 14408.64, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_873 = _this;
  _this setPos [2264.4106, 14408.64, 6.1035156e-005];
};

_vehicle_874 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2261.7393, 14395.263, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_874 = _this;
  _this setPos [2261.7393, 14395.263, 6.1035156e-005];
};

_vehicle_875 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2243.7363, 14415.642, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_875 = _this;
  _this setPos [2243.7363, 14415.642, 6.1035156e-005];
};

_vehicle_876 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2254.7915, 14411.919, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_876 = _this;
  _this setPos [2254.7915, 14411.919, -6.1035156e-005];
};

_vehicle_877 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2258.7141, 14410.676, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_877 = _this;
  _this setPos [2258.7141, 14410.676, 0.00012207031];
};

_vehicle_878 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2218.4175, 14425.19, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_878 = _this;
  _this setPos [2218.4175, 14425.19, 6.1035156e-005];
};

_vehicle_879 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2229.1279, 14421.088, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_879 = _this;
  _this setPos [2229.1279, 14421.088, 0];
};

_vehicle_880 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2182.7119, 14422.73, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_880 = _this;
  _this setPos [2182.7119, 14422.73, 0.00012207031];
};

_vehicle_881 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2141.686, 14332.979, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_881 = _this;
  _this setPos [2141.686, 14332.979, -3.0517578e-005];
};

_vehicle_882 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2150.4766, 14341.434, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_882 = _this;
  _this setPos [2150.4766, 14341.434, -3.0517578e-005];
};

_vehicle_883 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2155.4119, 14353.963, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_883 = _this;
  _this setPos [2155.4119, 14353.963, -0.00021362305];
};

_vehicle_884 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2160.2515, 14367.209, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_884 = _this;
  _this setPos [2160.2515, 14367.209, 3.0517578e-005];
};

_vehicle_885 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2165.3647, 14379.998, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_885 = _this;
  _this setPos [2165.3647, 14379.998, 6.1035156e-005];
};

_vehicle_886 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2168.8147, 14392.099, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_886 = _this;
  _this setPos [2168.8147, 14392.099, 9.1552734e-005];
};

_vehicle_887 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2175.1902, 14405.791, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_887 = _this;
  _this setPos [2175.1902, 14405.791, -6.1035156e-005];
};

_vehicle_888 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2177.6421, 14414.82, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_888 = _this;
  _this setPos [2177.6421, 14414.82, 0.00018310547];
};

_vehicle_889 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2269.7646, 14421.529, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_889 = _this;
  _this setPos [2269.7646, 14421.529, 0];
};

_vehicle_890 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2274.8884, 14435.949, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_890 = _this;
  _this setPos [2274.8884, 14435.949, 6.1035156e-005];
};

_vehicle_891 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2280.0793, 14450.264, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_891 = _this;
  _this setPos [2280.0793, 14450.264, 6.1035156e-005];
};

_vehicle_892 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2287.3225, 14462.072, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_892 = _this;
  _this setPos [2287.3225, 14462.072, 0.00018310547];
};

_vehicle_893 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2296.0659, 14474.106, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_893 = _this;
  _this setPos [2296.0659, 14474.106, 0.00012207031];
};

_vehicle_894 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2304.1755, 14486.42, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_894 = _this;
  _this setPos [2304.1755, 14486.42, 0.00024414063];
};

_vehicle_895 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2311.9312, 14498.969, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_895 = _this;
  _this setPos [2311.9312, 14498.969, 0.00012207031];
};

_vehicle_896 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2320.2832, 14504.916, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_896 = _this;
  _this setPos [2320.2832, 14504.916, 6.1035156e-005];
};

_vehicle_897 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2334.3337, 14506.254, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_897 = _this;
  _this setPos [2334.3337, 14506.254, -0.00012207031];
};

_vehicle_898 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2349.5493, 14498.578, 0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_898 = _this;
  _this setPos [2349.5493, 14498.578, 0.00030517578];
};

_vehicle_899 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2343.3616, 14502.709, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_899 = _this;
  _this setPos [2343.3616, 14502.709, 0];
};

_vehicle_900 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2360.0293, 14489.081, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_900 = _this;
  _this setPos [2360.0293, 14489.081, -0.00018310547];
};

_vehicle_901 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2368.3489, 14478.81, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_901 = _this;
  _this setPos [2368.3489, 14478.81, 0.00018310547];
};

_vehicle_902 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2375.3889, 14467.177, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_902 = _this;
  _this setPos [2375.3889, 14467.177, 0.00018310547];
};

_vehicle_903 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2382.1099, 14454.366, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_903 = _this;
  _this setPos [2382.1099, 14454.366, 6.1035156e-005];
};

_vehicle_904 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2381.1003, 14440.242, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_904 = _this;
  _this setPos [2381.1003, 14440.242, 0.00012207031];
};

_vehicle_905 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2383.1899, 14448.245, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_905 = _this;
  _this setPos [2383.1899, 14448.245, 6.1035156e-005];
};

_vehicle_906 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2378.5657, 14427.636, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_906 = _this;
  _this setPos [2378.5657, 14427.636, 0];
};

_vehicle_907 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2372.7051, 14413.22, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_907 = _this;
  _this setPos [2372.7051, 14413.22, 6.1035156e-005];
};

_vehicle_908 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2364.0833, 14395.161, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_908 = _this;
  _this setPos [2364.0833, 14395.161, 0];
};

_vehicle_909 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2352.1313, 14374.976, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_909 = _this;
  _this setPos [2352.1313, 14374.976, 0];
};

_vehicle_910 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2358.457, 14384.04, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_910 = _this;
  _this setPos [2358.457, 14384.04, -6.1035156e-005];
};

_vehicle_911 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2346.6865, 14362.393, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_911 = _this;
  _this setPos [2346.6865, 14362.393, 0];
};

_vehicle_912 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2349.7212, 14368.511, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_912 = _this;
  _this setPos [2349.7212, 14368.511, 0];
};

_vehicle_913 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2371.4548, 14390.24, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_913 = _this;
  _this setPos [2371.4548, 14390.24, 6.1035156e-005];
};

_vehicle_914 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2383.2568, 14388.556, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_914 = _this;
  _this setPos [2383.2568, 14388.556, -0.00012207031];
};

_vehicle_915 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2392.2849, 14378.947, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_915 = _this;
  _this setPos [2392.2849, 14378.947, 0];
};

_vehicle_916 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2398.0874, 14366.545, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_916 = _this;
  _this setPos [2398.0874, 14366.545, 0];
};

_vehicle_917 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2404.3167, 14356.442, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_917 = _this;
  _this setPos [2404.3167, 14356.442, -6.1035156e-005];
};

_vehicle_918 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2414.165, 14345.546, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_918 = _this;
  _this setPos [2414.165, 14345.546, 0.00012207031];
};

_vehicle_919 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2421.7268, 14337.178, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_919 = _this;
  _this setPos [2421.7268, 14337.178, 0];
};

_vehicle_920 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2430.408, 14327.584, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_920 = _this;
  _this setPos [2430.408, 14327.584, 6.1035156e-005];
};

_vehicle_921 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2438.9258, 14317.388, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_921 = _this;
  _this setPos [2438.9258, 14317.388, 0.00018310547];
};

_vehicle_922 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2260.8013, 14330.558, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_922 = _this;
  _this setPos [2260.8013, 14330.558, -0.00012207031];
};

_vehicle_923 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2265.1399, 14342.69, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_923 = _this;
  _this setPos [2265.1399, 14342.69, 0];
};

_vehicle_924 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2268.3271, 14351.346, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_924 = _this;
  _this setPos [2268.3271, 14351.346, 0];
};

};
