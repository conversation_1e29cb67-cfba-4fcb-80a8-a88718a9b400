//Made by Thug at TBsGaming.com

if (isServer) then {


_vehicle_721 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13624.747, 2732.2034, 1.2201103], [], 0, "CAN_COLLIDE"];
  _vehicle_721 = _this;
  _this setDir 17.249689;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13624.747, 2732.2034, 1.2201103];
};

_vehicle_723 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13587.453, 2744.8865, 1.4278597], [], 0, "CAN_COLLIDE"];
  _vehicle_723 = _this;
  _this setDir 20.211437;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13587.453, 2744.8865, 1.4278597];
};

_vehicle_725 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13549.349, 2756.5547, 1.2808826], [], 0, "CAN_COLLIDE"];
  _vehicle_725 = _this;
  _this setDir 13.502291;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13549.349, 2756.5547, 1.2808826];
};

_vehicle_727 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13663.347, 2723.6782, -0.18878511], [], 0, "CAN_COLLIDE"];
  _vehicle_727 = _this;
  _this setDir 7.7072902;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13663.347, 2723.6782, -0.18878511];
};

_vehicle_729 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13510.422, 2762.7173, 0.93599504], [], 0, "CAN_COLLIDE"];
  _vehicle_729 = _this;
  _this setDir 4.6712298;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13510.422, 2762.7173, 0.93599504];
};

_vehicle_731 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13473.912, 2753.6313, -0.41019356], [], 0, "CAN_COLLIDE"];
  _vehicle_731 = _this;
  _this setDir -32.494408;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13473.912, 2753.6313, -0.41019356];
};

_vehicle_733 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13439.084, 2736.6614, -0.25048831], [], 0, "CAN_COLLIDE"];
  _vehicle_733 = _this;
  _this setDir -19.523962;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13439.084, 2736.6614, -0.25048831];
};

_vehicle_735 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13404.826, 2718.5591, 1.1487277], [], 0, "CAN_COLLIDE"];
  _vehicle_735 = _this;
  _this setDir -36.167633;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13404.826, 2718.5591, 1.1487277];
};

_vehicle_737 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13370.955, 2701.1909, -0.81535679], [], 0, "CAN_COLLIDE"];
  _vehicle_737 = _this;
  _this setDir -18.852884;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13370.955, 2701.1909, -0.81535679];
};

_vehicle_741 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13297.69, 2680.5398, 1.6138431], [], 0, "CAN_COLLIDE"];
  _vehicle_741 = _this;
  _this setDir -7.4594355;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13297.69, 2680.5398, 1.6138431];
};

_vehicle_743 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13233.213, 2732.3408, 1.6268516], [], 0, "CAN_COLLIDE"];
  _vehicle_743 = _this;
  _this setDir 130.29881;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13233.213, 2732.3408, 1.6268516];
};

_vehicle_745 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13259.758, 2759.8076, 1.6934158], [], 0, "CAN_COLLIDE"];
  _vehicle_745 = _this;
  _this setDir -42.561905;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13259.758, 2759.8076, 1.6934158];
};

_vehicle_747 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13287.945, 2785.7976, 1.840688], [], 0, "CAN_COLLIDE"];
  _vehicle_747 = _this;
  _this setDir -42.567177;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13287.945, 2785.7976, 1.840688];
};

_vehicle_749 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13319.947, 2808.2729, -0.082111023], [], 0, "CAN_COLLIDE"];
  _vehicle_749 = _this;
  _this setDir -29.126482;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13319.947, 2808.2729, -0.082111023];
};

_vehicle_752 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13356.179, 2821.0237, 0.26909947], [], 0, "CAN_COLLIDE"];
  _vehicle_752 = _this;
  _this setDir -9.2050648;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13356.179, 2821.0237, 0.26909947];
};

_vehicle_754 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13386.459, 2825.9133, 0.68515509], [], 0, "CAN_COLLIDE"];
  _vehicle_754 = _this;
  _this setDir -9.0356321;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13386.459, 2825.9133, 0.68515509];
};

_vehicle_756 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13425.937, 2840.5691, 1.48048], [], 0, "CAN_COLLIDE"];
  _vehicle_756 = _this;
  _this setDir -32.672329;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13425.937, 2840.5691, 1.48048];
};

_vehicle_758 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13445.512, 2870.2292, 1.2639375], [], 0, "CAN_COLLIDE"];
  _vehicle_758 = _this;
  _this setDir -80.798882;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13445.512, 2870.2292, 1.2639375];
};

_vehicle_760 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13444.103, 2909.0471, 1.0745256], [], 0, "CAN_COLLIDE"];
  _vehicle_760 = _this;
  _this setDir -103.65543;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13444.103, 2909.0471, 1.0745256];
};

_vehicle_762 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13437.607, 2947.2842, 0.98719478], [], 0, "CAN_COLLIDE"];
  _vehicle_762 = _this;
  _this setDir -95.914024;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13437.607, 2947.2842, 0.98719478];
};

_vehicle_764 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13436.92, 2986.624, 2.1028574], [], 0, "CAN_COLLIDE"];
  _vehicle_764 = _this;
  _this setDir -84.843651;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13436.92, 2986.624, 2.1028574];
};

_vehicle_766 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13429.3, 3023.7522, 2.1084473], [], 0, "CAN_COLLIDE"];
  _vehicle_766 = _this;
  _this setDir -118.10445;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13429.3, 3023.7522, 2.1084473];
};


_vehicle_768 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13400.919, 3039.6638, 2.0135291], [], 0, "CAN_COLLIDE"];
  _vehicle_768 = _this;
  _this setDir -1.9510342;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13400.919, 3039.6638, 2.0135291];
};

_vehicle_770 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13361.642, 3036.6689, 1.6091206], [], 0, "CAN_COLLIDE"];
  _vehicle_770 = _this;
  _this setDir -9.5427246;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13361.642, 3036.6689, 1.6091206];
};

_vehicle_773 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13322.637, 3035.356, 0.12513775], [], 0, "CAN_COLLIDE"];
  _vehicle_773 = _this;
  _this setDir 6.8350658;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13322.637, 3035.356, 0.12513775];
};

_vehicle_775 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13285.306, 3045.0024, 0.13527344], [], 0, "CAN_COLLIDE"];
  _vehicle_775 = _this;
  _this setDir 20.344227;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13285.306, 3045.0024, 0.13527344];
};

_vehicle_777 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13248.889, 3060.5486, -0.7567423], [], 0, "CAN_COLLIDE"];
  _vehicle_777 = _this;
  _this setDir 26.236694;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13248.889, 3060.5486, -0.7567423];
};

_vehicle_779 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13212.365, 3074.7983, 1.771497], [], 0, "CAN_COLLIDE"];
  _vehicle_779 = _this;
  _this setDir 15.295551;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13212.365, 3074.7983, 1.771497];
};

_vehicle_781 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13173.153, 3081.7996, -0.52422917], [], 0, "CAN_COLLIDE"];
  _vehicle_781 = _this;
  _this setDir 5.3671918;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13173.153, 3081.7996, -0.52422917];
};

_vehicle_783 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13133.48, 3086.2656, 2.0747507], [], 0, "CAN_COLLIDE"];
  _vehicle_783 = _this;
  _this setDir 11.887084;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13133.48, 3086.2656, 2.0747507];
};

_vehicle_785 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13094.649, 3095.1843, 1.9237376], [], 0, "CAN_COLLIDE"];
  _vehicle_785 = _this;
  _this setDir 14.048556;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13094.649, 3095.1843, 1.9237376];
};

_vehicle_787 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13059.074, 3110.2988, 2.1853101], [], 0, "CAN_COLLIDE"];
  _vehicle_787 = _this;
  _this setDir 32.002495;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13059.074, 3110.2988, 2.1853101];
};

_vehicle_789 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13025.406, 3130.9795, 2.1242836], [], 0, "CAN_COLLIDE"];
  _vehicle_789 = _this;
  _this setDir 31.182495;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13025.406, 3130.9795, 2.1242836];
};

_vehicle_792 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [12991.483, 3151.458, 2.2254806], [], 0, "CAN_COLLIDE"];
  _vehicle_792 = _this;
  _this setDir 30.829941;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [12991.483, 3151.458, 2.2254806];
};

_vehicle_794 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [12965.304, 3178.2422, 1.5963595], [], 0, "CAN_COLLIDE"];
  _vehicle_794 = _this;
  _this setDir 58.719784;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [12965.304, 3178.2422, 1.5963595];
};

_vehicle_796 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [12959.826, 3212.8975, 1.695129], [], 0, "CAN_COLLIDE"];
  _vehicle_796 = _this;
  _this setDir -75.020248;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [12959.826, 3212.8975, 1.695129];
};

_vehicle_798 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [12977.206, 3247.3262, 2.0811174], [], 0, "CAN_COLLIDE"];
  _vehicle_798 = _this;
  _this setDir -51.711212;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [12977.206, 3247.3262, 2.0811174];
};

_vehicle_800 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [12993.32, 3268.2273, 2.1182415], [], 0, "CAN_COLLIDE"];
  _vehicle_800 = _this;
  _this setDir -52.367332;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [12993.32, 3268.2273, 2.1182415];
};

_vehicle_802 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13056.729, 3308.6506, 1.0605243], [], 0, "CAN_COLLIDE"];
  _vehicle_802 = _this;
  _this setDir -30.080925;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13056.729, 3308.6506, 1.0605243];
};

_vehicle_804 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13091.089, 3326.5674, 1.8316609], [], 0, "CAN_COLLIDE"];
  _vehicle_804 = _this;
  _this setDir -23.227993;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13091.089, 3326.5674, 1.8316609];
};

_vehicle_806 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13127.594, 3341.0601, 1.9567029], [], 0, "CAN_COLLIDE"];
  _vehicle_806 = _this;
  _this setDir -20.05176;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13127.594, 3341.0601, 1.9567029];
};

_vehicle_808 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13164.368, 3352.3955, 1.843191], [], 0, "CAN_COLLIDE"];
  _vehicle_808 = _this;
  _this setDir -16.149904;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13164.368, 3352.3955, 1.843191];
};

_vehicle_810 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13202.512, 3362.2114, 1.4652662], [], 0, "CAN_COLLIDE"];
  _vehicle_810 = _this;
  _this setDir -12.899655;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13202.512, 3362.2114, 1.4652662];
};

_vehicle_812 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13233.626, 3369.3142, 1.3333869], [], 0, "CAN_COLLIDE"];
  _vehicle_812 = _this;
  _this setDir -12.769547;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13233.626, 3369.3142, 1.3333869];
};

_vehicle_814 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13270.897, 3379.9102, 1.8161108], [], 0, "CAN_COLLIDE"];
  _vehicle_814 = _this;
  _this setDir -18.649527;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13270.897, 3379.9102, 1.8161108];
};

_vehicle_816 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13304.472, 3399.8787, 1.1862165], [], 0, "CAN_COLLIDE"];
  _vehicle_816 = _this;
  _this setDir -42.60598;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13304.472, 3399.8787, 1.1862165];
};

_vehicle_818 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13329.728, 3429.2698, 1.6188002], [], 0, "CAN_COLLIDE"];
  _vehicle_818 = _this;
  _this setDir -56.514828;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13329.728, 3429.2698, 1.6188002];
};



_vehicle_820 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13353.593, 3460.3086, 1.9356682], [], 0, "CAN_COLLIDE"];
  _vehicle_820 = _this;
  _this setDir -46.773556;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13353.593, 3460.3086, 1.9356682];
};

_vehicle_822 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13381.376, 3488.4241, 1.6552769], [], 0, "CAN_COLLIDE"];
  _vehicle_822 = _this;
  _this setDir -46.108459;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13381.376, 3488.4241, 1.6552769];
};

_vehicle_824 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13407.907, 3517.616, 1.7340099], [], 0, "CAN_COLLIDE"];
  _vehicle_824 = _this;
  _this setDir -48.609024;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13407.907, 3517.616, 1.7340099];
};

_vehicle_826 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13435.422, 3544.9009, 1.9016155], [], 0, "CAN_COLLIDE"];
  _vehicle_826 = _this;
  _this setDir -39.68642;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13435.422, 3544.9009, 1.9016155];
};

_vehicle_828 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13465.36, 3545.0662, 1.5410234], [], 0, "CAN_COLLIDE"];
  _vehicle_828 = _this;
  _this setDir 37.548149;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13465.36, 3545.0662, 1.5410234];
};

_vehicle_830 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13497.259, 3523.1606, 1.468462], [], 0, "CAN_COLLIDE"];
  _vehicle_830 = _this;
  _this setDir 31.339958;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13497.259, 3523.1606, 1.468462];
};

_vehicle_832 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13530.844, 3502.6519, 1.3012456], [], 0, "CAN_COLLIDE"];
  _vehicle_832 = _this;
  _this setDir 31.374308;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13530.844, 3502.6519, 1.3012456];
};

_vehicle_834 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13564.412, 3482.4065, 1.5042342], [], 0, "CAN_COLLIDE"];
  _vehicle_834 = _this;
  _this setDir 30.998472;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13564.412, 3482.4065, 1.5042342];
};

_vehicle_836 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13629.22, 3440.5354, 1.4379208], [], 0, "CAN_COLLIDE"];
  _vehicle_836 = _this;
  _this setDir 37.693588;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13629.22, 3440.5354, 1.4379208];
};

_vehicle_838 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13659.539, 3416.4146, 1.2989595], [], 0, "CAN_COLLIDE"];
  _vehicle_838 = _this;
  _this setDir 39.149002;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13659.539, 3416.4146, 1.2989595];
};

_vehicle_840 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13688.278, 3389.5393, 1.5413688], [], 0, "CAN_COLLIDE"];
  _vehicle_840 = _this;
  _this setDir 46.895634;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13688.278, 3389.5393, 1.5413688];
};

_vehicle_842 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13696.913, 3356.8059, 1.40792], [], 0, "CAN_COLLIDE"];
  _vehicle_842 = _this;
  _this setDir 102.82268;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13696.913, 3356.8059, 1.40792];
};

_vehicle_844 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13697.154, 3319.5503, 0.59300607], [], 0, "CAN_COLLIDE"];
  _vehicle_844 = _this;
  _this setDir 75.17746;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13697.154, 3319.5503, 0.59300607];
};

_vehicle_846 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13720.648, 3245.8108, -1.6224025], [], 0, "CAN_COLLIDE"];
  _vehicle_846 = _this;
  _this setDir 71.323296;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13720.648, 3245.8108, -1.6224025];
};

_vehicle_848 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13735.84, 3209.9658, 0.52038002], [], 0, "CAN_COLLIDE"];
  _vehicle_848 = _this;
  _this setDir 62.659431;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13735.84, 3209.9658, 0.52038002];
};

_vehicle_850 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13743.2, 3173.3137, 0.42224136], [], 0, "CAN_COLLIDE"];
  _vehicle_850 = _this;
  _this setDir 96.81192;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13743.2, 3173.3137, 0.42224136];
};

_vehicle_852 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13745.862, 3134.676, 0.041508824], [], 0, "CAN_COLLIDE"];
  _vehicle_852 = _this;
  _this setDir 74.231659;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13745.862, 3134.676, 0.041508824];
};

_vehicle_854 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13767.492, 3103.655, 0.56608605], [], 0, "CAN_COLLIDE"];
  _vehicle_854 = _this;
  _this setDir 37.468555;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13767.492, 3103.655, 0.56608605];
};

_vehicle_856 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13800.767, 3082.4814, -0.38674861], [], 0, "CAN_COLLIDE"];
  _vehicle_856 = _this;
  _this setDir 26.117628;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13800.767, 3082.4814, -0.38674861];
};

_vehicle_858 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13838.454, 3075.2253, 0.24088755], [], 0, "CAN_COLLIDE"];
  _vehicle_858 = _this;
  _this setDir -3.5174253;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13838.454, 3075.2253, 0.24088755];
};

_vehicle_860 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13877.104, 3079.6919, 0.047779243], [], 0, "CAN_COLLIDE"];
  _vehicle_860 = _this;
  _this setDir -9.3777046;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13877.104, 3079.6919, 0.047779243];
};

_vehicle_862 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13915.919, 3083.3191, 0.31101051], [], 0, "CAN_COLLIDE"];
  _vehicle_862 = _this;
  _this setDir -1.3437383;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13915.919, 3083.3191, 0.31101051];
};

_vehicle_864 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13953.006, 3093.9019, 0.38909546], [], 0, "CAN_COLLIDE"];
  _vehicle_864 = _this;
  _this setDir -31.065653;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13953.006, 3093.9019, 0.38909546];
};

_vehicle_866 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13992.951, 3065.1646, 0.24632952], [], 0, "CAN_COLLIDE"];
  _vehicle_866 = _this;
  _this setDir 59.982674;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13992.951, 3065.1646, 0.24632952];
};

_vehicle_868 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14019.17, 3038.2832, 0.24321558], [], 0, "CAN_COLLIDE"];
  _vehicle_868 = _this;
  _this setDir 32.256134;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14019.17, 3038.2832, 0.24321558];
};

_vehicle_870 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14052.104, 3017.5112, -0.49891144], [], 0, "CAN_COLLIDE"];
  _vehicle_870 = _this;
  _this setDir 31.182495;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14052.104, 3017.5112, -0.49891144];
};

_vehicle_872 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14087.016, 3000.0664, -0.044168435], [], 0, "CAN_COLLIDE"];
  _vehicle_872 = _this;
  _this setDir 22.090927;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14087.016, 3000.0664, -0.044168435];
};

_vehicle_874 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14123.219, 2987.5022, -0.26365274], [], 0, "CAN_COLLIDE"];
  _vehicle_874 = _this;
  _this setDir 15.14182;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14123.219, 2987.5022, -0.26365274];
};

_vehicle_876 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14146.055, 2964.4429, 0.46680677], [], 0, "CAN_COLLIDE"];
  _vehicle_876 = _this;
  _this setDir 77.029854;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14146.055, 2964.4429, 0.46680677];
};

_vehicle_878 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14146.197, 2926.3933, 0.46129805], [], 0, "CAN_COLLIDE"];
  _vehicle_878 = _this;
  _this setDir 100.66653;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14146.197, 2926.3933, 0.46129805];
};

_vehicle_880 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14156.196, 2892.5967, 0.45493796], [], 0, "CAN_COLLIDE"];
  _vehicle_880 = _this;
  _this setDir 48.85796;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14156.196, 2892.5967, 0.45493796];
};

_vehicle_882 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14178.406, 2860.7651, 0.22919247], [], 0, "CAN_COLLIDE"];
  _vehicle_882 = _this;
  _this setDir 61.26778;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14178.406, 2860.7651, 0.22919247];
};

_vehicle_884 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14194.571, 2824.6406, 0.37738806], [], 0, "CAN_COLLIDE"];
  _vehicle_884 = _this;
  _this setDir 70.464081;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14194.571, 2824.6406, 0.37738806];
};

_vehicle_886 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14207.071, 2786.635, 0.61553705], [], 0, "CAN_COLLIDE"];
  _vehicle_886 = _this;
  _this setDir 73.196266;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14207.071, 2786.635, 0.61553705];
};

_vehicle_888 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14209.111, 2779.8286, 0.61540109], [], 0, "CAN_COLLIDE"];
  _vehicle_888 = _this;
  _this setDir 73.260483;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14209.111, 2779.8286, 0.61540109];
};

_vehicle_890 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14200.989, 2732.2544, 1.6116579], [], 0, "CAN_COLLIDE"];
  _vehicle_890 = _this;
  _this setDir -79.19648;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14200.989, 2732.2544, 1.6116579];
};

_vehicle_892 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14145.642, 2671.3271, 0.46446341], [], 0, "CAN_COLLIDE"];
  _vehicle_892 = _this;
  _this setDir -5.3513331;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14145.642, 2671.3271, 0.46446341];
};

_vehicle_894 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14109.455, 2669.968, 1.2945031], [], 0, "CAN_COLLIDE"];
  _vehicle_894 = _this;
  _this setDir -0.79530722;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14109.455, 2669.968, 1.2945031];
};

_vehicle_896 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14070.313, 2672.7004, 1.791772], [], 0, "CAN_COLLIDE"];
  _vehicle_896 = _this;
  _this setDir 12.636163;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14070.313, 2672.7004, 1.791772];
};

_vehicle_898 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14034.48, 2687.0986, 1.8802483], [], 0, "CAN_COLLIDE"];
  _vehicle_898 = _this;
  _this setDir 29.286236;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14034.48, 2687.0986, 1.8802483];
};

_vehicle_900 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [14001.701, 2708.7197, 1.9524666], [], 0, "CAN_COLLIDE"];
  _vehicle_900 = _this;
  _this setDir 38.994282;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [14001.701, 2708.7197, 1.9524666];
};

_vehicle_902 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13968.301, 2729.2285, 2.2055924], [], 0, "CAN_COLLIDE"];
  _vehicle_902 = _this;
  _this setDir 23.523426;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13968.301, 2729.2285, 2.2055924];
};

_vehicle_904 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13931.649, 2743.7188, 2.0047262], [], 0, "CAN_COLLIDE"];
  _vehicle_904 = _this;
  _this setDir 19.805273;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13931.649, 2743.7188, 2.0047262];
};

_vehicle_906 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13893.176, 2752.2832, 2.0533013], [], 0, "CAN_COLLIDE"];
  _vehicle_906 = _this;
  _this setDir 4.7426338;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13893.176, 2752.2832, 2.0533013];
};

_vehicle_908 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13853.789, 2752.395, 2.0541296], [], 0, "CAN_COLLIDE"];
  _vehicle_908 = _this;
  _this setDir -3.7612033;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13853.789, 2752.395, 2.0541296];
};

_vehicle_910 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13814.431, 2746.8135, 1.8963013], [], 0, "CAN_COLLIDE"];
  _vehicle_910 = _this;
  _this setDir -12.602625;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13814.431, 2746.8135, 1.8963013];
};

_vehicle_912 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13777.774, 2733.668, 2.0788558], [], 0, "CAN_COLLIDE"];
  _vehicle_912 = _this;
  _this setDir -26.925592;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13777.774, 2733.668, 2.0788558];
};

_vehicle_914 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13741.076, 2724.696, 1.8671649], [], 0, "CAN_COLLIDE"];
  _vehicle_914 = _this;
  _this setDir -0.64972633;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13741.076, 2724.696, 1.8671649];
};

_vehicle_916 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13702.206, 2723.5413, -1.2503774], [], 0, "CAN_COLLIDE"];
  _vehicle_916 = _this;
  _this setDir -5.8912673;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13702.206, 2723.5413, -1.2503774];
};


_vehicle_925 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13336.382, 2689.1729, 1.4508693], [], 0, "CAN_COLLIDE"];
  _vehicle_925 = _this;
  _this setDir -17.632315;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13336.382, 2689.1729, 1.4508693];
};

_vehicle_927 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13259.235, 2681.1887, 1.7435466], [], 0, "CAN_COLLIDE"];
  _vehicle_927 = _this;
  _this setDir -171.00821;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13259.235, 2681.1887, 1.7435466];
};

_vehicle_929 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13230.212, 2700.8799, 1.9512266], [], 0, "CAN_COLLIDE"];
  _vehicle_929 = _this;
  _this setDir -120.9272;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13230.212, 2700.8799, 1.9512266];
};

_vehicle_931 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13023.033, 3291.811, 1.8481027], [], 0, "CAN_COLLIDE"];
  _vehicle_931 = _this;
  _this setDir -24.704603;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13023.033, 3291.811, 1.8481027];
};

_vehicle_934 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13598.221, 3461.8875, 1.1473042], [], 0, "CAN_COLLIDE"];
  _vehicle_934 = _this;
  _this setDir 31.341118;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13598.221, 3461.8875, 1.1473042];
};

_vehicle_937 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Dam_Barrier_40", [13979.625, 3088.2188, 0.35013077], [], 0, "CAN_COLLIDE"];
  _vehicle_937 = _this;
  _this setDir 59.871975;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13979.625, 3088.2188, 0.35013077];
 }; 
  
  _vehicle_16 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sign_danger", [13504.186, 4531.8052, -0.56753069], [], 0, "CAN_COLLIDE"];
  _vehicle_16 = _this;
  _this setDir -177.43756;
  _this setPos [13504.186, 4531.8052, -0.56753069];
};

_vehicle_18 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sign_danger", [13488.824, 4531.7573, -0.48940879], [], 0, "CAN_COLLIDE"];
  _vehicle_18 = _this;
  _this setDir -175.50237;
  _this setPos [13488.824, 4531.7573, -0.48940879];
};

_vehicle_24 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Danger", [13507.3, 4594.6011, 9.5367432e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_24 = _this;
  _this setPos [13507.3, 4594.6011, 9.5367432e-006];
};

_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Danger", [13489.848, 4597.2969, -7.2956085e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setPos [13489.848, 4597.2969, -7.2956085e-005];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sign_danger", [12376.676, 3537.896, -0.39856106], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setDir -275.28793;
  _this setPos [12376.676, 3537.896, -0.39856106];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sign_danger", [12379.318, 3522.1306, -0.4533411], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir -270.04095;
  _this setPos [12379.318, 3522.1306, -0.4533411];
};

_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Danger", [12304.488, 3528.2766, -0.083734103], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir 88.726669;
  _this setPos [12304.488, 3528.2766, -0.083734103];
};

_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Danger", [12304.145, 3512.4365, -0.068804704], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir 82.577934;
  _this setPos [12304.145, 3512.4365, -0.068804704];
};

_vehicle_43 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13617.367, 3859.7285, -0.13452342], [], 0, "CAN_COLLIDE"];
  _vehicle_43 = _this;
  _this setPos [13617.367, 3859.7285, -0.13452342];
};

_vehicle_46 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13608.126, 3862.0142, -0.39798355], [], 0, "CAN_COLLIDE"];
  _vehicle_46 = _this;
  _this setPos [13608.126, 3862.0142, -0.39798355];
};

_vehicle_48 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13595.495, 3854.4109, -0.25014985], [], 0, "CAN_COLLIDE"];
  _vehicle_48 = _this;
  _this setPos [13595.495, 3854.4109, -0.25014985];
};

_vehicle_50 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13612.323, 3861.9187, -0.017950878], [], 0, "CAN_COLLIDE"];
  _vehicle_50 = _this;
  _this setPos [13612.323, 3861.9187, -0.017950878];
};

_vehicle_52 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13587.414, 3862.2278, -0.36400315], [], 0, "CAN_COLLIDE"];
  _vehicle_52 = _this;
  _this setPos [13587.414, 3862.2278, -0.36400315];
};

_vehicle_54 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13646.082, 3892.1472, 4.3869019e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_54 = _this;
  _this setPos [13646.082, 3892.1472, 4.3869019e-005];
};

_vehicle_56 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13656.749, 3887.7, -0.09093938], [], 0, "CAN_COLLIDE"];
  _vehicle_56 = _this;
  _this setDir -23.272844;
  _this setPos [13656.749, 3887.7, -0.09093938];
};

_vehicle_58 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13651.214, 3890.3098, 0.011305016], [], 0, "CAN_COLLIDE"];
  _vehicle_58 = _this;
  _this setDir -1.4987669;
  _this setPos [13651.214, 3890.3098, 0.011305016];
};

_vehicle_64 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13650.92, 3896.3352, 0.05507043], [], 0, "CAN_COLLIDE"];
  _vehicle_64 = _this;
  _this setDir -22.122763;
  _this setPos [13650.92, 3896.3352, 0.05507043];
};

_vehicle_66 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13655.272, 3892.218, -1.335144e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_66 = _this;
  _this setPos [13655.272, 3892.218, -1.335144e-005];
};

_vehicle_68 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13496.31, 3931.6724, -0.16220868], [], 0, "CAN_COLLIDE"];
  _vehicle_68 = _this;
  _this setPos [13496.31, 3931.6724, -0.16220868];
};

_vehicle_70 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13466.638, 3906.4585, -0.18177314], [], 0, "CAN_COLLIDE"];
  _vehicle_70 = _this;
  _this setDir -33.474545;
  _this setPos [13466.638, 3906.4585, -0.18177314];
};

_vehicle_72 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13463.232, 3905.8403, -0.045517381], [], 0, "CAN_COLLIDE"];
  _vehicle_72 = _this;
  _this setDir 10.263848;
  _this setPos [13463.232, 3905.8403, -0.045517381];
};

_vehicle_74 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13427.19, 3889.7791, 6.6995621e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_74 = _this;
  _this setDir 2.2567973;
  _this setPos [13427.19, 3889.7791, 6.6995621e-005];
};

_vehicle_76 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13374.196, 3873.9983, -1.4066696e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_76 = _this;
  _this setDir -8.0782242;
  _this setPos [13374.196, 3873.9983, -1.4066696e-005];
};

_vehicle_78 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13347.987, 3863.1641, 0.00013017654], [], 0, "CAN_COLLIDE"];
  _vehicle_78 = _this;
  _this setDir 4.9957471;
  _this setPos [13347.987, 3863.1641, 0.00013017654];
};

_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13350.984, 3864.343, 0.00015878677], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setDir -51.606461;
  _this setPos [13350.984, 3864.343, 0.00015878677];
};

_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13377.39, 3875.3779, 0.00013756752], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setDir -40.877182;
  _this setPos [13377.39, 3875.3779, 0.00013756752];
};

_vehicle_86 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13430.546, 3890.4326, 4.7445297e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_86 = _this;
  _this setDir -26.592194;
  _this setPos [13430.546, 3890.4326, 4.7445297e-005];
};

_vehicle_88 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13499.814, 3930.7346, -0.11362953], [], 0, "CAN_COLLIDE"];
  _vehicle_88 = _this;
  _this setPos [13499.814, 3930.7346, -0.11362953];
};

_vehicle_112 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [13011.639, 3807.0691, -0.32659397], [], 0, "CAN_COLLIDE"];
  _vehicle_112 = _this;
  _this setDir -216.77014;
  _this setPos [13011.639, 3807.0691, -0.32659397];
};

_vehicle_114 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [13002.989, 3801.2556, -0.35216427], [], 0, "CAN_COLLIDE"];
  _vehicle_114 = _this;
  _this setDir -210.19495;
  _this setPos [13002.989, 3801.2556, -0.35216427];
};

_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [13003.046, 3795.0613, -0.43777809], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setDir -121.54526;
  _this setPos [13003.046, 3795.0613, -0.43777809];
};

_vehicle_121 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [13007.755, 3787.741, -0.4314411], [], 0, "CAN_COLLIDE"];
  _vehicle_121 = _this;
  _this setDir -121.54526;
  _this setPos [13007.755, 3787.741, -0.4314411];
};

_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [13017.151, 3804.8035, -0.3858678], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setDir -121.54526;
  _this setPos [13017.151, 3804.8035, -0.3858678];
};

_vehicle_125 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [13022.032, 3797.4219, -0.41128138], [], 0, "CAN_COLLIDE"];
  _vehicle_125 = _this;
  _this setDir -121.54526;
  _this setPos [13022.032, 3797.4219, -0.41128138];
};

_vehicle_128 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fortified_nest_big", [13012.111, 3793.3738, 0.21149293], [], 0, "CAN_COLLIDE"];
  _vehicle_128 = _this;
  _this setDir -32.036274;
  _this setPos [13012.111, 3793.3738, 0.21149293];
};

_vehicle_131 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13013.741, 3785.9851, -0.13643166], [], 0, "CAN_COLLIDE"];
  _vehicle_131 = _this;
  _this setDir -29.487366;
  _this setPos [13013.741, 3785.9851, -0.13643166];
};

_vehicle_133 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13017.938, 3788.1072, -0.056774464], [], 0, "CAN_COLLIDE"];
  _vehicle_133 = _this;
  _this setDir -29.487366;
  _this setPos [13017.938, 3788.1072, -0.056774464];
};

_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [13022.123, 3790.6255, -0.065500222], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir -32.438797;
  _this setPos [13022.123, 3790.6255, -0.065500222];
};

_vehicle_138 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fortified_nest_small", [13601.671, 3860.8381, -0.15955034], [], 0, "CAN_COLLIDE"];
  _vehicle_138 = _this;
  _this setDir 4.9428821;
  _this setPos [13601.671, 3860.8381, -0.15955034];
};

_vehicle_140 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fortified_nest_small", [13270.224, 3916.8535, -0.20563158], [], 0, "CAN_COLLIDE"];
  _vehicle_140 = _this;
  _this setDir -14.794216;
  _this setPos [13270.224, 3916.8535, -0.20563158];
};

_vehicle_142 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fortified_nest_small", [13168.414, 3894.1653, -0.13609672], [], 0, "CAN_COLLIDE"];
  _vehicle_142 = _this;
  _this setDir -31.244608;
  _this setPos [13168.414, 3894.1653, -0.13609672];
};

_vehicle_144 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fortified_nest_small", [12848.759, 3686.8948, -0.12525415], [], 0, "CAN_COLLIDE"];
  _vehicle_144 = _this;
  _this setDir -31.768951;
  _this setPos [12848.759, 3686.8948, -0.12525415];
};

_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13654.88, 3835.533, -20.307537], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setDir -130.93846;
  _this setPos [13654.88, 3835.533, -20.307537];
};

_vehicle_160 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13669.171, 3816.874, -15.902741], [], 0, "CAN_COLLIDE"];
  _vehicle_160 = _this;
  _this setDir -145.41966;
  _this setPos [13669.171, 3816.874, -15.902741];
};

_vehicle_162 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13678.103, 3809.5801, -16.613932], [], 0, "CAN_COLLIDE"];
  _vehicle_162 = _this;
  _this setDir -320.19705;
  _this setPos [13678.103, 3809.5801, -16.613932];
};

_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13696.293, 3793.0962, -21.147005], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setDir -320.19705;
  _this setPos [13696.293, 3793.0962, -21.147005];
};

_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13663.854, 3839.7146, -24.819654], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setDir -686.83911;
  _this setPos [13663.854, 3839.7146, -24.819654];
};

_vehicle_169 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13683.076, 3821.4668, -28.68779], [], 0, "CAN_COLLIDE"];
  _vehicle_169 = _this;
  _this setDir -492.93774;
  _this setPos [13683.076, 3821.4668, -28.68779];
};

_vehicle_171 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13694.098, 3815.0476, -28.426346], [], 0, "CAN_COLLIDE"];
  _vehicle_171 = _this;
  _this setDir -331.79483;
  _this setPos [13694.098, 3815.0476, -28.426346];
};

_vehicle_173 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13709.655, 3805.2915, -31.308554], [], 0, "CAN_COLLIDE"];
  _vehicle_173 = _this;
  _this setDir -320.19705;
  _this setPos [13709.655, 3805.2915, -31.308554];
};

_vehicle_175 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13719.165, 3794.4983, -30.039621], [], 0, "CAN_COLLIDE"];
  _vehicle_175 = _this;
  _this setDir -521.98163;
  _this setPos [13719.165, 3794.4983, -30.039621];
};

_vehicle_177 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13725.771, 3787.8706, -28.58069], [], 0, "CAN_COLLIDE"];
  _vehicle_177 = _this;
  _this setDir -320.19705;
  _this setPos [13725.771, 3787.8706, -28.58069];
};

_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13657.807, 3854.6965, -22.679176], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setDir -320.19705;
  _this setPos [13657.807, 3854.6965, -22.679176];
};

_vehicle_181 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13668.756, 3849.5671, -27.697092], [], 0, "CAN_COLLIDE"];
  _vehicle_181 = _this;
  _this setDir -320.19705;
  _this setPos [13668.756, 3849.5671, -27.697092];
};

_vehicle_183 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13697.53, 3805.2744, -27.602043], [], 0, "CAN_COLLIDE"];
  _vehicle_183 = _this;
  _this setDir -317.72986;
  _this setPos [13697.53, 3805.2744, -27.602043];
};

_vehicle_185 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13694.287, 3817.3823, -31.741163], [], 0, "CAN_COLLIDE"];
  _vehicle_185 = _this;
  _this setDir -500.68594;
  _this setPos [13694.287, 3817.3823, -31.741163];
};

_vehicle_187 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13735.975, 3779.5681, -30.26026], [], 0, "CAN_COLLIDE"];
  _vehicle_187 = _this;
  _this setDir -320.19705;
  _this setPos [13735.975, 3779.5681, -30.26026];
};

_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13732.033, 3784.4268, -30.297949], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setDir -313.09052;
  _this setPos [13732.033, 3784.4268, -30.297949];
};

_vehicle_191 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13716.242, 3806.2197, -32.425652], [], 0, "CAN_COLLIDE"];
  _vehicle_191 = _this;
  _this setDir -320.19705;
  _this setPos [13716.242, 3806.2197, -32.425652];
};

_vehicle_193 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13760.133, 3712.0486, -15.999249], [], 0, "CAN_COLLIDE"];
  _vehicle_193 = _this;
  _this setDir -149.481;
  _this setPos [13760.133, 3712.0486, -15.999249];
};

_vehicle_195 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13772.318, 3704.5544, -11.583512], [], 0, "CAN_COLLIDE"];
  _vehicle_195 = _this;
  _this setDir -320.19705;
  _this setPos [13772.318, 3704.5544, -11.583512];
};

_vehicle_197 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13789.401, 3698.4319, -17.142237], [], 0, "CAN_COLLIDE"];
  _vehicle_197 = _this;
  _this setDir -401.09695;
  _this setPos [13789.401, 3698.4319, -17.142237];
};

_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Nav_Boathouse_PierT", [13776.709, 3743.3083, -0.034125924], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setDir -84.665611;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [13776.709, 3743.3083, -0.034125924];
};

_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BoatSmall_1", [13772.292, 3746.5051, -0.058757573], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setDir 22.533751;
  _this setPos [13772.292, 3746.5051, -0.058757573];
};

_vehicle_244 = objNull;
if (true) then
{
  _this = createVehicle ["WoodLadder_DZ", [13790.688, 3740.0337, -1.8322777], [], 0, "CAN_COLLIDE"];
  _vehicle_244 = _this;
  _this setDir 13.713914;
  _this setPos [13790.688, 3740.0337, -1.8322777];
  
};

};