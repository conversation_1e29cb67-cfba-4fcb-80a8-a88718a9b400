if (isServer) then {

		_bldObj = objNull;
		if (true) then
		
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13718.973, 3368.9934, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir 3.3463643;
		  _bldObj setPos [13718.973, 3368.9934, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13717.181, 3344.4163, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir 5.0739827;
		  _bldObj setPos [13717.181, 3344.4163, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13714.58, 3320.1558, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -172.91692;
		  _bldObj setPos [13714.58, 3320.1558, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13710.959, 3296.1443, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir 9.9466639;
		  _bldObj setPos [13710.959, 3296.1443, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13708.3, 3273.7942, -0.652], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir 20.276444;
		  _bldObj setPos [13708.3, 3273.7942, -0.652];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13535.238, 3876.2686, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -0.89266175;
		  _bldObj setPos [13535.238, 3876.2686, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13535.563, 3852.2295, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setPos [13535.563, 3852.2295, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13536.438, 3828.1692, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -3.9450908;
		  _bldObj setPos [13536.438, 3828.1692, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13539.563, 3804.8772, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -11.258271;
		  _bldObj setPos [13539.563, 3804.8772, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13545.68, 3781.7183, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -18.445009;
		  _bldObj setPos [13545.68, 3781.7183, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13554.436, 3758.9407, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -23.64596;
		  _bldObj setPos [13554.436, 3758.9407, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13565.128, 3737.2363, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -28.878498;
		  _bldObj setPos [13565.128, 3737.2363, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13577.157, 3715.4272, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -29.006411;
		  _bldObj setPos [13577.157, 3715.4272, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13589.325, 3693.7878, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -209.83965;
		  _bldObj setPos [13589.325, 3693.7878, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13601.692, 3672.23, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -29.814951;
		  _bldObj setPos [13601.692, 3672.23, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13614.522, 3651.9612, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -34.625931;
		  _bldObj setPos [13614.522, 3651.9612, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13629.11, 3632.3455, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -38.668156;
		  _bldObj setPos [13629.11, 3632.3455, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13644.771, 3612.9971, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -39.296799;
		  _bldObj setPos [13644.771, 3612.9971, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13660.49, 3593.8728, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -39.518562;
		  _bldObj setPos [13660.49, 3593.8728, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13676.41, 3574.6606, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -39.86631;
		  _bldObj setPos [13676.41, 3574.6606, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13692.482, 3555.677, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -40.694519;
		  _bldObj setPos [13692.482, 3555.677, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13705.514, 3536.6353, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -27.351328;
		  _bldObj setPos [13705.514, 3536.6353, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13714.066, 3514.7532, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -15.730154;
		  _bldObj setPos [13714.066, 3514.7532, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13719.165, 3491.0972, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -8.9279337;
		  _bldObj setPos [13719.165, 3491.0972, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13721.553, 3467.2986, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir -2.5554588;
		  _bldObj setPos [13721.553, 3467.2986, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13721.82, 3443.2573, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir 1.2040678;
		  _bldObj setPos [13721.82, 3443.2573, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13721.238, 3418.2808, 2.0], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir 1.4159043;
		  _bldObj setPos [13721.238, 3418.2808, 2.0];
		  _bldObj setVectorUp [0, 0, 1];
		};

		_bldObj = objNull;
		if (true) then
		{
		  _bldObj = createVehicle ["MAP_bridge_asf1_25", [13720.31, 3393.7627, 2.01], [], 0, "CAN_COLLIDE"];
		  _bldObj setDir 2.904952;
		  _bldObj setPos [13720.31, 3393.7627, 2.01];
		  _bldObj setVectorUp [0, 0, 1];
		};
		
		};