/*
  CHERNARUS ENHANCEMENTS - Stary
  --------------------------------------------------------------
    Redeveloped Stary
    Email: <EMAIL>
    Steam: http://steamcommunity.com/profiles/76561198104994458/
*/

if (isServer) then {

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6285.3398, 7800.3687, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir -49.865536;
  _this setPos [6285.3398, 7800.3687, 3.0517578e-005];
};

_vehicle_6 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6281.1782, 7804.8281, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_6 = _this;
  _this setDir -32.163315;
  _this setPos [6281.1782, 7804.8281, -3.0517578e-005];
};

_vehicle_8 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6278.3838, 7810.4233], [], 0, "CAN_COLLIDE"];
  _vehicle_8 = _this;
  _this setDir -17.083616;
  _this setPos [6278.3838, 7810.4233];
};

_vehicle_10 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6275.6895, 7815.3579, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_10 = _this;
  _this setDir -33.591225;
  _this setPos [6275.6895, 7815.3579, -3.0517578e-005];
};

_vehicle_12 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6278.5229, 7832.6436, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_12 = _this;
  _this setDir 33.439877;
  _this setPos [6278.5229, 7832.6436, -3.0517578e-005];
};

_vehicle_17 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6282.9731, 7835.7397, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_17 = _this;
  _this setDir -111.6958;
  _this setPos [6282.9731, 7835.7397, 3.0517578e-005];
};

_vehicle_19 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6288.6362, 7837.0454], [], 0, "CAN_COLLIDE"];
  _vehicle_19 = _this;
  _this setDir -93.85009;
  _this setPos [6288.6362, 7837.0454];
};

_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6293.8989, 7836.144, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir -54.346973;
  _this setPos [6293.8989, 7836.144, 6.1035156e-005];
};

_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6299.146, 7833.4111, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir -244.68703;
  _this setPos [6299.146, 7833.4111, 3.0517578e-005];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6304.7197, 7831.1504, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir -246.45798;
  _this setPos [6304.7197, 7831.1504, -3.0517578e-005];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6279.1099, 7807.6685, 2.53069], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir -203.57813;
  _this setPos [6279.1099, 7807.6685, 2.53069];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6276.1772, 7828.5654], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setDir 26.576258;
  _this setPos [6276.1772, 7828.5654];
};

_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6293.2275, 7794.4951, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir -64.697647;
  _this setPos [6293.2275, 7794.4951, -3.0517578e-005];
};

_vehicle_37 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6289.1514, 7797.1636, 2.3704591], [], 0, "CAN_COLLIDE"];
  _vehicle_37 = _this;
  _this setDir -52.222004;
  _this setPos [6289.1514, 7797.1636, 2.3704591];
};

_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6310.0049, 7828.8647], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setDir -62.431007;
  _this setPos [6310.0049, 7828.8647];
};

_vehicle_41 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6315.0015, 7825.8022], [], 0, "CAN_COLLIDE"];
  _vehicle_41 = _this;
  _this setDir -57.128498;
  _this setPos [6315.0015, 7825.8022];
};

_vehicle_43 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6320.3984, 7822.917, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_43 = _this;
  _this setDir -60.013428;
  _this setPos [6320.3984, 7822.917, 6.1035156e-005];
};

_vehicle_45 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6325.5591, 7820.0254], [], 0, "CAN_COLLIDE"];
  _vehicle_45 = _this;
  _this setDir -58.945595;
  _this setPos [6325.5591, 7820.0254];
};

_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6330.6255, 7817.1021, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setDir -57.434349;
  _this setPos [6330.6255, 7817.1021, -9.1552734e-005];
};

_vehicle_49 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6335.5864, 7813.8633], [], 0, "CAN_COLLIDE"];
  _vehicle_49 = _this;
  _this setDir -54.781757;
  _this setPos [6335.5864, 7813.8633];
};

_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6340.2793, 7810.5249, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setDir -54.144264;
  _this setPos [6340.2793, 7810.5249, 9.1552734e-005];
};

_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6345.2158, 7806.9941, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setDir -56.574566;
  _this setPos [6345.2158, 7806.9941, 3.0517578e-005];
};

_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6350.0928, 7803.5073], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setDir -50.540646;
  _this setPos [6350.0928, 7803.5073];
};

_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6354.4385, 7799.6523], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setDir -49.489563;
  _this setPos [6354.4385, 7799.6523];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6358.0811, 7795.2295], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir -28.650618;
  _this setPos [6358.0811, 7795.2295];
};

_vehicle_61 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6360.1211, 7789.8091], [], 0, "CAN_COLLIDE"];
  _vehicle_61 = _this;
  _this setDir -8.7495184;
  _this setPos [6360.1211, 7789.8091];
};

_vehicle_63 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6360.2954, 7784.6372], [], 0, "CAN_COLLIDE"];
  _vehicle_63 = _this;
  _this setDir 4.6951418;
  _this setPos [6360.2954, 7784.6372];
};

_vehicle_65 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6358.8125, 7769.2896, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_65 = _this;
  _this setDir 12.555237;
  _this setPos [6358.8125, 7769.2896, -3.0517578e-005];
};

_vehicle_67 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6355.5283, 7765.3042, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_67 = _this;
  _this setDir 69.646011;
  _this setPos [6355.5283, 7765.3042, -3.0517578e-005];
};

_vehicle_69 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6349.709, 7764.3389, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_69 = _this;
  _this setDir -88.262764;
  _this setPos [6349.709, 7764.3389, 3.0517578e-005];
};

_vehicle_71 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6343.9023, 7765.0063, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_71 = _this;
  _this setDir -70.199654;
  _this setPos [6343.9023, 7765.0063, -3.0517578e-005];
};

_vehicle_73 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6338.2407, 7767.3218, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_73 = _this;
  _this setDir -66.618607;
  _this setPos [6338.2407, 7767.3218, -6.1035156e-005];
};

_vehicle_75 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6332.2026, 7769.5703, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_75 = _this;
  _this setDir -72.935478;
  _this setPos [6332.2026, 7769.5703, 3.0517578e-005];
};

_vehicle_77 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6326.6885, 7771.8257, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_77 = _this;
  _this setDir -59.033981;
  _this setPos [6326.6885, 7771.8257, 3.0517578e-005];
};

_vehicle_79 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6321.9175, 7774.9448], [], 0, "CAN_COLLIDE"];
  _vehicle_79 = _this;
  _this setDir -49.107616;
  _this setPos [6321.9175, 7774.9448];
};

_vehicle_81 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6335.2954, 7765.9341], [], 0, "CAN_COLLIDE"];
  _vehicle_81 = _this;
  _this setDir -67.301582;
  _this setPos [6335.2954, 7765.9341];
};

_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6337.0464, 7812.7891, 2.4463177], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setDir -47.306496;
  _this setPos [6337.0464, 7812.7891, 2.4463177];
};

_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6291.3555, 7836.8906, 2.5217109], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir -81.574867;
  _this setPos [6291.3555, 7836.8906, 2.5217109];
};

_vehicle_91 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6267.3794, 7807.7822], [], 0, "CAN_COLLIDE"];
  _vehicle_91 = _this;
  _this setDir 21.572401;
  _this setPos [6267.3794, 7807.7822];
};

_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6273.5449, 7793.5752], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setDir -11.972234;
  _this setPos [6273.5449, 7793.5752];
};

_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6357.6909, 7784.3755, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setDir -50.85421;
  _this setPos [6357.6909, 7784.3755, -6.1035156e-005];
};

_vehicle_97 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6355.8335, 7770.8076, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_97 = _this;
  _this setDir -97.639771;
  _this setPos [6355.8335, 7770.8076, 3.0517578e-005];
};

_vehicle_101 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_misc_amplion_conc", [6303.1514, 7858.5752], [], 0, "CAN_COLLIDE"];
  _vehicle_101 = _this;
  _this setDir -149.7877;
  _this setPos [6303.1514, 7858.5752];
};

_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_P_ciws", [6276.3252, 7828.708, 2.477356], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setDir 103.62038;
  _this setPos [6276.3252, 7828.708, 2.477356];
};

_vehicle_110 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_P_ciws", [6276.2798, 7813.2866, 2.4636757], [], 0, "CAN_COLLIDE"];
  _vehicle_110 = _this;
  _this setDir 73.402618;
  _this setPos [6276.2798, 7813.2866, 2.4636757];
};

_vehicle_116 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barbedwire", [6271.7065, 7812.5254], [], 0, "CAN_COLLIDE"];
  _vehicle_116 = _this;
  _this setDir -118.89729;
  _this setPos [6271.7065, 7812.5254];
};

_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barbedwire", [6280.8232, 7798.8535], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setDir -134.49637;
  _this setPos [6280.8232, 7798.8535];
};

_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barbedwire", [6282.853, 7790.4092, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir -143.78349;
  _this setPos [6282.853, 7790.4092, -3.0517578e-005];
};

_vehicle_122 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barbedwire", [6291.5684, 7787.0991, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_122 = _this;
  _this setDir -163.95316;
  _this setPos [6291.5684, 7787.0991, -3.0517578e-005];
};

_vehicle_124 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barbedwire", [6277.5396, 7841.5488, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_124 = _this;
  _this setDir -81.78257;
  _this setPos [6277.5396, 7841.5488, 6.1035156e-005];
};

_vehicle_133 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6316.4644, 7815.1416, 0.12316823], [], 0, "CAN_COLLIDE"];
  _vehicle_133 = _this;
  _this setDir 30.596329;
  _this setPos [6316.4644, 7815.1416, 0.12316823];
};

_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6319.0845, 7819.5938, 2.5658965], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir 32.219917;
  _this setPos [6319.0845, 7819.5938, 2.5658965];
};

_vehicle_137 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6320.6411, 7822.6323, 2.4789674], [], 0, "CAN_COLLIDE"];
  _vehicle_137 = _this;
  _this setDir -58.945595;
  _this setPos [6320.6411, 7822.6323, 2.4789674];
};

_vehicle_141 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fort_bagfence_corner", [6274.8306, 7826.2109], [], 0, "CAN_COLLIDE"];
  _vehicle_141 = _this;
  _this setDir 25.904995;
  _this setPos [6274.8306, 7826.2109];
};

_vehicle_143 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fort_bagfence_corner", [6274.0029, 7816.2773, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_143 = _this;
  _this setDir 56.359062;
  _this setPos [6274.0029, 7816.2773, 3.0517578e-005];
};

_vehicle_145 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fort_artillery_nest", [6240.5898, 7692.751, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_145 = _this;
  _this setDir -27.193762;
  _this setPos [6240.5898, 7692.751, -3.0517578e-005];
};

_vehicle_149 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [6278.3218, 7833.7759, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_149 = _this;
  _this setDir -7.29283;
  _this setPos [6278.3218, 7833.7759, 6.1035156e-005];
};

_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [6277.6245, 7808.853, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setDir -94.649452;
  _this setPos [6277.6245, 7808.853, 9.1552734e-005];
};

_vehicle_160 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_road_cone", [6273.0073, 7825.6367, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_160 = _this;
  _this setPos [6273.0073, 7825.6367, 6.1035156e-005];
};

_vehicle_182 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6360.4595, 7772.5635, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_182 = _this;
  _this setDir 24.046659;
  _this setPos [6360.4595, 7772.5635, 3.0517578e-005];
};

_vehicle_184 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6357.6421, 7773.0552, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_184 = _this;
  _this setDir -7.8390818;
  _this setPos [6357.6421, 7773.0552, 6.1035156e-005];
};

_vehicle_186 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6354.4897, 7772.5771, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_186 = _this;
  _this setDir -9.7906017;
  _this setPos [6354.4897, 7772.5771, 6.1035156e-005];
};

_vehicle_188 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6361.5947, 7780.8745, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_188 = _this;
  _this setDir -16.190605;
  _this setPos [6361.5947, 7780.8745, 6.1035156e-005];
};

_vehicle_190 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6358.8882, 7780.9468, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_190 = _this;
  _this setDir 24.046659;
  _this setPos [6358.8882, 7780.9468, 3.0517578e-005];
};

_vehicle_192 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6356.1362, 7782.7393, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_192 = _this;
  _this setDir 41.767548;
  _this setPos [6356.1362, 7782.7393, 3.0517578e-005];
};

_vehicle_194 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6353.8574, 7785.1855], [], 0, "CAN_COLLIDE"];
  _vehicle_194 = _this;
  _this setDir 55.801815;
  _this setPos [6353.8574, 7785.1855];
};

_vehicle_196 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6353.2119, 7767.5313, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_196 = _this;
  _this setDir 76.844215;
  _this setPos [6353.2119, 7767.5313, 3.0517578e-005];
};

_vehicle_198 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6357.1118, 7788.8765, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_198 = _this;
  _this setDir -22.694275;
  _this setPos [6357.1118, 7788.8765, 3.0517578e-005];
};

_vehicle_200 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6352.2954, 7770.8662, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_200 = _this;
  _this setDir -77.018318;
  _this setPos [6352.2954, 7770.8662, 6.1035156e-005];
};

_vehicle_202 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6353.8081, 7787.4863], [], 0, "CAN_COLLIDE"];
  _vehicle_202 = _this;
  _this setDir -52.253151;
  _this setPos [6353.8081, 7787.4863];
};

_vehicle_206 = objNull;
if (true) then
{
  _this = createVehicle ["UralWreck", [6259.103, 7802.3633, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_206 = _this;
  _this setDir 88.581734;
  _this setPos [6259.103, 7802.3633, 6.1035156e-005];
};

_vehicle_207 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_D", [6289.7383, 7849.0713, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_207 = _this;
  _this setDir 24.046659;
  _this setPos [6289.7383, 7849.0713, 3.0517578e-005];
};

_vehicle_212 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6299.8848, 7833.0557, 2.4534619], [], 0, "CAN_COLLIDE"];
  _vehicle_212 = _this;
  _this setDir -244.68703;
  _this setPos [6299.8848, 7833.0557, 2.4534619];
};

_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6310.7896, 7828.2808, 2.4715738], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setDir -239.4675;
  _this setPos [6310.7896, 7828.2808, 2.4715738];
};

_vehicle_216 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6360.4014, 7788.5015, 2.5465224], [], 0, "CAN_COLLIDE"];
  _vehicle_216 = _this;
  _this setDir -187.12241;
  _this setPos [6360.4014, 7788.5015, 2.5465224];
};

_vehicle_219 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BagFenceLong", [6282.3403, 7833.0581], [], 0, "CAN_COLLIDE"];
  _vehicle_219 = _this;
  _this setDir 46.837437;
  _this setPos [6282.3403, 7833.0581];
};

_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BagFenceLong", [6284.3062, 7830.9634, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setDir 46.837437;
  _this setPos [6284.3062, 7830.9634, 3.0517578e-005];
};

_vehicle_223 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BagFenceLong", [6289.6309, 7826.5498, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_223 = _this;
  _this setDir 25.513182;
  _this setPos [6289.6309, 7826.5498, 9.1552734e-005];
};

_vehicle_225 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BagFenceLong", [6292.397, 7825.8628], [], 0, "CAN_COLLIDE"];
  _vehicle_225 = _this;
  _this setDir 2.9526246;
  _this setPos [6292.397, 7825.8628];
};

_vehicle_227 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BagFenceLong", [6295.1255, 7826.312], [], 0, "CAN_COLLIDE"];
  _vehicle_227 = _this;
  _this setDir -22.478661;
  _this setPos [6295.1255, 7826.312];
};

_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BagFenceLong", [6297.2925, 7828.04, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setDir -55.380249;
  _this setPos [6297.2925, 7828.04, 3.0517578e-005];
};

_vehicle_231 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BagFenceLong", [6298.5737, 7830.7188], [], 0, "CAN_COLLIDE"];
  _vehicle_231 = _this;
  _this setDir 111.03683;
  _this setPos [6298.5737, 7830.7188];
};

_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BagFenceEnd", [6288.6646, 7827.3999], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setDir -135.94244;
  _this setPos [6288.6646, 7827.3999];
};

_vehicle_235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BagFenceEnd", [6285.2363, 7830.1182, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_235 = _this;
  _this setDir -320.72913;
  _this setPos [6285.2363, 7830.1182, 6.1035156e-005];
};

_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CamoNet_EAST", [6313.7656, 7823.7324], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setDir 31.222912;
  _this setPos [6313.7656, 7823.7324];
};

_vehicle_262 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Astan", [6348.5156, 7799.4971, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_262 = _this;
  _this setDir -49.039093;
  _this setPos [6348.5156, 7799.4971, -3.0517578e-005];
};

_vehicle_263 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_tent", [6346.606, 7794.1836, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_263 = _this;
  _this setDir 35.831299;
  _this setPos [6346.606, 7794.1836, 3.0517578e-005];
};

_vehicle_265 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_tent", [6348.5063, 7792.7466], [], 0, "CAN_COLLIDE"];
  _vehicle_265 = _this;
  _this setDir 35.831299;
  _this setPos [6348.5063, 7792.7466];
};

_vehicle_267 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_tent", [6350.2065, 7791.3481, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_267 = _this;
  _this setDir 35.831299;
  _this setPos [6350.2065, 7791.3481, 3.0517578e-005];
};

_vehicle_269 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_tent", [6352.2734, 7789.915], [], 0, "CAN_COLLIDE"];
  _vehicle_269 = _this;
  _this setDir 35.831299;
  _this setPos [6352.2734, 7789.915];
};

_vehicle_271 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Astan", [6351.3633, 7797.6064, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_271 = _this;
  _this setPos [6351.3633, 7797.6064, -3.0517578e-005];
};

_vehicle_273 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Astan", [6297.0449, 7831.397], [], 0, "CAN_COLLIDE"];
  _vehicle_273 = _this;
  _this setPos [6297.0449, 7831.397];
};

_vehicle_274 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_tent", [6292.2285, 7833.4292, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_274 = _this;
  _this setDir 39.034634;
  _this setPos [6292.2285, 7833.4292, -6.1035156e-005];
};

_vehicle_276 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_tent", [6289.4976, 7834.4575], [], 0, "CAN_COLLIDE"];
  _vehicle_276 = _this;
  _this setDir -2.9618921;
  _this setPos [6289.4976, 7834.4575];
};

_vehicle_278 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_tent", [6286.9248, 7833.9585], [], 0, "CAN_COLLIDE"];
  _vehicle_278 = _this;
  _this setDir -23.966791;
  _this setPos [6286.9248, 7833.9585];
};

_vehicle_280 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_tent", [6284.8145, 7832.4141, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_280 = _this;
  _this setDir -49.800148;
  _this setPos [6284.8145, 7832.4141, -3.0517578e-005];
};

_vehicle_287 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2f", [6306.2065, 7828.3926], [], 0, "CAN_COLLIDE"];
  _vehicle_287 = _this;
  _this setPos [6306.2065, 7828.3926];
};

_vehicle_288 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula1f", [6298.8882, 7829.3384, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_288 = _this;
  _this setDir -41.976334;
  _this setPos [6298.8882, 7829.3384, -3.0517578e-005];
};

_vehicle_290 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2s", [6356.7178, 7768.0737, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_290 = _this;
  _this setPos [6356.7178, 7768.0737, -3.0517578e-005];
};

_vehicle_291 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2w", [6328.8921, 7814.1699], [], 0, "CAN_COLLIDE"];
  _vehicle_291 = _this;
  _this setPos [6328.8921, 7814.1699];
};

_vehicle_292 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2f", [6280.7949, 7795.77, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_292 = _this;
  _this setDir -116.24093;
  _this setPos [6280.7949, 7795.77, -9.1552734e-005];
};

_vehicle_354 = objNull;
if (true) then
{
  _this = createVehicle ["Hanged", [6304.4512, 7827.4297, 2.8424919], [], 0, "CAN_COLLIDE"];
  _vehicle_354 = _this;
  _this setDir 57.69973;
  _this setPos [6304.4512, 7827.4297, 2.8424919];
};

_vehicle_361 = objNull;
if (true) then
{
  _this = createVehicle ["MBG_Killhouse_2_InEditor", [5107.3843, 8674.6367, -0.25641361], [], 0, "CAN_COLLIDE"];
  _vehicle_361 = _this;
  _this setDir 19.288124;
  _this setPos [5107.3843, 8674.6367, -0.25641361];
};

_vehicle_362 = objNull;
if (true) then
{
  _this = createVehicle ["MBG_Killhouse_1_InEditor", [5119.1206, 8671.0225, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_362 = _this;
  _this setDir 20.614647;
  _this setPos [5119.1206, 8671.0225, 3.0517578e-005];
};

_vehicle_367 = objNull;
if (true) then
{
  _this = createVehicle ["MBG_Killhouse_3_InEditor", [4231.3613, 8720.7578, 0.99351221], [], 0, "CAN_COLLIDE"];
  _vehicle_367 = _this;
  _this setDir -168.92529;
  _this setPos [4231.3613, 8720.7578, 0.99351221];
};

_vehicle_374 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fort_watchtower", [4471.1519, 9467.6533, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_374 = _this;
  _this setDir 48.732738;
  _this setPos [4471.1519, 9467.6533, 0.00012207031];
};

_vehicle_406 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Cargo1B", [6302.394, 7772.4741, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_406 = _this;
  _this setDir -84.203789;
  _this setPos [6302.394, 7772.4741, -0.00015258789];
};

_vehicle_414 = objNull;
if (true) then
{
  _this = createVehicle ["Red_Light_Blinking_EP1", [6361.2124, 7772.5352], [], 0, "CAN_COLLIDE"];
  _vehicle_414 = _this;
  _this setPos [6361.2124, 7772.5352];
};

_vehicle_416 = objNull;
if (true) then
{
  _this = createVehicle ["Red_Light_Blinking_EP1", [6361.7104, 7780.3989, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_416 = _this;
  _this setPos [6361.7104, 7780.3989, 6.1035156e-005];
};

_vehicle_418 = objNull;
if (true) then
{
  _this = createVehicle ["Red_Light_Blinking_EP1", [6275.478, 7818.3066, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_418 = _this;
  _this setPos [6275.478, 7818.3066, 3.0517578e-005];
};

_vehicle_420 = objNull;
if (true) then
{
  _this = createVehicle ["Red_Light_Blinking_EP1", [6276.4512, 7824.5557, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_420 = _this;
  _this setPos [6276.4512, 7824.5557, 0];
};

_vehicle_423 = objNull;
if (true) then
{
  _this = createVehicle ["MetalBucket", [6285.6523, 7830.4604], [], 0, "CAN_COLLIDE"];
  _vehicle_423 = _this;
  _this setPos [6285.6523, 7830.4604];
};

_vehicle_428 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_crates_EP1", [6308.6909, 7823.8149], [], 0, "CAN_COLLIDE"];
  _vehicle_428 = _this;
  _this setDir 31.255358;
  _this setPos [6308.6909, 7823.8149];
};

_vehicle_431 = objNull;
if (true) then
{
  _this = createVehicle ["PowerGenerator", [6315.0771, 7817.0186], [], 0, "CAN_COLLIDE"];
  _vehicle_431 = _this;
  _this setDir 26.698105;
  _this setPos [6315.0771, 7817.0186];
};

_vehicle_434 = objNull;
if (true) then
{
  _this = createVehicle ["HeliHCivil", [6374.8789, 7766.8564, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_434 = _this;
  _this setPos [6374.8789, 7766.8564, -3.0517578e-005];
};

_vehicle_435 = objNull;
if (true) then
{
  _this = createVehicle ["HeliHCivil", [6389.0425, 7793.7563, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_435 = _this;
  _this setPos [6389.0425, 7793.7563, 3.0517578e-005];
};

_vehicle_436 = objNull;
if (true) then
{
  _this = createVehicle ["HeliHCivil", [6344.6348, 7780.9521, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_436 = _this;
  _this setPos [6344.6348, 7780.9521, 3.0517578e-005];
};

_vehicle_441 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta2", [6303.5903, 7825.7495, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_441 = _this;
  _this setDir -24.880373;
  _this setPos [6303.5903, 7825.7495, -3.0517578e-005];
};

_vehicle_443 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_can", [6304.8862, 7829.4556], [], 0, "CAN_COLLIDE"];
  _vehicle_443 = _this;
  _this setDir 13.838522;
  _this setPos [6304.8862, 7829.4556];
};

_vehicle_448 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_can", [6307.8604, 7828.2139, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_448 = _this;
  _this setDir 13.838522;
  _this setPos [6307.8604, 7828.2139, 3.0517578e-005];
};

_vehicle_452 = objNull;
if (true) then
{
  _this = createVehicle ["Land_radar", [6293.9854, 7859.6138, -0.082470872], [], 0, "CAN_COLLIDE"];
  _vehicle_452 = _this;
  _this setPos [6293.9854, 7859.6138, -0.082470872];
};

_vehicle_453 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6276.6807, 7825.5996, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_453 = _this;
  _this setPos [6276.6807, 7825.5996, 3.0517578e-005];
};

_vehicle_455 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6316.1133, 7818.3232], [], 0, "CAN_COLLIDE"];
  _vehicle_455 = _this;
  _this setPos [6316.1133, 7818.3232];
};

_vehicle_458 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6300.0078, 7830.9507], [], 0, "CAN_COLLIDE"];
  _vehicle_458 = _this;
  _this setPos [6300.0078, 7830.9507];
};

_vehicle_460 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6279.355, 7812.353], [], 0, "CAN_COLLIDE"];
  _vehicle_460 = _this;
  _this setPos [6279.355, 7812.353];
};

_vehicle_462 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6284.0493, 7803.394, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_462 = _this;
  _this setPos [6284.0493, 7803.394, -3.0517578e-005];
};

_vehicle_464 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6292.8384, 7796.6416], [], 0, "CAN_COLLIDE"];
  _vehicle_464 = _this;
  _this setPos [6292.8384, 7796.6416];
};

_vehicle_466 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6289.9175, 7794.0581, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_466 = _this;
  _this setPos [6289.9175, 7794.0581, -3.0517578e-005];
};

_vehicle_468 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6286.1426, 7797.2012, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_468 = _this;
  _this setPos [6286.1426, 7797.2012, -3.0517578e-005];
};

_vehicle_470 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6354.5049, 7768.668], [], 0, "CAN_COLLIDE"];
  _vehicle_470 = _this;
  _this setPos [6354.5049, 7768.668];
};

_vehicle_472 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6357.6616, 7787.457, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_472 = _this;
  _this setPos [6357.6616, 7787.457, 3.0517578e-005];
};

_vehicle_474 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6355.3208, 7784.0239, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_474 = _this;
  _this setPos [6355.3208, 7784.0239, -3.0517578e-005];
};

_vehicle_476 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6335.8926, 7769.8457, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_476 = _this;
  _this setPos [6335.8926, 7769.8457, -3.0517578e-005];
};

_vehicle_478 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6327.4937, 7772.98], [], 0, "CAN_COLLIDE"];
  _vehicle_478 = _this;
  _this setPos [6327.4937, 7772.98];
};

_vehicle_481 = objNull;
if (true) then
{
  _this = createVehicle ["EntranceGate_EP1", [6275.2148, 7821.6226], [], 0, "CAN_COLLIDE"];
  _vehicle_481 = _this;
  _this setDir -89.408531;
  _this setPos [6275.2148, 7821.6226];
};

_vehicle_482 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierCDF_EP1", [6272.3301, 7828.062], [], 0, "CAN_COLLIDE"];
  _vehicle_482 = _this;
  _this setPos [6272.3301, 7828.062];
};

_vehicle_483 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierNATO_EP1", [6271.4897, 7816.8232, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_483 = _this;
  _this setPos [6271.4897, 7816.8232, -3.0517578e-005];
};

_vehicle_486 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_palletsfoiled", [6296.624, 7825.3506, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_486 = _this;
  _this setDir -57.383591;
  _this setPos [6296.624, 7825.3506, 6.1035156e-005];
};

_vehicle_489 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Campfire_burning", [6347.7832, 7796.4995, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_489 = _this;
  _this setPos [6347.7832, 7796.4995, 6.1035156e-005];
};

_vehicle_490 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_waterl_EP1", [6300.8608, 7825.4287], [], 0, "CAN_COLLIDE"];
  _vehicle_490 = _this;
  _this setDir -4.5633855;
  _this setPos [6300.8608, 7825.4287];
};

_vehicle_499 = objNull;
if (true) then
{
  _this = createVehicle ["Satelit", [6314.8516, 7817.083, 1.5414408], [], 0, "CAN_COLLIDE"];
  _vehicle_499 = _this;
  _this setDir 97.694138;
  _this setPos [6314.8516, 7817.083, 1.5414408];
};

_vehicle_500 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Pneu", [6309.0469, 7826.9038, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_500 = _this;
  _this setPos [6309.0469, 7826.9038, -3.0517578e-005];
};

_vehicle_510 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6295.2954, 7824.5029, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_510 = _this;
  _this setDir -126.97519;
  _this setPos [6295.2954, 7824.5029, 3.0517578e-005];
};

_vehicle_512 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6299.2207, 7821.356], [], 0, "CAN_COLLIDE"];
  _vehicle_512 = _this;
  _this setDir -144.69588;
  _this setPos [6299.2207, 7821.356];
};

_vehicle_514 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6303.6841, 7818.1787, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_514 = _this;
  _this setDir -147.12917;
  _this setPos [6303.6841, 7818.1787, 3.0517578e-005];
};

_vehicle_516 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6307.6016, 7815.6519, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_516 = _this;
  _this setDir -153.49696;
  _this setPos [6307.6016, 7815.6519, -3.0517578e-005];
};

_vehicle_518 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [6311.8853, 7813.8623, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_518 = _this;
  _this setDir -160.95456;
  _this setPos [6311.8853, 7813.8623, 3.0517578e-005];
};

_vehicle_520 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_betula2s", [6327.1211, 7811.2944, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_520 = _this;
  _this setDir 33.51334;
  _this setPos [6327.1211, 7811.2944, 6.1035156e-005];
};

_vehicle_523 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Campfire_burning", [6291.4658, 7829.3965, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_523 = _this;
  _this setPos [6291.4658, 7829.3965, -3.0517578e-005];
};

_vehicle_526 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6268.9409, 7817.3481], [], 0, "CAN_COLLIDE"];
  _vehicle_526 = _this;
  _this setPos [6268.9409, 7817.3481];
};

_vehicle_529 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6256.2998, 7814.1392, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_529 = _this;
  _this setPos [6256.2998, 7814.1392, -0.00012207031];
};

_vehicle_531 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6247.73, 7806.6582, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_531 = _this;
  _this setPos [6247.73, 7806.6582, -6.1035156e-005];
};

_vehicle_533 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6241.6646, 7798.5527, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_533 = _this;
  _this setPos [6241.6646, 7798.5527, -9.1552734e-005];
};

_vehicle_535 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6268.7085, 7827.6147, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_535 = _this;
  _this setPos [6268.7085, 7827.6147, 3.0517578e-005];
};

_vehicle_537 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6253.6787, 7824.8345, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_537 = _this;
  _this setPos [6253.6787, 7824.8345, -3.0517578e-005];
};

_vehicle_539 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6239.7695, 7813.7334, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_539 = _this;
  _this setPos [6239.7695, 7813.7334, -3.0517578e-005];
};

_vehicle_541 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6233.3516, 7803.7124, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_541 = _this;
  _this setPos [6233.3516, 7803.7124, -9.1552734e-005];
};

_vehicle_543 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_vez", [6344.9258, 7803.1162, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_543 = _this;
  _this setDir 40.580322;
  _this setPos [6344.9258, 7803.1162, -3.0517578e-005];
};

_vehicle_546 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CamoNet_EAST_var1", [6351.6099, 7794.458], [], 0, "CAN_COLLIDE"];
  _vehicle_546 = _this;
  _this setDir 31.429899;
  _this setPos [6351.6099, 7794.458];
};

_vehicle_549 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fort_watchtower", [6339.9336, 7801.4951, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_549 = _this;
  _this setDir -139.12798;
  _this setPos [6339.9336, 7801.4951, 6.1035156e-005];
};

_vehicle_552 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barbedwire", [6358.8071, 7761.3911], [], 0, "CAN_COLLIDE"];
  _vehicle_552 = _this;
  _this setDir -24.05636;
  _this setPos [6358.8071, 7761.3911];
};

_vehicle_554 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barbedwire", [6364.0347, 7766.5913, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_554 = _this;
  _this setDir -76.275742;
  _this setPos [6364.0347, 7766.5913, -6.1035156e-005];
};

_vehicle_556 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barbedwire", [6365.8555, 7784.0283], [], 0, "CAN_COLLIDE"];
  _vehicle_556 = _this;
  _this setDir -86.712158;
  _this setPos [6365.8555, 7784.0283];
};

_vehicle_558 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barbedwire", [6363.6953, 7792.4595, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_558 = _this;
  _this setDir -129.16788;
  _this setPos [6363.6953, 7792.4595, -3.0517578e-005];
};

_vehicle_560 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fort_rampart", [6335.8779, 7814.627, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_560 = _this;
  _this setDir -144.14178;
  _this setPos [6335.8779, 7814.627, -3.0517578e-005];
};

_vehicle_577 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_RUSpecialWeapons", [6333.0762, 7809.8594, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_577 = _this;
  _this setDir 37.533901;
  _this setPos [6333.0762, 7809.8594, -3.0517578e-005];
};

_vehicle_580 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_USSpecialWeapons", [6336.2461, 7807.4546], [], 0, "CAN_COLLIDE"];
  _vehicle_580 = _this;
  _this setDir -51.154541;
  _this setPos [6336.2461, 7807.4546];
};

_vehicle_581 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_USVehicleAmmo", [6332.3525, 7806.2041, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_581 = _this;
  _this setPos [6332.3525, 7806.2041, 3.0517578e-005];
};

};
