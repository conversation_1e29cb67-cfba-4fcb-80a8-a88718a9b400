private ["_name","_item","_player","_playerID","_clientID","_able","_query","_result","_return","_class","_price","_query1"];

_player = 	_this select 0;
_item = 	_this select 1;
_playerID = getPlayerUID _player;
_clientID = owner _player;
_name = name _player;
_able = 	false;

_query = format["SELECT PlayerUID, Classname, Cost, Type, PlayerName FROM customTrade WHERE ID='%1'",_item];
_result = [_query, 2, true] call fn_asyncCall;

waitUntil {!isNil "_result"};

if (count _result > 0) then {
	_able = true;
	_return = _result select 0;
	_class = _return select 1;
	_price = _return select 2;

	_query1 = format["DELETE FROM customTrade WHERE ID='%1'",_item];
	[_query1, 1, true] call fn_asyncCall;

	diag_log format["[O9 Custom Trading]: Player %3 (%1) removed their %2 for a price of %4 from the auctionhouse!",_playerID,_class,_name,_price];
} else {
	diag_log format["[O9 Custom Trading]: ID: %1 not found in Database.",_item];
};

PVDZE_removeItemResult = _able;

if(!isNull _player) then {
	_clientID publicVariableClient "PVDZE_removeItemResult";
};
