if (isserver) then {

_vehicle_1 = objNull;
if (true) then
{
  _this = createVehicle ["MetalBucket", [4275.3569, 12977.294, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_1 = _this;
  _this setDir -3563.5422;
  _this setPos [4275.3569, 12977.294, 9.1552734e-005];
};

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [1866.1185, 12361.729, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir 92.532265;
  _this setPos [1866.1185, 12361.729, 6.1035156e-005];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [1872.2615, 12361.47, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setDir 93.642242;
  _this setPos [1872.2615, 12361.47, 0.00012207031];
};

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [1913.6655, 12353.273, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setDir -77.287674;
  _this setPos [1913.6655, 12353.273, -1.5258789e-005];
};

_vehicle_8 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [1937.9733, 12347.822], [], 0, "CAN_COLLIDE"];
  _vehicle_8 = _this;
  _this setDir -77.287674;
  _this setPos [1937.9733, 12347.822];
};

_vehicle_9 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [1962.0695, 12342.277, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_9 = _this;
  _this setDir -77.287674;
  _this setPos [1962.0695, 12342.277, 0.00018310547];
};

_vehicle_10 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [1986.2379, 12336.691, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_10 = _this;
  _this setDir -76.73278;
  _this setPos [1986.2379, 12336.691, -1.5258789e-005];
};

_vehicle_12 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2003.3959, 12334.188, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_12 = _this;
  _this setDir -86.599487;
  _this setPos [2003.3959, 12334.188, -4.5776367e-005];
};

_vehicle_13 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2020.6415, 12334.884, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_13 = _this;
  _this setDir -97.337555;
  _this setPos [2020.6415, 12334.884, -1.5258789e-005];
};

_vehicle_14 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2020.564, 12334.873], [], 0, "CAN_COLLIDE"];
  _vehicle_14 = _this;
  _this setDir 82.731766;
  _this setPos [2020.564, 12334.873];
};

_vehicle_15 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2037.9778, 12335.615, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_15 = _this;
  _this setDir 92.539452;
  _this setPos [2037.9778, 12335.615, -3.0517578e-005];
};

_vehicle_16 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2055.0422, 12333.361], [], 0, "CAN_COLLIDE"];
  _vehicle_16 = _this;
  _this setDir 101.4978;
  _this setPos [2055.0422, 12333.361];
};

_vehicle_17 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2071.6848, 12328.456, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_17 = _this;
  _this setDir 111.51448;
  _this setPos [2071.6848, 12328.456, -6.1035156e-005];
};

_vehicle_18 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2087.2524, 12320.734, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_18 = _this;
  _this setDir 121.72868;
  _this setPos [2087.2524, 12320.734, 9.1552734e-005];
};

_vehicle_19 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2133.6875, 12283.525, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_19 = _this;
  _this setDir -58.693222;
  _this setPos [2133.6875, 12283.525, 3.0517578e-005];
};

_vehicle_20 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2119.7053, 12293.77], [], 0, "CAN_COLLIDE"];
  _vehicle_20 = _this;
  _this setDir 311.81839;
  _this setPos [2119.7053, 12293.77];
};

_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2154.999, 12270.768, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir -59.113518;
  _this setPos [2154.999, 12270.768, 6.1035156e-005];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2176.0195, 12257.953, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir -58.480595;
  _this setPos [2176.0195, 12257.953, -1.5258789e-005];
};

_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2191.4241, 12249.984, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir -67.551323;
  _this setPos [2191.4241, 12249.984, -6.1035156e-005];
};

_vehicle_24 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2237.269, 12230.542, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_24 = _this;
  _this setDir -67.03743;
  _this setPos [2237.269, 12230.542, 0.0001373291];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2214.3687, 12240.252, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir -67.03743;
  _this setPos [2214.3687, 12240.252, 4.5776367e-005];
};

_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2260.1196, 12220.792, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setDir -67.035225;
  _this setPos [2260.1196, 12220.792, 6.1035156e-005];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2283.1033, 12211.276, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir 292.48611;
  _this setPos [2283.1033, 12211.276, -0.00010681152];
};

_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2305.9204, 12201.756], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setDir 292.48611;
  _this setPos [2305.9204, 12201.756];
};

_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2328.9399, 12192.358, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir -247.89735;
  _this setPos [2328.9399, 12192.358, -3.0517578e-005];
};

_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2305.8049, 12201.81, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir -247.72548;
  _this setPos [2305.8049, 12201.81, -9.1552734e-005];
};

_vehicle_37 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_0_2000", [2351.9292, 12183.016, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_37 = _this;
  _this setDir 112.049;
  _this setPos [2351.9292, 12183.016, -7.6293945e-005];
};

_vehicle_38 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [2387.1733, 12172.917, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_38 = _this;
  _this setDir -90.922867;
  _this setPos [2387.1733, 12172.917, -7.6293945e-005];
};

_vehicle_40 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_0_2000", [2404.4341, 12173.092, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_40 = _this;
  _this setDir -90.660706;
  _this setPos [2404.4341, 12173.092, 4.5776367e-005];
};

_vehicle_42 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [2423.4907, 12177, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_42 = _this;
  _this setDir -112.76065;
  _this setPos [2423.4907, 12177, 9.1552734e-005];
};

_vehicle_43 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_22_50", [2439.6157, 12187.985, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_43 = _this;
  _this setDir -135.49893;
  _this setPos [2439.6157, 12187.985, -3.0517578e-005];
};

_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2457.1687, 12205.718, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setDir -135.28015;
  _this setPos [2457.1687, 12205.718, -1.5258789e-005];
};

_vehicle_45 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2474.9038, 12223.198, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_45 = _this;
  _this setDir -134.54077;
  _this setPos [2474.9038, 12223.198, 6.1035156e-005];
};

_vehicle_46 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2492.6917, 12240.58, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_46 = _this;
  _this setDir -134.24738;
  _this setPos [2492.6917, 12240.58, 3.0517578e-005];
};

_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2497.0754, 12244.869, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setDir 45.158401;
  _this setPos [2497.0754, 12244.869, 3.0517578e-005];
};

_vehicle_48 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2510.3655, 12255.989, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_48 = _this;
  _this setDir 56.105293;
  _this setPos [2510.3655, 12255.989, -3.0517578e-005];
};

_vehicle_49 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2525.5198, 12264.403, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_49 = _this;
  _this setDir 65.686363;
  _this setPos [2525.5198, 12264.403, -3.0517578e-005];
};

_vehicle_50 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2541.9395, 12270.158], [], 0, "CAN_COLLIDE"];
  _vehicle_50 = _this;
  _this setDir 75.341736;
  _this setPos [2541.9395, 12270.158];
};

_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [2492.6377, 12240.541, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setDir 45.237267;
  _this setPos [2492.6377, 12240.541, 1.5258789e-005];
};

_vehicle_52 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_0_2000", [2558.9636, 12273.071, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_52 = _this;
  _this setDir 85.97728;
  _this setPos [2558.9636, 12273.071, 1.5258789e-005];
};

_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2593.4041, 12276.796, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setDir -103.69901;
  _this setPos [2593.4041, 12276.796, 4.5776367e-005];
};

_vehicle_56 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2645.7932, 12304.164, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_56 = _this;
  _this setDir -122.1442;
  _this setPos [2645.7932, 12304.164, 6.1035156e-005];
};

_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2666.8218, 12317.428, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setDir -122.52628;
  _this setPos [2666.8218, 12317.428, 9.1552734e-005];
};

_vehicle_58 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2609.8201, 12282.543, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_58 = _this;
  _this setDir -114.29415;
  _this setPos [2609.8201, 12282.543, 6.1035156e-005];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2624.8435, 12290.999, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir -124.24756;
  _this setPos [2624.8435, 12290.999, 1.5258789e-005];
};

_vehicle_60 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2687.7344, 12331.015, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_60 = _this;
  _this setDir -123.46096;
  _this setPos [2687.7344, 12331.015, 1.5258789e-005];
};

_vehicle_61 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2708.7852, 12344.374, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_61 = _this;
  _this setDir -122.40861;
  _this setPos [2708.7852, 12344.374, 9.1552734e-005];
};

_vehicle_62 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2729.9756, 12357.443, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_62 = _this;
  _this setDir -121.71666;
  _this setPos [2729.9756, 12357.443, 9.1552734e-005];
};

_vehicle_63 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2751.1445, 12370.557, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_63 = _this;
  _this setDir -121.91878;
  _this setPos [2751.1445, 12370.557, -9.1552734e-005];
};

_vehicle_64 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2772.0723, 12383.96, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_64 = _this;
  _this setDir -122.78027;
  _this setPos [2772.0723, 12383.96, 7.6293945e-005];
};

_vehicle_65 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2792.9097, 12397.416, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_65 = _this;
  _this setDir -122.78027;
  _this setPos [2792.9097, 12397.416, 4.5776367e-005];
};

_vehicle_66 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2813.9058, 12410.87, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_66 = _this;
  _this setDir -122.52063;
  _this setPos [2813.9058, 12410.87, 3.0517578e-005];
};

_vehicle_67 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2834.8423, 12424.219, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_67 = _this;
  _this setDir -122.52063;
  _this setPos [2834.8423, 12424.219, 4.5776367e-005];
};

_vehicle_69 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2855.9033, 12437.576, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_69 = _this;
  _this setDir -122.24912;
  _this setPos [2855.9033, 12437.576, 9.1552734e-005];
};

_vehicle_76 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2876.7681, 12450.871], [], 0, "CAN_COLLIDE"];
  _vehicle_76 = _this;
  _this setDir -122.49984;
  _this setPos [2876.7681, 12450.871];
};

_vehicle_78 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2890.5576, 12461.437, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_78 = _this;
  _this setDir -132.36032;
  _this setPos [2890.5576, 12461.437, -9.1552734e-005];
};

_vehicle_79 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2902.2686, 12474.238], [], 0, "CAN_COLLIDE"];
  _vehicle_79 = _this;
  _this setDir -142.52266;
  _this setPos [2902.2686, 12474.238];
};

_vehicle_80 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2911.3857, 12488.83, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_80 = _this;
  _this setDir -152.92049;
  _this setPos [2911.3857, 12488.83, 0.00015258789];
};

_vehicle_81 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2922.5708, 12510.83, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_81 = _this;
  _this setDir -152.98549;
  _this setPos [2922.5708, 12510.83, 6.1035156e-005];
};

_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2925.3665, 12516.204, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setDir 26.610163;
  _this setPos [2925.3665, 12516.204, 3.0517578e-005];
};

_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2934.3672, 12530.906, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setDir 35.680779;
  _this setPos [2934.3672, 12530.906, 3.0517578e-005];
};

_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2945.6919, 12544.061, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setDir 45.52597;
  _this setPos [2945.6919, 12544.061, 1.5258789e-005];
};

_vehicle_85 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2959.0144, 12555.029, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_85 = _this;
  _this setDir 55.097858;
  _this setPos [2959.0144, 12555.029, -4.5776367e-005];
};

_vehicle_86 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [2922.5547, 12510.814, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_86 = _this;
  _this setDir 27.110859;
  _this setPos [2922.5547, 12510.814, 4.5776367e-005];
};

_vehicle_87 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2973.9966, 12563.654, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_87 = _this;
  _this setDir 65.362709;
  _this setPos [2973.9966, 12563.654, -9.1552734e-005];
};

_vehicle_88 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [3006.7917, 12573.646, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_88 = _this;
  _this setDir 75.470818;
  _this setPos [3006.7917, 12573.646, 3.0517578e-005];
};

_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [3023.8899, 12576.506, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir 85.154205;
  _this setPos [3023.8899, 12576.506, -1.5258789e-005];
};

_vehicle_91 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_0_2000", [2990.3225, 12569.514, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_91 = _this;
  _this setDir 75.462593;
  _this setPos [2990.3225, 12569.514, -6.1035156e-005];
};

_vehicle_92 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3065.9272, 12573.384, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_92 = _this;
  _this setDir -82.897362;
  _this setPos [3065.9272, 12573.384, 4.5776367e-005];
};

_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3090.7056, 12570.274], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setDir -82.897362;
  _this setPos [3090.7056, 12570.274];
};

_vehicle_96 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_0_2000", [3108.0317, 12568.198, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_96 = _this;
  _this setDir -83.361977;
  _this setPos [3108.0317, 12568.198, 7.6293945e-005];
};

_vehicle_101 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3132.7788, 12565.126, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_101 = _this;
  _this setDir -82.982101;
  _this setPos [3132.7788, 12565.126, 1.5258789e-005];
};

_vehicle_103 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3157.311, 12561.696], [], 0, "CAN_COLLIDE"];
  _vehicle_103 = _this;
  _this setDir 97.714088;
  _this setPos [3157.311, 12561.696];
};

_vehicle_104 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3157.3953, 12561.679, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_104 = _this;
  _this setDir -82.004303;
  _this setPos [3157.3953, 12561.679, 0.00022888184];
};

_vehicle_105 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3206.824, 12555.024, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_105 = _this;
  _this setDir -82.35202;
  _this setPos [3206.824, 12555.024, -9.1552734e-005];
};

_vehicle_107 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [3224.2312, 12554.347, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_107 = _this;
  _this setDir -92.709923;
  _this setPos [3224.2312, 12554.347, 0.00016784668];
};

_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [3241.4048, 12556.6, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setDir -102.41763;
  _this setPos [3241.4048, 12556.6, 0.0001373291];
};

_vehicle_109 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [3258.0027, 12561.783, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_109 = _this;
  _this setDir -112.23093;
  _this setPos [3258.0027, 12561.783, 9.1552734e-005];
};

_vehicle_110 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3281.1909, 12570.981, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_110 = _this;
  _this setDir -111.64008;
  _this setPos [3281.1909, 12570.981, 0.0001373291];
};

_vehicle_111 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3304.2031, 12580.027, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_111 = _this;
  _this setDir -111.64008;
  _this setPos [3304.2031, 12580.027, 0.00012207031];
};

_vehicle_112 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3327.3606, 12589.26, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_112 = _this;
  _this setDir -111.88274;
  _this setPos [3327.3606, 12589.26, 7.6293945e-005];
};

_vehicle_113 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3350.502, 12598.57, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_113 = _this;
  _this setDir -111.88274;
  _this setPos [3350.502, 12598.57, 0.00010681152];
};

_vehicle_114 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3373.665, 12607.929, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_114 = _this;
  _this setDir -112.21402;
  _this setPos [3373.665, 12607.929, 1.5258789e-005];
};

_vehicle_115 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3396.769, 12617.356, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_115 = _this;
  _this setDir -112.21402;
  _this setPos [3396.769, 12617.356, 7.6293945e-005];
};

_vehicle_116 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3419.884, 12626.846, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_116 = _this;
  _this setDir -112.26342;
  _this setPos [3419.884, 12626.846, 3.0517578e-005];
};

_vehicle_117 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3442.9819, 12636.234, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_117 = _this;
  _this setDir -112.19778;
  _this setPos [3442.9819, 12636.234, -4.5776367e-005];
};

_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3466.1104, 12645.65, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setDir -111.97103;
  _this setPos [3466.1104, 12645.65, 6.1035156e-005];
};

_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3489.262, 12655.018], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setDir -111.97103;
  _this setPos [3489.262, 12655.018];
};

_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3512.3315, 12664.329, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir -111.97103;
  _this setPos [3512.3315, 12664.329, 0.00019836426];
};

_vehicle_121 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3558.7449, 12682.606, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_121 = _this;
  _this setDir -111.33475;
  _this setPos [3558.7449, 12682.606, 0.00010681152];
};

_vehicle_122 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3535.5005, 12673.538, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_122 = _this;
  _this setDir -111.33475;
  _this setPos [3535.5005, 12673.538, 9.1552734e-005];
};

_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3582.0081, 12691.711, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setDir -111.33475;
  _this setPos [3582.0081, 12691.711, 0.00010681152];
};

_vehicle_124 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [3620.2559, 12709.032, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_124 = _this;
  _this setDir -123.43324;
  _this setPos [3620.2559, 12709.032, 1.5258789e-005];
};

_vehicle_126 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3605.2026, 12700.851, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_126 = _this;
  _this setDir -111.75002;
  _this setPos [3605.2026, 12700.851, -7.6293945e-005];
};

_vehicle_127 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3640.9341, 12722.755, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_127 = _this;
  _this setDir -123.59092;
  _this setPos [3640.9341, 12722.755, 6.1035156e-005];
};

_vehicle_128 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3661.4001, 12736.898, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_128 = _this;
  _this setDir -124.63544;
  _this setPos [3661.4001, 12736.898, 0.00015258789];
};

_vehicle_129 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3661.2466, 12736.787, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_129 = _this;
  _this setDir 54.577217;
  _this setPos [3661.2466, 12736.787, 9.1552734e-005];
};

_vehicle_130 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3681.5056, 12751.188, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_130 = _this;
  _this setDir 54.493641;
  _this setPos [3681.5056, 12751.188, 0.0001373291];
};

_vehicle_131 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3701.8062, 12765.631, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_131 = _this;
  _this setDir 54.589397;
  _this setPos [3701.8062, 12765.631, -0.00010681152];
};

_vehicle_132 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3721.9868, 12779.892, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_132 = _this;
  _this setDir 54.583214;
  _this setPos [3721.9868, 12779.892, -0.00015258789];
};

_vehicle_133 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [3742.3445, 12794.381, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_133 = _this;
  _this setDir 54.519901;
  _this setPos [3742.3445, 12794.381, 0.00012207031];
};

_vehicle_134 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [3757.1372, 12803.135, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_134 = _this;
  _this setDir 63.349125;
  _this setPos [3757.1372, 12803.135, 3.0517578e-005];
};

_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3796.8147, 12817.427, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir -108.54182;
  _this setPos [3796.8147, 12817.427, -4.5776367e-005];
};

_vehicle_136 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3820.3342, 12825.22, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_136 = _this;
  _this setDir -108.54182;
  _this setPos [3820.3342, 12825.22, 0.00019836426];
};

_vehicle_137 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3843.9927, 12833.212, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_137 = _this;
  _this setDir -108.76432;
  _this setPos [3843.9927, 12833.212, 7.6293945e-005];
};

_vehicle_138 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3867.6255, 12841.191, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_138 = _this;
  _this setDir -108.76432;
  _this setPos [3867.6255, 12841.191, 0.00015258789];
};

_vehicle_139 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3890.9019, 12849.721, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_139 = _this;
  _this setDir -110.1577;
  _this setPos [3890.9019, 12849.721, 0.00015258789];
};

_vehicle_140 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3914.0581, 12858.789, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_140 = _this;
  _this setDir -111.4057;
  _this setPos [3914.0581, 12858.789, 0.0001373291];
};

_vehicle_141 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3937.2395, 12867.889, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_141 = _this;
  _this setDir -111.4057;
  _this setPos [3937.2395, 12867.889, 9.1552734e-005];
};

_vehicle_142 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3960.3025, 12876.914, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_142 = _this;
  _this setDir -111.4057;
  _this setPos [3960.3025, 12876.914, 0.00016784668];
};

_vehicle_143 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [3983.5271, 12886.011, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_143 = _this;
  _this setDir -111.4057;
  _this setPos [3983.5271, 12886.011, 0.00012207031];
};

_vehicle_144 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4006.5916, 12895.005, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_144 = _this;
  _this setDir -111.44606;
  _this setPos [4006.5916, 12895.005, -3.0517578e-005];
};

_vehicle_145 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4029.7139, 12904.061, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_145 = _this;
  _this setDir -111.44606;
  _this setPos [4029.7139, 12904.061, 0.00016784668];
};

_vehicle_147 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [4029.5693, 12904.01, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_147 = _this;
  _this setDir 68.34935;
  _this setPos [4029.5693, 12904.01, 0.0001373291];
};

_vehicle_149 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [4046.2349, 12908.973, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_149 = _this;
  _this setDir 78.109261;
  _this setPos [4046.2349, 12908.973, 0.00012207031];
};

_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4088.3704, 12911.796, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setDir -91.778069;
  _this setPos [4088.3704, 12911.796, 4.5776367e-005];
};

_vehicle_151 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4113.2422, 12912.533, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_151 = _this;
  _this setDir -91.778069;
  _this setPos [4113.2422, 12912.533, -1.5258789e-005];
};

_vehicle_152 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4138.0557, 12913.296, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_152 = _this;
  _this setDir -91.778069;
  _this setPos [4138.0557, 12913.296, 4.5776367e-005];
};

_vehicle_153 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4163.0449, 12914.033, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_153 = _this;
  _this setDir -91.778069;
  _this setPos [4163.0449, 12914.033, -6.1035156e-005];
};

_vehicle_154 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4187.9229, 12914.827, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_154 = _this;
  _this setDir -91.847153;
  _this setPos [4187.9229, 12914.827, 0.0001373291];
};

_vehicle_156 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [4187.8364, 12914.835, 0.0002746582], [], 0, "CAN_COLLIDE"];
  _vehicle_156 = _this;
  _this setDir 88.372398;
  _this setPos [4187.8364, 12914.835, 0.0002746582];
};

_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4229.9458, 12910.198, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setDir -81.657646;
  _this setPos [4229.9458, 12910.198, 0.00018310547];
};

_vehicle_159 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4254.6333, 12906.573, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_159 = _this;
  _this setDir -81.657646;
  _this setPos [4254.6333, 12906.573, 6.1035156e-005];
};

_vehicle_160 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4279.2734, 12902.914, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_160 = _this;
  _this setDir -81.657646;
  _this setPos [4279.2734, 12902.914, 0.0001373291];
};

_vehicle_161 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4319.8096, 12891.231, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_161 = _this;
  _this setDir -71.631798;
  _this setPos [4319.8096, 12891.231, -7.6293945e-005];
};

_vehicle_162 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [4279.2275, 12902.908, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_162 = _this;
  _this setDir 97.715065;
  _this setPos [4279.2275, 12902.908, 6.1035156e-005];
};

_vehicle_163 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4357.5991, 12872.48, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_163 = _this;
  _this setDir -61.531082;
  _this setPos [4357.5991, 12872.48, 7.6293945e-005];
};

_vehicle_164 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4379.501, 12860.636, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_164 = _this;
  _this setDir -61.531082;
  _this setPos [4379.501, 12860.636, -1.5258789e-005];
};

_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4401.4263, 12848.793, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setDir -61.531082;
  _this setPos [4401.4263, 12848.793, 9.1552734e-005];
};

_vehicle_166 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4423.332, 12836.891, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_166 = _this;
  _this setDir -61.531082;
  _this setPos [4423.332, 12836.891, 0.00019836426];
};

_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4445.2075, 12825.085, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setDir -61.531082;
  _this setPos [4445.2075, 12825.085, -7.6293945e-005];
};

_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4467.1279, 12813.191, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setDir -61.531082;
  _this setPos [4467.1279, 12813.191, 0.00019836426];
};

_vehicle_169 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [4319.7241, 12891.288, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_169 = _this;
  _this setDir 108.45475;
  _this setPos [4319.7241, 12891.288, -3.0517578e-005];
};

_vehicle_170 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4489.0693, 12801.435, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_170 = _this;
  _this setDir -61.880249;
  _this setPos [4489.0693, 12801.435, 3.0517578e-005];
};

_vehicle_171 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4511.0859, 12789.65, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_171 = _this;
  _this setDir -61.881725;
  _this setPos [4511.0859, 12789.65, 9.1552734e-005];
};

_vehicle_172 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [4516.5649, 12786.739, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_172 = _this;
  _this setDir -62.037762;
  _this setPos [4516.5649, 12786.739, 0.00012207031];
};

_vehicle_173 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [4249.2236, 12906.396], [], 0, "CAN_COLLIDE"];
  _vehicle_173 = _this;
  _this setDir 10.2408;
  _this setPos [4249.2236, 12906.396];
};

_vehicle_174 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [4250.3086, 12912.382, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_174 = _this;
  _this setDir 9.5600834;
  _this setPos [4250.3086, 12912.382, 3.0517578e-005];
};

_vehicle_175 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [4254.5454, 12928.971, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_175 = _this;
  _this setDir 21.016487;
  _this setPos [4254.5454, 12928.971, 1.5258789e-005];
};

_vehicle_176 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [4262.1406, 12944.509], [], 0, "CAN_COLLIDE"];
  _vehicle_176 = _this;
  _this setDir 30.902843;
  _this setPos [4262.1406, 12944.509];
};

_vehicle_178 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [4272.188, 12958.423, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_178 = _this;
  _this setDir 38.817139;
  _this setPos [4272.188, 12958.423, 4.5776367e-005];
};

_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [4291.7319, 12982.682, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setDir -140.87584;
  _this setPos [4291.7319, 12982.682, -4.5776367e-005];
};

_vehicle_180 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [4304.7314, 12994.871, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_180 = _this;
  _this setDir 49.068192;
  _this setPos [4304.7314, 12994.871, -4.5776367e-005];
};

_vehicle_181 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_10_100", [4292.437, 12982.68, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_181 = _this;
  _this setDir 40.199364;
  _this setPos [4292.437, 12982.68, -1.5258789e-005];
};

_vehicle_182 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_grav_6konec", [4288.3921, 12977.934, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_182 = _this;
  _this setDir 40.190979;
  _this setPos [4288.3921, 12977.934, -1.5258789e-005];
};

_vehicle_193 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_RoadBarrier_long", [4289.3901, 12976.307, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_193 = _this;
  _this setDir 44.151516;
  _this setPos [4289.3901, 12976.307, 7.6293945e-005];
};

_vehicle_194 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_RoadBarrier_long", [4286.0479, 12978.28, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_194 = _this;
  _this setDir 41.055935;
  _this setPos [4286.0479, 12978.28, 4.5776367e-005];
};

_vehicle_199 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sign_roadworks_new", [4263.3809, 12934.978, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_199 = _this;
  _this setDir 22.649473;
  _this setPos [4263.3809, 12934.978, 3.0517578e-005];
};

_vehicle_201 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [4311.0015, 12975.257, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_201 = _this;
  _this setDir 68.174828;
  _this setPos [4311.0015, 12975.257, 9.1552734e-005];
};

_vehicle_202 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Toilet", [4310.0415, 12977.205, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_202 = _this;
  _this setDir 57.114906;
  _this setPos [4310.0415, 12977.205, -1.5258789e-005];
};

_vehicle_203 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_GContainer_Big", [4274.689, 12980.057, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_203 = _this;
  _this setDir 38.40366;
  _this setPos [4274.689, 12980.057, 0.00016784668];
};

_vehicle_204 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_IronPipes_EP1", [4278.8145, 12986.479, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_204 = _this;
  _this setPos [4278.8145, 12986.479, -1.5258789e-005];
};

_vehicle_205 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_IronPipes_EP1", [4277.8965, 12988.003, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_205 = _this;
  _this setDir 179.61481;
  _this setPos [4277.8965, 12988.003, -3.0517578e-005];
};

_vehicle_206 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_IronPipes_EP1", [4274.4014, 12988.002, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_206 = _this;
  _this setDir -94.029213;
  _this setPos [4274.4014, 12988.002, 1.5258789e-005];
};

_vehicle_207 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta2", [4271.8477, 12977.096, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_207 = _this;
  _this setDir -193.50056;
  _this setPos [4271.8477, 12977.096, 4.5776367e-005];
};

_vehicle_208 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta2", [4272.3252, 12974.396, 0.38694832], [], 0, "CAN_COLLIDE"];
  _vehicle_208 = _this;
  _this setDir -5.7332411;
  _this setPos [4272.3252, 12974.396, 0.38694832];
};

_vehicle_210 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta1", [4271.9507, 12975.204, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_210 = _this;
  _this setPos [4271.9507, 12975.204, 4.5776367e-005];
};

_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta1", [4271.9131, 12975.261, 0.19355692], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setDir 11.880268;
  _this setPos [4271.9131, 12975.261, 0.19355692];
};

_vehicle_212 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta1", [4272.2305, 12976.852, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_212 = _this;
  _this setDir 8.3989353;
  _this setPos [4272.2305, 12976.852, 1.5258789e-005];
};

_vehicle_213 = objNull;
if (true) then
{
  _this = createVehicle ["Paleta1", [4272.1621, 12976.767, 0.1935807], [], 0, "CAN_COLLIDE"];
  _vehicle_213 = _this;
  _this setDir -2.5720603;
  _this setPos [4272.1621, 12976.767, 0.1935807];
};

_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Pneu", [4275.9883, 12985.245], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setPos [4275.9883, 12985.245];
};

_vehicle_215 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Dirthump03_EP1", [4303.9214, 12959.856, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_215 = _this;
  _this setDir 11.920853;
  _this setPos [4303.9214, 12959.856, 7.6293945e-005];
};

_vehicle_216 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Dirthump02_EP1", [4301.4341, 12952.568, -0.2618891], [], 0, "CAN_COLLIDE"];
  _vehicle_216 = _this;
  _this setDir 195.06979;
  _this setPos [4301.4341, 12952.568, -0.2618891];
};

_vehicle_217 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2524.0281, 11768.157, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_217 = _this;
  _this setDir -111.79333;
  _this setPos [2524.0281, 11768.157, 0.0001373291];
};

_vehicle_218 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6", [2529.8005, 11770.321, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_218 = _this;
  _this setDir -110.80219;
  _this setPos [2529.8005, 11770.321, -0.00010681152];
};

_vehicle_220 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2553.0845, 11779.13, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_220 = _this;
  _this setDir -111.14908;
  _this setPos [2553.0845, 11779.13, 9.1552734e-005];
};

_vehicle_222 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2568.1978, 11786.9, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_222 = _this;
  _this setDir -121.70558;
  _this setPos [2568.1978, 11786.9, 0.00024414063];
};

_vehicle_224 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2597.2607, 11805.917, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_224 = _this;
  _this setDir -130.2868;
  _this setPos [2597.2607, 11805.917, 9.1552734e-005];
};

_vehicle_225 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_0_2000", [2568.1282, 11786.902], [], 0, "CAN_COLLIDE"];
  _vehicle_225 = _this;
  _this setDir 58.846924;
  _this setPos [2568.1282, 11786.902];
};

_vehicle_226 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2608.7563, 11818.636, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_226 = _this;
  _this setDir -142.68546;
  _this setPos [2608.7563, 11818.636, 0.00024414063];
};

_vehicle_227 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2617.6404, 11833.486, 0.00022888184], [], 0, "CAN_COLLIDE"];
  _vehicle_227 = _this;
  _this setDir 205.84581;
  _this setPos [2617.6404, 11833.486, 0.00022888184];
};

_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2628.5115, 11855.803, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setDir -154.00081;
  _this setPos [2628.5115, 11855.803, 0.00016784668];
};

_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2628.4565, 11855.632, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setDir 26.132776;
  _this setPos [2628.4565, 11855.632, -4.5776367e-005];
};

_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2639.377, 11877.983, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setDir 26.132776;
  _this setPos [2639.377, 11877.983, 7.6293945e-005];
};

_vehicle_231 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2650.3335, 11900.344, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_231 = _this;
  _this setDir 25.60935;
  _this setPos [2650.3335, 11900.344, -3.0517578e-005];
};

_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2661.0623, 11922.647, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setDir 25.60935;
  _this setPos [2661.0623, 11922.647, 0.00010681152];
};

_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2671.8247, 11945.083, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setDir 25.584816;
  _this setPos [2671.8247, 11945.083, 0.00019836426];
};

_vehicle_234 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2682.5837, 11967.552, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_234 = _this;
  _this setDir 25.584816;
  _this setPos [2682.5837, 11967.552, -4.5776367e-005];
};

_vehicle_235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2693.2417, 11989.86, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_235 = _this;
  _this setDir 25.584816;
  _this setPos [2693.2417, 11989.86, 6.1035156e-005];
};

_vehicle_236 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2703.9275, 12012.235, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_236 = _this;
  _this setDir 25.584816;
  _this setPos [2703.9275, 12012.235, 1.5258789e-005];
};

_vehicle_237 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2714.626, 12034.637, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_237 = _this;
  _this setDir 25.584816;
  _this setPos [2714.626, 12034.637, 4.5776367e-005];
};

_vehicle_238 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2725.3862, 12057.159, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_238 = _this;
  _this setDir 25.383177;
  _this setPos [2725.3862, 12057.159, 1.5258789e-005];
};

_vehicle_239 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2736.0581, 12079.591, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_239 = _this;
  _this setDir 25.383177;
  _this setPos [2736.0581, 12079.591, 4.5776367e-005];
};

_vehicle_240 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2746.7117, 12102.112], [], 0, "CAN_COLLIDE"];
  _vehicle_240 = _this;
  _this setDir 25.383177;
  _this setPos [2746.7117, 12102.112];
};

_vehicle_241 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2757.4265, 12124.608], [], 0, "CAN_COLLIDE"];
  _vehicle_241 = _this;
  _this setDir 25.383177;
  _this setPos [2757.4265, 12124.608];
};

_vehicle_242 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2768.1907, 12147.169, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_242 = _this;
  _this setDir 25.383177;
  _this setPos [2768.1907, 12147.169, 0.00012207031];
};

_vehicle_243 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2778.8645, 12169.704, 0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_243 = _this;
  _this setDir 25.416164;
  _this setPos [2778.8645, 12169.704, 0.00016784668];
};

_vehicle_244 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2789.5837, 12192.208, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_244 = _this;
  _this setDir 25.416164;
  _this setPos [2789.5837, 12192.208, 9.1552734e-005];
};

_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2806.5237, 12230.997, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setDir -163.97383;
  _this setPos [2806.5237, 12230.997, 6.1035156e-005];
};

_vehicle_247 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2809.812, 12247.889, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_247 = _this;
  _this setDir -174.02884;
  _this setPos [2809.812, 12247.889, 7.6293945e-005];
};

_vehicle_248 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2810.0881, 12265.216, 0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_248 = _this;
  _this setDir -184.04626;
  _this setPos [2810.0881, 12265.216, 0.00010681152];
};

_vehicle_249 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2807.1724, 12282.271, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_249 = _this;
  _this setDir 165.38423;
  _this setPos [2807.1724, 12282.271, 9.1552734e-005];
};

_vehicle_250 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_10_100", [2801.1128, 12298.506], [], 0, "CAN_COLLIDE"];
  _vehicle_250 = _this;
  _this setDir 154.66026;
  _this setPos [2801.1128, 12298.506];
};

_vehicle_251 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2801.4517, 12297.748, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_251 = _this;
  _this setDir -25.285599;
  _this setPos [2801.4517, 12297.748, 4.5776367e-005];
};

_vehicle_252 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2790.7876, 12320.289, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_252 = _this;
  _this setDir -25.285599;
  _this setPos [2790.7876, 12320.289, 4.5776367e-005];
};

_vehicle_253 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_25", [2780.2239, 12342.654, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _vehicle_253 = _this;
  _this setDir -25.285599;
  _this setPos [2780.2239, 12342.654, 0.0001373291];
};

_vehicle_255 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_12", [2770.8054, 12362.527, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_255 = _this;
  _this setDir -27.803276;
  _this setPos [2770.8054, 12362.527, 6.1035156e-005];
};

_vehicle_256 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf2_6konec", [2761.9548, 12378.647, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_256 = _this;
  _this setDir 149.05762;
  _this setPos [2761.9548, 12378.647, 4.5776367e-005];
};

_vehicle_257 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Svodidla_5m", [4513.8755, 12790.885, 0.00019836426], [], 0, "CAN_COLLIDE"];
  _vehicle_257 = _this;
  _this setDir -60.302917;
  _this setPos [4513.8755, 12790.885, 0.00019836426];
};

_vehicle_258 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Svodidla_5m", [4511.4727, 12786.536, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_258 = _this;
  _this setDir -61.432365;
  _this setPos [4511.4727, 12786.536, 0.00021362305];
};
};
