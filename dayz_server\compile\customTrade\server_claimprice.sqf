private ["_name","_player","_clientID","_playerUID","_query","_result","_query1"];

_player = _this;
_clientID = owner _player;
_playerUID = getPlayerUID _player;
_name = name _player;

_query = format["SELECT ClaimCoins FROM player_data WHERE PlayerUID='%1'",_playerUID];
_result = [_query, 2, true] call fn_asyncCall;

waitUntil {!isNil "_result"};

diag_log format ["_result %1",_result];
_result = _result select 0 select 0;
diag_log format ["_result %1",_result];

if (_result > 0) then {
	_query1 = format["UPDATE player_data SET ClaimCoins = '%1' WHERE PlayerUID = '%2'",0,_playerUID];
	[_query1, 1, true] call fn_asyncCall;
	diag_log format ["[O9 Custom Trading]: Player %2 (%1) claimed %3 Coins from the market!",_playerUID,_name,[_result] call BIS_fnc_numberText];
};

PVDZE_claimmoneyResult = _result;

if (!isNull _player) then {
	_clientID publicVariableClient "PVDZE_claimmoneyResult";
};
