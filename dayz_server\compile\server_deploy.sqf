///////////////////////////////////////////////////////////////////////////////////////////////////
//
//	server_deploy
//
//	Description:	Creates a vehicle server side triggered from the client.
//	Syntax:			[classname,pos,dir,uid] spawn server_deploy;
//
//	classname		String - Classname of the vehicle
//	pos				Array - Position the object is created
//  dir 			Number - Direction of the vehicle
//	uid				String - UID of the player who created the vehicle
//
//	Return Value:	Nothing
//
//	Called by:		Server
//
//	Note:			Called from Client with Deploy Anything.
//
///////////////////////////////////////////////////////////////////////////////////////////////////

#define DEPLOY_SERVER_DEBUG

#ifdef DEPLOY_SERVER_DEBUG
	diag_log format["[Server]: [Deploy Vehicle]: Function called with arguments: %1",_this];
#endif

local _classname = _this select 0;
local _pos = _this select 1;
local _dir = _this select 2;
local _uid = _this select 3;

//	Create the vehicle
local _veh = _classname createVehicle _pos;
_veh setDir _dir;

//	Handle position set if the vehicle is over water
if (surfaceiswater _pos) then {
	_veh setPosASL _pos;
} else {
	_veh setPosATL _pos;
};

#ifdef DEPLOY_SERVER_DEBUG
	diag_log format["[Server]: [Deploy Vehicle]: Vehicle %1 created at grid: %2",_classname,mapGridPosition _pos];
#endif

//	Add the vehicle to the server vehicles so it is not flagged as a vehicle by a hacker
dayz_serverObjectMonitor set [count dayz_serverObjectMonitor, _veh];

//	Clear the vehicle cargo just to be sure there are no other weapons and ammo in
clearWeaponCargoGlobal _veh;
clearMagazineCargoGlobal _veh;

//	Add custom weapons and ammo
[_veh,false,DZE_addVehicleAmmo] call fn_vehicleAddons;

_veh setVehicleLock "UNLOCKED";

_veh setVariable ["DeployedBy",_uid, true]; 

// Claiming Deployed vehicle removal
_veh setVariable ["canKey","no",true];

//	Add eventhandlers
_veh call fnc_veh_ResetEH;

//	Broadcast vehicle with eventhandlers to every client
PVDZE_veh_Init = _veh;
publicVariable 'PVDZE_veh_Init';