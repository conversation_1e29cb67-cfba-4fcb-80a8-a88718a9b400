// North east airfield

if (isServer) then {
	if (true) then
	{
	  _this = createVehicle ["Land_Destroyer", [13214.927, 10009.651, 0.1673592], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 3.7607927;
	  _this setVehicleLock "LOCKED";
	  _this setPos [13214.927, 10009.651, 0.1673592];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_Fregata", [13121.344, 10123.611, -0.028005123], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -34.288754;
	  _this setVehicleLock "LOCKED";
	  _this setPos [13121.344, 10123.611, -0.028005123];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_Fregata", [13151.975, 10143.911, 0.1116961], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -34.288754;
	  _this setVehicleLock "LOCKED";
	  _this setPos [13151.975, 10143.911, 0.1116961];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13129.996, 10356.416, 2.1934509e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -73.469803;
	  _this setPos [13129.996, 10356.416, 2.1934509e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13130.854, 10359.254, 1.4305115e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -73.469803;
	  _this setPos [13130.854, 10359.254, 1.4305115e-006];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13121.662, 10358.894, 1.001358e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -73.469803;
	  _this setPos [13121.662, 10358.894, 1.001358e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13122.517, 10361.779, 7.6293945e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -73.469803;
	  _this setPos [13122.517, 10361.779, 7.6293945e-006];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_corner", [13124.166, 10363.379, 7.724762e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 104.98193;
	  _this setPos [13124.166, 10363.379, 7.724762e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_corner", [13130.177, 10361.818, 8.4877014e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -164.30222;
	  _this setPos [13130.177, 10361.818, 8.4877014e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["SandNest_DZ", [13132.616, 10360.361, -0.18033585], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 195.23729;
	  _this setPos [13132.616, 10360.361, -0.18033585];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ZavoraAnim", [13068.217, 10234.097, 0.36656427], [], 0, "CAN_COLLIDE"];
	  _this setDir -252.45662;
	  _this setPos [13068.217, 10234.097, 0.36656427];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13112.708, 10348.842, -0.11380637], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -74.303093;
	  _this setPos [13112.708, 10348.842, -0.11380637];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13113.586, 10351.756, -0.10156961], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -73.469803;
	  _this setPos [13113.586, 10351.756, -0.10156961];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13187.943, 10336.428, -0.19033568], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 15.939365;
	  _this setPos [13187.943, 10336.428, -0.19033568];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13190.9, 10335.547, -0.15981075], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 15.584167;
	  _this setPos [13190.9, 10335.547, -0.15981075];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_Misc_GContainer_Big", [13233.276, 10321.821, 0.00015759468], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13233.276, 10321.821, 0.00015759468];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_Misc_Cargo1C", [13237.308, 10319.641, -0.00010061264], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 31.704742;
	  _this setPos [13237.308, 10319.641, -0.00010061264];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_Misc_Cargo2C", [13241.549, 10318.429, -0.37217391], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -50.42334;
	  _this setPos [13241.549, 10318.429, -0.37217391];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_Misc_Cargo1D", [13246.859, 10315.783, -1.5070117], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -95.788879;
	  _this setPos [13246.859, 10315.783, -1.5070117];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13077.279, 10239.659, -0.12891196], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -162.33023;
	  _this setPos [13077.279, 10239.659, -0.12891196];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13074.432, 10240.567, -0.11413277], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -162.00702;
	  _this setPos [13074.432, 10240.567, -0.11413277];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13074.78, 10231.173, -0.14454834], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -159.39537;
	  _this setPos [13074.78, 10231.173, -0.14454834];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13071.927, 10232.173, -0.093649507], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -159.39537;
	  _this setPos [13071.927, 10232.173, -0.093649507];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_corner", [13068.959, 10234.745, -0.10974845], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 19.056292;
	  _this setPos [13068.959, 10234.745, -0.10974845];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_corner", [13071.625, 10240.768, -0.16293345], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -253.55217;
	  _this setPos [13071.625, 10240.768, -0.16293345];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ZavoraAnim", [13065.148, 10238.415, 0.36656427], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -252.45662;
	  _this setPos [13065.148, 10238.415, 0.36656427];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13082.199, 10248.807, -0.22541057], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -75.196075;
	  _this setPos [13082.199, 10248.807, -0.22541057];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13081.486, 10245.832, -0.12660785], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -76.56324;
	  _this setPos [13081.486, 10245.832, -0.12660785];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13090.981, 10277.271, -0.11677776], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -72.030502;
	  _this setPos [13090.981, 10277.271, -0.11677776];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13089.93, 10274.356, -0.11035497], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -251.1281;
	  _this setPos [13089.93, 10274.356, -0.11035497];
	};

	
	if (true) then
	{
	  _this = createVehicle ["UralWreck", [13065.865, 10206.266, 0.00012779236], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13065.865, 10206.266, 0.00012779236];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_Fort_Watchtower_EP1", [13063.516, 10188.51, -0.038900677], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -273.20676;
	  _this setPos [13063.516, 10188.51, -0.038900677];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier5", [13066.674, 10211.783, 3.528595e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -64.827042;
	  _this setPos [13066.674, 10211.783, 3.528595e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13070.702, 10215.831, -0.20742771], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -82.835083;
	  _this setPos [13070.702, 10215.831, -0.20742771];
	};

	
	if (true) then
	{
	  _this = createVehicle ["MAP_76n6_ClamShell_Lower", [13140.916, 10237.077, 0.00014257431], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 44.558311;
	  _this setPos [13140.916, 10237.077, 0.00014257431];
	};

	
	if (true) then
	{
	  _this = createVehicle ["MAP_fortified_nest_big", [13137.41, 10273.829, 0.15962824], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 69.914261;
	  _this setPos [13137.41, 10273.829, 0.15962824];
	};

	
	if (true) then
	{
	  _this = createVehicle ["US_WarfareBAircraftFactory_Base_EP1", [13147.218, 10309.923, -0.44252378], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 0.81875789;
	  _this setPos [13147.218, 10309.923, -0.44252378];
	};

	
	if (true) then
	{
	  _this = createVehicle ["US_WarfareBFieldhHospital_Base_EP1", [13138.863, 10331.024, -0.069985092], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -26.923265;
	  _this setPos [13138.863, 10331.024, -0.069985092];
	};

	
	if (true) then
	{
	  _this = createVehicle ["US_WarfareBHeavyFactory_Base_EP1", [13118.229, 10319.094, -0.3442246], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 225.49672;
	  _this setPos [13118.229, 10319.094, -0.3442246];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_Fort_Watchtower_EP1", [13133.234, 10277.514, 9.5367432e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -138.22342;
	  _this setPos [13133.234, 10277.514, 9.5367432e-005];
	};
	
	
	if (true) then
	{
	  _this = createVehicle ["US_WarfareBUAVterminal_Base_EP1", [13124.546, 10262.769, 6.4849854e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -157.39877;
	  _this setPos [13124.546, 10262.769, 6.4849854e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["US_WarfareBLightFactory_base_EP1", [13099.508, 10238.832, -0.37653601], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 7.86695;
	  _this setPos [13099.508, 10238.832, -0.37653601];
	};

	
	if (true) then
	{
	  _this = createVehicle ["MAP_fort_artillery_nest", [13218.754, 10313.615, -0.29205334], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -99.579193;
	  _this setPos [13218.754, 10313.615, -0.29205334];
	};

	
	if (true) then
	{
	  _this = createVehicle ["MAP_fort_rampart", [13054.318, 10182.029, -0.3403669], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 294.41498;
	  _this setPos [13054.318, 10182.029, -0.3403669];
	};

	
	if (true) then
	{
	  _this = createVehicle ["MAP_MASH", [13099.138, 10202.212, 5.8174133e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -67.714119;
	  _this setPos [13099.138, 10202.212, 5.8174133e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["MAP_MASH", [13093.241, 10198.736, 0.826424], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -67.714119;
	  _this setPos [13093.241, 10198.736, 0.826424];
	};

	
	if (true) then
	{
	  _this = createVehicle ["MAP_MASH", [13087.524, 10194.485, 0.00019264221], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -67.714119;
	  _this setPos [13087.524, 10194.485, 0.00019264221];
	};
	
	if (true) then
	{
	  _this = createVehicle ["CampEast", [13096.341, 10219.5, 0.056661479], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -161.1515;
	  _this setPos [13096.341, 10219.5, 0.056661479];
	};

	
	if (true) then
	{
	  _this = createVehicle ["CampEast", [13085.425, 10223.956, -0.032346018], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -161.1515;
	  _this setPos [13085.425, 10223.956, -0.032346018];
	};

	
	if (true) then
	{
	  _this = createVehicle ["MAP_asf3_7_100", [13132.784, 10304.11, 5.865097e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 93.396179;
	  _this setPos [13132.784, 10304.11, 5.865097e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["MAP_asf3_7_100", [13145.506, 10302.476, -2.8610229e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 96.511543;
	  _this setPos [13145.506, 10302.476, -2.8610229e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["CampEast", [13164.686, 10305.521, -0.022719961], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -291.25143;
	  _this setPos [13164.686, 10305.521, -0.022719961];
	};

	
	if (true) then
	{
	  _this = createVehicle ["CampEast", [13160.562, 10293.003, 0.051143974], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -212.47076;
	  _this setPos [13160.562, 10293.003, 0.051143974];
	};

	
	if (true) then
	{
	  _this = createVehicle ["CampEast", [13148.018, 10291.952, -0.024248384], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -168.61639;
	  _this setPos [13148.018, 10291.952, -0.024248384];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Misc_cargo_cont_small", [13048.609, 10165.457, -1.7166138e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -5.5346642;
	  _this setPos [13048.609, 10165.457, -1.7166138e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_Misc_Cargo1E_EP1", [13052.627, 10167.031, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir 29.607107;
	  _this setPos [13052.627, 10167.031, 1.5258789e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Misc_Cargo1B_military", [13056.518, 10165.05, 8.7738037e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -53.700779;
	  _this setPos [13056.518, 10165.05, 8.7738037e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13072.612, 10138.719, -0.13292705], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -123.47453;
	  _this setPos [13072.612, 10138.719, -0.13292705];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13074.279, 10136.198, -0.13152297], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -124.17802;
	  _this setPos [13074.279, 10136.198, -0.13152297];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13084.245, 10121.45, -0.15333399], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -123.36115;
	  _this setPos [13084.245, 10121.45, -0.15333399];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_fort_bagfence_long", [13082.513, 10123.896, -0.13330285], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -126.13323;
	  _this setPos [13082.513, 10123.896, -0.13330285];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [13098.378, 10107.55, 0.00010204315], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13098.378, 10107.55, 0.00010204315];
	};

	
	if (true) then
	{
	  _this = createVehicle ["Land_HBarrier_large", [13108.257, 10114.894, 5.7550902], [], 0, "CAN_COLLIDE"];
	  
	  _this setDir -33.780281;
	  _this setPos [13108.257, 10114.894, 5.7550902];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13141.725, 10299.67, 1.001358e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13141.725, 10299.67, 1.001358e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13156.631, 10304.453, 4.2915344e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13156.631, 10304.453, 4.2915344e-006];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13151.02, 10305.266, 7.1525574e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13151.02, 10305.266, 7.1525574e-006];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13153.288, 10296.629, 1.3828278e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13153.288, 10296.629, 1.3828278e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13155.043, 10287.51, -1.0490417e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13155.043, 10287.51, -1.0490417e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13158.727, 10309.402, 1.2874603e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13158.727, 10309.402, 1.2874603e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13168.72, 10300.457, 3.7193298e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13168.72, 10300.457, 3.7193298e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13144.964, 10302.181, -9.5367432e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13144.964, 10302.181, -9.5367432e-006];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13148.173, 10304.135, 9.5367432e-007], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13148.173, 10304.135, 9.5367432e-007];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13152.566, 10305.369, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13152.566, 10305.369, 1.9073486e-006];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13097.642, 10220.854, -7.6293945e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13097.642, 10220.854, -7.6293945e-006];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13085.296, 10224.335, 6.7710876e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13085.296, 10224.335, 6.7710876e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13158.121, 10303.856, -7.1048737e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13158.121, 10303.856, -7.1048737e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13160.239, 10303.557, 4.7206879e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13160.239, 10303.557, 4.7206879e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13160.954, 10303.925, -5.9604645e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13160.954, 10303.925, -5.9604645e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13068.169, 10155.354, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13068.169, 10155.354, -1.1444092e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13064.079, 10168.928, 1.9073486e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13064.079, 10168.928, 1.9073486e-006];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13068.142, 10180.471, 2.2888184e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13068.142, 10180.471, 2.2888184e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13072.925, 10190.866, 4.7683716e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13072.925, 10190.866, 4.7683716e-006];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13092.162, 10123.956, -3.8146973e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13092.162, 10123.956, -3.8146973e-006];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13076.827, 10147.255, 0], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13076.827, 10147.255, 0];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13079.464, 10140.287, -1.335144e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13079.464, 10140.287, -1.335144e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13052.857, 10175.054, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13052.857, 10175.054, 5.7220459e-006];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13057.566, 10180.834, 5.531311e-005], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13057.566, 10180.834, 5.531311e-005];
	};

	
	if (true) then
	{
	  _this = createVehicle ["ClutterCutter_EP1", [13058.53, 10175.309, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
	  
	  _this setPos [13058.53, 10175.309, 5.7220459e-006];
	};
};
