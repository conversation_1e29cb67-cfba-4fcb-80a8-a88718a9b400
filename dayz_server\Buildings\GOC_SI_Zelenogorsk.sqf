// Ghost of Chernarus - Structure Improvement Zelenogorsk
// by Charlatan a.k.a. Foar
// Give Credit if you use any of this =)


if (isServer) then {

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["LAND_A_Hospital", [2591.9668, 5887.7681, 1.6970869], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 240;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2591.5, 5889.4, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_nav_pier_c_90", [2596.61, 5913.4463, -137.98828], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2596.61, 5913.4463, 248.304];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_nav_pier_c_90", [2614.5532, 5882.1299, -58.191082], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2614.5532, 5882.1299, 248.303];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_nav_pier_c", [2606.3477, 5895.4009, -200.54219], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2606.3477, 5895.4009, 248.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_nav_pier_c", [2588.1604, 5887.084, -51.956989], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2589.5, 5887.0, 248.302];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_nav_pier_c_90", [2597.1, 5872.12, -193.29539], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 240;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2597.20, 5872.10, 248.304];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_nav_pier_c_90", [2579.2263, 5903.4546, -86.124863], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2579.4, 5903.45, 248.303];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_kasna_new", [2605.5732, 5896.1685, 3.4617929], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2605.5732, 5896.1685, 254.4];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_kasna_new", [2604.0764, 5898.7451, 3.32181], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2604.0764, 5898.7451, 254.4];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Piskoviste", [2602.614, 5905.2017, -8.6788273], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -30;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2602.614, 5905.2017, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Piskoviste", [2599.177, 5903.2017, -4.5767283], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -30;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2599.177, 5903.2017, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Piskoviste", [2610.4597, 5891.5693, -9.8012056], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -30;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2610.4597, 5891.5693, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Piskoviste", [2607.0239, 5889.5811, -8.3342371], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -30;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2607.0239, 5889.5811, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_molo_krychle", [2605.3528, 5897.7739, -38.742935], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 329.9;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2605.3528, 5897.7739, 252.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_molo_krychle", [2601.4189, 5904.584, -39.363564], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 329.9;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2601.4189, 5904.584, 252.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_molo_krychle", [2609.293, 5890.939, -34.359882], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 329.9;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2609.293, 5890.939, 252.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_molo_krychle", [2597.469, 5911.4287, -11.997241], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 329.9;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2597.469, 5911.4287, 252.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_molo_krychle", [2593.6799, 5918.6655, -25.473543], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 329.9;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2593.54, 5918.25, 252.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_molo_krychle", [2613.2021, 5884.1299, -1.4531946], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 329.9;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2613.2021, 5884.1299, 252.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_molo_krychle", [2617.1069, 5877.2808, -11.295967], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 329.9;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2617.1069, 5877.2808, 252.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2579.9045, 5914.6782, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2579.9045, 5914.6782, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2584.3232, 5913.9199], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2584.3232, 5913.9199, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2584.3137, 5913.9082, 2.1971843], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2584.3137, 5913.9082, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2585.8005, 5911.3096, 2.0362618], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2585.8005, 5911.3096, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2580.3235, 5914.8853, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2580.3235, 5914.8853, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2574.5349, 5911.5801], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2574.5349, 5911.5801, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2569.1404, 5908.3789, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2569.1404, 5908.3789, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2574.7019, 5911.687, 1.576468], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2574.7019, 5911.687, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2569.1665, 5908.4536, 0.73458683], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2569.1665, 5908.4536, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2568.0496, 5904.2964, 2.9179127], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2568.0496, 5904.2964, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2571.3411, 5898.7437, 1.498719], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 240;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2571.3411, 5898.7437, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2568.271, 5904], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 240;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2568.271, 5904, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2574.2964, 5893.4888, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 240;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2574.2964, 5893.4888, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2577.1868, 5888.3359, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 240;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2577.1868, 5888.3359, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2580.0374, 5883.1514, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 240;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2580.0374, 5883.1514, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2589.0391, 5867.0405, -0.26066941], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 240;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2589.0391, 5867.0405, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2592.1631, 5861.6313, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 240;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2592.1631, 5861.6313, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2571.2268, 5898.939, 0.0001373291], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2571.2268, 5898.939, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2574.1538, 5893.6777, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2574.1538, 5893.6777, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2577.0935, 5888.458], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2577.0935, 5888.458, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2579.9165, 5883.2173, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2579.9165, 5883.2173, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2587.2241, 5870.0015, 1.0471613], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2587.2241, 5870.0015, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2588.792, 5867.3232, 0.78744859], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2588.792, 5867.3232, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2592.0352, 5861.6821, 1.0173107], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2592.0352, 5861.6821, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2585.7322, 5872.6641, 0.77960056], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2585.7322, 5872.6641, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2582.8274, 5878.1782, 0.13094607], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2582.78, 5878.1465, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2584.2402, 5875.3979, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2584.2402, 5875.3979, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5_D_2", [2582.7693, 5878.2524, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 242;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2582.7693, 5878.2524, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5_D_2", [2584.2793, 5875.2676, -0.61923355], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2584.2793, 5875.2676, 253];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_paletyC", [2583.5557, 5876.2769, 0.16845432], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 234;
  _bldObj setPos [2583.5557, 5876.2769, 0.16845432];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_paletaA", [2583.0007, 5876.7437, 0.59246719], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 72;
  _bldObj setPos [2583.0007, 5876.7437, 0.59246719];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_paletaA", [2582.0288, 5876.0913, -0.083942674], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58;
  _bldObj setPos [2582.0288, 5876.0913, -0.083942674];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_paletaA", [2585.5024, 5870.9678, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setVectorDirAndUp [[-0.6,1,0],[-1,0,1]];
  _bldObj setPosASL [2585.6, 5871.1, 253.7];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5_D_2", [2585.8318, 5911.5347, 0.0048466665], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2585.8318, 5911.5347, 252.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2601.1357, 5863.4961, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2601.1357, 5863.4961, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2596.1768, 5860.6768, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2596.1768, 5860.6768, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2608.0723, 5867.4937, 0.90491056], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2608.0723, 5867.4937, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_5", [2607.3081, 5874.5181, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60.044533;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2607.3081, 5874.5181, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2605.4851, 5865.9712, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2605.4851, 5865.9712, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2607.8757, 5867.3047, 0.59021115], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2607.8757, 5867.3047, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2601.0166, 5863.4927, 0.19934806], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2601.0166, 5863.4927, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2595.8428, 5860.5376, 1.662371], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2595.8428, 5860.5376, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_molo_krychle", [2599.863, 5867.3604, -190.69943], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 329.9;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2599.9, 5867.32, 252.19];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2608.9629, 5871.436, 3.5692117], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2609.05, 5871.436, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_plot_rust_brank_o", [2609.9373, 5870.6313, 3.8086581], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 61.233761;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2610, 5870.6, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2610.2031, 5869.3818], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2610.2, 5869.38, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndVar1_Pole", [2608.7527, 5871.9336, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2608.7527, 5871.9336, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_molo_krychle", [2576.4988, 5908.3696, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 329.9;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2576.4988, 5908.3696, 252.19];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2598.5532, 5925.5957, -0.13033578], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2598.5532, 5925.5957, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2591.6306, 5921.5669, -2.8442483], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2591.6306, 5921.5669, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2584.574, 5917.5342, -4.9693403], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2584.574, 5917.5342, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2588.1072, 5919.5601, -4.3439894], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2588.1072, 5919.5601, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2595.1047, 5923.604, -2.4587197], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2595.1047, 5923.604, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2598.5532, 5925.5957, -0.13033578], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2598.5532, 5925.5957, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2591.6306, 5921.5669, -2.8442483], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2591.6306, 5921.5669, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2584.574, 5917.5342, -4.9693403], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2584.574, 5917.5342, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2588.1072, 5919.5601, -4.3439894], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2588.1072, 5919.5601, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2595.1047, 5923.604, -2.4587197], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2595.1047, 5923.604, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2626.0205, 5877.833, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2626.0205, 5877.833, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2619.0979, 5873.8042, -4.0697937], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2619.0979, 5873.8042, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2612.0413, 5869.7715, -6.6023407], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2612.0413, 5869.7715, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2615.5745, 5871.7974, -5.9199677], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2615.5745, 5871.7974, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2622.572, 5875.8413, -3.0383759], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2622.572, 5875.8413, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2626.0205, 5877.833, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2626.0205, 5877.833, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2619.0979, 5873.8042, -4.0697937], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2619.0979, 5873.8042, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2612.0413, 5869.7715, -6.6023407], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2612.0413, 5869.7715, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2615.5745, 5871.7974, -5.9199677], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2615.5745, 5871.7974, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2622.572, 5875.8413, -3.0383759], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2622.572, 5875.8413, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2609.0984, 5911.0874, -0.6517666], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2609.0984, 5911.0874, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2605.1919, 5917.8223, -4.4674077], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2605.1919, 5917.8223, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2601.2124, 5924.8159, -6.4174652], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2601.2124, 5924.8159, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2603.23, 5921.2793, -5.9881744], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2603.23, 5921.2793, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2607.1228, 5914.4819, -3.4912126], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2607.1228, 5914.4819, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2609.0984, 5911.0874, -0.6517666], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2609.0984, 5911.0874, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2605.1919, 5917.8223, -4.4674077], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2605.1919, 5917.8223, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2601.2124, 5924.8159, -6.4174652], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2601.2124, 5924.8159, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2603.23, 5921.2793, -5.9881744], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2603.23, 5921.2793, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2607.1228, 5914.4819, -3.4912126], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2607.1228, 5914.4819, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2618.812, 5894.3223, 0.052244976], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2618.812, 5894.3223, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2622.6682, 5887.6255, -4.1592116], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2622.6682, 5887.6255, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2626.6709, 5880.5498, -6.4237833], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2626.6709, 5880.5498, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2624.6604, 5884.0918, -5.7564402], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2624.6604, 5884.0918, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2620.7395, 5890.9497, -2.9737716], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2620.7395, 5890.9497, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2618.812, 5894.3223, 0.052244976], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2618.812, 5894.3223, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2622.6682, 5887.6255, -4.1592116], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2622.6682, 5887.6255, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2626.6709, 5880.5498, -6.4237833], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2626.6709, 5880.5498, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2624.6604, 5884.0918, -5.7564402], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2624.6604, 5884.0918, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2620.7395, 5890.9497, -2.9737716], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2620.7395, 5890.9497, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2616.9072, 5897.6157, -0.76352894], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2616.9072, 5897.6157, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2616.9072, 5897.6157, -0.76352894], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 330;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2616.9072, 5897.6157, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2610.9985, 5907.7725, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2610.9985, 5907.7725, 254.5];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Patniky", [2610.9985, 5907.7725, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 150;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2610.9985, 5907.7725, 255.14];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2601.3333, 5905.7666, 2.7182209], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2601.3333, 5905.7666, 2.7182209];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_fern", [2609.9443, 5890.4434, 3.4729977], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2609.9443, 5890.4434, 3.4729977];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_misc_stub2", [2599.2939, 5902.0249, -5.9256477], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -138.62077;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2599.2939, 5902.0249, 255];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_b_betulaHumilis", [2606.1851, 5890.1011, 2.5807681], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2606.1851, 5890.1011, 2.5807681];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_GrassAutumnBrown", [2599.2156, 5902.0723, 2.7351663], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2599.2156, 5902.0723, 2.7351663];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_GrassAutumnBrown", [2599.1882, 5903.73, 2.8318605], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 158.41187;
  _bldObj setPos [2599.1882, 5903.73, 2.8318605];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_GrassAutumnBrown", [2599.8613, 5902.4912, 2.6543329], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 326.02582;
  _bldObj setPos [2599.8613, 5902.4912, 2.6543329];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_GrassAutumnBrown", [2607.1685, 5889.1802, 3.4554551], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2607.1685, 5889.1802, 3.4554551];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_p_urtica", [2602.6609, 5905.5767, 2.4919636], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2602.6609, 5905.5767, 2.4919636];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_p_urtica", [2602.2439, 5903.6748, 2.5140088], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 121.52324;
  _bldObj setPos [2602.2439, 5903.6748, 2.5140088];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_p_urtica", [2602.8865, 5904.231, 2.5139782], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2602.8865, 5904.231, 2.5139782];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_p_urtica", [2603.3411, 5904.8354, 2.5139325], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 37.084419;
  _bldObj setPos [2603.3411, 5904.8354, 2.5139325];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_p_urtica", [2602.3787, 5904.8164, 2.5139477], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 75.503448;
  _bldObj setPos [2602.3787, 5904.8164, 2.5139477];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_p_urtica", [2601.0488, 5905.2202, 2.509366], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2601.0488, 5905.2202, 2.509366];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_p_urtica", [2601.6704, 5905.3398, 2.4751461], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 65.123505;
  _bldObj setPos [2601.6704, 5905.3398, 2.4751461];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_fern", [2602.3335, 5906.2451, 2.7827055], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2602.3335, 5906.2451, 2.7827055];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_fern", [2603.9854, 5905.0781, 2.8547807], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 94.200912;
  _bldObj setPos [2603.9854, 5905.0781, 2.8547807];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2601.5605, 5904.8735, 2.7084188], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 49.41814;
  _bldObj setPos [2601.5605, 5904.8735, 2.7084188];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2603.0386, 5906.8325, 2.7951002], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -59.601494;
  _bldObj setPos [2603.0386, 5906.8325, 2.7951002];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2601.9844, 5904.7217, 2.7416062], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -81.517181;
  _bldObj setPos [2601.9844, 5904.7217, 2.7416062];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2602.7693, 5904.1763, 2.7390122], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -32.099014;
  _bldObj setPos [2602.7693, 5904.1763, 2.7390122];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2603.533, 5905.7964, 2.8611383], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 66.94696;
  _bldObj setPos [2603.533, 5905.7964, 2.8611383];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2604.1914, 5904.6138, 2.8507843], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 116.36511;
  _bldObj setPos [2604.1914, 5904.6138, 2.8507843];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2603.2583, 5906.1914, 2.8668118], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -169.35002;
  _bldObj setPos [2603.2583, 5906.1914, 2.8668118];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2601.7605, 5904.2744, 2.6837137], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -119.93185;
  _bldObj setPos [2601.7605, 5904.2744, 2.6837137];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_p_urtica", [2609.8762, 5890.0166, 3.3755], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2609.8762, 5890.0166, 3.3755];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_p_urtica", [2609.6934, 5892.2495, 3.323602], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 74.425087;
  _bldObj setPos [2609.6934, 5892.2495, 3.323602];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2610.9448, 5892.4336, 3.5794232], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 68.404114;
  _bldObj setPos [2610.9448, 5892.4336, 3.5794232];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2609.0767, 5892.0513, 3.5014036], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -90.175095;
  _bldObj setPos [2609.0767, 5892.0513, 3.5014036];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2611.2961, 5892.1533, 3.6510174], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -62.531185;
  _bldObj setPos [2611.2961, 5892.1533, 3.6510174];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2611.8606, 5891.3809, 3.7171795], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -13.113046;
  _bldObj setPos [2611.8606, 5891.3809, 3.7171795];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2610.0295, 5891.4106, 3.6223905], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 36.373348;
  _bldObj setPos [2610.0295, 5891.4106, 3.6223905];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2611.197, 5890.7266, 3.6780393], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 85.791504;
  _bldObj setPos [2611.197, 5890.7266, 3.6780393];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2609.592, 5891.6099, 3.6041105], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -199.92365;
  _bldObj setPos [2609.592, 5891.6099, 3.6041105];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_c_blueBerry", [2610.9382, 5891.8013, 3.5920575], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -100.94585;
  _bldObj setPos [2610.9382, 5891.8013, 3.5920575];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_t_pyrus2s", [2607.7261, 5889.6455, 3.5527966], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2607.7261, 5889.6455, 3.5527966];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2615.6743, 5903.7158, -1.8287852], [], 0, "CAN_COLLIDE"];
  _bldObj setVectorDirAndUp [[1,0.565,-0.5],[0,0,1]];
  _bldObj setPosASL [2615.63, 5903.7158, 250.7];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2614.4783, 5905.8481], [], 0, "CAN_COLLIDE"];
  _bldObj setVectorDirAndUp [[1,0.565,-0.5],[0,0,1]];
  _bldObj setPosASL [2614.4, 5905.8481, 250.7];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2616.8633, 5901.6226, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setVectorDirAndUp [[1,0.565,-0.5],[0,0,1]];
  _bldObj setPosASL [2616.82, 5901.6226, 250.7];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2619.2368, 5908.5532, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setVectorDirAndUp [[1,0.565,-0.5],[0,0,1]];
  _bldObj setPosASL [2619.2368, 5908.5532, 248.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2620.4329, 5906.4209, -1.7752533], [], 0, "CAN_COLLIDE"];
  _bldObj setVectorDirAndUp [[1,0.565,-0.5],[0,0,1]];
  _bldObj setPosASL [2620.4329, 5906.4209, 248.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2621.6218, 5904.3276, 0.12420654], [], 0, "CAN_COLLIDE"];
  _bldObj setVectorDirAndUp [[1,0.565,-0.5],[0,0,1]];
  _bldObj setPosASL [2621.6218, 5904.3276, 248.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_molo_krychle", [2621.3354, 5906.8638, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setVectorDirAndUp [[-0.565,1,0],[0.5,0,1]];
  _bldObj setPosASL [2621.2, 5906.9, 248.3];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_mud_6konec", [2647.5637, 5918.7788], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -109.7009;
  _bldObj setPos [2647.5637, 5918.7788];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_mud_10_100", [2625.9458, 5909.3726, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60.241428;
  _bldObj setPos [2625.9458, 5909.3726, 9.1552734e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_mud_12", [2615.1729, 5903.1426, 7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60.235691;
  _bldObj setPos [2615.1729, 5903.1426, 7.6293945e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_mud_6konec", [2627.6819, 5910.355, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -133.77817;
  _bldObj setPos [2627.6819, 5910.355, 6.1035156e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_mud_6konec", [2626.3445, 5910.7188, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -115.79647;
  _bldObj setPos [2626.3445, 5910.7188, 4.5776367e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_mud_6konec", [2621.97, 5909.0259, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 70.644669;
  _bldObj setPos [2621.97, 5909.0259, 6.1035156e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_mud_6konec", [2633.6665, 5912.5024, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -104.65065;
  _bldObj setPos [2633.6665, 5912.5024, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_mud_6konec", [2646.521, 5919.5313], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -109.7009;
  _bldObj setPos [2646.521, 5919.5313];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_mud_10_100", [2624.9031, 5910.125, 0.24688721], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 60.241428;
  _bldObj setPos [2624.9031, 5910.125, 0.24688721];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_lampa_sidl", [2621.3623, 5904.144, 2.0921898], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -120.70142;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2621.3, 5904.3, 252];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_lampa_sidl", [2616.5771, 5901.4199, 2.862633], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -119.64686;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2616.5771, 5901.6, 254];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_lampa_sidl", [2616.7417, 5906.8916, 0.82830811], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -299.85248;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2616.7, 5906.95, 252];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_lampa_sidl", [2611.9624, 5904.2031, 2.1335731], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -299.85248;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPosASL [2611.9624, 5904.35, 254];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SignB_Pharmacy", [2599.3254, 5894.3555, 5.6924429], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -210.84367;
  _bldObj setPos [2599.3254, 5894.3555, 5.6924429];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_sign_hospital", [2646.4116, 5915.5054, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -75.878189;
  _bldObj setPos [2646.4116, 5915.5054, -3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_garbage_misc", [2581.6956, 5899.0708, 1.0322646], [], 0, "CAN_COLLIDE"];
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPos [2581.6956, 5899.0708, 1.0322646];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_rubble_concrete_03", [2613.2461, 5908.314, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -51.983749;
  _bldObj setPos [2613.2461, 5908.314, -4.5776367e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_ruin_rubble", [2617.9663, 5897.3447, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 81.35215;
  _bldObj setPos [2617.9663, 5897.3447, 6.1035156e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_ruin_rubble", [2620.8047, 5900.7104, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 203.43024;
  _bldObj setPos [2620.8047, 5900.7104, -4.5776367e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_ural_wrecked", [2621.3428, 5898.0225, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 103.58508;
  _bldObj setPos [2621.3428, 5898.0225, 3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_rubble7", [2623.8748, 5907.0537, -0.25048608], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 225.87735;
  _bldObj setPos [2623.8748, 5907.0537, -0.25048608];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_R2_Boulder1", [2645.2727, 5914.0347, -0.14572613], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -49.561817;
  _bldObj setPos [2645.2727, 5914.0347, -0.14572613];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_R2_Boulder2", [2623.4509, 5911.6748, -0.2223855], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 258.83502;
  _bldObj setPos [2623.4509, 5911.6748, -0.2223855];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_b_corylus", [2645.759, 5912.9448, -0.39998671], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2645.759, 5912.9448, -0.39998671];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_b_corylus2s", [2624.3696, 5902.9819, -1.0390694], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2624.3696, 5902.9819, -1.0390694];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_b_craet1", [2628.5493, 5916.9966, -0.68894225], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2628.5493, 5916.9966, -0.68894225];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_b_craet1", [2629.9834, 5892.8369, -0.52150655], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 98.556984;
  _bldObj setPos [2629.9834, 5892.8369, -0.52150655];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_b_craet1", [2640.5222, 5883.0757, -0.38763171], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -12.512784;
  _bldObj setPos [2640.5222, 5883.0757, -0.38763171];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_b_craet1", [2646.1704, 5912.5938, -0.99137706], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2646.1704, 5912.5938, -0.99137706];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["Land_ladder_half_EP1", [2604.6089, 5865.1987], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -30.697491;
  _bldObj setVectorUp [0,0,0.01];
  _bldObj setPos [2604.6089, 5865.1987];
};




//  Start of Warehouse





_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["LAND_Hangar_2", [2488.8943, 5086.8691, -0.015337981], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -32.491524;
  _bldObj setPos [2488.8943, 5086.8691, -0.015337981];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["LAND_Hangar_2", [2514.2148, 5103.0137], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -32.491524;
  _bldObj setPos [2514.2148, 5103.0137];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["LAND_Ind_Workshop01_01", [2565.7305, 5048.707], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -32.549358;
  _bldObj setPos [2565.7305, 5048.707];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Ind_TankSmall", [2478.8616, 5072.1216, -0.20704745], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 57.056267;
  _bldObj setPos [2478.8616, 5072.1216, -0.20704745];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Ind_TankSmall", [2474.7439, 5078.6484, -0.20704745], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 57.056267;
  _bldObj setPos [2474.7439, 5078.6484, -0.20704745];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Ind_TankSmall", [2470.573, 5085.1084, -0.20707797], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 57.738205;
  _bldObj setPos [2470.573, 5085.1084, -0.20707797];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Nasypka", [2533.1421, 5104.5532], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 237.51433;
  _bldObj setPos [2533.1421, 5104.5532];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Nasypka", [2522.9309, 5120.7041, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 237.51433;
  _bldObj setPos [2522.9309, 5120.7041, 3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Nasypka", [2528.1497, 5112.3921], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 237.51433;
  _bldObj setPos [2528.1497, 5112.3921];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_ural_wrecked", [2495.4668, 5076.6851, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -123.32419;
  _bldObj setPos [2495.4668, 5076.6851, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1B", [2508.1516, 5067.7134, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 50.78775;
  _bldObj setPos [2508.1516, 5067.7134, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1Bo", [2505.886, 5063.6099, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -64.137207;
  _bldObj setPos [2505.886, 5063.6099, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2505.6213, 5057.126], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 42.655437;
  _bldObj setPos [2505.6213, 5057.126];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1G", [2512.7625, 5048.5679], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 49.477539;
  _bldObj setPos [2512.7625, 5048.5679];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo2B", [2514.804, 5046.7241, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 50.906315;
  _bldObj setPos [2514.804, 5046.7241, -3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo2C", [2519.3787, 5050.7305, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 48.298775;
  _bldObj setPos [2519.3787, 5050.7305, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo2B", [2516.4822, 5044.6699], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 49.883709;
  _bldObj setPos [2516.4822, 5044.6699];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo2B", [2521.2798, 5048.7866, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 49.49704;
  _bldObj setPos [2521.2798, 5048.7866, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2515.7622, 5071.4556, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 30.711269;
  _bldObj setPos [2515.7622, 5071.4556, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2520.5359, 5044.5645, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 46.297432;
  _bldObj setPos [2520.5359, 5044.5645, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo2D", [2503.6892, 5059.8613], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 26.626112;
  _bldObj setPos [2503.6892, 5059.8613];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo2B", [2529.3352, 5059.1011], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 49.800186;
  _bldObj setPos [2529.3352, 5059.1011];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1F", [2528.8062, 5061.9956], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 48.580856;
  _bldObj setPos [2528.8062, 5061.9956];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1F", [2524.1118, 5068.6382, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 48.347572;
  _bldObj setPos [2524.1118, 5068.6382, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo2E", [2530.793, 5056.9316], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 42.825756;
  _bldObj setPos [2530.793, 5056.9316];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo2A", [2533.375, 5062.5386], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -21.823997;
  _bldObj setPos [2533.375, 5062.5386];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1F", [2525.4775, 5071.6299, 2.532361], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 87.994812;
  _bldObj setPos [2525.4775, 5071.6299, 2.532361];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1F", [2526.3269, 5066.9053], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 45.416965;
  _bldObj setPos [2526.3269, 5066.9053];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1F", [2527.9551, 5064.7188], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 49.872772;
  _bldObj setPos [2527.9551, 5064.7188];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1F", [2528.811, 5072.6768, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 50.105186;
  _bldObj setPos [2528.811, 5072.6768, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1F", [2529.4827, 5064.8618, 2.5339482], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 154.3578;
  _bldObj setPos [2529.4827, 5064.8618, 2.5339482];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo2E", [2510.7573, 5066.3228], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 43.218178;
  _bldObj setPos [2510.7573, 5066.3228];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1A", [2505.9475, 5063.5991, 2.5524547], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -62.415993;
  _bldObj setPos [2505.9475, 5063.5991, 2.5524547];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2525.728, 5048.5635, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 59.824375;
  _bldObj setPos [2525.728, 5048.5635, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2526.2454, 5052.6421, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 49.387623;
  _bldObj setPos [2526.2454, 5052.6421, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2519.4546, 5072.5317, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 209.17438;
  _bldObj setPos [2519.4546, 5072.5317, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1E", [2522.2495, 5070.6729, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 43.734589;
  _bldObj setPos [2522.2495, 5070.6729, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1F", [2531.0759, 5071.1138, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 48.920845;
  _bldObj setPos [2531.0759, 5071.1138, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1C", [2515.7126, 5070.3223, 2.5399499], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 55.801182;
  _bldObj setPos [2515.7126, 5070.3223, 2.5399499];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo2B", [2518.1223, 5055.1196], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -39.599537;
  _bldObj setPos [2518.1223, 5055.1196];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1Ao", [2507.5393, 5055.0806, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 227.02695;
  _bldObj setPos [2507.5393, 5055.0806, 4.5776367e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1Ao", [2509.0459, 5052.7993, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 231.90842;
  _bldObj setPos [2509.0459, 5052.7993, 4.5776367e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1Ao", [2510.7852, 5050.4595, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 233.13033;
  _bldObj setPos [2510.7852, 5050.4595, 6.1035156e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1A", [2507.9104, 5051.7285, 2.5357771], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -39.831123;
  _bldObj setPos [2507.9104, 5051.7285, 2.5357771];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1A", [2516.2983, 5046.1016, 5.0696092], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -107.35036;
  _bldObj setPos [2516.2983, 5046.1016, 5.0696092];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1A", [2502.0344, 5046.6973, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -54.247795;
  _bldObj setPos [2502.0344, 5046.6973, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1F", [2527.1477, 5075.3511, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 39.624863;
  _bldObj setPos [2527.1477, 5075.3511, -4.5776367e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1A", [2542.3503, 5054.9976, -0.0069122314], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 6.5796847;
  _bldObj setPos [2542.3503, 5054.9976, -0.0069122314];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_Cargo1Ao", [2545.0928, 5054.5459, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 189.84802;
  _bldObj setPos [2545.0928, 5054.5459, 4.5776367e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_garbage_misc", [2527.2725, 5050.9019], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -57.975574;
  _bldObj setPos [2527.2725, 5050.9019];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_garbage_paleta", [2544.781, 5058.4731, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -191.77669;
  _bldObj setPos [2544.781, 5058.4731, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Ind_BoardsPack1", [2532.249, 5055.5068], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 43.846634;
  _bldObj setPos [2532.249, 5055.5068];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Ind_Timbers", [2531.5605, 5101.2153, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 38.995811;
  _bldObj setPos [2531.5605, 5101.2153, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Kontejner", [2506.5486, 5084.5933, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 55.744503;
  _bldObj setPos [2506.5486, 5084.5933, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Kontejner", [2507.9402, 5085.6514, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 57.508179;
  _bldObj setPos [2507.9402, 5085.6514, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Kontejner", [2509.4509, 5086.54], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 65.906769;
  _bldObj setPos [2509.4509, 5086.54];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Misc_GContainer_Big", [2542.853, 5049.7534, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 11.792575;
  _bldObj setPos [2542.853, 5049.7534, 4.5776367e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_pneu", [2546.8235, 5050.0313, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setPos [2546.8235, 5050.0313, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkCorner", [2555.5635, 5074.8857, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 238.44876;
  _bldObj setPos [2555.5635, 5074.8857, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearLong", [2558.9331, 5069.3345], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.098431;
  _bldObj setPos [2558.9331, 5069.3345];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkShortEnd", [2560.1846, 5077.7412, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 418.24429;
  _bldObj setPos [2560.1846, 5077.7412, -3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearShort", [2558.9089, 5076.9541], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 238.18968;
  _bldObj setPos [2558.9089, 5076.9541];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkCorner", [2572.5825, 5046.7012, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 59.014378;
  _bldObj setPos [2572.5825, 5046.7012, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearShort", [2558.0742, 5076.4336, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 238.18968;
  _bldObj setPos [2558.0742, 5076.4336, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearShort", [2557.2383, 5075.9141], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 238.18968;
  _bldObj setPos [2557.2383, 5075.9141];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearLong", [2564.083, 5060.7949, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.098431;
  _bldObj setPos [2564.083, 5060.7949, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearLong", [2569.2278, 5052.2627, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.098431;
  _bldObj setPos [2569.2278, 5052.2627, 3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearLong", [2567.0693, 5043.3652, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.711605;
  _bldObj setPos [2567.0693, 5043.3652, -6.1035156e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearLong", [2558.5725, 5038.1758, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.477261;
  _bldObj setPos [2558.5725, 5038.1758, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearLong", [2550.0718, 5032.9727], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.594433;
  _bldObj setPos [2550.0718, 5032.9727];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearLong", [2541.5732, 5027.6948, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 57.667706;
  _bldObj setPos [2541.5732, 5027.6948, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearMiddle", [2535.324, 5023.6831, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 56.670036;
  _bldObj setPos [2535.324, 5023.6831, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkClearMiddle", [2531.2722, 5020.8931, -0.0099334717], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 54.761974;
  _bldObj setPos [2531.2722, 5020.8931, -0.0099334717];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_SidewalkShortEnd", [2528.4163, 5018.8643, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 594.93024;
  _bldObj setPos [2528.4163, 5018.8643, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndCnc_4", [2523.0435, 5021.2295, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 24.355574;
  _bldObj setPos [2523.0435, 5021.2295, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndCnc_4_D", [2517.6353, 5023.6567, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 23.708744;
  _bldObj setPos [2517.6353, 5023.6567, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndCnc_Pole", [2526.7654, 5019.5552, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 24.018236;
  _bldObj setPos [2526.7654, 5019.5552, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndCnc_4", [2512.2131, 5026.1284, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 26.011707;
  _bldObj setPos [2512.2131, 5026.1284, 3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndCnc_4", [2506.8706, 5028.812, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 27.519258;
  _bldObj setPos [2506.8706, 5028.812, 3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndCnc_4", [2496.4316, 5034.5195, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 31.540939;
  _bldObj setPos [2496.4316, 5034.5195, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndCnc_4", [2491.6499, 5038.0474, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 41.377205;
  _bldObj setPos [2491.6499, 5038.0474, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_Wall_IndCnc_4", [2501.5898, 5031.5737], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 27.882498;
  _bldObj setPos [2501.5898, 5031.5737];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2527.7886, 5020.4956, -7.6293945e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -35.272125;
  _bldObj setPos [2527.7886, 5020.4956, -7.6293945e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2530.0261, 5022.083, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -35.272125;
  _bldObj setPos [2530.0261, 5022.083, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2532.2427, 5023.6387], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -34.525654;
  _bldObj setPos [2532.2427, 5023.6387];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2534.491, 5025.1484, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -33.373241;
  _bldObj setPos [2534.491, 5025.1484, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2536.7302, 5026.6055, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -32.566566;
  _bldObj setPos [2536.7302, 5026.6055, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2539.0315, 5028.0781], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -32.386421;
  _bldObj setPos [2539.0315, 5028.0781];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2541.3345, 5029.5498], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.962809;
  _bldObj setPos [2541.3345, 5029.5498];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2543.6519, 5031], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -32.634174;
  _bldObj setPos [2543.6519, 5031];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2545.9902, 5032.4321, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.630533;
  _bldObj setPos [2545.9902, 5032.4321, 3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2548.3096, 5033.8486], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.02696;
  _bldObj setPos [2548.3096, 5033.8486];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2550.6262, 5035.2764], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.765593;
  _bldObj setPos [2550.6262, 5035.2764];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2552.9849, 5036.7251], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.346815;
  _bldObj setPos [2552.9849, 5036.7251];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2555.3491, 5038.1846, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.226324;
  _bldObj setPos [2555.3491, 5038.1846, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2557.6277, 5039.5605, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.578045;
  _bldObj setPos [2557.6277, 5039.5605, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2559.9434, 5040.9712], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -32.076694;
  _bldObj setPos [2559.9434, 5040.9712];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2562.2937, 5042.4038], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.449888;
  _bldObj setPos [2562.2937, 5042.4038];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2564.6182, 5043.8062], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.067469;
  _bldObj setPos [2564.6182, 5043.8062];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2566.9426, 5045.2183, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.629139;
  _bldObj setPos [2566.9426, 5045.2183, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2569.3308, 5046.686, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.606548;
  _bldObj setPos [2569.3308, 5046.686, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2569.6919, 5048.3364, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 57.690075;
  _bldObj setPos [2569.6919, 5048.3364, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2568.2266, 5050.6235], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.861801;
  _bldObj setPos [2568.2266, 5050.6235];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2566.8403, 5052.9604, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.965969;
  _bldObj setPos [2566.8403, 5052.9604, 3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2565.4441, 5055.249], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.851421;
  _bldObj setPos [2565.4441, 5055.249];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2564.0564, 5057.5771], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.840893;
  _bldObj setPos [2564.0564, 5057.5771];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2562.5962, 5059.9458, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.614445;
  _bldObj setPos [2562.5962, 5059.9458, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2561.1318, 5062.3887], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.604023;
  _bldObj setPos [2561.1318, 5062.3887];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2559.7104, 5064.7515, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.934151;
  _bldObj setPos [2559.7104, 5064.7515, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2558.2729, 5067.126, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.514194;
  _bldObj setPos [2558.2729, 5067.126, 3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2556.8638, 5069.4819, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 59.190125;
  _bldObj setPos [2556.8638, 5069.4819, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2555.4675, 5071.8115, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 59.466244;
  _bldObj setPos [2555.4675, 5071.8115, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2553.9954, 5074.2275, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 58.960693;
  _bldObj setPos [2553.9954, 5074.2275, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_asf3_6konec", [2578.5166, 5048.9014], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -31.664679;
  _bldObj setPos [2578.5166, 5048.9014];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_asf3_0_2000", [2575.2854, 5054.0957, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -30.788595;
  _bldObj setPos [2575.2854, 5054.0957, 3.0517578e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_asf3_6", [2566.3445, 5069.0386, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir -30.497742;
  _bldObj setPos [2566.3445, 5069.0386, -1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_asf3_6konec", [2560.28, 5078.9233, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 149.672;
  _bldObj setPos [2560.28, 5078.9233, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["LAND_Hlidac_budka", [2554.165, 5077.7119, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 148.31598;
  _bldObj setPos [2554.165, 5077.7119, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2565.8518, 5083.1411, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 149.72964;
  _bldObj setPos [2565.8518, 5083.1411, 1.5258789e-005];
};

_bldObj = objNull;
if (true) then
{
  _bldObj = createVehicle ["MAP_CncBlock", [2563.3345, 5081.647], [], 0, "CAN_COLLIDE"];
  _bldObj setDir 149.21753;
  _bldObj setPos [2563.3345, 5081.647];
};





//X END OF MAP





};