//Devil's Castle Outpost
//Made by AVendettaForYou 06/07/2013
//If any changes are made give credit where it is due. Thank you.

if (isServer) then {

_vehicle_0 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [6944.0449, 11346.682, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_0 = _this;
  _this setDir -51.209064;
  _this setPos [6944.0449, 11346.682, 9.1552734e-005];
};

_vehicle_1 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6940.4561, 11340.179, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_1 = _this;
  _this setPos [6940.4561, 11340.179, 0.00015258789];
};

_vehicle_2 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6945.27, 11346.001, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_2 = _this;
  _this setPos [6945.27, 11346.001, -9.1552734e-005];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6929.2856, 11348.986, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setDir 40.58305;
  _this setPos [6929.2856, 11348.986, 3.0517578e-005];
};

_vehicle_6 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6934.2988, 11356.046, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_6 = _this;
  _this setDir 43.574402;
  _this setPos [6934.2988, 11356.046, -0.00015258789];
};

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6940.4297, 11350.441], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setDir 47.092278;
  _this setPos [6940.4297, 11350.441];
};

_vehicle_8 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6935.4834, 11343.997, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_8 = _this;
  _this setDir 41.061543;
  _this setPos [6935.4834, 11343.997, 6.1035156e-005];
};

_vehicle_18 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BarGate2", [6919.1147, 11361.582, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_18 = _this;
  _this setDir -54.496674;
  _this setPos [6919.1147, 11361.582, 0.00018310547];
};

_vehicle_20 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [6920.5928, 11364.458, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_20 = _this;
  _this setDir -75.580536;
  _this setPos [6920.5928, 11364.458, -0.00012207031];
};

_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["Land_BagFenceLong", [6916.7329, 11359.278, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir -31.080763;
  _this setPos [6916.7329, 11359.278, -6.1035156e-005];
};

_vehicle_24 = objNull;
if (true) then
{
  _this = createVehicle ["Land_A_Castle_Donjon", [6933.7686, 11453.47, 0.0011901855], [], 0, "CAN_COLLIDE"];
  _vehicle_24 = _this;
  _this setDir -12.740684;
  _this setPos [6933.7686, 11453.47, 0.0011901855];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6944.1538, 11430.731, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir -98.408989;
  _this setPos [6944.1538, 11430.731, -3.0517578e-005];
};

_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6941.9893, 11438.699, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setDir 66.999504;
  _this setPos [6941.9893, 11438.699, -6.1035156e-005];
};

_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6946.561, 11422.797, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setDir 73.530014;
  _this setPos [6946.561, 11422.797, 0.00021362305];
};

_vehicle_30 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6909.4121, 11501.936, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_30 = _this;
  _this setDir 57.459156;
  _this setPos [6909.4121, 11501.936, 3.0517578e-005];
};

_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6905.7002, 11507.119], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir 49.04464;
  _this setPos [6905.7002, 11507.119];
};

_vehicle_32 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6840.2397, 11442.194, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_32 = _this;
  _this setDir 83.24987;
  _this setPos [6840.2397, 11442.194, -3.0517578e-005];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6839.1118, 11450.227, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setDir 86.705849;
  _this setPos [6839.1118, 11450.227, -3.0517578e-005];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6856.8076, 11401.961, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir 47.243954;
  _this setPos [6856.8076, 11401.961, -3.0517578e-005];
};

_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6862.3853, 11395.779, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir 53.821083;
  _this setPos [6862.3853, 11395.779, 3.0517578e-005];
};

_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6867.6406, 11388.986, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir 56.361145;
  _this setPos [6867.6406, 11388.986, 9.1552734e-005];
};

_vehicle_40 = objNull;
if (true) then
{
  _this = createVehicle ["HeliH", [6884.4795, 11438.455, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_40 = _this;
  _this setPos [6884.4795, 11438.455, -3.0517578e-005];
};

_vehicle_41 = objNull;
if (true) then
{
  _this = createVehicle ["Loudspeakers_EP1", [6935.5303, 11359.423, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_41 = _this;
  _this setPos [6935.5303, 11359.423, -3.0517578e-005];
};

_vehicle_42 = objNull;
if (true) then
{
  _this = createVehicle ["Loudspeakers_EP1", [6926.2783, 11348.688, 0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_42 = _this;
  _this setDir 3.6119335;
  _this setPos [6926.2783, 11348.688, 0.00024414063];
};

_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["Notice_board", [6879.3267, 11458.492, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setDir 0.96491027;
  _this setPos [6879.3267, 11458.492, 6.1035156e-005];
};

_vehicle_50 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNetB_EAST", [6928.1982, 11382.772, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_50 = _this;
  _this setDir -229.35413;
  _this setPos [6928.1982, 11382.772, -3.0517578e-005];
};

_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast_EP1", [6893.8101, 11454.019, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setDir 40.516323;
  _this setPos [6893.8101, 11454.019, -3.0517578e-005];
};

_vehicle_52 = objNull;
if (true) then
{
  _this = createVehicle ["Camp_EP1", [6884.896, 11459.612, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_52 = _this;
  _this setDir -181.22377;
  _this setPos [6884.896, 11459.612, -6.1035156e-005];
};

_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["Camp_EP1", [6875.1914, 11459.195, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setDir -186.55463;
  _this setPos [6875.1914, 11459.195, -3.0517578e-005];
};

_vehicle_54 = objNull;
if (true) then
{
  _this = createVehicle ["PowGen_Big", [6888.4834, 11458.634], [], 0, "CAN_COLLIDE"];
  _vehicle_54 = _this;
  _this setDir 34.330414;
  _this setPos [6888.4834, 11458.634];
};

_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["Land_GuardShed", [6919.002, 11368.654, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setDir 39.775673;
  _this setPos [6919.002, 11368.654, -9.1552734e-005];
};

_vehicle_56 = objNull;
if (true) then
{
  _this = createVehicle ["Land_GuardShed", [6948.0703, 11346.384], [], 0, "CAN_COLLIDE"];
  _vehicle_56 = _this;
  _this setPos [6948.0703, 11346.384];
};

_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net3", [6911.0249, 11419.886], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setPos [6911.0249, 11419.886];
};

_vehicle_58 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1Bo_military", [6902.0313, 11428.943, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_58 = _this;
  _this setPos [6902.0313, 11428.943, 3.0517578e-005];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Antenna", [6888.4766, 11464.509, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setPos [6888.4766, 11464.509, -3.0517578e-005];
};

_vehicle_60 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrates_NoInteractive_Large", [6877.2295, 11450.461, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_60 = _this;
  _this setPos [6877.2295, 11450.461, -3.0517578e-005];
};

_vehicle_61 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrates_NoInteractive_Large", [6879.2236, 11449.346], [], 0, "CAN_COLLIDE"];
  _vehicle_61 = _this;
  _this setDir 18.390692;
  _this setPos [6879.2236, 11449.346];
};

_vehicle_62 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrates_NoInteractive_Medium", [6882.6108, 11449.804], [], 0, "CAN_COLLIDE"];
  _vehicle_62 = _this;
  _this setPos [6882.6108, 11449.804];
};

_vehicle_70 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [6870.5737, 11454.516, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_70 = _this;
  _this setDir -2.9462724;
  _this setPos [6870.5737, 11454.516, -9.1552734e-005];
};

_vehicle_71 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Bench_EP1", [6870.7544, 11451.979, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_71 = _this;
  _this setDir -4.0112624;
  _this setPos [6870.7544, 11451.979, 3.0517578e-005];
};

_vehicle_74 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel", [6877.3794, 11447.699, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_74 = _this;
  _this setPos [6877.3794, 11447.699, -9.1552734e-005];
};

_vehicle_75 = objNull;
if (true) then
{
  _this = createVehicle ["Land_bags_EP1", [6893.4185, 11410.304, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_75 = _this;
  _this setPos [6893.4185, 11410.304, -9.1552734e-005];
};

_vehicle_76 = objNull;
if (true) then
{
  _this = createVehicle ["Land_bags_stack_EP1", [6891.4102, 11409.343, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_76 = _this;
  _this setPos [6891.4102, 11409.343, -3.0517578e-005];
};

_vehicle_78 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Market_stalls_02_EP1", [6880.5771, 11413.664, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_78 = _this;
  _this setDir 66.002396;
  _this setPos [6880.5771, 11413.664, -3.0517578e-005];
};

_vehicle_79 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [6889.2065, 11409.225, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_79 = _this;
  _this setPos [6889.2065, 11409.225, -6.1035156e-005];
};

_vehicle_80 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_meat_EP1", [6885.7808, 11423.278], [], 0, "CAN_COLLIDE"];
  _vehicle_80 = _this;
  _this setDir 73.14624;
  _this setPos [6885.7808, 11423.278];
};

_vehicle_81 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Timbers", [6898.0366, 11408.506, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_81 = _this;
  _this setDir -18.122396;
  _this setPos [6898.0366, 11408.506, -6.1035156e-005];
};

_vehicle_82 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Timbers", [6879.3848, 11381.119], [], 0, "CAN_COLLIDE"];
  _vehicle_82 = _this;
  _this setDir -22.561941;
  _this setPos [6879.3848, 11381.119];
};

_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Timbers", [6900.624, 11366.892], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setDir -58.395554;
  _this setPos [6900.624, 11366.892];
};

_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["Axe_woodblock", [6896.9072, 11410.738, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setPos [6896.9072, 11410.738, -6.1035156e-005];
};

_vehicle_88 = objNull;
if (true) then
{
  _this = createVehicle ["Land_radar", [6835.1992, 11482.544, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_88 = _this;
  _this setPos [6835.1992, 11482.544, -0.00012207031];
};

_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["Land_radar", [6932.1318, 11484.578, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir 33.046867;
  _this setPos [6932.1318, 11484.578, 0.00018310547];
};

_vehicle_90 = objNull;
if (true) then
{
  _this = createVehicle ["Land_radar", [6883.9849, 11528.692, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_90 = _this;
  _this setDir -20.254295;
  _this setPos [6883.9849, 11528.692, -6.1035156e-005];
};

_vehicle_91 = objNull;
if (true) then
{
  _this = createVehicle ["Satelit", [6884.3105, 11448.354, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_91 = _this;
  _this setDir -119.14141;
  _this setPos [6884.3105, 11448.354, -0.00015258789];
};

_vehicle_92 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [6898.8281, 11444.857, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_92 = _this;
  _this setDir -117.90459;
  _this setPos [6898.8281, 11444.857, -3.0517578e-005];
};

_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [6892.3955, 11502.171, 0.20307693], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setDir 23.180914;
  _this setPos [6892.3955, 11502.171, 0.20307693];
};

_vehicle_94 = objNull;
if (true) then
{
  _this = createVehicle ["Camp", [6898.3418, 11491.911], [], 0, "CAN_COLLIDE"];
  _vehicle_94 = _this;
  _this setDir -107.48416;
  _this setPos [6898.3418, 11491.911];
};

_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["Camp", [6881.9512, 11500.775, 0.069173381], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setDir 164.58336;
  _this setPos [6881.9512, 11500.775, 0.069173381];
};

_vehicle_97 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net3", [6888.002, 11485.946], [], 0, "CAN_COLLIDE"];
  _vehicle_97 = _this;
  _this setPos [6888.002, 11485.946];
};

_vehicle_99 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [6887.8481, 11492.544, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_99 = _this;
  _this setPos [6887.8481, 11492.544, 9.1552734e-005];
};

_vehicle_103 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo2E", [6850.1514, 11425.831, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_103 = _this;
  _this setPos [6850.1514, 11425.831, 6.1035156e-005];
};

_vehicle_104 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_deerstand", [6845.6592, 11444.98, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_104 = _this;
  _this setDir 74.292976;
  _this setPos [6845.6592, 11444.98, -9.1552734e-005];
};

_vehicle_105 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_deerstand", [6866.4683, 11399.634, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_105 = _this;
  _this setDir 46.123497;
  _this setPos [6866.4683, 11399.634, -9.1552734e-005];
};

_vehicle_106 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_palletsfoiled_heap", [6856.2246, 11413.851, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_106 = _this;
  _this setDir -21.627594;
  _this setPos [6856.2246, 11413.851, -6.1035156e-005];
};

_vehicle_107 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [6938.7588, 11445.744, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_107 = _this;
  _this setDir 67.595123;
  _this setPos [6938.7588, 11445.744, 0.00012207031];
};
};