/*
    Custom flags and Signs for Stomping Grounds
    Th3-Hunter333
    First test run using the load screen confirmed working
    Flags are untested at this present time
*/

if (isServer) then {

/*************** Th3-Hunter333 Custom Flags ***************/
// Stary Trader // Removed for testing deployed signs with custom textures
_flags1 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierCDF_EP1", [6323.88, 7790.8, 0.00140381], [], 0, "CAN_COLLIDE"];
  _flag1 = _this;
  _this setVehicleInit "this SetFlagTexture ""images\flag_stomp.jpg""";
  _this setPos [6323.88, 7790.8, 0.00140381];
};

/*************** Th3-Hunter333 Custom Signs ***************/
// Stary Trader
_sign1 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [6324.53, 7781.93, 0.00143433], [], 0, "CAN_COLLIDE"];
      _sign1 = _this;
      _this setDir 274;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [6324.53, 7781.93, 0.00143433];
  };
_zone1 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [6323.78, 7785.87, 0.00143433], [], 0, "CAN_COLLIDE"];
      _zone1 = _this;
      _this setDir 245;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [6323.78, 7785.87, 0.00143433];
  };

// Bash Trader
_sign2 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [4082.84, 11650, 0.00152588], [], 0, "CAN_COLLIDE"];
      _sign2 = _this;
      _this setDir 280;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [4082.84, 11650, 0.00152588];
  };
_zone2 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [4089.41, 11658.6, 0.00146484], [], 0, "CAN_COLLIDE"];
      _zone2 = _this;
      _this setDir 318;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [4089.41, 11658.6, 0.00146484];
  };

// Klen Trader
_sign3 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [11445.4, 11367.2, 0.00143433], [], 0, "CAN_COLLIDE"];
      _sign3 = _this;
      _this setDir 125;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [11445.4, 11367.2, 0.00143433];
  };
_zone3 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [11440, 11359.1, 0.00134277], [], 0, "CAN_COLLIDE"];
      _zone3 = _this;
      _this setDir 126;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [11440, 11359.1, 0.00134277];
  };

// Kozlovka Trader
_sign4 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [4677.67, 4705.32, 0.00143433], [], 0, "CAN_COLLIDE"];
      _sign4 = _this;
      _this setDir 64;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [4677.67, 4705.32, 0.00143433];
  };
_zone4 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [4668.82, 4699.56, 0.00143433], [], 0, "CAN_COLLIDE"];
      _zone4 = _this;
      _this setDir 135;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [4668.82, 4699.56, 0.00143433];
  };

// Staroye Trader
_sign5 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [9921.84, 5429.46, 0.00146484], [], 0, "CAN_COLLIDE"];
      _sign5 = _this;
      _this setDir 291;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [9921.84, 5429.46, 0.00146484];
  };
_zone5 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [9919.98, 5421.66, 0.00146484], [], 0, "CAN_COLLIDE"];
      _zone5 = _this;
      _this setDir 289;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [9919.98, 5421.66, 0.00146484];
  };

// Northwest Airfield Trader
_sign6 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [5041.87, 9718.95, 0.00143433], [], 0, "CAN_COLLIDE"];
      _sign6 = _this;
      _this setDir 50;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [5041.87, 9718.95, 0.00143433];
  };
_zone6 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [5043.89, 9714.68, 0.00143433], [], 0, "CAN_COLLIDE"];
      _zone6 = _this;
      _this setDir 106;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [5043.89, 9714.68, 0.00143433];
  };

// Eastern Airfield Trader
_sign7 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [12062.6, 12633.1, 0.0584259], [], 0, "CAN_COLLIDE"];
      _sign7 = _this;
      _this setDir 185;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [12062.6, 12633.1, 0.0584259];
  };
_zone7 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [12059.4, 12633.9, 0.0584259], [], 0, "CAN_COLLIDE"];
      _zone7 = _this;
      _this setDir 200;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [12059.4, 12633.9, 0.0584259];
  };

// Eastern Hero Trader
_sign8 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [12948.1,12762.7,0.0014801], [], 0, "CAN_COLLIDE"];
      _sign8 = _this;
      _this setDir 80;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [12948.1,12762.7,0.0014801];
  };
_zone8 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [12939.9, 12756.1, 0.0012207], [], 0, "CAN_COLLIDE"];
      _zone8 = _this;
      _this setDir 0;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [12939.9, 12756.1, 0.0012207];
  };

// Western Hero Trader
_sign9 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [1624.89, 7795.08, 0.00143433], [], 0, "CAN_COLLIDE"];
      _sign9 = _this;
      _this setDir 30;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [1624.89, 7795.08, 0.00143433];
  };
_zone9 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [1629.5, 7792.04, 0.00137329], [], 0, "CAN_COLLIDE"];
      _zone9 = _this;
      _this setDir 345;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [1629.5, 7792.04, 0.00137329];
  };

// Super Hero Trader
_sign10 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [2136.61, 14931.2, 0.00170898], [], 0, "CAN_COLLIDE"];
      _sign10 = _this;
      _this setDir 145;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [2136.61, 14931.2, 0.00170898];
  };
_zone10 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [2149.68, 14940.5, 0.0015564], [], 0, "CAN_COLLIDE"];
      _zone10 = _this;
      _this setDir 225;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [2149.68, 14940.5, 0.0015564];
  };

// Bunny's Trader
_sign11 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [4621.52, 2515.79, 0.00139618], [], 0, "CAN_COLLIDE"];
      _sign11 = _this;
      _this setDir 305;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [4621.52, 2515.79, 0.00139618];
  };
_zone11 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [4639.87, 2519.82, 0.00143909], [], 0, "CAN_COLLIDE"];
      _zone11 = _this;
      _this setDir 214;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [4639.87, 2519.82, 0.00143909];
  };

// Th3-Hunter333's Creations
_sign12 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [9304.26,8888.81,0.00125122], [], 0, "CAN_COLLIDE"];
      _sign12 = _this;
      _this setDir 52;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_signs.jpg""]";
      _this setPos [9304.26,8888.81,0.00125122];
  };
_zone12 = objNull;
if (true) then
  {
      _this = createVehicle ["WarfareBunkerSign", [9300.13, 8883.43, 0.00146484], [], 0, "CAN_COLLIDE"];
      _zone12 = _this;
      _this setDir 110;
      _this setVehicleInit "this setObjectTexture [0, ""images\traders_zones.jpg""]";
      _this setPos [9300.13, 8883.43, 0.00146484];
  };

};