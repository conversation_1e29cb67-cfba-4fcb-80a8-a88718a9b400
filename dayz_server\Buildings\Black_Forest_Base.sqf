if (isServer) then {

_vehicle_6 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [8579.8125, 8105.707, 0.087842226], [], 0, "CAN_COLLIDE"];
  _vehicle_6 = _this;
  _this setDir 245.97978;
  _this setPos [8579.8125, 8105.707, 0.087842226];
};

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8565.5234, 8085.665, -0.15908273], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setDir -23.423813;
  _this setvectorup [0,0,1];
  _this setPos [8565.5234, 8085.665, -0.15908273];
};

_vehicle_12 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small", [8550.4824, 8078.6953, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_12 = _this;
  _this setDir -25.491095;
  _this setPos [8550.4824, 8078.6953, 0.00015258789];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small", [8559.1357, 8082.6904, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir -25.491095;
  _this setPos [8559.1357, 8082.6904, -9.1552734e-005];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8573.21, 8089.2119, 0.14073715], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir -23.423813;
  _this setvectorup [0,0,1];
  _this setPos [8573.21, 8089.2119, 0.14073715];
};

_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8580.9004, 8092.7705, 0.11130691], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setDir -23.423813;
  _this setvectorup [0,0,1];
  _this setPos [8580.9004, 8092.7705, 0.11130691];
};

_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8545.0205, 8076.2354], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir -23.423813;
  _this setvectorup [0,0,1];
  _this setPos [8545.0205, 8076.2354];
};

_vehicle_38 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8537.1787, 8072.5815, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_38 = _this;
  _this setDir -23.423813;
  _this setvectorup [0,0,1];
  _this setPos [8537.1787, 8072.5815, -3.0517578e-005];
};

_vehicle_40 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [8559.6084, 8080.5786, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_40 = _this;
  _this setDir -23.401848;
  _this setPos [8559.6084, 8080.5786, -3.0517578e-005];
  _this animate ["bargate",1];
};

_vehicle_41 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [8543.8633, 8081.7832, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_41 = _this;
  _this setDir -23.754656;
  _this setvectorup [0,0,1];
  _this setPos [8543.8633, 8081.7832, 0.00018310547];
};

_vehicle_42 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [8562.0039, 8090.0586, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_42 = _this;
  _this setDir -24.093859;
  _this setvectorup [0,0,1];
  _this setPos [8562.0039, 8090.0586, 3.0517578e-005];
};

_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8588.4551, 8096.27, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setDir -23.423813;
  _this setvectorup [0,0,1];
  _this setPos [8588.4551, 8096.27, -6.1035156e-005];
};

_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8529.4502, 8068.9995, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setDir -23.423813;
  _this setvectorup [0,0,1];
  _this setPos [8529.4502, 8068.9995, 6.1035156e-005];
};

_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [8592.4219, 8102.8784, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setDir -204.62703;
  _this setvectorup [0,0,1];
  _this setPos [8592.4219, 8102.8784, -3.0517578e-005];
};

_vehicle_54 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8590.9053, 8111.1152, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_54 = _this;
  _this setDir -111.83819;
  _this setvectorup [0,0,1];
  _this setPos [8590.9053, 8111.1152, 3.0517578e-005];
};

_vehicle_57 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8587.4512, 8119.0635, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_57 = _this;
  _this setDir -111.83819;
  _this setvectorup [0,0,1];
  _this setPos [8587.4512, 8119.0635, -6.1035156e-005];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8583.957, 8126.9331, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir -111.83819;
  _this setvectorup [0,0,1];
  _this setPos [8583.957, 8126.9331, 3.0517578e-005];
};

_vehicle_61 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8580.4492, 8134.7949], [], 0, "CAN_COLLIDE"];
  _vehicle_61 = _this;
  _this setDir -111.83819;
  _this setvectorup [0,0,1];
  _this setPos [8580.4492, 8134.7949];
};

_vehicle_63 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8576.9795, 8142.6372, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_63 = _this;
  _this setDir -111.83819;
  _this setvectorup [0,0,1];
  _this setPos [8576.9795, 8142.6372, -6.1035156e-005];
};

_vehicle_65 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8573.5674, 8150.416, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_65 = _this;
  _this setDir -111.83819;
  _this setvectorup [0,0,1];
  _this setPos [8573.5674, 8150.416, -0.00021362305];
};

_vehicle_67 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8570.1357, 8158.1616, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_67 = _this;
  _this setDir -111.83819;
  _this setvectorup [0,0,1];
  _this setPos [8570.1357, 8158.1616, 6.1035156e-005];
};

_vehicle_69 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8566.6289, 8165.8013], [], 0, "CAN_COLLIDE"];
  _vehicle_69 = _this;
  _this setDir -111.83819;
  _this setvectorup [0,0,1];
  _this setPos [8566.6289, 8165.8013];
};

_vehicle_72 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [8556.7588, 8177.1621, 0.030987872], [], 0, "CAN_COLLIDE"];
  _vehicle_72 = _this;
  _this setDir -204.0018;
  _this setvectorup [0,0,1];
  _this setPos [8556.7588, 8177.1621, 0.030987872];
};

_vehicle_75 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8563.0391, 8173.4639, -0.25904581], [], 0, "CAN_COLLIDE"];
  _vehicle_75 = _this;
  _this setDir -111.83819;
  _this setvectorup [0,0,1];
  _this setPos [8563.0391, 8173.4639, -0.25904581];
};

_vehicle_77 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8559.6641, 8181.0898, -0.43435743], [], 0, "CAN_COLLIDE"];
  _vehicle_77 = _this;
  _this setDir -111.83819;
  _this setvectorup [0,0,1];
  _this setPos [8559.6641, 8181.0898, -0.43435743];
};

_vehicle_80 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small", [8549.6797, 8180.1997, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_80 = _this;
  _this setDir -169.27167;
  _this setPos [8549.6797, 8180.1997, 6.1035156e-005];
};

_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8555.583, 8182.4932, -0.36642075], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setDir -204.043;
  _this setvectorup [0,0,1];
  _this setPos [8555.583, 8182.4932, -0.36642075];
};

_vehicle_86 = objNull;
if (true) then
{
  _this = createVehicle ["Land_fortified_nest_small", [8541.1152, 8181.9307], [], 0, "CAN_COLLIDE"];
  _vehicle_86 = _this;
  _this setDir -169.27167;
  _this setPos [8541.1152, 8181.9307];
};

_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8533.8369, 8183.3921, -0.15926652], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir -166.56529;
  _this setvectorup [0,0,1];
  _this setPos [8533.8369, 8183.3921, -0.15926652];
};

_vehicle_92 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8525.5723, 8185.2061, -0.35298538], [], 0, "CAN_COLLIDE"];
  _vehicle_92 = _this;
  _this setDir -166.56529;
  _this setvectorup [0,0,1];
  _this setPos [8525.5723, 8185.2061, -0.35298538];
};

_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8519.4453, 8183.0264, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setDir -254.37166;
  _this setvectorup [0,0,1];
  _this setPos [8519.4453, 8183.0264, 3.0517578e-005];
};

_vehicle_98 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8517.2559, 8174.7393, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_98 = _this;
  _this setDir -254.37166;
  _this setvectorup [0,0,1];
  _this setPos [8517.2559, 8174.7393, 0.00021362305];
};

_vehicle_100 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8515.0664, 8166.4526, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_100 = _this;
  _this setDir -254.37166;
  _this setvectorup [0,0,1];
  _this setPos [8515.0664, 8166.4526, -6.1035156e-005];
};

_vehicle_102 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8512.8623, 8158.1387, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_102 = _this;
  _this setDir -254.37166;
  _this setvectorup [0,0,1];
  _this setPos [8512.8623, 8158.1387, -0.00015258789];
};

_vehicle_105 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8510.7178, 8149.938, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_105 = _this;
  _this setDir -254.37166;
  _this setvectorup [0,0,1];
  _this setPos [8510.7178, 8149.938, -0.00015258789];
};

_vehicle_107 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8508.5576, 8141.7813, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_107 = _this;
  _this setDir -254.37166;
  _this setvectorup [0,0,1];
  _this setPos [8508.5576, 8141.7813, -0.00024414063];
};

_vehicle_109 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8506.3574, 8133.5977, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_109 = _this;
  _this setDir -254.37166;
  _this setvectorup [0,0,1];
  _this setPos [8506.3574, 8133.5977, 0.00021362305];
};

_vehicle_112 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8504.1201, 8125.4893, -0.00036621094], [], 0, "CAN_COLLIDE"];
  _vehicle_112 = _this;
  _this setDir -254.37166;
  _this setvectorup [0,0,1];
  _this setPos [8504.1201, 8125.4893, -0.00036621094];
};

_vehicle_114 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8502.082, 8117.2861, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_114 = _this;
  _this setDir -254.37166;
  _this setvectorup [0,0,1];
  _this setPos [8502.082, 8117.2861, 6.1035156e-005];
};

_vehicle_116 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8499.9258, 8109.1182, 0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_116 = _this;
  _this setDir -254.37166;
  _this setvectorup [0,0,1];
  _this setPos [8499.9258, 8109.1182, 0.00021362305];
};

_vehicle_118 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8499.1895, 8103.6875, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_118 = _this;
  _this setDir -264.80685;
  _this setvectorup [0,0,1];
  _this setPos [8499.1895, 8103.6875, -9.1552734e-005];
};

_vehicle_125 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8511.8936, 8073.6934, -0.10236382], [], 0, "CAN_COLLIDE"];
  _vehicle_125 = _this;
  _this setDir -293.3949;
  _this setvectorup [0,0,1];
  _this setPos [8511.8936, 8073.6934, -0.10236382];
};

_vehicle_127 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8508.1689, 8081.3105, -0.068313755], [], 0, "CAN_COLLIDE"];
  _vehicle_127 = _this;
  _this setDir -293.3949;
  _this setvectorup [0,0,1];
  _this setPos [8508.1689, 8081.3105, -0.068313755];
};

_vehicle_130 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8504.4912, 8088.8125, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_130 = _this;
  _this setDir -293.3949;
  _this setvectorup [0,0,1];
  _this setPos [8504.4912, 8088.8125, 0.00018310547];
};

_vehicle_132 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8500.9375, 8096.4448, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_132 = _this;
  _this setDir -293.3949;
  _this setvectorup [0,0,1];
  _this setPos [8500.9375, 8096.4448, 9.1552734e-005];
};

_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [8517.7617, 8066.1475, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir -26.206108;
  _this setPos [8517.7617, 8066.1475, -9.1552734e-005];
};

_vehicle_138 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [8548.6611, 8182.4487, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_138 = _this;
  _this setDir 10.677711;
  _this setPos [8548.6611, 8182.4487, -3.0517578e-005];
  _this animate ["bargate",1];
};

_vehicle_141 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Mil_Barracks_i", [8570.9658, 8101.7725, 0.12629919], [], 0, "CAN_COLLIDE"];
  _vehicle_141 = _this;
  _this setDir 246.32756;
  _this setPos [8570.9658, 8101.7725, 0.12629919];
};

_vehicle_147 = objNull;
if (true) then
{
  _this = createVehicle ["Land_HBarrier_large", [8525.166, 8066.9653], [], 0, "CAN_COLLIDE"];
  _vehicle_147 = _this;
  _this setDir -23.423813;
  _this setvectorup [0,0,1];
  _this setPos [8525.166, 8066.9653];
};

_vehicle_148 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [8563.9023, 8152.645, -0.072862312], [], 0, "CAN_COLLIDE"];
  _vehicle_148 = _this;
  _this setDir 56.206341;
  _this setPos [8563.9023, 8152.645, -0.072862312];
};

_vehicle_149 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Bo", [8566.4492, 8150.0801, -0.10483958], [], 0, "CAN_COLLIDE"];
  _vehicle_149 = _this;
  _this setDir 55.079376;
  _this setPos [8566.4492, 8150.0801, -0.10483958];
};

_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1C", [8570.5713, 8146.1758, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setDir -21.534214;
  _this setPos [8570.5713, 8146.1758, 0.00012207031];
};

_vehicle_151 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1D", [8564.6074, 8140.4292, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_151 = _this;
  _this setDir 40.024101;
  _this setPos [8564.6074, 8140.4292, 6.1035156e-005];
};

_vehicle_152 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo2B", [8568.2256, 8144.2681, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_152 = _this;
  _this setDir -25.652191;
  _this setPos [8568.2256, 8144.2681, 0.00012207031];
};

_vehicle_155 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Ao", [8571.4814, 8139.9795, -0.028024152], [], 0, "CAN_COLLIDE"];
  _vehicle_155 = _this;
  _this setDir 64.896042;
  _this setPos [8571.4814, 8139.9795, -0.028024152];
};

_vehicle_156 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Cargo1Bo", [8572.9844, 8136.7827, -0.080912083], [], 0, "CAN_COLLIDE"];
  _vehicle_156 = _this;
  _this setDir 62.91325;
  _this setPos [8572.9844, 8136.7827, -0.080912083];
};

_vehicle_157 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [8570.6836, 8135.9536, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_157 = _this;
  _this setPos [8570.6836, 8135.9536, 3.0517578e-005];
};

_vehicle_159 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [8563.7354, 8149.7607, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_159 = _this;
  _this setPos [8563.7354, 8149.7607, 0];
};

_vehicle_162 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Mil_Barracks_i", [8521.2744, 8081.1367, 0.18488453], [], 0, "CAN_COLLIDE"];
  _vehicle_162 = _this;
  _this setDir 333.90945;
  _this setPos [8521.2744, 8081.1367, 0.18488453];
};

_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Mil_Barracks_i", [8516.3945, 8091.0186, 0.25813976], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setDir 333.90945;
  _this setPos [8516.3945, 8091.0186, 0.25813976];
};

_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["ACamp", [8548.3721, 8147.6079, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setDir 34.384815;
  _this setPos [8548.3721, 8147.6079, 3.0517578e-005];
};

_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [8574.1445, 8129.9341, -0.0040999595], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setDir -657.35913;
  _this setPos [8574.1445, 8129.9341, -0.0040999595];
};

_vehicle_169 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [8578.0322, 8122.1069, 0.056453817], [], 0, "CAN_COLLIDE"];
  _vehicle_169 = _this;
  _this setDir 64.62542;
  _this setPos [8578.0322, 8122.1069, 0.056453817];
};

_vehicle_170 = objNull;
if (true) then
{
  _this = createVehicle ["Camp", [8514.6484, 8143.2974, -0.046627], [], 0, "CAN_COLLIDE"];
  _vehicle_170 = _this;
  _this setDir 103.73878;
  _this setPos [8514.6484, 8143.2974, -0.046627];
};

_vehicle_172 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [8516.4863, 8149.9688, -0.013794499], [], 0, "CAN_COLLIDE"];
  _vehicle_172 = _this;
  _this setDir 103.17104;
  _this setPos [8516.4863, 8149.9688, -0.013794499];
};

_vehicle_174 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [8517.9287, 8156.731, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_174 = _this;
  _this setDir 103.17104;
  _this setPos [8517.9287, 8156.731, 0.00018310547];
};

_vehicle_176 = objNull;
if (true) then
{
  _this = createVehicle ["MASH_EP1", [8519.3672, 8163.4224], [], 0, "CAN_COLLIDE"];
  _vehicle_176 = _this;
  _this setDir 103.17104;
  _this setPos [8519.3672, 8163.4224];
};

_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["Camp", [8512.6758, 8137.0601, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setDir 103.73878;
  _this setPos [8512.6758, 8137.0601, 9.1552734e-005];
};

_vehicle_181 = objNull;
if (true) then
{
  _this = createVehicle ["Camp", [8511.0654, 8130.8872], [], 0, "CAN_COLLIDE"];
  _vehicle_181 = _this;
  _this setDir 103.73878;
  _this setPos [8511.0654, 8130.8872];
};

_vehicle_183 = objNull;
if (true) then
{
  _this = createVehicle ["HeliH", [8558.5938, 8120.0313, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_183 = _this;
  _this setPos [8558.5938, 8120.0313, -6.1035156e-005];
};

_vehicle_185 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [8558.3125, 8120.1416, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_185 = _this;
  _this setPos [8558.3125, 8120.1416, 0];
};

_vehicle_188 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concrete", [8550.4746, 8187.8101, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_188 = _this;
  _this setDir -80.320244;
  _this setPos [8550.4746, 8187.8101, -3.0517578e-005];
};

_vehicle_190 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concrete", [8542.2402, 8188.9492], [], 0, "CAN_COLLIDE"];
  _vehicle_190 = _this;
  _this setDir -258.94589;
  _this setPos [8542.2402, 8188.9492];
};

_vehicle_192 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concrete", [8561.7549, 8075.9302, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_192 = _this;
  _this setDir -110.88203;
  _this setPos [8561.7549, 8075.9302, -3.0517578e-005];
};

_vehicle_194 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concrete", [8554.0225, 8072.4438, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_194 = _this;
  _this setDir 68.754448;
  _this setPos [8554.0225, 8072.4438, 3.0517578e-005];
};

_vehicle_196 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierINS", [8561.6035, 8083.2046, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_196 = _this;
  _this setPos [8561.6035, 8083.2046, -6.1035156e-005];
};

_vehicle_206 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierINS", [8549.1592, 8075.9492], [], 0, "CAN_COLLIDE"];
  _vehicle_206 = _this;
  _this setPos [8549.1592, 8075.9492];
};

_vehicle_209 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierINS", [8538.4209, 8184.6045, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_209 = _this;
  _this setPos [8538.4209, 8184.6045, -9.1552734e-005];
};

_vehicle_210 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierINS", [8551.3613, 8182.0649], [], 0, "CAN_COLLIDE"];
  _vehicle_210 = _this;
  _this setPos [8551.3613, 8182.0649];
};

_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8543.1914, 8193.8633, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setPos [8543.1914, 8193.8633, 6.1035156e-005];
};

_vehicle_213 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8551.0664, 8192.7197, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_213 = _this;
  _this setPos [8551.0664, 8192.7197, -3.0517578e-005];
};

_vehicle_215 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8563.623, 8071.2651, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_215 = _this;
  _this setPos [8563.623, 8071.2651, -3.0517578e-005];
};

_vehicle_217 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8556.0527, 8067.8999, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_217 = _this;
  _this setPos [8556.0527, 8067.8999, 3.0517578e-005];
};

_vehicle_219 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8501.749, 8100.0532], [], 0, "CAN_COLLIDE"];
  _vehicle_219 = _this;
  _this setPos [8501.749, 8100.0532];
};

_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8512.751, 8147.4048, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setPos [8512.751, 8147.4048, -0.00015258789];
};

_vehicle_223 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8522.2441, 8183.731, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_223 = _this;
  _this setPos [8522.2441, 8183.731, 6.1035156e-005];
};

_vehicle_226 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_IlluminantTower", [8543.5313, 8132.3838, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_226 = _this;
  _this setPos [8543.5313, 8132.3838, 0];
};

_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8561.2383, 8171.9971, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setPos [8561.2383, 8171.9971, -0.00021362305];
};

_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8575.249, 8125.5469, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setPos [8575.249, 8125.5469, -9.1552734e-005];
};

_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8589.1514, 8099.2319], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setPos [8589.1514, 8099.2319];
};

_vehicle_234 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8566.9063, 8088.731], [], 0, "CAN_COLLIDE"];
  _vehicle_234 = _this;
  _this setPos [8566.9063, 8088.731];
};

_vehicle_236 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [8541.3174, 8077.0518, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_236 = _this;
  _this setPos [8541.3174, 8077.0518, -0.00015258789];
};

_vehicle_239 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [8510.9141, 8118.3203, 0.064769559], [], 0, "CAN_COLLIDE"];
  _vehicle_239 = _this;
  _this setDir -796.31689;
  _this setPos [8510.9141, 8118.3203, 0.064769559];
};

_vehicle_241 = objNull;
if (true) then
{
  _this = createVehicle ["CampEast", [8510.8184, 8102.2158, 0.025168031], [], 0, "CAN_COLLIDE"];
  _vehicle_241 = _this;
  _this setDir -836.36273;
  _this setPos [8510.8184, 8102.2158, 0.025168031];
};

_vehicle_243 = objNull;
if (true) then
{
  _this = createVehicle ["Hanged", [8541.585, 8130.2134, 11.630387], [], 0, "CAN_COLLIDE"];
  _vehicle_243 = _this;
  _this setPos [8541.585, 8130.2134, 11.630387];
};

_vehicle_244 = objNull;
if (true) then
{
  _this = createVehicle ["Hanged_MD", [8541.6572, 8134.0903, 12.11177], [], 0, "CAN_COLLIDE"];
  _vehicle_244 = _this;
  _this setDir 112.61148;
  _this setPos [8541.6572, 8134.0903, 12.11177];
};

_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Church_tomb_1", [8499.9346, 8161.1118, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setPos [8499.9346, 8161.1118, -6.1035156e-005];
};

_vehicle_247 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Church_tomb_2", [8503.0234, 8161.1436, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_247 = _this;
  _this setPos [8503.0234, 8161.1436, -6.1035156e-005];
};

_vehicle_248 = objNull;
if (true) then
{
  _this = createVehicle ["GraveCrossHelmet", [8497.0186, 8161.0752, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_248 = _this;
  _this setPos [8497.0186, 8161.0752, 6.1035156e-005];
};

_vehicle_249 = objNull;
if (true) then
{
  _this = createVehicle ["Body", [8523.252, 8169.9961], [], 0, "CAN_COLLIDE"];
  _vehicle_249 = _this;
  _this setDir -45.757816;
  _this setPos [8523.252, 8169.9961];
};

_vehicle_250 = objNull;
if (true) then
{
  _this = createVehicle ["Body", [8522.7578, 8167.7583], [], 0, "CAN_COLLIDE"];
  _vehicle_250 = _this;
  _this setDir -45.757816;
  _this setPos [8522.7578, 8167.7583];
};

_vehicle_251 = objNull;
if (true) then
{
  _this = createVehicle ["Body", [8522.7666, 8169.0596, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_251 = _this;
  _this setDir -45.757816;
  _this setPos [8522.7666, 8169.0596, 3.0517578e-005];
};

_vehicle_252 = objNull;
if (true) then
{
  _this = createVehicle ["GraveCross1", [8497.5986, 8155.3306, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_252 = _this;
  _this setPos [8497.5986, 8155.3306, -0.00021362305];
};

_vehicle_253 = objNull;
if (true) then
{
  _this = createVehicle ["Grave", [8497.3818, 8154.4517, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_253 = _this;
  _this setPos [8497.3818, 8154.4517, -6.1035156e-005];
};

_vehicle_255 = objNull;
if (true) then
{
  _this = createVehicle ["Grave", [8496.9824, 8160.1758, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_255 = _this;
  _this setPos [8496.9824, 8160.1758, -6.1035156e-005];
};

_vehicle_257 = objNull;
if (true) then
{
  _this = createVehicle ["Grave", [8502.7314, 8159.9199], [], 0, "CAN_COLLIDE"];
  _vehicle_257 = _this;
  _this setPos [8502.7314, 8159.9199];
};

_vehicle_259 = objNull;
if (true) then
{
  _this = createVehicle ["Grave", [8499.7168, 8160.1216, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_259 = _this;
  _this setPos [8499.7168, 8160.1216, 0.00015258789];
};
};