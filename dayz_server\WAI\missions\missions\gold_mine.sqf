local _mission = count WAI_MissionData -1;
local _aiType = _this select 0; // "Bandit" or "Hero"
local _position = [30] call WAI_FindPos;
local _name = "Gold Mine";
local _startTime = diag_tickTime;
local _difficulty = "Hard";
local _localized = ["STR_CL_MISSION_BANDIT", "STR_CL_MISSION_HERO"] select (_aiType == "Hero");
local _localName = "STR_CUST_WAI_GOLDMINE_TITLE";

diag_log format["[WAI]: %1 %2 started at %3.",_aiType,_name,_position];

local _messages = if (_aiType == "Hero") then {
	["STR_CUST_WAI_HERO_GOLDMINE_START","STR_CUST_WAI_HERO_GOLDMINE_WIN","STR_CUST_WAI_HERO_GOLDMINE_FAIL"];
} else {
	["STR_CUST_WAI_BANDIT_GOLDMINE_START","STR_CUST_WAI_BANDIT_GOLDMINE_WIN","STR_CUST_WAI_BANDIT_GOLDMINE_FAIL"];
};

////////////////////// Do not edit this section ///////////////////////////
local _markers = [1,1,1,1];
//[position,createMarker,setMarkerColor,setMarkerType,setMarkerShape,setMarkerBrush,setMarkerSize,setMarkerText,setMarkerAlpha]
_markers set [0, [_position, "WAI" + str(_mission), "ColorRed", "", "ELLIPSE", "SolidBorder", [300,300], [], 0]];
_markers set [1, [_position, "WAI" + str(_mission) + "dot", "ColorBlue", "mil_dot", "", "", [], [_localName], 0]];
if (WAI_AutoClaim) then {_markers set [2, [_position, "WAI" + str(_mission) + "auto", "ColorBlue", "", "ELLIPSE", "Border", [WAI_AcAlertDistance,WAI_AcAlertDistance], [], 0]];};
DZE_ServerMarkerArray set [count DZE_ServerMarkerArray, _markers]; // Markers added to global array for JIP player requests.
_markerIndex = count DZE_ServerMarkerArray - 1;
PVDZ_ServerMarkerSend = ["start",_markers];
publicVariable "PVDZ_ServerMarkerSend";

WAI_MarkerReady = true;

// Add the mission's position to the global array so that other missions do not spawn near it.
DZE_MissionPositions set [count DZE_MissionPositions, _position];
local _posIndex = count DZE_MissionPositions - 1;

// Send announcement
[_difficulty,(_messages select 0)] call WAI_Message;

// Wait until a player is within range or timeout is reached.
local _timeout = false;
local _claimPlayer = objNull;

while {WAI_WaitForPlayer && !_timeout && {isNull _claimPlayer}} do {
	_claimPlayer = [_position, WAI_TimeoutDist] call isClosestPlayer;
	
	if (diag_tickTime - _startTime >= (WAI_Timeout * 60)) then {
		_timeout = true;
	};
	uiSleep 1;
};

if (_timeout) exitWith {
	[_mission, _aiType, _markerIndex, _posIndex] call WAI_AbortMission;
	[_difficulty,(_messages select 2)] call WAI_Message;
	diag_log format["WAI: %1 %2 aborted.",_aiType,_name,_position];
};
//////////////////////////////// End //////////////////////////////////////

//Spawn Crates      [14,5,1,3,2] // Dynamic crate array
local _loot = if (_aiType == "Hero") then {Loot_GemTower select 0;} else {Loot_GemTower select 1;};
[[
	[_loot,WAI_CrateSm,[-0.01,0]]
],_position,_mission] call WAI_SpawnCrate;

// Spawn Objects
[[
	//Buildings 
	["MAP_R2_RockWall",[-24,11,-7.1],-85.388],
	["MAP_R2_RockWall",[6,16,-5.9],-151.6],
	["MAP_R2_RockWall",[30,3,-7.1],40.18],
	["MAP_R2_RockWall",[45,-18,-7.1],131.1],
	["MAP_R2_RockWall",[-29,-12,-7.1],-258.57],
	["MAP_R2_RockWall",[-0.1,15,16.85],0],
	["MAP_R2_RockWall",[19,-9,17.6],0],
	["MAP_R2_RockWall",[-11,-3,14.8],-128.72],
	["MAP_R2_RockWall",[-3,-9,20.37],165.8],
	["MAP_R2_RockWall",[-29,-27,-7.1],0],
	["MAP_R2_RockWall",[-20,-36,-7.1],66.4],
	["MAP_R2_RockWall",[-2,-25,16.2],23.5],
	["MAP_R2_RockWall",[11,-30,16.2],-13.9],
	["MAP_R2_RockTower",[7,- 24,-20.4],-28.07],
	["MAP_R2_Boulder2",[14,-27,-0.015],0],
	["MAP_R2_Boulder1",[18,-37,-0.015],0],
	["MAP_R2_Rock1",[-2,-7,-25.05],-4.74],
	["MAP_Ind_HammerMill",[30,-53,-6.1],-69.97],
	["UralWreck",[34,-59,-0.01],14.54],
	["MAP_Ind_Conveyer",[22,-45,0.1],116.29],
	["Land_Dirthump01",[13,-38,-1.41],92.4],
	["Land_Dirthump01",[13,-39,-1.45],42.23],
	["MAP_R2_RockTower",[60,-55,-26.69],0],
	["MAP_R2_RockTower",[12,-121,-0.02],98.4],
	["Gold_Vein_DZE",[13,-14,-0.01],0],
	["Gold_Vein_DZE",[-6,-25,-0.01],0],
	["Gold_Vein_DZE",[-8,-17,-0.01],0],
	["fiberplant",[7,-1,-0.02],0],
	["fiberplant",[5,-6,-0.02],0],
	["fiberplant",[-13,-21,-0.02],0],
	["fiberplant",[19,-21,-0.02],0]
	
],_position,_mission] call WAI_SpawnObjects;

// Group Spawn Examples
// Parameters:	0: Position
//				1: Unit Count
//				2: Unit Skill ("easy","medium","hard","extreme" or "random")
//				3: Primary gun - "Random", "Unarmed", "Classname", Array
//				4: Launcher - "AT", "AA", or "" for no launcher
//				5: Backpack - "Random", "Classname", "none" or Array
//				6: Skin (_aiType, "Random","classname", array)
//				7: Gearset - 0,1,2, or "Random"
//				8: AI Type (_aiType, or ["type", #] format to overwrite default gain amount) ***Used to determine humanity gain or loss***
//				9: Mission variable from line 5 (_mission)

//AI Spawns
local _num = round (random 3) + 4; // You can use this to get a random number of AI between 4 and 7.   
[[(_position select 0) + 20, (_position select 1) - 54, 0],_num,_difficulty,WAI_Scar,"AA",WAI_Packs,WAI_AllSkin,"Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) - 15, (_position select 1) - 70, 0],2,_difficulty,WAI_Sniper,"AA",WAI_Packs,WAI_AllSkin,"Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) + 60, (_position select 1) - 38, 0],4,_difficulty,WAI_RandomWeapon,"AT",WAI_Packs,WAI_AllSkin,"Random",_aiType,_mission] call WAI_SpawnGroup;
[[(_position select 0) + 33, (_position select 1) + 20, 0],4,_difficulty,WAI_ACR,"AT",WAI_Packs,WAI_AllSkin,"Random",_aiType,_mission] call WAI_SpawnGroup;

//Static Guns
[[
	[(_position select 0) + 44, (_position select 1) - 55, 0],
	[(_position select 0) + 4, (_position select 1) - 70, 0]
],"M2StaticMG",_difficulty,_aiType,_aiType,"Random","Random","Random",_mission] call WAI_SpawnStatic;

[
	_mission, // Mission number
	_position, // Position of mission
	_difficulty, // Difficulty
	_name, // Name of Mission
	_localName, // localized marker text
	_aiType, // "Bandit" or "Hero"
	_markerIndex,
	_posIndex,
	_claimPlayer,
	true, // show mission marker?
	true, // make minefields available for this mission
	["crate"], // Completion type: ["crate"], ["kill"], or ["assassinate", _unitGroup],
	_messages
] spawn WAI_MissionMonitor;
