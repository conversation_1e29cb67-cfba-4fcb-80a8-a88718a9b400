if (isServer) then {

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["Land_GuardShed", [2098.7759, 14938.806], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setDir -87.288643;
  _this setPos [2098.7759, 14938.806];
};

_vehicle_12 = objNull;
if (true) then
{
  _this = createVehicle ["PowGen_Big", [2141.0042, 14935.984], [], 0, "CAN_COLLIDE"];
  _vehicle_12 = _this;
  _this setDir 32.995869;
  _this setPos [2141.0042, 14935.984];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CamoNetB_EAST", [2141.0344, 14931.761, 0.19058657], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir 116.93661;
  _this setPos [2141.0344, 14931.761, 0.19058657];
};

_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2150.6953, 14933.69, 0.033433847], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir -4.2803578;
  _this setPos [2150.6953, 14933.69, 0.033433847];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net3", [2139.1392, 14929.316, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir -33.239319;
  _this setPos [2139.1392, 14929.316, -6.1035156e-005];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2147.1895, 14923.646, -0.05544287], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir 44.911156;
  _this setPos [2147.1895, 14923.646, -0.05544287];
};

_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2149.8369, 14928.528, -0.04561371], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setDir 24.3606;
  _this setPos [2149.8369, 14928.528, -0.04561371];
};

_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["Land_tent_east", [2153.8892, 14959.384, -0.11286713], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setDir 33.385288;
  _this setPos [2153.8892, 14959.384, -0.11286713];
};

_vehicle_144 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dum_istan3_pumpa", [2167.2817, 14958.979, 0.13811207], [], 0, "CAN_COLLIDE"];
  _vehicle_144 = _this;
  _this setDir 361.06018;
  _this setPos [2167.2817, 14958.979, 0.13811207];
};

_vehicle_163 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Nasypka", [2146.9924, 14953.993, 0.16030884], [], 0, "CAN_COLLIDE"];
  _vehicle_163 = _this;
  _this setDir -272.07217;
  _this setPos [2146.9924, 14953.993, 0.16030884];
};

_vehicle_227 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Build", [2093.1074, 14833.817, 0.27317572], [], 0, "CAN_COLLIDE"];
  _vehicle_227 = _this;
  _this setDir -60.118404;
  _this setPos [2093.1074, 14833.817, 0.27317572];
};

_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Feed", [2115.2222, 14828.084, 0.33311558], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setDir -60.116028;
  _this setPos [2115.2222, 14828.084, 0.33311558];
};

_vehicle_229 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Feed", [2107.907, 14829.234, 0.38961241], [], 0, "CAN_COLLIDE"];
  _vehicle_229 = _this;
  _this setDir -63.846508;
  _this setPos [2107.907, 14829.234, 0.38961241];
};

_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Feed", [2108.9224, 14831.162, 0.33474725], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setDir -61.761787;
  _this setPos [2108.9224, 14831.162, 0.33474725];
};

_vehicle_231 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Feed", [2114.1641, 14826.043, 0.44586688], [], 0, "CAN_COLLIDE"];
  _vehicle_231 = _this;
  _this setDir -59.685654;
  _this setPos [2114.1641, 14826.043, 0.44586688];
};

_vehicle_232 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Shed", [2108.7251, 14830.563, 0.069765814], [], 0, "CAN_COLLIDE"];
  _vehicle_232 = _this;
  _this setDir -151.23163;
  _this setPos [2108.7251, 14830.563, 0.069765814];
};

_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Sign", [2091.0491, 14820.339, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setDir 87.571182;
  _this setPos [2091.0491, 14820.339, -0.00015258789];
};

_vehicle_235 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Feed", [2101.8787, 14832.699, 0.59550613], [], 0, "CAN_COLLIDE"];
  _vehicle_235 = _this;
  _this setDir -60.735458;
  _this setPos [2101.8787, 14832.699, 0.59550613];
};

_vehicle_236 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_FuelStation_Feed", [2103.0547, 14834.6, 0.54081309], [], 0, "CAN_COLLIDE"];
  _vehicle_236 = _this;
  _this setDir -61.024509;
  _this setPos [2103.0547, 14834.6, 0.54081309];
};

_vehicle_243 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_WaterStation", [2202.1714, 14933.541, 0.00011936412], [], 0, "CAN_COLLIDE"];
  _vehicle_243 = _this;
  _this setDir 89.451012;
  _this setPos [2202.1714, 14933.541, 0.00011936412];
};

_vehicle_246 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHV_lampa_sidlconc", [2154.8679, 14941.614], [], 0, "CAN_COLLIDE"];
  _vehicle_246 = _this;
  _this setDir 91.889282;
  _this setPos [2154.8679, 14941.614];
};

_vehicle_250 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHV_lampa_sidlconc", [2184.6536, 14948.134, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_250 = _this;
  _this setDir -88.170876;
  _this setPos [2184.6536, 14948.134, -6.1035156e-005];
};

_vehicle_251 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHV_lampa_sidlconc", [2103.0745, 14843.188, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_251 = _this;
  _this setDir -150.9377;
  _this setPos [2103.0745, 14843.188, -3.0517578e-005];
};

_vehicle_252 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHV_lampa_sidlconc", [2089.6592, 14820.122, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_252 = _this;
  _this setDir 207.24438;
  _this setPos [2089.6592, 14820.122, 3.0517578e-005];
};

_vehicle_253 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHV_lampa_sidlconc", [2184.2598, 14940.24, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_253 = _this;
  _this setDir 87.622177;
  _this setPos [2184.2598, 14940.24, -0.00015258789];
};

_vehicle_254 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHV_lampa_sidlconc", [2153.9783, 14947.904], [], 0, "CAN_COLLIDE"];
  _vehicle_254 = _this;
  _this setDir -85.481613;
  _this setPos [2153.9783, 14947.904];
};

_vehicle_255 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_PowLines_Transformer1", [2200.2654, 14930.939, 0.32657728], [], 0, "CAN_COLLIDE"];
  _vehicle_255 = _this;
  _this setDir -173.12096;
  _this setPos [2200.2654, 14930.939, 0.32657728];
};

_vehicle_270 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garaz", [2113.1072, 14908.659, 0.11316685], [], 0, "CAN_COLLIDE"];
  _vehicle_270 = _this;
  _this setDir 2.6069996;
  _this setPos [2113.1072, 14908.659, 0.11316685];
};

_vehicle_271 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garaz_mala", [2141.3967, 14957.765, 0.10974173], [], 0, "CAN_COLLIDE"];
  _vehicle_271 = _this;
  _this setDir -177.77774;
  _this setPos [2141.3967, 14957.765, 0.10974173];
};

_vehicle_274 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garaz", [2128.4302, 14907.991, 0.13623895], [], 0, "CAN_COLLIDE"];
  _vehicle_274 = _this;
  _this setDir 2.2195172;
  _this setPos [2128.4302, 14907.991, 0.13623895];
};

_vehicle_275 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garaz", [2093.1956, 14947.162, 0.19350262], [], 0, "CAN_COLLIDE"];
  _vehicle_275 = _this;
  _this setDir 92.229904;
  _this setPos [2093.1956, 14947.162, 0.19350262];
};

_vehicle_276 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garaz", [2125.9929, 14958.319, 0.090862945], [], 0, "CAN_COLLIDE"];
  _vehicle_276 = _this;
  _this setDir -177.8466;
  _this setPos [2125.9929, 14958.319, 0.090862945];
};

_vehicle_277 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garaz", [2107.0647, 14958.912, 0.2122539], [], 0, "CAN_COLLIDE"];
  _vehicle_277 = _this;
  _this setDir -178.43073;
  _this setPos [2107.0647, 14958.912, 0.2122539];
};

_vehicle_281 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garaz", [2092.6582, 14931.915, 0.1825231], [], 0, "CAN_COLLIDE"];
  _vehicle_281 = _this;
  _this setDir -268.07309;
  _this setPos [2092.6582, 14931.915, 0.1825231];
};

_vehicle_284 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garaz", [2142.2297, 14914.079, 0.12453169], [], 0, "CAN_COLLIDE"];
  _vehicle_284 = _this;
  _this setDir -88.249214;
  _this setPos [2142.2297, 14914.079, 0.12453169];
};

_vehicle_303 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2134.783, 14944.234, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_303 = _this;
  _this setDir 89.267136;
  _this setPos [2134.783, 14944.234, -0.00012207031];
};

_vehicle_304 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [2202.9761, 14943.801, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_304 = _this;
  _this setDir -89.892395;
  _this setPos [2202.9761, 14943.801, -6.1035156e-005];
};

_vehicle_305 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [2186.1333, 14944.143, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_305 = _this;
  _this setDir -89.892395;
  _this setPos [2186.1333, 14944.143, -0.00012207031];
};

_vehicle_306 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_1_1000", [2168.9927, 14944.33, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_306 = _this;
  _this setDir -89.892395;
  _this setPos [2168.9927, 14944.33, -9.1552734e-005];
};

_vehicle_333 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2156.6172, 14945.118, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_333 = _this;
  _this setPos [2156.6172, 14945.118, -6.1035156e-005];
};

_vehicle_334 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2163.4082, 14944.982, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_334 = _this;
  _this setPos [2163.4082, 14944.982, -6.1035156e-005];
};

_vehicle_335 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2169.5156, 14944.577, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_335 = _this;
  _this setPos [2169.5156, 14944.577, -9.1552734e-005];
};

_vehicle_336 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2174.521, 14944.494, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_336 = _this;
  _this setPos [2174.521, 14944.494, -9.1552734e-005];
};

_vehicle_337 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2182.1094, 14944.648, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_337 = _this;
  _this setPos [2182.1094, 14944.648, -3.0517578e-005];
};

_vehicle_338 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2193.3374, 14944.836, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_338 = _this;
  _this setPos [2193.3374, 14944.836, 0];
};

_vehicle_339 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2150.6301, 14944.882, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_339 = _this;
  _this setPos [2150.6301, 14944.882, -0.00015258789];
};

_vehicle_340 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2146.1179, 14945.534], [], 0, "CAN_COLLIDE"];
  _vehicle_340 = _this;
  _this setPos [2146.1179, 14945.534];
};

_vehicle_341 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2141.7549, 14945.389, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_341 = _this;
  _this setPos [2141.7549, 14945.389, -3.0517578e-005];
};

_vehicle_342 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2143.9978, 14944.525, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_342 = _this;
  _this setPos [2143.9978, 14944.525, -3.0517578e-005];
};

_vehicle_343 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2143.5649, 14945.957, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_343 = _this;
  _this setPos [2143.5649, 14945.957, -0.00015258789];
};

_vehicle_353 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_runwayold_40_main", [2117.8171, 14933.519, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_353 = _this;
  _this setDir 2.0595999;
  _this setPos [2117.8171, 14933.519, 3.0517578e-005];
};

_vehicle_355 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf2_asf3", [2208.4619, 14945.295, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_355 = _this;
  _this setDir -14.216831;
  _this setPos [2208.4619, 14945.295, -0.00015258789];
};

_vehicle_356 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2110.4822, 14934.212, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_356 = _this;
  _this setPos [2110.4822, 14934.212, -9.1552734e-005];
};

_vehicle_357 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2110.2393, 14945.226, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_357 = _this;
  _this setPos [2110.2393, 14945.226, 0];
};

_vehicle_358 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2127.02, 14942.834, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_358 = _this;
  _this setPos [2127.02, 14942.834, 0];
};

_vehicle_359 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2135.6819, 14949.801, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_359 = _this;
  _this setPos [2135.6819, 14949.801, -6.1035156e-005];
};

_vehicle_360 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2137.4888, 14944.227, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_360 = _this;
  _this setPos [2137.4888, 14944.227, 3.0517578e-005];
};

_vehicle_361 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2139.8721, 14943.576, -0.00024414063], [], 0, "CAN_COLLIDE"];
  _vehicle_361 = _this;
  _this setPos [2139.8721, 14943.576, -0.00024414063];
};

_vehicle_362 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2132.5032, 14951.551, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_362 = _this;
  _this setPos [2132.5032, 14951.551, -9.1552734e-005];
};

_vehicle_363 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2137.9868, 14951.146, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_363 = _this;
  _this setPos [2137.9868, 14951.146, -9.1552734e-005];
};

_vehicle_364 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2100.6536, 14927.13, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_364 = _this;
  _this setPos [2100.6536, 14927.13, 0.00015258789];
};

_vehicle_365 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2100.5503, 14930.155, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_365 = _this;
  _this setPos [2100.5503, 14930.155, 6.1035156e-005];
};

_vehicle_366 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2098.0513, 14927.129, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_366 = _this;
  _this setPos [2098.0513, 14927.129, 0];
};

_vehicle_367 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2103.2534, 14925.508, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_367 = _this;
  _this setPos [2103.2534, 14925.508, -3.0517578e-005];
};

_vehicle_368 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2099.1638, 14937.056, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_368 = _this;
  _this setPos [2099.1638, 14937.056, 0];
};

_vehicle_369 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2101.7791, 14947.352, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_369 = _this;
  _this setPos [2101.7791, 14947.352, 0];
};

_vehicle_370 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2118.3071, 14945.521, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_370 = _this;
  _this setPos [2118.3071, 14945.521, 3.0517578e-005];
};

_vehicle_371 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2118.3198, 14948.654, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_371 = _this;
  _this setPos [2118.3198, 14948.654, -0.00018310547];
};

_vehicle_372 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2122.2676, 14930.887, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_372 = _this;
  _this setPos [2122.2676, 14930.887, -9.1552734e-005];
};

_vehicle_373 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2128.0701, 14931.412, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_373 = _this;
  _this setPos [2128.0701, 14931.412, -0.00021362305];
};

_vehicle_375 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [2139.1211, 14938.231, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_375 = _this;
  _this setDir -26.107241;
  _this setPos [2139.1211, 14938.231, 0.00012207031];
};

_vehicle_378 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [2106.6338, 14971.566, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_378 = _this;
  _this setDir -1.2046148;
  _this setPos [2106.6338, 14971.566, -0.00012207031];
};

_vehicle_379 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [2161.769, 14938.205, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_379 = _this;
  _this setPos [2161.769, 14938.205, 3.0517578e-005];
};

_vehicle_380 = objNull;
if (true) then
{
  _this = createVehicle ["Land_tires_EP1", [2099.0527, 14941.151, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_380 = _this;
  _this setPos [2099.0527, 14941.151, 0.00012207031];
};

_vehicle_383 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Market_stalls_01_EP1", [2124.6143, 14974.251, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_383 = _this;
  _this setDir 4.873333;
  _this setPos [2124.6143, 14974.251, -9.1552734e-005];
};

_vehicle_391 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_TankSmall2_EP1", [2094.8806, 14838.993, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_391 = _this;
  _this setDir 30.577656;
  _this setPos [2094.8806, 14838.993, -6.1035156e-005];
};

_vehicle_397 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [2146.7104, 14941.074, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_397 = _this;
  _this setDir -180.7119;
  _this setPos [2146.7104, 14941.074, -6.1035156e-005];
};

_vehicle_398 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [2140.6174, 14949.654, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_398 = _this;
  _this setDir 35.407238;
  _this setPos [2140.6174, 14949.654, 3.0517578e-005];
};

_vehicle_403 = objNull;
if (true) then
{
  _this = createVehicle ["Barrels", [2139.6382, 14948.596, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_403 = _this;
  _this setDir 0.29632479;
  _this setPos [2139.6382, 14948.596, -9.1552734e-005];
};

_vehicle_408 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [2139.0969, 14952.867], [], 0, "CAN_COLLIDE"];
  _vehicle_408 = _this;
  _this setDir 1.882571;
  _this setPos [2139.0969, 14952.867];
};

_vehicle_410 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Garb_Heap_EP1", [2142.0071, 14951.509, 0.10292136], [], 0, "CAN_COLLIDE"];
  _vehicle_410 = _this;
  _this setDir 4.3486996;
  _this setPos [2142.0071, 14951.509, 0.10292136];
};

_vehicle_415 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2148.6248, 14938.024, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_415 = _this;
  _this setDir -45.935883;
  _this setPos [2148.6248, 14938.024, -6.1035156e-005];
};

_vehicle_416 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2164.5977, 14963.187], [], 0, "CAN_COLLIDE"];
  _vehicle_416 = _this;
  _this setDir -12.324309;
  _this setPos [2164.5977, 14963.187];
};

_vehicle_417 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2160.0369, 14970.823, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_417 = _this;
  _this setDir -87.469421;
  _this setPos [2160.0369, 14970.823, 6.1035156e-005];
};

_vehicle_418 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2148.1377, 14971.372, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_418 = _this;
  _this setDir 92.536118;
  _this setPos [2148.1377, 14971.372, -6.1035156e-005];
};

_vehicle_419 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2127.7813, 14979.419, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_419 = _this;
  _this setDir -82.715019;
  _this setPos [2127.7813, 14979.419, -0.00012207031];
};

_vehicle_420 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2137.699, 14964.235, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_420 = _this;
  _this setDir 3.4776254;
  _this setPos [2137.699, 14964.235, -6.1035156e-005];
};

_vehicle_421 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2100.0042, 14965.477, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_421 = _this;
  _this setPos [2100.0042, 14965.477, 0.00018310547];
};

_vehicle_422 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2099.9729, 14971.475, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_422 = _this;
  _this setPos [2099.9729, 14971.475, -0.00012207031];
};

_vehicle_423 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2101.2939, 14976.387, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_423 = _this;
  _this setDir 32.190197;
  _this setPos [2101.2939, 14976.387, -0.00015258789];
};

_vehicle_424 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2105.0225, 14978.729, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_424 = _this;
  _this setDir 81.993248;
  _this setPos [2105.0225, 14978.729, 0.00018310547];
};

_vehicle_425 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2116.7947, 14980.113, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_425 = _this;
  _this setDir 84.167831;
  _this setPos [2116.7947, 14980.113, 9.1552734e-005];
};

_vehicle_426 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2122.3726, 14980.027, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_426 = _this;
  _this setDir -82.837006;
  _this setPos [2122.3726, 14980.027, -9.1552734e-005];
};

_vehicle_427 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2133.1406, 14978.061, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_427 = _this;
  _this setDir -69.981125;
  _this setPos [2133.1406, 14978.061, 3.0517578e-005];
};

_vehicle_428 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2110.7336, 14979.37, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_428 = _this;
  _this setDir -96.197456;
  _this setPos [2110.7336, 14979.37, 6.1035156e-005];
};

_vehicle_429 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2164.145, 14968.899, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_429 = _this;
  _this setDir 2.4261541;
  _this setPos [2164.145, 14968.899, 3.0517578e-005];
};

_vehicle_430 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2142.1853, 14971.677], [], 0, "CAN_COLLIDE"];
  _vehicle_430 = _this;
  _this setDir -87.089523;
  _this setPos [2142.1853, 14971.677];
};

_vehicle_431 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2154.0725, 14971.077, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_431 = _this;
  _this setDir -87.279442;
  _this setPos [2154.0725, 14971.077, 3.0517578e-005];
};

_vehicle_432 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2136.8386, 14974.979, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_432 = _this;
  _this setDir -30.130142;
  _this setPos [2136.8386, 14974.979, -0.00012207031];
};

_vehicle_433 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2137.9954, 14970.095, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_433 = _this;
  _this setDir 3.2745998;
  _this setPos [2137.9954, 14970.095, 3.0517578e-005];
};

_vehicle_434 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2146.8955, 14923.51, 2.5465682], [], 0, "CAN_COLLIDE"];
  _vehicle_434 = _this;
  _this setDir 45.55505;
  _this setPos [2146.8955, 14923.51, 2.5465682];
};

_vehicle_435 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2149.5139, 14928.544, 2.5250638], [], 0, "CAN_COLLIDE"];
  _vehicle_435 = _this;
  _this setDir 23.851454;
  _this setPos [2149.5139, 14928.544, 2.5250638];
};

_vehicle_436 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2150.512, 14933.626, 2.526746], [], 0, "CAN_COLLIDE"];
  _vehicle_436 = _this;
  _this setDir -2.5002229;
  _this setPos [2150.512, 14933.626, 2.526746];
};

_vehicle_437 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2148.2776, 14937.911, 2.543865], [], 0, "CAN_COLLIDE"];
  _vehicle_437 = _this;
  _this setDir -44.722885;
  _this setPos [2148.2776, 14937.911, 2.543865];
};

_vehicle_438 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2164.5151, 14963.015, 2.5324595], [], 0, "CAN_COLLIDE"];
  _vehicle_438 = _this;
  _this setDir -12.710224;
  _this setPos [2164.5151, 14963.015, 2.5324595];
};

_vehicle_439 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2164.1272, 14968.734, 2.5862608], [], 0, "CAN_COLLIDE"];
  _vehicle_439 = _this;
  _this setPos [2164.1272, 14968.734, 2.5862608];
};

_vehicle_440 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2159.7634, 14970.456, 2.5199122], [], 0, "CAN_COLLIDE"];
  _vehicle_440 = _this;
  _this setDir 91.263855;
  _this setPos [2159.7634, 14970.456, 2.5199122];
};

_vehicle_441 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2154.1438, 14970.977, 2.7062929], [], 0, "CAN_COLLIDE"];
  _vehicle_441 = _this;
  _this setDir -87.037498;
  _this setPos [2154.1438, 14970.977, 2.7062929];
};

_vehicle_442 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2148.2664, 14970.991, 2.4957616], [], 0, "CAN_COLLIDE"];
  _vehicle_442 = _this;
  _this setDir -89.103012;
  _this setPos [2148.2664, 14970.991, 2.4957616];
};

_vehicle_444 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2142.2761, 14971.355, 2.6008611], [], 0, "CAN_COLLIDE"];
  _vehicle_444 = _this;
  _this setDir -86.891663;
  _this setPos [2142.2761, 14971.355, 2.6008611];
};

_vehicle_445 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2136.7656, 14974.978, 2.5529506], [], 0, "CAN_COLLIDE"];
  _vehicle_445 = _this;
  _this setDir -30.534271;
  _this setPos [2136.7656, 14974.978, 2.5529506];
};

_vehicle_446 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2131.7712, 14978.635, 2.550544], [], 0, "CAN_COLLIDE"];
  _vehicle_446 = _this;
  _this setDir -79.074715;
  _this setPos [2131.7712, 14978.635, 2.550544];
};

_vehicle_447 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2125.5542, 14979.951, 2.4976308], [], 0, "CAN_COLLIDE"];
  _vehicle_447 = _this;
  _this setDir -98.066139;
  _this setPos [2125.5542, 14979.951, 2.4976308];
};

_vehicle_448 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2118.9438, 14980.204, 2.5062504], [], 0, "CAN_COLLIDE"];
  _vehicle_448 = _this;
  _this setDir -89.633568;
  _this setPos [2118.9438, 14980.204, 2.5062504];
};

_vehicle_449 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2113.0112, 14979.611, 2.5420921], [], 0, "CAN_COLLIDE"];
  _vehicle_449 = _this;
  _this setDir -97.205261;
  _this setPos [2113.0112, 14979.611, 2.5420921];
};

_vehicle_450 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2106.8625, 14978.808, 2.5081263], [], 0, "CAN_COLLIDE"];
  _vehicle_450 = _this;
  _this setDir 81.382111;
  _this setPos [2106.8625, 14978.808, 2.5081263];
};

_vehicle_451 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2099.9629, 14966.189, 2.4904461], [], 0, "CAN_COLLIDE"];
  _vehicle_451 = _this;
  _this setPos [2099.9629, 14966.189, 2.4904461];
};

_vehicle_452 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2099.9224, 14972.538, 2.557445], [], 0, "CAN_COLLIDE"];
  _vehicle_452 = _this;
  _this setPos [2099.9224, 14972.538, 2.557445];
};

_vehicle_453 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [2101.8789, 14977.264, 2.5650454], [], 0, "CAN_COLLIDE"];
  _vehicle_453 = _this;
  _this setDir 54.178669;
  _this setPos [2101.8789, 14977.264, 2.5650454];
};

_vehicle_456 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf2_asf3", [2098.5686, 14782.32, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_456 = _this;
  _this setDir 27.762192;
  _this setPos [2098.5686, 14782.32, -3.0517578e-005];
};

_vehicle_457 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [2095.2725, 14813.204, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_457 = _this;
  _this setDir 28.590443;
  _this setPos [2095.2725, 14813.204, -0.00012207031];
};

_vehicle_460 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [2098.5051, 14805.046, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_460 = _this;
  _this setDir 29.535557;
  _this setPos [2098.5051, 14805.046, -6.1035156e-005];
};

_vehicle_462 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_25", [2110.5093, 14826.535], [], 0, "CAN_COLLIDE"];
  _vehicle_462 = _this;
  _this setDir 29.699705;
  _this setPos [2110.5093, 14826.535];
};

_vehicle_467 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2093.4565, 14785.155, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_467 = _this;
  _this setDir -13.850893;
  _this setPos [2093.4565, 14785.155, -3.0517578e-005];
};

_vehicle_470 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_22_50", [2091.3037, 14795.56, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_470 = _this;
  _this setDir 2.5086422;
  _this setPos [2091.3037, 14795.56, -3.0517578e-005];
};

_vehicle_471 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [2094.6233, 14779.947, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_471 = _this;
  _this setDir -11.334475;
  _this setPos [2094.6233, 14779.947, -6.1035156e-005];
};

_vehicle_472 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_12", [2092.5112, 14794.345, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_472 = _this;
  _this setDir 28.843958;
  _this setPos [2092.5112, 14794.345, -9.1552734e-005];
};

_vehicle_473 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2095.0955, 14797.104, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_473 = _this;
  _this setPos [2095.0955, 14797.104, -3.0517578e-005];
};

_vehicle_474 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2099.3506, 14807.151, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_474 = _this;
  _this setPos [2099.3506, 14807.151, -0.00012207031];
};

_vehicle_475 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2148.7576, 14863.042, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_475 = _this;
  _this setDir -56.913227;
  _this setPos [2148.7576, 14863.042, -3.0517578e-005];
};

_vehicle_476 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2124.5142, 14850.151, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_476 = _this;
  _this setPos [2124.5142, 14850.151, 3.0517578e-005];
};

_vehicle_477 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2149.0938, 14865.823, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_477 = _this;
  _this setDir -56.913227;
  _this setPos [2149.0938, 14865.823, -6.1035156e-005];
};

_vehicle_478 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2151.5889, 14861.398, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_478 = _this;
  _this setDir -56.913227;
  _this setPos [2151.5889, 14861.398, -0.00012207031];
};

_vehicle_479 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_EP1", [2150.3335, 14858.529, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_479 = _this;
  _this setDir -56.913227;
  _this setPos [2150.3335, 14858.529, -0.00012207031];
};

_vehicle_481 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_2_EP1", [2111.9441, 14839.223, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_481 = _this;
  _this setDir 145.29411;
  _this setPos [2111.9441, 14839.223, -6.1035156e-005];
};

_vehicle_482 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_2_EP1", [2110.6816, 14837.888, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_482 = _this;
  _this setPos [2110.6816, 14837.888, 3.0517578e-005];
};

_vehicle_483 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2110.3701, 14838.329, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_483 = _this;
  _this setPos [2110.3701, 14838.329, 3.0517578e-005];
};

_vehicle_485 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_75", [2106.8276, 14834.595, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_485 = _this;
  _this setDir 395.02997;
  _this setPos [2106.8276, 14834.595, -9.1552734e-005];
};

_vehicle_486 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [2119.502, 14848.787, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_486 = _this;
  _this setDir 226.77484;
  _this setPos [2119.502, 14848.787, -0.00012207031];
};

_vehicle_487 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2137.2476, 14856.389, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_487 = _this;
  _this setDir 239.85841;
  _this setPos [2137.2476, 14856.389, -6.1035156e-005];
};

_vehicle_488 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_12", [2127.0552, 14850.253, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_488 = _this;
  _this setDir -111.00695;
  _this setPos [2127.0552, 14850.253, -3.0517578e-005];
};

_vehicle_489 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_kr_t_asf2_asf3", [2143.3022, 14853.767, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_489 = _this;
  _this setDir 37.209171;
  _this setPos [2143.3022, 14853.767, -6.1035156e-005];
};

_vehicle_491 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [2136.3113, 14855.938, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_491 = _this;
  _this setDir 69.165237;
  _this setPos [2136.3113, 14855.938, -6.1035156e-005];
};

_vehicle_492 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [2148.8723, 14940.248, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_492 = _this;
  _this setDir 43.06134;
  _this setPos [2148.8723, 14940.248, 9.1552734e-005];
};

_vehicle_493 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [2139.052, 14951.56, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_493 = _this;
  _this setDir 65.905937;
  _this setPos [2139.052, 14951.56, -3.0517578e-005];
};

_vehicle_494 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [2143.022, 14948.963, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_494 = _this;
  _this setDir -0.34071481;
  _this setPos [2143.022, 14948.963, -6.1035156e-005];
};

_vehicle_495 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [2145.5383, 14948.993], [], 0, "CAN_COLLIDE"];
  _vehicle_495 = _this;
  _this setDir -0.80602312;
  _this setPos [2145.5383, 14948.993];
};

_vehicle_496 = objNull;
if (true) then
{
  _this = createVehicle ["Land_CncBlock_Stripes", [2148.0806, 14949.014, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_496 = _this;
  _this setDir -1.0458651;
  _this setPos [2148.0806, 14949.014, -6.1035156e-005];
};

_vehicle_497 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [2156.134, 14940.612, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_497 = _this;
  _this setDir 87.667374;
  _this setPos [2156.134, 14940.612, -0.00012207031];
};

_vehicle_500 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_concrete_High", [2153.8337, 14933.616, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_500 = _this;
  _this setDir -125.80305;
  _this setPos [2153.8337, 14933.616, 0.00012207031];
};

_vehicle_501 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_concrete_High", [2162.9373, 14936.222, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_501 = _this;
  _this setDir 32.610546;
  _this setPos [2162.9373, 14936.222, -6.1035156e-005];
};

_vehicle_502 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Misc_Coil_EP1", [2154.2153, 14930.546, 0.090929389], [], 0, "CAN_COLLIDE"];
  _vehicle_502 = _this;
  _this setPos [2154.2153, 14930.546, 0.090929389];
};

_vehicle_504 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Hlidac_budka", [2157.5193, 14936.926, -0.12337244], [], 0, "CAN_COLLIDE"];
  _vehicle_504 = _this;
  _this setDir -185.62485;
  _this setPos [2157.5193, 14936.926, -0.12337244];
};

_vehicle_505 = objNull;
if (true) then
{
  _this = createVehicle ["Bunker_PMC", [2171.1213, 14931.492, -0.22696026], [], 0, "CAN_COLLIDE"];
  _vehicle_505 = _this;
  _this setDir -180.64989;
  _this setPos [2171.1213, 14931.492, -0.22696026];
};

_vehicle_545 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVOld", [2136.0671, 14966.422, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_545 = _this;
  _this setDir -73.207954;
  _this setPos [2136.0671, 14966.422, -3.0517578e-005];
};

_vehicle_555 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVOld", [2118.7205, 14963.386, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_555 = _this;
  _this setPos [2118.7205, 14963.386, -6.1035156e-005];
};

_vehicle_556 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVOld", [2099.7317, 14942.448], [], 0, "CAN_COLLIDE"];
  _vehicle_556 = _this;
  _this setDir -268.57437;
  _this setPos [2099.7317, 14942.448];
};

_vehicle_557 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVOld", [2164.6953, 14936.466, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_557 = _this;
  _this setDir 41.743488;
  _this setPos [2164.6953, 14936.466, -6.1035156e-005];
};

_vehicle_558 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVOld", [2164.6597, 14958.437, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_558 = _this;
  _this setDir 231.67949;
  _this setPos [2164.6597, 14958.437, -0.00018310547];
};

_vehicle_559 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVOld", [2139.6855, 14961.084, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_559 = _this;
  _this setDir 86.132393;
  _this setPos [2139.6855, 14961.084, 6.1035156e-005];
};

_vehicle_560 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVOld", [2094.0784, 14828.695], [], 0, "CAN_COLLIDE"];
  _vehicle_560 = _this;
  _this setDir 58.311966;
  _this setPos [2094.0784, 14828.695];
};

_vehicle_561 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_2_EP1", [2189.7278, 14943.585, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_561 = _this;
  _this setPos [2189.7278, 14943.585, 3.0517578e-005];
};

_vehicle_562 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_2_EP1", [2199.2368, 14943.34, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_562 = _this;
  _this setPos [2199.2368, 14943.34, 6.1035156e-005];
};

_vehicle_563 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_2_EP1", [2194.8743, 14943.514, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_563 = _this;
  _this setPos [2194.8743, 14943.514, -3.0517578e-005];
};

_vehicle_564 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_2_EP1", [2182.3521, 14943.963, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_564 = _this;
  _this setPos [2182.3521, 14943.963, -9.1552734e-005];
};

_vehicle_566 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2199.9226, 14943.67, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_566 = _this;
  _this setPos [2199.9226, 14943.67, -3.0517578e-005];
};

_vehicle_567 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2186.4561, 14943.832, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_567 = _this;
  _this setPos [2186.4561, 14943.832, 0.00015258789];
};

_vehicle_568 = objNull;
if (true) then
{
  _this = createVehicle ["HeliHEmpty", [2111.9771, 14938.672], [], 0, "CAN_COLLIDE"];
  _vehicle_568 = _this;
  _this setPos [2111.9771, 14938.672];
};

_vehicle_579 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_T34", [2200.5688, 14917.402, 0.25116163], [], 0, "CAN_COLLIDE"];
  _vehicle_579 = _this;
  _this setDir -3.6325817;
  _this setPos [2200.5688, 14917.402, 0.25116163];
};

_vehicle_587 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garaz", [2092.1794, 14916.497, 0.11349829], [], 0, "CAN_COLLIDE"];
  _vehicle_587 = _this;
  _this setDir 91.784599;
  _this setPos [2092.1794, 14916.497, 0.11349829];
};

_vehicle_588 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garaz_mala", [2091.9885, 14910.888, 0.1136832], [], 0, "CAN_COLLIDE"];
  _vehicle_588 = _this;
  _this setDir 91.518967;
  _this setPos [2091.9885, 14910.888, 0.1136832];
};

_vehicle_589 = objNull;
if (true) then
{
  _this = createVehicle ["Garbage_container", [2139.1985, 14954.664, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_589 = _this;
  _this setPos [2139.1985, 14954.664, -9.1552734e-005];
};

_vehicle_591 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2103.4302, 14918.89, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_591 = _this;
  _this setPos [2103.4302, 14918.89, -3.0517578e-005];
};

_vehicle_592 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2133.5793, 14918.165, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_592 = _this;
  _this setPos [2133.5793, 14918.165, 0];
};

_vehicle_593 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2117.769, 14918.186, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_593 = _this;
  _this setPos [2117.769, 14918.186, -6.1035156e-005];
};

_vehicle_594 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_2_EP1", [2136.728, 14938.518, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_594 = _this;
  _this setPos [2136.728, 14938.518, -0.00012207031];
};

_vehicle_595 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_2_EP1", [2137.3047, 14940.041, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_595 = _this;
  _this setPos [2137.3047, 14940.041, 3.0517578e-005];
};

_vehicle_596 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_2_EP1", [2135.7166, 14952.09, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_596 = _this;
  _this setPos [2135.7166, 14952.09, 0];
};

_vehicle_597 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_small_2_EP1", [2137.9597, 14947.859, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_597 = _this;
  _this setPos [2137.9597, 14947.859, -9.1552734e-005];
};

_vehicle_598 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [2125.908, 14892.057, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_598 = _this;
  _this setDir 122.37494;
  _this setPos [2125.908, 14892.057, 3.0517578e-005];
};

_vehicle_600 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_12", [2101.2632, 14901.757, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_600 = _this;
  _this setDir 2.3466933;
  _this setPos [2101.2632, 14901.757, -9.1552734e-005];
};

_vehicle_601 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2106.033, 14892.966, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_601 = _this;
  _this setDir -57.105709;
  _this setPos [2106.033, 14892.966, -0.00012207031];
};

_vehicle_602 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2116.0293, 14892.53, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_602 = _this;
  _this setDir -117.14381;
  _this setPos [2116.0293, 14892.53, -6.1035156e-005];
};

_vehicle_603 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_60_10", [2115.9661, 14892.592, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_603 = _this;
  _this setDir 62.766628;
  _this setPos [2115.9661, 14892.592, -6.1035156e-005];
};

_vehicle_604 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [2156.3279, 14872.509, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_604 = _this;
  _this setDir -56.728802;
  _this setPos [2156.3279, 14872.509, -3.0517578e-005];
};

_vehicle_607 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [2145.8984, 14879.309, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_607 = _this;
  _this setDir -56.913227;
  _this setPos [2145.8984, 14879.309, -3.0517578e-005];
};

_vehicle_608 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6", [2151.1067, 14875.894, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_608 = _this;
  _this setDir -56.486305;
  _this setPos [2151.1067, 14875.894, -9.1552734e-005];
};

_vehicle_640 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2146.6108, 14868.509], [], 0, "CAN_COLLIDE"];
  _vehicle_640 = _this;
  _this setPos [2146.6108, 14868.509];
};

_vehicle_641 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2093.2725, 14895.779, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_641 = _this;
  _this setPos [2093.2725, 14895.779, -3.0517578e-005];
};

_vehicle_642 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2103.4272, 14885.904, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_642 = _this;
  _this setPos [2103.4272, 14885.904, -3.0517578e-005];
};

_vehicle_643 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2119.4172, 14885.342, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_643 = _this;
  _this setPos [2119.4172, 14885.342, -9.1552734e-005];
};

_vehicle_644 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2132.1797, 14878.06, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_644 = _this;
  _this setPos [2132.1797, 14878.06, 0];
};

_vehicle_645 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2111.5925, 14899.012, -0.00021362305], [], 0, "CAN_COLLIDE"];
  _vehicle_645 = _this;
  _this setPos [2111.5925, 14899.012, -0.00021362305];
};

_vehicle_646 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2136.6555, 14896.653, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_646 = _this;
  _this setPos [2136.6555, 14896.653, -3.0517578e-005];
};

_vehicle_647 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2144.8416, 14903.023, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_647 = _this;
  _this setPos [2144.8416, 14903.023, -0.00012207031];
};

_vehicle_648 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2157.1177, 14914.626, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_648 = _this;
  _this setPos [2157.1177, 14914.626, 3.0517578e-005];
};

_vehicle_649 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2157.5581, 14889.494, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_649 = _this;
  _this setPos [2157.5581, 14889.494, -6.1035156e-005];
};

_vehicle_650 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2154.605, 14880.319, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_650 = _this;
  _this setPos [2154.605, 14880.319, 3.0517578e-005];
};

_vehicle_651 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_t_pinusN2s", [2173.189, 14899.875, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_651 = _this;
  _this setPos [2173.189, 14899.875, -6.1035156e-005];
};

_vehicle_687 = objNull;
if (true) then
{
  _this = createVehicle ["PARACHUTE_TARGET", [2157.2859, 14901.841, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_687 = _this;
  _this setPos [2157.2859, 14901.841, -3.0517578e-005];
};

_vehicle_689 = objNull;
if (true) then
{
  _this = createVehicle ["ZavoraAnim", [2105.3936, 14904.714, 0.26932088], [], 0, "CAN_COLLIDE"];
  _vehicle_689 = _this;
  _this setDir 1.3806187;
  _this setPos [2105.3936, 14904.714, 0.26932088];
};

_vehicle_691 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_water_tank", [2119.7322, 14898.764, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_691 = _this;
  _this setDir 176.86299;
  _this setPos [2119.7322, 14898.764, 3.0517578e-005];
};

_vehicle_775 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2144.1445, 14941.021, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_775 = _this;
  _this setDir 0.64930618;
  _this setPos [2144.1445, 14941.021, -0.00015258789];
};

_vehicle_776 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2141.5618, 14941.053, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_776 = _this;
  _this setDir 2.2246006;
  _this setPos [2141.5618, 14941.053, -9.1552734e-005];
};

_vehicle_777 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2138.9829, 14941.1, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_777 = _this;
  _this setDir 1.9016994;
  _this setPos [2138.9829, 14941.1, 6.1035156e-005];
};

_vehicle_778 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2136.6887, 14940.477, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_778 = _this;
  _this setDir -33.70779;
  _this setPos [2136.6887, 14940.477, 3.0517578e-005];
};

_vehicle_779 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2135.4719, 14938.544, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_779 = _this;
  _this setDir -83.314262;
  _this setPos [2135.4719, 14938.544, 6.1035156e-005];
};

_vehicle_780 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2132.616, 14927.861, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_780 = _this;
  _this setDir -87.341469;
  _this setPos [2132.616, 14927.861, -6.1035156e-005];
};

_vehicle_781 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2135.9092, 14922.528, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_781 = _this;
  _this setPos [2135.9092, 14922.528, -0.00015258789];
};

_vehicle_782 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2133.5637, 14923.303, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_782 = _this;
  _this setDir -143.29897;
  _this setPos [2133.5637, 14923.303, -0.00018310547];
};

_vehicle_783 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2132.5217, 14925.289, -0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_783 = _this;
  _this setDir -87.16111;
  _this setPos [2132.5217, 14925.289, -0.00015258789];
};

_vehicle_784 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2133.1226, 14930.314, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_784 = _this;
  _this setDir -67.823898;
  _this setPos [2133.1226, 14930.314, 3.0517578e-005];
};

_vehicle_785 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2104.5857, 14904.466, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_785 = _this;
  _this setDir 92.196564;
  _this setPos [2104.5857, 14904.466, 6.1035156e-005];
};

_vehicle_786 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2096.8142, 14902.253, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_786 = _this;
  _this setDir -0.60722113;
  _this setPos [2096.8142, 14902.253, -6.1035156e-005];
};

_vehicle_787 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2106.936, 14904.444], [], 0, "CAN_COLLIDE"];
  _vehicle_787 = _this;
  _this setDir -89.22567;
  _this setPos [2106.936, 14904.444];
};

_vehicle_788 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2105.6013, 14903.162, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_788 = _this;
  _this setDir 1.0432909;
  _this setPos [2105.6013, 14903.162, 9.1552734e-005];
};

_vehicle_789 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2098.2761, 14911.147], [], 0, "CAN_COLLIDE"];
  _vehicle_789 = _this;
  _this setDir -86.969925;
  _this setPos [2098.2761, 14911.147];
};

_vehicle_790 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2104.7498, 14911.885, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_790 = _this;
  _this setDir -88.508011;
  _this setPos [2104.7498, 14911.885, 6.1035156e-005];
};

_vehicle_791 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2097.7195, 14913.498, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_791 = _this;
  _this setDir 61.140396;
  _this setPos [2097.7195, 14913.498, 6.1035156e-005];
};

_vehicle_792 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2105.874, 14913.546, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_792 = _this;
  _this setDir -19.304581;
  _this setPos [2105.874, 14913.546, -3.0517578e-005];
};

_vehicle_793 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2104.6899, 14907.006, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_793 = _this;
  _this setDir 92.249611;
  _this setPos [2104.6899, 14907.006, -6.1035156e-005];
};

_vehicle_794 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2104.7195, 14909.367, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_794 = _this;
  _this setDir -88.008698;
  _this setPos [2104.7195, 14909.367, 0.00012207031];
};

_vehicle_795 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2098.179, 14908.627, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_795 = _this;
  _this setDir -87.764587;
  _this setPos [2098.179, 14908.627, 9.1552734e-005];
};

_vehicle_796 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2098.0813, 14906.071, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_796 = _this;
  _this setDir -87.763657;
  _this setPos [2098.0813, 14906.071, 9.1552734e-005];
};

_vehicle_797 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CncBlock_stripes", [2097.989, 14903.513, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_797 = _this;
  _this setDir -87.942284;
  _this setPos [2097.989, 14903.513, 0.00018310547];
};

_vehicle_802 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2163.0442, 14901.828, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_802 = _this;
  _this setPos [2163.0442, 14901.828, -3.0517578e-005];
};

_vehicle_803 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2157.3037, 14896.047, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_803 = _this;
  _this setPos [2157.3037, 14896.047, -6.1035156e-005];
};

_vehicle_804 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2151.5144, 14901.844, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_804 = _this;
  _this setPos [2151.5144, 14901.844, -0.00012207031];
};

_vehicle_805 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbBLUP", [2157.2822, 14907.611, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_805 = _this;
  _this setPos [2157.2822, 14907.611, -6.1035156e-005];
};

_vehicle_806 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_BulbYELB", [2157.2671, 14901.793, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_806 = _this;
  _this setPos [2157.2671, 14901.793, 3.0517578e-005];
};

_vehicle_825 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2116.3105, 14885.361, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_825 = _this;
  _this setDir 2.3251109;
  _this setPos [2116.3105, 14885.361, -0.00018310547];
};

_vehicle_826 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2106.1465, 14886, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_826 = _this;
  _this setDir 3.3014641;
  _this setPos [2106.1465, 14886, 3.0517578e-005];
};

_vehicle_827 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2097.5393, 14889.665, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_827 = _this;
  _this setDir 40.548138;
  _this setPos [2097.5393, 14889.665, -0.00012207031];
};

_vehicle_828 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2093.1401, 14897.993, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_828 = _this;
  _this setDir 87.004196;
  _this setPos [2093.1401, 14897.993, 3.0517578e-005];
};

_vehicle_829 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2125.5322, 14882.609], [], 0, "CAN_COLLIDE"];
  _vehicle_829 = _this;
  _this setDir 28.806038;
  _this setPos [2125.5322, 14882.609];
};

_vehicle_830 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2134.4092, 14877.38, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_830 = _this;
  _this setDir 29.50086;
  _this setPos [2134.4092, 14877.38, 0.00015258789];
};

_vehicle_831 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2142.5435, 14871.992, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_831 = _this;
  _this setDir 33.399624;
  _this setPos [2142.5435, 14871.992, 3.0517578e-005];
};

_vehicle_832 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2155.3555, 14882.755, 0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_832 = _this;
  _this setDir -54.580242;
  _this setPos [2155.3555, 14882.755, 0.00018310547];
};

_vehicle_833 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2161.7471, 14890.204, 0.00015258789], [], 0, "CAN_COLLIDE"];
  _vehicle_833 = _this;
  _this setDir 136.27768;
  _this setPos [2161.7471, 14890.204, 0.00015258789];
};

_vehicle_834 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2169.3018, 14896.975, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_834 = _this;
  _this setDir -38.049763;
  _this setPos [2169.3018, 14896.975, 6.1035156e-005];
};

_vehicle_835 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2178.321, 14903.618, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_835 = _this;
  _this setDir -36.015293;
  _this setPos [2178.321, 14903.618, -9.1552734e-005];
};

_vehicle_836 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2187.0679, 14909.524], [], 0, "CAN_COLLIDE"];
  _vehicle_836 = _this;
  _this setDir -30.447508;
  _this setPos [2187.0679, 14909.524];
};

_vehicle_837 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2147.2598, 14884.886, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_837 = _this;
  _this setDir 31.867945;
  _this setPos [2147.2598, 14884.886, -9.1552734e-005];
};

_vehicle_838 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2138.2019, 14890.774, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_838 = _this;
  _this setDir 33.061996;
  _this setPos [2138.2019, 14890.774, -6.1035156e-005];
};

_vehicle_839 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_dragonTeethBig", [2133.4089, 14899.073, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_839 = _this;
  _this setDir -87.355705;
  _this setPos [2133.4089, 14899.073, 0.00012207031];
};

_vehicle_842 = objNull;
if (true) then
{
  _this = createVehicle ["HeliHCivil", [2115.894, 14932.544, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_842 = _this;
  _this setDir 1.5097771;
  _this setPos [2115.894, 14932.544, -0.00012207031];
};

_vehicle_845 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2120.0413, 14893.429, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_845 = _this;
  _this setPos [2120.0413, 14893.429, -6.1035156e-005];
};

_vehicle_846 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2107.4836, 14892.44, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_846 = _this;
  _this setPos [2107.4836, 14892.44, 0];
};

_vehicle_847 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2101.533, 14899.914, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_847 = _this;
  _this setPos [2101.533, 14899.914, 3.0517578e-005];
};

_vehicle_848 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2127.9998, 14890.145, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_848 = _this;
  _this setPos [2127.9998, 14890.145, -3.0517578e-005];
};

_vehicle_849 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2138.4182, 14883.893, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_849 = _this;
  _this setPos [2138.4182, 14883.893, -3.0517578e-005];
};

_vehicle_850 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2147.1941, 14877.942, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_850 = _this;
  _this setPos [2147.1941, 14877.942, 3.0517578e-005];
};

_vehicle_851 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [2157.1343, 14901.767, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_851 = _this;
  _this setPos [2157.1343, 14901.767, -3.0517578e-005];
};

};