local _mission = count WAI_MissionData - 1; // DO NOT CHANGE THIS LINE.

/*
	Drug Empire Static Mission
*/

// Spawn the mission objects
// call compile preprocessFileLineNumbers "\z\addons\dayz_server\WAI\static\POIs\skalisty_island.sqf";
// local _villa = ([13716.6,2911.42,0] nearObjects ["Land_A_Villa_EP1", 50]) select 0; // Need to get this building object for spawning other objects inside it.

// Config
local _aiType = "Bandit"; // "Bandit" or "Hero" - determines the humanity and kill rewards.
local _position = [13686.7,2929.16]; // Select a position on the map for your mission. This should be the mission center. Just use the X and Y coordinates.
local _name = "[Warning] Drug Empire"; // Used in marker text. If you know how to make localized strings and add them to a mission side string table then you can do it.
local _autoClaim = true; // Enable the auto-claim feature for this static location. Markers must also be enabled.
local _borderMarker = true; // This places a border marker on the perimeter defined by the _triggerDistance variable. It is also enabled with auto-claim.
local _triggerDistance = 2250; // This is how close in meters a player must be to spawn the AI. Also used for the auto-claim.
local _enableRespawn = false; // Allow an AI group to respawn after all members have been killed.
local _respawnTimer = 120; // The amount of time in minutes before an AI group is allowed to respawn.
local _aiCaching = true; // If players are not near then the behavioral FSM, targeting, and movement is disabled and the AI objects are hidden. Known to improve server performance.
local _enableMarkers = true; // Define the parameters below to place a custom JIP compatible marker on your mission.
local _markerColor = "ColorOrange"; // Color of the mission marker. https://community.bistudio.com/wiki/Arma_3:_CfgMarkerColors
local _markerSize = [800,800]; // Sets the size of the marker. https://community.bistudio.com/wiki/setMarkerSize
local _markerShape = "ELLIPSE"; // https://community.bistudio.com/wiki/setMarkerShape
local _markerBrush = "Solid"; // Fill texture for the marker. https://community.bistudio.com/wiki/setMarkerBrush
local _markerAlpha = 1; // From 0 to 1. Sets the marker transparency. https://community.bistudio.com/wiki/setMarkerAlpha
local _showMarkerText = true; // Show the name of the mission in the center of the marker. Needed for AI counter.
local _showAiCount = true; // Show the AI count in the mission marker.
local _markWhenClear = true; // If all AI are dead, place a marker that says that the mission has been cleared. Respawns must be disabled.
local _killPercent = 92; // The percentage of AI that must be killed for the mission to be marked "clear". Because the AI counts can be slightly off for some reason, I recommend not using 100%.
local _lootWhenClear = true; // Wait until the mission is cleared to spawn the loot. True or false.
//End Config

////////////////////// Do not edit this section ///////////////////////////
local _markers = [1,1,1,1];
local _markerIndex = -1;
if (_enableMarkers) then {
	_markers set [0,[_position,"WAI" + str(_mission),_markerColor,"",_markerShape,_markerBrush,_markerSize,[],_markerAlpha]];
	if (_showMarkerText) then {_markers set [1,[_position,"WAI" + str(_mission) + "dot","ColorBlack","mil_dot","","",[],[_name],0]];};
	if (_autoClaim || _borderMarker) then {_markers set [2, [_position, "WAI" + str(_mission) + "auto", "ColorRed", "", "ELLIPSE", "Border", [_triggerDistance,_triggerDistance], [], 0]];};
	DZE_ServerMarkerArray set [count DZE_ServerMarkerArray, _markers];
	_markerIndex = count DZE_ServerMarkerArray - 1;
	PVDZ_ServerMarkerSend = ["start",_markers];
	publicVariable "PVDZ_ServerMarkerSend";
};
WAI_StaticMarkerReady = true;

// Add the mission's position to the global array so that other missions do not spawn near it.
DZE_MissionPositions set [count DZE_MissionPositions, _position];

// Wait until a player is within range.
local _claimPlayer = objNull;
while {isNull _claimPlayer} do {
	_claimPlayer = [_position, _triggerDistance] call isClosestPlayer;
	uiSleep 1;
};
//////////////////////////////// End //////////////////////////////////////

/*
Custom group spawns Eg.

[
	[953.237,4486.48,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"Random",				// Skill level of unit (easy, medium, hard, extreme, Random)
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"",						// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"Random",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;

Place your custom group spawns below
*/

// Drug Empire FOB Skalisty
[
	[13606,3165.77,0],		// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_ACR,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AA",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_eco_stalker_mask_camo_DZ",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13600.7,3198.5,0],		// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_ACR,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AA",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_eco_stalker_mask_camo_DZ",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13639.6,3188.62,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_HK417,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_eco_stalker_mask_camo_DZ",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13480.1,3334.82,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_HK417,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_eco_stalker_mask_camo_DZ",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13702.1,3062.34,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_RandomWeapon,		// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_eco_stalker_mask_camo_DZ",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13672.6,3057.96,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_RandomWeapon,		// Primary gun - "Random", "Unarmed", "Classname", Array
	"AA",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_eco_stalker_mask_camo_DZ",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13832.5,2878.6,0],		// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AA",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_eco_stalker_mask_camo_DZ",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13902.4,2871.07,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_eco_stalker_mask_camo_DZ",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13541.3,2988.16,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_Sniper,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"GhillieFull_Wood01_DZ",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13484.7,3365.29,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_Sniper,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AA",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"GhillieFull_Wood01_DZ",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;

// Drug Empire CAVE Skalisty Island
[
	[13607,2921.7,0],		// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_Sniper,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"GhillieFull_Wood01_DZ",				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13701.7,2920.78,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_TWS,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"Dealer_DZ",			// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13664.4,2867.49,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	5,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_TWS,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"Dealer_DZ",			// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13729.6,2878.79,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	3,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_ValueWeapon,		// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"MafiaBoss_DZ",			// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13655.4,2922.39,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	4,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_LMG,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_eco_stalker_mask_camo_DZ",			// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13723.4,2892.72,0],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	4,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_LMG,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_eco_stalker_mask_camo_DZ",			// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13707.1,2846.18,3],	// Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	4,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_RandomWeapon,		// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_eco_stalker_mask_camo_DZ",			// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13697.4,2869.9,3],	    // Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	4,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_HK417,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_scientist1_DZ",	// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13668.3,2913.2,1],     // Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	4,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_RK95,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_scientist1_DZ",	// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;
[
	[13651.4,2912.68,1],   // Position - If you use a number greater than 0 for the z- coordinate, the AI will get "Sentry" WayPoints. Otherwise they are standard ground troops.
	4,						// Number Of units
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	WAI_G36,				// Primary gun - "Random", "Unarmed", "Classname", Array
	"AT",					// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"gsc_scientist1_DZ",	// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnGroup;

/*
Custom static weapon spawns Eg. (with multiple positions)

[
	[						// Position(s) (can be multiple)
		[911.21,4532.76,2.62],
		[921.21,4542.76,2.62]
	],
	"M2StaticMG",			// Classname of turret - Can use "Random"
	"easy",					// Skill level of unit (easy, medium, hard, extreme, Random)
	_aiType,				// Skin - "Random", "Classname", or Array 
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnStatic;

// Random Static Gun Positions
local _random1 = [[13728.8,2858.17,0],[13772.7,2851.03,1.25],[13652.7,2860.58,0.9],[13695.3,2856.22,0]] call BIS_fnc_selectRandom;
local _random2 = [[13646.2,2923.39,1.2],[13699,2948.24,0],[13786.8,2911.45,1.05],[13744.3,2930.9,0]] call BIS_fnc_selectRandom;
local _random3 = [[13778.9,2980.68,1.2],[13740.3,2955.42,0],[13715,2968.29,0],[13695.5,2970.86,0]] call BIS_fnc_selectRandom;
local _random4 = [[13716,2915.39,10.6], [13734.1,2895.29,8.7], [13693.2,2900.96,8.14],[13714.6,2895.7,4.81]] call BIS_fnc_selectRandom;
local _random5 = [[13732.3,2903.29,1.24],[13722.8,2917.98,0.6],[13716.7,2914.34,0.72],[13702.4,2909.99,0.76],[13692.5,2899.75,1]] call BIS_fnc_selectRandom;
local _random6 = [[13623.1,3152.25,0],[13613.2,3147.17,0],[13588.5,3160.9,0],[13638.8,3183.57,0],[13613.9,3129.21,0]] call BIS_fnc_selectRandom;

[
	[	// Position(s) (can be multiple)
		_random1, // inside castle
		_random2, // inside castle
		_random3, // inside castle
		_random4, // villa roof
		_random5, // inside villa
		_random6  // outpost
	],
	"Random",				// Classname of turret - Can use "Random"
	"easy",					// Skill level of unit (easy, medium, hard, extreme, Random)
	_aiType,				// Skin - "Random", "Classname", or Array 
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"Random",				// Backpack - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnStatic;

Place your custom static weapon spawns below
*/

// Drug Empire FOB Skalisty

// Random KORD Positions FOB
local _random1 = [[13683.2,3048.1,0],[13655,3016.54,0],[13637.1,2995.43,0]] call BIS_fnc_selectRandom;
local _random2 = [[13606.6,3088.62,0],[13628.5,3204.61,0],[13593.9,3258.72,0]] call BIS_fnc_selectRandom;
local _random3 = [[13601.6,3183.91,0],[13618.2,3186.47,0],[13613.1,3157.28,0]] call BIS_fnc_selectRandom;
// Random Static Positions FOB
local _random4 = [[13692.3,3133.7,0],[13649.7,3072.45,0],[13602.4,3053.02,0]] call BIS_fnc_selectRandom;
local _random5 = [[13571.8,2994.55,0],[13550,3019.51,0],[13605,3015.04,0]] call BIS_fnc_selectRandom;

[
	[						// Position(s) (can be multiple)
		[13614.8,3180.57,3],
		[13611.9,3191.95,3]
	],
	"KORD_high",			// Classname of turret - Can use "Random"
	"extreme",					// Skill level of unit (easy, medium, hard, extreme, Random)
	"gsc_military_helmet_wdl_DZ",				// Skin - "Random", "Classname", or Array 
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnStatic;

[
	[						// Position(s) (can be multiple)
		[13590,3176.66,13]
	],
	"Stinger_Pod_US_EP1",	// Classname of turret - Can use "Random"
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	"gsc_military_helmet_wdl_DZ",				// Skin - "Random", "Classname", or Array 
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnStatic;

[
	[						// Position(s) (can be multiple)
		[13381.2,2756.73,21],
		[13800.2,2856.69,21],
		[13833.9,2923.15,21],
		[13588.8,3159.23,21],
		[13581.2,3201.5,21],
		[13690.9,3292.59,21],
		[13712.6,3259.33,21],
		// Off Island // Facing Bridge and Island
		[13718.1,3724.81,0]
	],
	"ZU23_ins",				// Classname of turret - Can use "Random"
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	"gsc_military_helmet_wdl_DZ",				// Skin - "Random", "Classname", or Array 
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnStatic;

[
	[	// Position(s) (can be multiple)
		_random1,
		_random2,
		_random3
	],
	"KORD_high",			// Classname of turret - Can use "Random"
	"extreme",					// Skill level of unit (easy, medium, hard, extreme, Random)
	"gsc_military_helmet_wdl_DZ",				// Skin - "Random", "Classname", or Array 
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnStatic;

[
	[	// Position(s) (can be multiple)
		_random4,
		_random5
	],
	"Random",				// Classname of turret - Can use "Random"
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	"gsc_military_helmet_wdl_DZ",				// Skin - "Random", "Classname", or Array 
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"Random",				// Backpack - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnStatic;

// Static KORD Positions Cave
[
	[	// Position(s) (can be multiple)
		[13713.3,2898.16,0],
		[13719.2,2902.75,0],
		[13718.9,2866.51,0],
		[13721.4,2859.09,0],
		[13626.6,2885.51,0],
		[13618.7,2883.07,0],
		[13558.6,2926.67,2.86855]
	],
	"KORD_high",			// Classname of turret - Can use "Random"
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	"gsc_military_helmet_wdl_DZ",				// Skin - "Random", "Classname", or Array 
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnStatic;

// Static TOW Launchers
[
	[						// Position(s) (can be multiple)
		// Inside of Cave
		[13574.6,2913.41,0],
		[13592,2910.99,0],
		[13613.1,2933.93,0],
		[13674.2,2928.49,0],
		// Outside of Cave Below
		[13795.5,3016.44,0],
		[13294.3,2741.33,0],
		// Off Island // Facing Bridge and Island
		[13738.9,3717.74,0]
	],
	"TOW_TriPod",			// Classname of turret - Can use "Random"
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	"gsc_military_helmet_wdl_DZ",				// Skin - "Random", "Classname", or Array 
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnStatic;

// Random KORD Positions Cave
local _random6 = [[13575.8,2940.48,0],[13609.2,2952.72,0],[13592.7,2948.1,0]] call BIS_fnc_selectRandom;
local _random7 = [[13628.6,2916.52,0],[13646.6,2921.3,0],[13670.5,2924.92,0]] call BIS_fnc_selectRandom;
local _random8 = [[13661.8,2912.7,0],[13651.7,2933.54,0],[13651.7,2933.54,0]] call BIS_fnc_selectRandom;
local _random9 = [[13679.7,2906.6,0],[13678.7,2913.14,0],[13688.2,2901.16,0]] call BIS_fnc_selectRandom;
local _random10 = [[13706,2904.95,0],[13704.1,2923.28,0],[13704.1,2923.28,0]] call BIS_fnc_selectRandom;
local _random11 = [[13655,2906.13,4.38124],[13670.4,2915.37,4.33261],[13660.1,2916.22,7.17775]] call BIS_fnc_selectRandom;
local _random12 = [[13653.5,2917.33,7.48765],[13654,2917.47,10.5347],[13686.1,2914.5,3.6424]] call BIS_fnc_selectRandom;
local _random13 = [[13707.2,2857.71,0],[13681.3,2860,0],[13648.9,2864.46,0]] call BIS_fnc_selectRandom;
local _random14 = [[13693.8,2869.9,0],[13683.5,2847.95,0],[13703.5,2847.58,0]] call BIS_fnc_selectRandom;
local _random15 = [[13695.8,2846.02,0],[13695.8,2846.02,0],[13684.8,2854.34,0],[13705.8,2869.25,0]] call BIS_fnc_selectRandom;

[
	[	// Position(s) (can be multiple)
		_random6,
		_random7,
		_random8,
		_random9,
		_random10,
		_random11,
		_random12,
		_random13,
		_random14,
		_random15
	],
	"KORD_high",			// Classname of turret - Can use "Random"
	"extreme",				// Skill level of unit (easy, medium, hard, extreme, Random)
	"gsc_military_helmet_wdl_DZ",				// Skin - "Random", "Classname", or Array 
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"Random",				// Backpack - "Random", "Classname", "none" or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_SpawnStatic;

/*
Custom Chopper Patrol spawn Eg.

[
	[725.391,4526.06,0],	// Position to patrol
	500,					// Radius of patrol
	10,						// Number of waypoints to give
	"UH1H_DZ",				// Classname of helicopter - Do not use DZE models because they spawn with no ammo.
	"Random",				// Skill level of units (easy, medium, hard, extreme, Random)
	"Random",				// Skin - "Random", "Classname", or Array
	_aitype,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_HeliPatrol;

Place your Chopper patrols below
*/

[
	_position,				// Position to patrol
	700,					// Radius of patrol
	10,						// Number of waypoints to give
	"UH1Y",					// Classname of helicopter - Do not use DZE models because they spawn with no ammo.
	"extreme",				// Skill level of units (easy, medium, hard, extreme, Random)
	"Random",				// Skin - "Random", "Classname", or Array
	_aitype,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_HeliPatrol;

[
	_position,				// Position to patrol
	700,					// Radius of patrol
	10,						// Number of waypoints to give
	"Ka52Black",			// Classname of helicopter - Do not use DZE models because they spawn with no ammo.
	"extreme",				// Skill level of units (easy, medium, hard, extreme, Random)
	"Random",				// Skin - "Random", "Classname", or Array
	_aitype,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_HeliPatrol;

/* 
Custom vehicle patrol spawns Eg.

[
	[725.391,4526.06,0],	// Position to patrol
	[725.391,4526.06,0],	// Position to spawn at
	200,					// Radius of patrol
	10,						// Number of waypoints to give
	"HMMWV_Armored",		// Classname of vehicle (make sure it has driver and gunner). Do not use DZE models because they spawn with no ammo.
	"Random",				// Skill level of units (easy, medium, hard, extreme, Random)
	_aiType,				// Skin - "Random", "Classname", or Array
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_VehPatrol;

Place your vehicle patrols below this line
*/

// Drug Empire FOB Skalisty
[
	[13971.8,2852.2,0],		// Position to patrol
	[13971.8,2852.2,0],		// Position to spawn at
	100,					// Radius of patrol
	10,						// Number of waypoints to give
	"M6_EP1",				// Classname of vehicle (make sure it has driver and gunner). Do not use DZE models because they spawn with no ammo.
	"extreme",				// Skill level of units (easy, medium, hard, extreme, Random)
	_aiType,				// Skin - "Random", "Classname", or Array
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_VehPatrol;

// Drug Empire FOB Skalisty
[
	[13655.7,3109.52,0],	// Position to patrol
	[13655.7,3109.52,0],	// Position to spawn at
	100,					// Radius of patrol
	10,						// Number of waypoints to give
	"T90",					// Classname of vehicle (make sure it has driver and gunner). Do not use DZE models because they spawn with no ammo.
	"extreme",				// Skill level of units (easy, medium, hard, extreme, Random)
	_aiType,				// Skin - "Random", "Classname", or Array
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_VehPatrol;

/* 
Custom Boat patrol spawns

[
	[725.391,4526.06,0],	// Position to patrol
	[725.391,4526.06,0],	// Position to spawn at
	150,					// Radius of patrol. Your spawn point should be at least this distance from shore.
	10,						// Number of waypoints to give
	"RHIB",					// Classname of armed boat (make sure it has driver and gunner). Do not use DZE models because they spawn with no ammo.
	"Random",				// Skill level of units (easy, medium, hard, extreme, Random)
	"Random",				// Skin - "Random", "Classname", or Array
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission				// This is the mission variable established on line 1. Do not change.
] call WAI_VehPatrol;

Place your boat patrols below this line
*/

[
	[13117.2,2866.65,0],			// Position to patrol
	[13117.2,2866.65,0],			// Position to spawn at, can be same as patrol location
	150,							// Radius of patrol
	10,								// Number of waypoints to give
	"RHIB",							// Classname of armed boat (make sure it has driver and gunner). Do not use DZE models because they spawn with no ammo.
	"extreme",						// Skill level of units (easy, medium, hard, extreme, Random)
	_aiType,						// Skin - "Random", "Classname", or Array
	_aiType,						// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission						// This is the mission variable established on line 1. Do not change.
] call WAI_VehPatrol;

[
	[13552.5,2566.86,0],			// Position to patrol
	[13552.5,2566.86,0],			// Position to spawn at, can be same as patrol location
	150,							// Radius of patrol
	10,								// Number of waypoints to give
	"RHIB",							// Classname of armed boat (make sure it has driver and gunner). Do not use DZE models because they spawn with no ammo.
	"extreme",						// Skill level of units (easy, medium, hard, extreme, Random)
	_aiType,						// Skin - "Random", "Classname", or Array
	_aiType,						// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission						// This is the mission variable established on line 1. Do not change.
] call WAI_VehPatrol;

[
	[13908.3,3259.23,0],			// Position to patrol
	[13908.3,3259.23,0],			// Position to spawn at, can be same as patrol location
	150,							// Radius of patrol
	10,								// Number of waypoints to give
	"RHIB",							// Classname of armed boat (make sure it has driver and gunner). Do not use DZE models because they spawn with no ammo.
	"extreme",						// Skill level of units (easy, medium, hard, extreme, Random)
	_aiType,						// Skin - "Random", "Classname", or Array
	_aiType,						// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	_mission						// This is the mission variable established on line 1. Do not change.
] call WAI_VehPatrol;


/*
Paradropped unit custom spawn Eg.

[
	_position,				// Position that units will be dropped
	400,					// Radius from drop position a player has to be to spawn chopper
	"UH1H_DZ",				// Classname of chopper. Do not use DZE models because they spawn with no ammo.
	"North",				// Direction of approach for the helicopter. Options: "North","South","East","West"
	[3000,4000],			// Random distance from the mission the helicopter should start. [min distance, max distance].
	150,					// Fly in height of the helicopter. Be careful that the height is not too low or the AI might die when they hit the ground
	1.0,					// Time in seconds between each deployed paratrooper. Higher number means paradropped AI will be more spread apart. Time of 0 means they all jump out rapidly.
	200,					// Distance from the mission the helicopter should start dropping paratroopers
	5,						// Number of units to be para dropped
	"Random",				// Skill level of units (easy, medium, hard, extreme, Random)
	"Random",				// Primary gun - "Random", "Unarmed", "Classname", Array
	"",						// Launcher - "AT", "AA", or "" for no launcher
	"Random",				// Backpack - "Random", "Classname", or Array
	_aiType,				// Skin - "Random", "Classname", or Array
	"Random",				// Gearset - 0,1,2, or "Random"
	_aiType,				// AI Type, "Hero" or "Bandit". Defined at the top of this file with variable _aiType.
	true,					// true: Aircraft will stay at position and fight. false: Heli will leave if not under fire. 
	_mission				// This is the mission variable established on line 1. Do not change.
] spawn WAI_HeliPara;

Place your paradrop spawns under this line
*/



/* Custom Crate Spawns

local _loot = 
[
	0, // Max number of long guns OR [MAX number of long guns,gun_array]
	0, // Max number of tools OR [MAX number of tools,tool_array]
	0, // Max number of items OR [MAX number of items,item_array]
	0, // Max number of pistols OR [MAX number of pistol,pistol_array]
	0 // Max number of backpacks OR [MAX number of backpacks,backpack_array]
];

[
	_loot,							// loot
	WAI_CrateSm,					// crate type - array or class
	[[[x,y,z],optional direction]],	// position and optional direction
	_mission						// mission variable - DO NOT CHANGE
] call WAI_SpawnStaticCrate;

[ // example with multiple positions - it will pick one at random.
	_loot,							// loot
	WAI_CrateSm,					// crate type - array or class
	[[[x,y,z],optional direction],[[x,y,z],optional direction],[[x,y,z],optional direction]],	
	_mission					// mission variable - DO NOT CHANGE
] call WAI_SpawnStaticCrate;

Place your crate spawns under this line
*/

// FOB Crate
[
	[[3,WAI_FOB_weapons_empire],0,[10,WAI_FOB_items_empire],0,0],			// loot
	WAI_CrateMd,										// crate type - array or class
	[
		[[13602.9,3203.28,0],0],
		[[13607.6,3167.13,0],0],
		[[13594.7,3189.52,0],0]
	],
	_mission											// mission variable - DO NOT CHANGE
] call WAI_SpawnStaticCrate;

// CAVE Maincrate
[
	[[4,WAI_cave_weapons_empire],0,[20,WAI_cave_items_empire],0,0],			// loot
	WAI_CrateMd,										// crate type - array or class
	[
		[[13643.8,2904.07,0],0],
		[[13731.1,2881.94,0],0],
		[[13677.2,2868.23,0],0]
	],
	_mission											// mission variable - DO NOT CHANGE
] call WAI_SpawnStaticCrate;

// CAVE Epic-crate
[
	[[4,WAI_cave_special_empire],[3,WAI_cave_epic_empire],[10,WAI_cave_special_items_empire],0,0],			// loot
	WAI_CrateSm,										// crate type - array or class
	[
		[[13642.6,2877.91,0],0],
		[[13662.5,2846.44,0],0],
		[[13700.6,2840.8,0],0]
	],
	_mission											// mission variable - DO NOT CHANGE
] call WAI_SpawnStaticCrate;

// CAVE Drug-crates
[
	[0,0,[15,WAI_cave_drugs_empire],0,0],			// loot
	WAI_CrateSm,										// crate type - array or class
	[
		[[13690.2,2849,0],0],
		[[13698.3,2870.01,0],0],
		[[13708.5,2848.22,0],0]
	],
	_mission											// mission variable - DO NOT CHANGE
] call WAI_SpawnStaticCrate;
[
	[0,0,[15,WAI_cave_drugs_empire],0,0],				// loot
	WAI_CrateSm,										// crate type - array or class
	[
		[[13685.9,2865.54,0],0],
		[[13641,2859.22,0],0],
		[[13627.2,2870.1,0],0]
	],
	_mission											// mission variable - DO NOT CHANGE
] call WAI_SpawnStaticCrate;
[
	[0,0,[15,WAI_cave_drugs_empire],0,0],				// loot
	WAI_CrateSm,										// crate type - array or class
	[
		[[13668.3,2933.69,0],0],
		[[13646.1,2931.01,0.396606],0],
		[[13636.3,2936.96,0.552322],0]
	],
	_mission											// mission variable - DO NOT CHANGE
] call WAI_SpawnStaticCrate;
[
	[0,0,[15,WAI_cave_drugs_empire],0,0],				// loot
	WAI_CrateSm,										// crate type - array or class
	[
		[[13662.5,2908.49,0],0],
		[[13667.8,2908.6,4.39636],0],
		[[13653.6,2906.58,10.5228],0]
	],
	_mission											// mission variable - DO NOT CHANGE
] call WAI_SpawnStaticCrate;
[
	[0,0,[15,WAI_cave_drugs_empire],0,0],				// loot
	WAI_CrateSm,										// crate type - array or class
	[
		[[13690.3,2907.86,0],0],
		[[13697.8,2912.15,0],0],
		[[13700.4,2906.58,0],0]
	],
	_mission											// mission variable - DO NOT CHANGE
] call WAI_SpawnStaticCrate;

// End

// Begin the mission monitoring thread.
[
	_mission, // Mission number
	_position, // Position of mission
	_name, // Name of Mission
	_triggerDistance, // used for AI caching
	_aiType, // "Bandit" or "Hero"
	_aiCaching, // true or false
	_autoClaim, // true or false
	_killPercent, // Percentage of AI killed to clear the mission
	_lootWhenClear, // Wait to spawn loot in crates until mission cleared
	_claimPlayer,
	[_enableRespawn,_respawnTimer], // Respawn options
	[_enableMarkers,_markerIndex,_showMarkerText,_showAiCount,_markWhenClear] // Marker parameters
] execVM "\z\addons\dayz_server\WAI\static\compile\staticMissionMonitor.sqf";

diag_log format ["WAI: static mission %1 started.",_name];
