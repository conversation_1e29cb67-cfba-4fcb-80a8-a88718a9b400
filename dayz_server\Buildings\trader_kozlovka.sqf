/*
 
        Kozlovka Trader Outpost
---------------------------------------
        filename: trader_kozlovka.sqf
        made by:  <PERSON><PERSON><PERSON>   (EpochMods forum)
 
*/
 
 
if (isServer) then {
 
_vehicle_20 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4675.187, 4687.1309, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_20 = _this;
  _this setDir 109.43417;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4675.187, 4687.1309, -9.1552734e-005];
};
 
_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4681.0142, 4685.2886, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir -71.535011;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4681.0142, 4685.2886, -3.0517578e-005];
};
 
_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4686.6929, 4683.2227], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir -71.409035;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4686.6929, 4683.2227];
};
 
_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4692.4253, 4681.2109, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir 109.26269;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4692.4253, 4681.2109, -3.0517578e-005];
};
 
_vehicle_24 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4698.1094, 4678.8096], [], 0, "CAN_COLLIDE"];
  _vehicle_24 = _this;
  _this setDir 112.22902;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4698.1094, 4678.8096];
};
 
_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4703.6362, 4676.5635, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir -68.222382;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4703.6362, 4676.5635, 3.0517578e-005];
};
 
_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4709.2456, 4674.356, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setDir -70.047356;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4709.2456, 4674.356, 3.0517578e-005];
};
 
_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4714.9268, 4672.3716, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir -72.43251;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4714.9268, 4672.3716, 3.0517578e-005];
};
 
_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4720.6333, 4670.5, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setDir -73.434601;
  _this setPos [4720.6333, 4670.5, 6.1035156e-005];
};
 
_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4726.4048, 4668.8481, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setDir -75.813141;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4726.4048, 4668.8481, 3.0517578e-005];
};
 
_vehicle_30 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4732.1855, 4670.9102, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_30 = _this;
  _this setDir 47.721973;
  _this setPos [4732.1855, 4670.9102, 3.0517578e-005];
};
 
_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4736.5146, 4675.2227, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir 43.984669;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4736.5146, 4675.2227, 3.0517578e-005];
};
 
_vehicle_32 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4740.6201, 4679.8149, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_32 = _this;
  _this setDir 39.554882;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4740.6201, 4679.8149, -9.1552734e-005];
};
 
_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4744.2305, 4684.9351], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setDir 29.440477;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4744.2305, 4684.9351];
};
 
_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4747.2847, 4690.2866, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir 28.673834;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4747.2847, 4690.2866, 3.0517578e-005];
};
 
_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4750.2271, 4695.686, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir 26.074873;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4750.2271, 4695.686, -3.0517578e-005];
};
 
_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4752.0493, 4701.5796], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir 2.6335235;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4752.0493, 4701.5796];
};
 
_vehicle_37 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4749.8247, 4707.0332, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_37 = _this;
  _this setDir -41.065304;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4749.8247, 4707.0332, -3.0517578e-005];
};
 
_vehicle_38 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4745.0757, 4711.2554, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_38 = _this;
  _this setDir -56.837143;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4745.0757, 4711.2554, 3.0517578e-005];
};
 
_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4738.8828, 4713.1411], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setDir -89.793076;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4738.8828, 4713.1411];
};
 
_vehicle_40 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4732.8296, 4713.3018, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_40 = _this;
  _this setDir -88.751778;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4732.8296, 4713.3018, -3.0517578e-005];
};
 
_vehicle_41 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4726.7603, 4713.3262, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_41 = _this;
  _this setDir -91.009674;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4726.7603, 4713.3262, -3.0517578e-005];
};
 
_vehicle_42 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4720.5083, 4713.4565, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_42 = _this;
  _this setDir 93.65123;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4720.5083, 4713.4565, -6.1035156e-005];
};
 
_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4714.5566, 4713.729, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setDir 90.666168;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4714.5566, 4713.729, -3.0517578e-005];
};
 
_vehicle_46 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1Bo_military", [4710.27, 4713.2222], [], 0, "CAN_COLLIDE"];
  _vehicle_46 = _this;
  _this setDir -0.91412306;
  _this setPos [4710.27, 4713.2222];
};
 
_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1Bo_military", [4707.9136, 4713.1748, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setDir 0.12852822;
  _this setPos [4707.9136, 4713.1748, -0.00012207031];
};
 
_vehicle_48 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4703.6846, 4712.4028, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_48 = _this;
  _this setDir -88.31147;
  _this setPos [4703.6846, 4712.4028, -3.0517578e-005];
};
 
_vehicle_49 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4698.0962, 4712.917, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_49 = _this;
  _this setDir -85.96846;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4698.0962, 4712.917, -6.1035156e-005];
};
 
_vehicle_50 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4692.0747, 4713.9697], [], 0, "CAN_COLLIDE"];
  _vehicle_50 = _this;
  _this setDir -75.202972;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4692.0747, 4713.9697];
};
 
_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4686.126, 4716.0176, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setDir -67.044739;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4686.126, 4716.0176, -3.0517578e-005];
};
 
_vehicle_52 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4680.5938, 4718.3198, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_52 = _this;
  _this setDir -67.427757;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4680.5938, 4718.3198, -6.1035156e-005];
};
 
_vehicle_53 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_Cargo1B_military", [4675.0283, 4720.6797], [], 0, "CAN_COLLIDE"];
  _vehicle_53 = _this;
  _this setDir -64.803741;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4675.0283, 4720.6797];
};
 
_vehicle_55 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4694.4399, 4709.7559, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_55 = _this;
  _this setDir 100.47068;
  _this setPos [4694.4399, 4709.7559, 9.1552734e-005];
};
 
_vehicle_56 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Wall_Tin_4", [4693.6362, 4705.8623, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_56 = _this;
  _this setDir 103.16486;
  _this setPos [4693.6362, 4705.8623, -6.1035156e-005];
};
 
_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CamoNet_NATO_var1", [4697.8936, 4703.6074, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir -92.501526;
  _this setPos [4697.8936, 4703.6074, 3.0517578e-005];
};
 
_vehicle_64 = objNull;
if (true) then
{
  _this = createVehicle ["TK_GUE_WarfareBFieldhHospital_EP1", [4685.9365, 4710.1025, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_64 = _this;
  _this setDir -246.41701;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4685.9365, 4710.1025, -6.1035156e-005];
};
 
_soundSource_0 = objNull;
if (true) then
{
  _soundSource_0 = createSoundSource ["Sound_BadDog", [4675.4116, 4688.4429, 9.1552734e-005], [], 0];
};
 
_vehicle_65 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Antenna", [4691.2168, 4709.8813], [], 0, "CAN_COLLIDE"];
  _vehicle_65 = _this;
  _this setPos [4691.2168, 4709.8813];
};
 
_vehicle_67 = objNull;
if (true) then
{
  _this = createVehicle ["PowGen_Big", [4695.0356, 4695.1157, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_67 = _this;
  _this setDir -80.365425;
  _this setPos [4695.0356, 4695.1157, -6.1035156e-005];
};
 
_vehicle_70 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_datsun02T", [4725.2295, 4703.5322, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_70 = _this;
  _this setDir 43.517776;
  _this setPos [4725.2295, 4703.5322, 6.1035156e-005];
};
 
_vehicle_72 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_HMMWV_wrecked", [4716.9424, 4709.9722, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_72 = _this;
  _this setDir -70.547752;
  _this setPos [4716.9424, 4709.9722, 6.1035156e-005];
};
 
_vehicle_74 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_rubble3", [4735.6943, 4701.5698, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_74 = _this;
  _this setPos [4735.6943, 4701.5698, 6.1035156e-005];
};
 
_vehicle_76 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_ruin_corner_1", [4735.5767, 4698.7842, -0.56299394], [], 0, "CAN_COLLIDE"];
  _vehicle_76 = _this;
  _this setDir 0.62597859;
  _this setPos [4735.5767, 4698.7842, -0.56299394];
};
 
_vehicle_77 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Heli_H_army", [4732.4419, 4730.2139], [], 0, "CAN_COLLIDE"];
  _vehicle_77 = _this;
  _this setPos [4732.4419, 4730.2139];
};
 
_vehicle_78 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Heli_H_army", [4716.6279, 4731.3042, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_78 = _this;
  _this setPos [4716.6279, 4731.3042, 0];
};

_vehicle_79 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4716.6836, 4731.5952, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_79 = _this;
  _this setPos [4716.6836, 4731.5952, 9.1552734e-005];
};
 
_vehicle_80 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4732.6416, 4730.2568, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_80 = _this;
  _this setPos [4732.6416, 4730.2568, 6.1035156e-005];
};
 
_vehicle_81 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4721.6084, 4682.084, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_81 = _this;
  _this setPos [4721.6084, 4682.084, 0];
};
 
_vehicle_83 = objNull;
if (true) then
{
  _this = createVehicle ["Land_radar", [4739.0532, 4707.9131, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_83 = _this;
  _this setDir -285.0361;
  _this setPos [4739.0532, 4707.9131, 3.0517578e-005];
};
 
_vehicle_84 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_meat_EP1", [4689.4946, 4700.6172, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_84 = _this;
  _this setDir -10.514383;
  _this setPos [4689.4946, 4700.6172, 0.00012207031];
};
 
_vehicle_85 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_small_EP1", [4689.084, 4698.3403, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_85 = _this;
  _this setDir -76.650055;
  _this setPos [4689.084, 4698.3403, 0.00012207031];
};
 
_vehicle_86 = objNull;
if (true) then
{
  _this = createVehicle ["Land_stand_waterl_EP1", [4689.1113, 4696.0825, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_86 = _this;
  _this setDir 141.14397;
  _this setPos [4689.1113, 4696.0825, -3.0517578e-005];
};
 
_vehicle_87 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Timbers", [4675.7197, 4717.5659, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_87 = _this;
  _this setDir 22.152496;
  _this setPos [4675.7197, 4717.5659, 9.1552734e-005];
};
 
_vehicle_88 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Ind_Timbers", [4673.21, 4718.4067], [], 0, "CAN_COLLIDE"];
  _vehicle_88 = _this;
  _this setDir 21.595154;
  _this setPos [4673.21, 4718.4067];
};
 
_vehicle_89 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_TyreHeapEP1", [4669.5801, 4708.7588], [], 0, "CAN_COLLIDE"];
  _vehicle_89 = _this;
  _this setDir -179.80896;
  _this setPos [4669.5801, 4708.7588];
};
 
_vehicle_90 = objNull;
if (true) then
{
  _this = createVehicle ["Fort_Crate_wood", [4674.105, 4708.2271, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_90 = _this;
  _this setPos [4674.105, 4708.2271, 3.0517578e-005];
};
 
_vehicle_91 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_crates_EP1", [4678.7827, 4706.1318, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_91 = _this;
  _this setDir 66.610397;
  _this setPos [4678.7827, 4706.1318, -3.0517578e-005];
};
 
_vehicle_92 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Barrel_empty", [4677.3965, 4707.4336, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_92 = _this;
  _this setPos [4677.3965, 4707.4336, -3.0517578e-005];
};
 
_vehicle_93 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Barrel_empty", [4676.8989, 4707.7393], [], 0, "CAN_COLLIDE"];
  _vehicle_93 = _this;
  _this setPos [4676.8989, 4707.7393];
};
 
_vehicle_94 = objNull;
if (true) then
{
  _this = createVehicle ["Land_cages_EP1", [4673.9443, 4696.3271, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_94 = _this;
  _this setDir -72.520279;
  _this setPos [4673.9443, 4696.3271, 3.0517578e-005];
};
 
_vehicle_95 = objNull;
if (true) then
{
  _this = createVehicle ["Pile_of_wood", [4670.0498, 4697.585, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_95 = _this;
  _this setDir -74.14872;
  _this setPos [4670.0498, 4697.585, 0.00012207031];
};
 
_vehicle_105 = objNull;
if (true) then
{
  _this = createVehicle ["Gunrack2", [4695.7144, 4706.9355, 0.15653165], [], 0, "CAN_COLLIDE"];
  _vehicle_105 = _this;
  _this setDir -81.061897;
  _this setVehicleInit "this setVectorUp [0,0,1]; clearweaponcargo this; clearmagazinecargo this;";
  _this setPos [4695.7144, 4706.9355, 0.15653165];
};
 
_vehicle_106 = objNull;
if (true) then
{
  _this = createVehicle ["GunrackTK_EP1", [4696.0303, 4707.8257, 0.20974416], [], 0, "CAN_COLLIDE"];
  _vehicle_106 = _this;
  _this setDir -79.912552;
  _this setVehicleInit "this setVectorUp [0,0,1]; clearweaponcargo this; clearmagazinecargo this,";
  _this setPos [4696.0303, 4707.8257, 0.20974416];
};
 
_vehicle_107 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_PowGen_Big", [4727.9204, 4675.3369, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_107 = _this;
  _this setDir 43.478092;
  _this setPos [4727.9204, 4675.3369, -3.0517578e-005];
};
 
_vehicle_108 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Fort_Razorwire", [4661.5547, 4698.4224, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_108 = _this;
  _this setDir -69.620132;
  _this setPos [4661.5547, 4698.4224, -6.1035156e-005];
};
 
_vehicle_110 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Fort_Razorwire", [4667.2954, 4710.4878], [], 0, "CAN_COLLIDE"];
  _vehicle_110 = _this;
  _this setDir -63.776833;
  _this setPos [4667.2954, 4710.4878];
};
 
_vehicle_111 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Fort_Razorwire", [4670.52, 4717.5161], [], 0, "CAN_COLLIDE"];
  _vehicle_111 = _this;
  _this setDir -66.229668;
  _this setPos [4670.52, 4717.5161];
};
 
_vehicle_114 = objNull;
if (true) then
{
  _this = createVehicle ["TK_GUE_WarfareBVehicleServicePoint_EP1", [4730.6968, 4684.2822, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_114 = _this;
  _this setDir -97.250259;
  _this setPos [4730.6968, 4684.2822, 3.0517578e-005];
};
 
_vehicle_115 = objNull;
if (true) then
{
  _this = createVehicle ["ClutterCutter_EP1", [4713.9453, 4682.8252, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_115 = _this;
  _this setPos [4713.9453, 4682.8252, 0];
};
 
_vehicle_117 = objNull;
if (true) then
{
  _this = createVehicle ["FlagCarrierNATO_EP1", [4665.2739, 4702.855], [], 0, "CAN_COLLIDE"];
  _vehicle_117 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4665.2739, 4702.855];
};
 
_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [4666.2383, 4705.666, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setDir -67.548203;
  _this setPos [4666.2383, 4705.666, -6.1035156e-005];
};
 
_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [4650.2363, 4712.2285, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir -65.27211;
  _this setPos [4650.2363, 4712.2285, 3.0517578e-005];
};
 
_vehicle_124 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [4616.5254, 4724.1685, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_124 = _this;
  _this setDir 90.954803;
  _this setPos [4616.5254, 4724.1685, -3.0517578e-005];
};
 
_vehicle_125 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [4620.709, 4723.6919, -0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_125 = _this;
  _this setDir 103.04012;
  _this setPos [4620.709, 4723.6919, -0.00012207031];
};
 
_vehicle_127 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [4599.2563, 4722.8418, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_127 = _this;
  _this setDir 80.624382;
  _this setPos [4599.2563, 4722.8418, -9.1552734e-005];
};
 
_vehicle_128 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [4585.1841, 4718.5166, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_128 = _this;
  _this setDir 68.845634;
  _this setPos [4585.1841, 4718.5166, 3.0517578e-005];
};
 
_vehicle_129 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [4581.5815, 4716.4248, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_129 = _this;
  _this setDir 57.961433;
  _this setPos [4581.5815, 4716.4248, 6.1035156e-005];
};
 
_vehicle_130 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [4567.4922, 4706.3052, -0.00018310547], [], 0, "CAN_COLLIDE"];
  _vehicle_130 = _this;
  _this setDir 49.295219;
  _this setPos [4567.4922, 4706.3052, -0.00018310547];
};
 
_vehicle_131 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [4556.248, 4693.6113, -1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_131 = _this;
  _this setDir 36.37508;
  _this setPos [4556.248, 4693.6113, -1.5258789e-005];
};
 
_vehicle_132 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [4547.1099, 4681.3013], [], 0, "CAN_COLLIDE"];
  _vehicle_132 = _this;
  _this setDir 31.429846;
  _this setPos [4547.1099, 4681.3013];
};
 
_vehicle_133 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [4537.6504, 4667.1289, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_133 = _this;
  _this setDir 33.817055;
  _this setPos [4537.6504, 4667.1289, -0.00010681152];
};
 
_vehicle_134 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [4527.8599, 4653.6021, -0.00016784668], [], 0, "CAN_COLLIDE"];
  _vehicle_134 = _this;
  _this setDir 35.780334;
  _this setPos [4527.8599, 4653.6021, -0.00016784668];
};
 
_vehicle_135 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_25", [4528.0439, 4653.7227, -0.00010681152], [], 0, "CAN_COLLIDE"];
  _vehicle_135 = _this;
  _this setDir -143.99324;
  _this setPos [4528.0439, 4653.7227, -0.00010681152];
};
 
_vehicle_136 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_10_100", [4525.4126, 4650.4854, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_136 = _this;
  _this setDir -133.56946;
  _this setPos [4525.4126, 4650.4854, 6.1035156e-005];
};
 
_vehicle_137 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [4497.4966, 4629.7222, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_137 = _this;
  _this setDir 55.157173;
  _this setPos [4497.4966, 4629.7222, 3.0517578e-005];
};
 
_vehicle_138 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_0_2000", [4483.4717, 4619.6313, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_138 = _this;
  _this setDir 54.197453;
  _this setPos [4483.4717, 4619.6313, 6.1035156e-005];
};
 
_vehicle_139 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_asf3_6konec", [4671.9419, 4703.2661, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_139 = _this;
  _this setDir -66.423035;
  _this setPos [4671.9419, 4703.2661, -3.0517578e-005];
};
 
_vehicle_141 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHSStre1", [4645.1758, 4720.9302, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_141 = _this;
  _this setDir -61.158195;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4645.1758, 4720.9302, -3.0517578e-005];
};
 
_vehicle_142 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHSStre1", [4583.1357, 4722.6094, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_142 = _this;
  _this setDir 247.39311;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4583.1357, 4722.6094, -6.1035156e-005];
};
 
_vehicle_143 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHSStre1", [4536.6133, 4674.1279, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_143 = _this;
  _this setDir 218.11455;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4536.6133, 4674.1279, 6.1035156e-005];
};
 
_vehicle_144 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHSSidl3", [4483.4961, 4626.8696, -4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_144 = _this;
  _this setDir -42.896683;
  _this setPos [4483.4961, 4626.8696, -4.5776367e-005];
};
 
_vehicle_146 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sign_left", [4638.1313, 4722.0635, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_146 = _this;
  _this setDir -63.810291;
  _this setPos [4638.1313, 4722.0635, -9.1552734e-005];
};
 
_vehicle_147 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_sign_right", [4551.7021, 4681.5972, 1.5258789e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_147 = _this;
  _this setDir 26.407524;
  _this setPos [4551.7021, 4681.5972, 1.5258789e-005];
};
 
_vehicle_150 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Campfire_burning", [4698.5313, 4704.3052, 0.14054893], [], 0, "CAN_COLLIDE"];
  _vehicle_150 = _this;
  _this setDir 10.099758;
  _this setPos [4698.5313, 4704.3052, 0.14054893];
};
 
_vehicle_151 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [4666.6128, 4699.6958, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_151 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4666.6128, 4699.6958, 0.00012207031];
};
 
_vehicle_152 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [4687.1567, 4701.7104, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_152 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4687.1567, 4701.7104, 0.00012207031];
};
 
_vehicle_153 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [4726.0381, 4689.9873, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_153 = _this;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4726.0381, 4689.9873, -3.0517578e-005];
};
 
_vehicle_155 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVSidl3", [4699.9429, 4695.1494, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_155 = _this;
  _this setDir 51.059086;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4699.9429, 4695.1494, 6.1035156e-005];
};
 
_vehicle_156 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVSidl3", [4679.1836, 4689.1865], [], 0, "CAN_COLLIDE"];
  _vehicle_156 = _this;
  _this setDir 25.483931;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4679.1836, 4689.1865];
};
 
_vehicle_157 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVSidl3", [4727.8359, 4679.2593], [], 0, "CAN_COLLIDE"];
  _vehicle_157 = _this;
  _this setDir -61.891651;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4727.8359, 4679.2593];
};
 
_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_EU_LHVSidl3", [4680.3892, 4706.2334, 9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setDir 48.932304;
  _this setVehicleInit "this setVectorUp [0,0,1];";
  _this setPos [4680.3892, 4706.2334, 9.1552734e-005];
};
 
_vehicle_159 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_runway_BluelightB", [4716.6187, 4738.1138, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_159 = _this;
  _this setPos [4716.6187, 4738.1138, 3.0517578e-005];
};
 
_vehicle_160 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_runway_BluelightB", [4716.3965, 4724.9165, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_160 = _this;
  _this setPos [4716.3965, 4724.9165, -3.0517578e-005];
};
 
_vehicle_161 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_runway_BluelightB", [4722.8511, 4731.0488], [], 0, "CAN_COLLIDE"];
  _vehicle_161 = _this;
  _this setPos [4722.8511, 4731.0488];
};
 
_vehicle_162 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_runway_BluelightB", [4710.2441, 4731.2031, 0.00012207031], [], 0, "CAN_COLLIDE"];
  _vehicle_162 = _this;
  _this setPos [4710.2441, 4731.2031, 0.00012207031];
};
 
_vehicle_163 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_runway_BluelightB", [4732.5669, 4737.063, 0], [], 0, "CAN_COLLIDE"];
  _vehicle_163 = _this;
  _this setPos [4732.5669, 4737.063, 0];
};
 
_vehicle_164 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_runway_BluelightB", [4732.4155, 4723.8159, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_164 = _this;
  _this setPos [4732.4155, 4723.8159, -6.1035156e-005];
};
 
_vehicle_165 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_runway_BluelightB", [4738.8823, 4730.6035, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_165 = _this;
  _this setPos [4738.8823, 4730.6035, 6.1035156e-005];
};
 
_vehicle_166 = objNull;
if (true) then
{
  _this = createVehicle ["ASC_runway_BluelightB", [4726.1104, 4730.8799, 3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_166 = _this;
  _this setPos [4726.1104, 4730.8799, 3.0517578e-005];
};
 
_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [4712.6548, 4709.0278], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setPos [4712.6548, 4709.0278];
};
 
_vehicle_168 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fire_barrel_burning", [4705.7134, 4708.9009, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_168 = _this;
  _this setPos [4705.7134, 4708.9009, 6.1035156e-005];
};
 
_vehicle_177 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrates_NoInteractive_Large", [4695.2646, 4705.7202, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_177 = _this;
  _this setDir 50.163651;
  _this setPos [4695.2646, 4705.7202, -6.1035156e-005];
};
 
_vehicle_178 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrates_NoInteractive_Medium", [4694.8682, 4703.5718, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_178 = _this;
  _this setDir -0.45332512;
  _this setPos [4694.8682, 4703.5718, -3.0517578e-005];
};
 
_vehicle_179 = objNull;
if (true) then
{
  _this = createVehicle ["AmmoCrate_NoInteractive_", [4696.4409, 4709.7891, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_179 = _this;
  _this setPos [4696.4409, 4709.7891, -9.1552734e-005];
};
 
_vehicle_182 = objNull;
if (true) then
{
  _this = createVehicle ["Misc_cargo_cont_net2", [4692.7983, 4699.6753, -6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_182 = _this;
  _this setDir 19.449417;
  _this setPos [4692.7983, 4699.6753, -6.1035156e-005];
};
 
_vehicle_186 = objNull;
if (true) then
{
  _this = createVehicle ["Land_transport_crates_EP1", [4724.7354, 4696.5181, -9.1552734e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_186 = _this;
  _this setPos [4724.7354, 4696.5181, -9.1552734e-005];
};
 
_vehicle_187 = objNull;
if (true) then
{
  _this = createVehicle ["PowerGenerator", [4741.3906, 4706.4551, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_187 = _this;
  _this setDir -39.717056;
  _this setPos [4741.3906, 4706.4551, 6.1035156e-005];
};

_vehicle_188 = objNull;
if (true) then
{
  _this = createVehicle ["HeliHEmpty", [4710.86,4689.96,0.00125122], [], 0, "CAN_COLLIDE"];
  _vehicle_188 = _this;
  _this setPos [4710.86,4689.96,0.00125122];
};
 
};