if (isServer) then {	
_vehicle_0 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13690.663, 2831.3127, -2.45667], [], 0, "CAN_COLLIDE"];
  _vehicle_0 = _this;
  _this setDir 1.9017864;
  _this setPos [13690.663, 2831.3127, -2.45667];
};

_vehicle_2 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13731.986, 2846.6143, 8.1192255], [], 0, "CAN_COLLIDE"];
  _vehicle_2 = _this;
  _this setDir -56.525894;
  _this setPos [13731.986, 2846.6143, 8.1192255];
};

_vehicle_3 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13728.937, 2845.9956, -3.7967858], [], 0, "CAN_COLLIDE"];
  _vehicle_3 = _this;
  _this setDir -56.240055;
  _this setPos [13728.937, 2845.9956, -3.7967858];
};

_vehicle_4 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13694.667, 2884.0308, -2.8925302], [], 0, "CAN_COLLIDE"];
  _vehicle_4 = _this;
  _this setDir -181.89565;
  _this setPos [13694.667, 2884.0308, -2.8925302];
};

_vehicle_5 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13743.284, 2880.2178, -8.7003155], [], 0, "CAN_COLLIDE"];
  _vehicle_5 = _this;
  _this setDir -109.60667;
  _this setPos [13743.284, 2880.2178, -8.7003155];
};

_vehicle_6 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13727.416, 2912.405, -8.4182463], [], 0, "CAN_COLLIDE"];
  _vehicle_6 = _this;
  _this setDir -126.35225;
  _this setPos [13727.416, 2912.405, -8.4182463];
};

_vehicle_7 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13699.305, 2936.3105, -7.567379], [], 0, "CAN_COLLIDE"];
  _vehicle_7 = _this;
  _this setDir -172.98186;
  _this setPos [13699.305, 2936.3105, -7.567379];
};

_vehicle_9 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13705.476, 2843.229, 20.26675], [], 0, "CAN_COLLIDE"];
  _vehicle_9 = _this;
  _this setDir -29.756226;
  _this setPos [13705.476, 2843.229, 20.26675];
};

_vehicle_10 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13703.578, 2856.3967, 19.320124], [], 0, "CAN_COLLIDE"];
  _vehicle_10 = _this;
  _this setDir -21.686335;
  _this setPos [13703.578, 2856.3967, 19.320124];
};

_vehicle_11 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13708.512, 2865.2813, 19.700081], [], 0, "CAN_COLLIDE"];
  _vehicle_11 = _this;
  _this setDir -179.04546;
  _this setPos [13708.512, 2865.2813, 19.700081];
};

_vehicle_12 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13733.608, 2876.4282, 16.363365], [], 0, "CAN_COLLIDE"];
  _vehicle_12 = _this;
  _this setDir -124.35089;
  _this setPos [13733.608, 2876.4282, 16.363365];
};

_vehicle_13 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13716.144, 2873.6038, 18.932024], [], 0, "CAN_COLLIDE"];
  _vehicle_13 = _this;
  _this setDir -126.78888;
  _this setPos [13716.144, 2873.6038, 18.932024];
};

_vehicle_14 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13720.871, 2903.2336, 16.310785], [], 0, "CAN_COLLIDE"];
  _vehicle_14 = _this;
  _this setDir -105.97884;
  _this setPos [13720.871, 2903.2336, 16.310785];
};

_vehicle_15 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13711.456, 2909.5981, 15.607181], [], 0, "CAN_COLLIDE"];
  _vehicle_15 = _this;
  _this setDir 100.73753;
  _this setPos [13711.456, 2909.5981, 15.607181];
};

_vehicle_16 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13699.389, 2925.8374, 14.368251], [], 0, "CAN_COLLIDE"];
  _vehicle_16 = _this;
  _this setDir -163.50594;
  _this setPos [13699.389, 2925.8374, 14.368251];
};

_vehicle_17 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13698.482, 2919.8191, 12.504976], [], 0, "CAN_COLLIDE"];
  _vehicle_17 = _this;
  _this setDir -164.51219;
  _this setPos [13698.482, 2919.8191, 12.504976];
};

_vehicle_18 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13694.059, 2899.7996, 16.646872], [], 0, "CAN_COLLIDE"];
  _vehicle_18 = _this;
  _this setDir -279.75797;
  _this setPos [13694.059, 2899.7996, 16.646872];
};

_vehicle_19 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13684.961, 2911.5032, 16.7255], [], 0, "CAN_COLLIDE"];
  _vehicle_19 = _this;
  _this setDir 81.636154;
  _this setPos [13684.961, 2911.5032, 16.7255];
};

_vehicle_20 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13655.416, 2892.1321, -5.1109881], [], 0, "CAN_COLLIDE"];
  _vehicle_20 = _this;
  _this setDir -4.8028336;
  _this setPos [13655.416, 2892.1321, -5.1109881];
};

_vehicle_21 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13664.627, 2947.0391, -6.8600574], [], 0, "CAN_COLLIDE"];
  _vehicle_21 = _this;
  _this setDir -166.0677;
  _this setPos [13664.627, 2947.0391, -6.8600574];
};

_vehicle_22 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13649.274, 2839.8503, -4.9554772], [], 0, "CAN_COLLIDE"];
  _vehicle_22 = _this;
  _this setDir 18.847399;
  _this setPos [13649.274, 2839.8503, -4.9554772];
};

_vehicle_23 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13622.454, 2853.1631, -4.3577418], [], 0, "CAN_COLLIDE"];
  _vehicle_23 = _this;
  _this setDir -154.92332;
  _this setPos [13622.454, 2853.1631, -4.3577418];
};

_vehicle_24 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13606.289, 2880.3357, -4.9982848], [], 0, "CAN_COLLIDE"];
  _vehicle_24 = _this;
  _this setDir 67.536736;
  _this setPos [13606.289, 2880.3357, -4.9982848];
};

_vehicle_25 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13640.027, 2951.2625, -7.6714792], [], 0, "CAN_COLLIDE"];
  _vehicle_25 = _this;
  _this setDir -201.55124;
  _this setPos [13640.027, 2951.2625, -7.6714792];
};

_vehicle_26 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13622.779, 2965.2473, -9.8902893], [], 0, "CAN_COLLIDE"];
  _vehicle_26 = _this;
  _this setDir -105.35159;
  _this setPos [13622.779, 2965.2473, -9.8902893];
};

_vehicle_27 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13585.292, 2899.6565, -6.250567], [], 0, "CAN_COLLIDE"];
  _vehicle_27 = _this;
  _this setDir 0.85026014;
  _this setPos [13585.292, 2899.6565, -6.250567];
};

_vehicle_28 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_Rock1", [13592.408, 2961.6238, -4.7366166], [], 0, "CAN_COLLIDE"];
  _vehicle_28 = _this;
  _this setDir 0.99636871;
  _this setPos [13592.408, 2961.6238, -4.7366166];
};

_vehicle_29 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockTower", [12012.817, 3305.8667, -7.3539815], [], 0, "CAN_COLLIDE"];
  _vehicle_29 = _this;
  _this setPos [12012.817, 3305.8667, -7.3539815];
};

_vehicle_30 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13561.452, 2966.8687, -7.8158846], [], 0, "CAN_COLLIDE"];
  _vehicle_30 = _this;
  _this setDir 98.520592;
  _this setPos [13561.452, 2966.8687, -7.8158846];
};

_vehicle_31 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13567.818, 2919.2732, 9.1964626], [], 0, "CAN_COLLIDE"];
  _vehicle_31 = _this;
  _this setDir 56.104462;
  _this setPos [13567.818, 2919.2732, 9.1964626];
};

_vehicle_32 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13561.604, 2949.0901, 12.357695], [], 0, "CAN_COLLIDE"];
  _vehicle_32 = _this;
  _this setDir 86.894379;
  _this setPos [13561.604, 2949.0901, 12.357695];
};

_vehicle_33 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13580.946, 2970.5056, 10.269987], [], 0, "CAN_COLLIDE"];
  _vehicle_33 = _this;
  _this setDir 162.58482;
  _this setPos [13580.946, 2970.5056, 10.269987];
};

_vehicle_34 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13611.155, 2967.895, 9.3344355], [], 0, "CAN_COLLIDE"];
  _vehicle_34 = _this;
  _this setDir -152.56419;
  _this setPos [13611.155, 2967.895, 9.3344355];
};

_vehicle_35 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13583.504, 2957.5459, 18.14953], [], 0, "CAN_COLLIDE"];
  _vehicle_35 = _this;
  _this setDir 135.06146;
  _this setPos [13583.504, 2957.5459, 18.14953];
};

_vehicle_36 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13602.201, 2956.5112, 16.051783], [], 0, "CAN_COLLIDE"];
  _vehicle_36 = _this;
  _this setDir -175.32707;
  _this setPos [13602.201, 2956.5112, 16.051783];
};

_vehicle_37 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13573.766, 2930.176, 18.906107], [], 0, "CAN_COLLIDE"];
  _vehicle_37 = _this;
  _this setDir 63.190258;
  _this setPos [13573.766, 2930.176, 18.906107];
};

_vehicle_38 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13643.926, 2952.5095, 15.248436], [], 0, "CAN_COLLIDE"];
  _vehicle_38 = _this;
  _this setDir -158.75056;
  _this setPos [13643.926, 2952.5095, 15.248436];
};

_vehicle_39 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13663.597, 2925.5374, 20.231358], [], 0, "CAN_COLLIDE"];
  _vehicle_39 = _this;
  _this setDir -98.629669;
  _this setPos [13663.597, 2925.5374, 20.231358];
};

_vehicle_40 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13638.256, 2941.6091, 18.936848], [], 0, "CAN_COLLIDE"];
  _vehicle_40 = _this;
  _this setDir -157.45949;
  _this setPos [13638.256, 2941.6091, 18.936848];
};

_vehicle_41 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13653.669, 2921.9854, 21.245796], [], 0, "CAN_COLLIDE"];
  _vehicle_41 = _this;
  _this setDir 85.031326;
  _this setPos [13653.669, 2921.9854, 21.245796];
};

_vehicle_42 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13676.919, 2886.8066, 16.616241], [], 0, "CAN_COLLIDE"];
  _vehicle_42 = _this;
  _this setDir 75.31398;
  _this setPos [13676.919, 2886.8066, 16.616241];
};

_vehicle_43 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13677.149, 2850.9714, 12.310699], [], 0, "CAN_COLLIDE"];
  _vehicle_43 = _this;
  _this setDir 94.57444;
  _this setPos [13677.149, 2850.9714, 12.310699];
};

_vehicle_44 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13654.719, 2879.6907, 21.803907], [], 0, "CAN_COLLIDE"];
  _vehicle_44 = _this;
  _this setDir -99.657791;
  _this setPos [13654.719, 2879.6907, 21.803907];
};

_vehicle_45 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13650.547, 2851.4912, 22.956415], [], 0, "CAN_COLLIDE"];
  _vehicle_45 = _this;
  _this setDir 10.801881;
  _this setPos [13650.547, 2851.4912, 22.956415];
};

_vehicle_46 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13627.022, 2863.5784, 18.889914], [], 0, "CAN_COLLIDE"];
  _vehicle_46 = _this;
  _this setDir 30.998579;
  _this setPos [13627.022, 2863.5784, 18.889914];
};

_vehicle_47 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13632.928, 2874.0955, 23.338049], [], 0, "CAN_COLLIDE"];
  _vehicle_47 = _this;
  _this setDir -155.34489;
  _this setPos [13632.928, 2874.0955, 23.338049];
};

_vehicle_48 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13636.19, 2909.5488, 26.259056], [], 0, "CAN_COLLIDE"];
  _vehicle_48 = _this;
  _this setDir -91.743477;
  _this setPos [13636.19, 2909.5488, 26.259056];
};

_vehicle_49 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13612.141, 2940.8062, 30.87652], [], 0, "CAN_COLLIDE"];
  _vehicle_49 = _this;
  _this setDir 10.71393;
  _this setPos [13612.141, 2940.8062, 30.87652];
};

_vehicle_50 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13606.197, 2915.3381, 20.776112], [], 0, "CAN_COLLIDE"];
  _vehicle_50 = _this;
  _this setDir -60.23251;
  _this setPos [13606.197, 2915.3381, 20.776112];
};

_vehicle_51 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13590.643, 2927.2986, 23.068266], [], 0, "CAN_COLLIDE"];
  _vehicle_51 = _this;
  _this setDir -94.992096;
  _this setPos [13590.643, 2927.2986, 23.068266];
};

_vehicle_52 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13615.691, 2905.7556, 20.566422], [], 0, "CAN_COLLIDE"];
  _vehicle_52 = _this;
  _this setDir -47.362667;
  _this setPos [13615.691, 2905.7556, 20.566422];
};

_vehicle_59 = objNull;
if (true) then
{
  _this = createVehicle ["Land_Fort_Watchtower_EP1", [13560.271, 2924.5249, -0.00029563904], [], 0, "CAN_COLLIDE"];
  _vehicle_59 = _this;
  _this setDir -21.793327;
  _this setPos [13560.271, 2924.5249, -0.00029563904];
};

_vehicle_60 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13562.534, 2917.9897, -0.00026321411], [], 0, "CAN_COLLIDE"];
  _vehicle_60 = _this;
  _this setDir -108.34544;
  _this setPos [13562.534, 2917.9897, -0.00026321411];
};

_vehicle_61 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13557.956, 2932.4773, -0.00050926208], [], 0, "CAN_COLLIDE"];
  _vehicle_61 = _this;
  _this setDir -110.16699;
  _this setPos [13557.956, 2932.4773, -0.00050926208];
};

_vehicle_68 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13582.685, 2985.3643, -0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_68 = _this;
  _this setPos [13582.685, 2985.3643, -0.00020599365];
};

_vehicle_69 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13592.617, 2985.5186, -0.0004196167], [], 0, "CAN_COLLIDE"];
  _vehicle_69 = _this;
  _this setPos [13592.617, 2985.5186, -0.0004196167];
};

_vehicle_70 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13602.613, 2985.6987, -0.00026321411], [], 0, "CAN_COLLIDE"];
  _vehicle_70 = _this;
  _this setPos [13602.613, 2985.6987, -0.00026321411];
};

_vehicle_72 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_R2_RockWall", [13578.813, 2937.1558, 20.806734], [], 0, "CAN_COLLIDE"];
  _vehicle_72 = _this;
  _this setDir 60.413609;
  _this setPos [13578.813, 2937.1558, 20.806734];
};

_vehicle_97 = objNull;
if (true) then
{
  _this = createVehicle ["mbg_apartments_big_04_EO", [13661.304, 2911.4607, -0.020619193], [], 0, "CAN_COLLIDE"];
  _vehicle_97 = _this;
  _this setDir -365.37076;
  _this setPos [13661.304, 2911.4607, -0.020619193];
};

_vehicle_103 = objNull;
if (true) then
{
  _this = createVehicle ["mbg_brickhouse_02_EO", [13696.573, 2868.769, -2.6702881e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_103 = _this;
  _this setDir -1.7226026;
  _this setPos [13696.573, 2868.769, -2.6702881e-005];
};

_vehicle_104 = objNull;
if (true) then
{
  _this = createVehicle ["mbg_brickhouse_02_EO", [13704.658, 2847.989, 5.3405762e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_104 = _this;
  _this setDir -184.54762;
  _this setPos [13704.658, 2847.989, 5.3405762e-005];
};

_vehicle_112 = objNull;
if (true) then
{
  _this = createVehicle ["Land_MBG_GER_RHUS_1", [13645.551, 2934.3037, -0.00017166138], [], 0, "CAN_COLLIDE"];
  _vehicle_112 = _this;
  _this setDir -177.39664;
  _this setPos [13645.551, 2934.3037, -0.00017166138];
};

_vehicle_113 = objNull;
if (true) then
{
  _this = createVehicle ["Land_MBG_GER_RHUS_1", [13636.415, 2933.2068, -0.00035095215], [], 0, "CAN_COLLIDE"];
  _vehicle_113 = _this;
  _this setDir 2.1073127;
  _this setPos [13636.415, 2933.2068, -0.00035095215];
};

_vehicle_114 = objNull;
if (true) then
{
  _this = createVehicle ["MBG_Killhouse_1_InEditor", [13688.916, 2909.3481, -3.4332275e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_114 = _this;
  _this setDir -174.18117;
  _this setPos [13688.916, 2909.3481, -3.4332275e-005];
};

_vehicle_115 = objNull;
if (true) then
{
  _this = createVehicle ["MBG_Killhouse_1_InEditor", [13698.884, 2908.2844, -0.20238826], [], 0, "CAN_COLLIDE"];
  _vehicle_115 = _this;
  _this setDir 185.76669;
  _this setPos [13698.884, 2908.2844, -0.20238826];
};

_vehicle_116 = objNull;
if (true) then
{
  _this = createVehicle ["mbg_brickhouse_02_EO", [13686.906, 2849.2102, -4.9591064e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_116 = _this;
  _this setDir -179.43929;
  _this setPos [13686.906, 2849.2102, -4.9591064e-005];
};

_vehicle_117 = objNull;
if (true) then
{
  _this = createVehicle ["Hanged", [13561.969, 2919.1028, 1.7555993], [], 0, "CAN_COLLIDE"];
  _vehicle_117 = _this;
  _this setPos [13561.969, 2919.1028, 1.7555993];
};

_vehicle_119 = objNull;
if (true) then
{
  _this = createVehicle ["Hanged", [13563.361, 2916.9185, 1.4129428], [], 0, "CAN_COLLIDE"];
  _vehicle_119 = _this;
  _this setDir 63.221851;
  _this setPos [13563.361, 2916.9185, 1.4129428];
};

_vehicle_120 = objNull;
if (true) then
{
  _this = createVehicle ["Hanged_MD", [13557.961, 2931.4045, 2.5297868], [], 0, "CAN_COLLIDE"];
  _vehicle_120 = _this;
  _this setDir 0.35305908;
  _this setPos [13557.961, 2931.4045, 2.5297868];
};

_vehicle_121 = objNull;
if (true) then
{
  _this = createVehicle ["Hanged", [13557.171, 2935.0237, 2.8090999], [], 0, "CAN_COLLIDE"];
  _vehicle_121 = _this;
  _this setDir -105.37003;
  _this setPos [13557.171, 2935.0237, 2.8090999];
};

_vehicle_123 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Danger", [13585.649, 2997.9722, 4.196167e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_123 = _this;
  _this setPos [13585.649, 2997.9722, 4.196167e-005];
};

_vehicle_124 = objNull;
if (true) then
{
  _this = createVehicle ["Sign_Danger", [13598.834, 2997.9575, -0.00030517578], [], 0, "CAN_COLLIDE"];
  _vehicle_124 = _this;
  _this setPos [13598.834, 2997.9575, -0.00030517578];
};

_vehicle_131 = objNull;
if (true) then
{
  _this = createVehicle ["SignM_UN_Base_EP1", [13592.483, 2988.1194, -0.00011444092], [], 0, "CAN_COLLIDE"];
  _vehicle_131 = _this;
  _this setDir -180.17647;
  _this setPos [13592.483, 2988.1194, -0.00011444092];
};

_vehicle_137 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CamoNet_EAST", [13626.276, 2930.9707, -6.4849854e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_137 = _this;
  _this setDir -20.282536;
  _this setPos [13626.276, 2930.9707, -6.4849854e-005];
};

_vehicle_139 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_CamoNet_EAST", [13641.569, 2910.8623, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_139 = _this;
  _this setDir -181.56149;
  _this setPos [13641.569, 2910.8623, 4.5776367e-005];
};

_vehicle_140 = objNull;
if (true) then
{
  _this = createVehicle ["GunRack_DZ", [13639.79, 2910.3423, -0.00015640259], [], 0, "CAN_COLLIDE"];
  _vehicle_140 = _this;
  _this setDir 0.26481771;
  _this setPos [13639.79, 2910.3423, -0.00015640259];
};

_vehicle_141 = objNull;
if (true) then
{
  _this = createVehicle ["GunRack_DZ", [13643.689, 2910.4172, -3.0517578e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_141 = _this;
  _this setPos [13643.689, 2910.4172, -3.0517578e-005];
};

_vehicle_142 = objNull;
if (true) then
{
  _this = createVehicle ["GunRack_DZ", [13645.787, 2910.4438, -0.00020599365], [], 0, "CAN_COLLIDE"];
  _vehicle_142 = _this;
  _this setPos [13645.787, 2910.4438, -0.00020599365];
};

_vehicle_143 = objNull;
if (true) then
{
  _this = createVehicle ["GunRack_DZ", [13641.718, 2910.3972, 0.054125469], [], 0, "CAN_COLLIDE"];
  _vehicle_143 = _this;
  _this setDir 1.0204993;
  _this setPos [13641.718, 2910.3972, 0.054125469];
};

_vehicle_146 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_tent", [13622.569, 2929.5061, -8.392334e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_146 = _this;
  _this setDir -35.228985;
  _this setPos [13622.569, 2929.5061, -8.392334e-005];
};

_vehicle_147 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_tent", [13625.671, 2930.2725, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_147 = _this;
  _this setDir -5.1026578;
  _this setPos [13625.671, 2930.2725, -1.1444092e-005];
};

_vehicle_148 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_A_tent", [13628.805, 2930.365, 0.00012588501], [], 0, "CAN_COLLIDE"];
  _vehicle_148 = _this;
  _this setDir 5.2484436;
  _this setPos [13628.805, 2930.365, 0.00012588501];
};

_vehicle_158 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Fort_Barricade", [13597.252, 2924.8542, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_158 = _this;
  _this setDir 113.2771;
  _this setPos [13597.252, 2924.8542, 3.8146973e-006];
};

_vehicle_166 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fortified_nest_small", [13618.967, 2884.0078, -0.058283668], [], 0, "CAN_COLLIDE"];
  _vehicle_166 = _this;
  _this setDir 158.13435;
  _this setPos [13618.967, 2884.0078, -0.058283668];
};

_vehicle_167 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fortified_nest_small", [13626.632, 2886.7354, -0.27385747], [], 0, "CAN_COLLIDE"];
  _vehicle_167 = _this;
  _this setDir 156.04549;
  _this setPos [13626.632, 2886.7354, -0.27385747];
};

_vehicle_172 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fort_bagfence_corner", [13654.114, 2931.1545, -0.32453671], [], 0, "CAN_COLLIDE"];
  _vehicle_172 = _this;
  _this setDir -89.588066;
  _this setPos [13654.114, 2931.1545, -0.32453671];
};

_vehicle_173 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fort_bagfence_long", [13650.396, 2930.0901, 0.0098251812], [], 0, "CAN_COLLIDE"];
  _vehicle_173 = _this;
  _this setPos [13650.396, 2930.0901, 0.0098251812];
};

_vehicle_181 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_fort_bagfence_corner", [13653.485, 2934.791, -0.1249614], [], 0, "CAN_COLLIDE"];
  _vehicle_181 = _this;
  _this setDir -180.37494;
  _this setPos [13653.485, 2934.791, -0.1249614];
};

_vehicle_182 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_campfire", [13642.759, 2912.4436, -6.8664551e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_182 = _this;
  _this setPos [13642.759, 2912.4436, -6.8664551e-005];
};

_vehicle_188 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_garbage_misc", [13664.486, 2926.0806, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_188 = _this;
  _this setDir 42.081543;
  _this setPos [13664.486, 2926.0806, -1.1444092e-005];
};

_vehicle_189 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Greenhouse", [13664.691, 2874.4543, 0.066660441], [], 0, "CAN_COLLIDE"];
  _vehicle_189 = _this;
  _this setDir -83.328239;
  _this setPos [13664.691, 2874.4543, 0.066660441];
};

_vehicle_190 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Greenhouse", [13661.241, 2874.6467, 0.065176457], [], 0, "CAN_COLLIDE"];
  _vehicle_190 = _this;
  _this setDir -85.051155;
  _this setPos [13661.241, 2874.6467, 0.065176457];
};

_vehicle_191 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Greenhouse", [13657.967, 2874.7883, 0.11461795], [], 0, "CAN_COLLIDE"];
  _vehicle_191 = _this;
  _this setDir -85.848259;
  _this setPos [13657.967, 2874.7883, 0.11461795];
};

_vehicle_192 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Greenhouse", [13654.543, 2874.6125, 0.20189942], [], 0, "CAN_COLLIDE"];
  _vehicle_192 = _this;
  _this setDir -85.225807;
  _this setPos [13654.543, 2874.6125, 0.20189942];
};

_vehicle_193 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Greenhouse", [13664.764, 2850.8411, 0.4336589], [], 0, "CAN_COLLIDE"];
  _vehicle_193 = _this;
  _this setDir 97.113792;
  _this setPos [13664.764, 2850.8411, 0.4336589];
};

_vehicle_194 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Greenhouse", [13661.436, 2851.2605, 0.39894757], [], 0, "CAN_COLLIDE"];
  _vehicle_194 = _this;
  _this setDir 98.889275;
  _this setPos [13661.436, 2851.2605, 0.39894757];
};

_vehicle_195 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Greenhouse", [13658.089, 2851.7598, 0.4297421], [], 0, "CAN_COLLIDE"];
  _vehicle_195 = _this;
  _this setDir 98.762886;
  _this setPos [13658.089, 2851.7598, 0.4297421];
};

_vehicle_196 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_Greenhouse", [13654.611, 2852.4329, 0.3969503], [], 0, "CAN_COLLIDE"];
  _vehicle_196 = _this;
  _this setDir 99.230705;
  _this setPos [13654.611, 2852.4329, 0.3969503];
};

_vehicle_197 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpa", [13670.095, 2872.6245, 0.098142132], [], 0, "CAN_COLLIDE"];
  _vehicle_197 = _this;
  _this setDir 93.286873;
  _this setPos [13670.095, 2872.6245, 0.098142132];
};

_vehicle_198 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_pumpa", [13651.443, 2854.0874, 0.34165001], [], 0, "CAN_COLLIDE"];
  _vehicle_198 = _this;
  _this setDir -76.983582;
  _this setPos [13651.443, 2854.0874, 0.34165001];
};

_vehicle_199 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barels3", [13686.733, 2871.5859, 4.5776367e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_199 = _this;
  _this setDir 2.0680308;
  _this setPos [13686.733, 2871.5859, 4.5776367e-005];
};

_vehicle_200 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barels3", [13686.932, 2870.0474, -7.2479248e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_200 = _this;
  _this setPos [13686.932, 2870.0474, -7.2479248e-005];
};

_vehicle_201 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barels3", [13687.053, 2868.4036, -8.0108643e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_201 = _this;
  _this setPos [13687.053, 2868.4036, -8.0108643e-005];
};

_vehicle_203 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel1", [13684.816, 2871.8367, -0.00011062622], [], 0, "CAN_COLLIDE"];
  _vehicle_203 = _this;
  _this setPos [13684.816, 2871.8367, -0.00011062622];
};

_vehicle_204 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel1", [13685.441, 2871.3054, -0.00011825562], [], 0, "CAN_COLLIDE"];
  _vehicle_204 = _this;
  _this setPos [13685.441, 2871.3054, -0.00011825562];
};

_vehicle_205 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel1", [13683.963, 2871.0027, -4.196167e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_205 = _this;
  _this setPos [13683.963, 2871.0027, -4.196167e-005];
};

_vehicle_206 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel1", [13683.979, 2872.2021, -1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_206 = _this;
  _this setPos [13683.979, 2872.2021, -1.9073486e-005];
};

_vehicle_207 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel1", [13684.605, 2870.105, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_207 = _this;
  _this setPos [13684.605, 2870.105, -1.1444092e-005];
};

_vehicle_208 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel1", [13685.515, 2869.7356, 3.8146973e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_208 = _this;
  _this setPos [13685.515, 2869.7356, 3.8146973e-006];
};

_vehicle_209 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barel1", [13685.732, 2872.9121, -3.4332275e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_209 = _this;
  _this setPos [13685.732, 2872.9121, -3.4332275e-005];
};

_vehicle_210 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barels", [13683.378, 2867.571, -1.1444092e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_210 = _this;
  _this setPos [13683.378, 2867.571, -1.1444092e-005];
};

_vehicle_211 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Barels", [13685.132, 2867.5183, 6.1035156e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_211 = _this;
  _this setPos [13685.132, 2867.5183, 6.1035156e-005];
};

_vehicle_212 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_Misc_G_Pipes", [13673.95, 2850.4573, -0.030669767], [], 0, "CAN_COLLIDE"];
  _vehicle_212 = _this;
  _this setDir 8.7210913;
  _this setPos [13673.95, 2850.4573, -0.030669767];
};

_vehicle_213 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [13713.954, 2866.4836, -0.35771939], [], 0, "CAN_COLLIDE"];
  _vehicle_213 = _this;
  _this setDir -90.459328;
  _this setPos [13713.954, 2866.4836, -0.35771939];
};

_vehicle_214 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [13718.777, 2855.5938, -0.016762568], [], 0, "CAN_COLLIDE"];
  _vehicle_214 = _this;
  _this setDir -135.06877;
  _this setPos [13718.777, 2855.5938, -0.016762568];
};

_vehicle_215 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [13716.914, 2908.0044, 4.9591064e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_215 = _this;
  _this setDir -32.716606;
  _this setPos [13716.914, 2908.0044, 4.9591064e-005];
};

_vehicle_216 = objNull;
if (true) then
{
  _this = createVehicle ["MAP_BigHBarrier", [13709.598, 2899.1233], [], 0, "CAN_COLLIDE"];
  _vehicle_216 = _this;
  _this setDir -69.545792;
  _this setPos [13709.598, 2899.1233];
};
_vehicle_219 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13567.25, 2918.2759, 1.9073486e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_219 = _this;
  _this setDir -107.08727;
  _this setPos [13567.25, 2918.2759, 1.9073486e-005];
};

_vehicle_221 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13564.688, 2926.6829, 5.7220459e-006], [], 0, "CAN_COLLIDE"];
  _vehicle_221 = _this;
  _this setDir -110.36623;
  _this setPos [13564.688, 2926.6829, 5.7220459e-006];
};

_vehicle_224 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13561.714, 2935.0115, -0.00033187866], [], 0, "CAN_COLLIDE"];
  _vehicle_224 = _this;
  _this setDir -110.16699;
  _this setPos [13561.714, 2935.0115, -0.00033187866];
};

_vehicle_226 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13580.903, 2980.9827, -0.00032043457], [], 0, "CAN_COLLIDE"];
  _vehicle_226 = _this;
  _this setDir -178.63283;
  _this setPos [13580.903, 2980.9827, -0.00032043457];
};

_vehicle_228 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13589.692, 2981.1248, -0.00050735474], [], 0, "CAN_COLLIDE"];
  _vehicle_228 = _this;
  _this setDir -182.31403;
  _this setPos [13589.692, 2981.1248, -0.00050735474];
};

_vehicle_230 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13599.156, 2981.6804, -0.0001411438], [], 0, "CAN_COLLIDE"];
  _vehicle_230 = _this;
  _this setDir -181.26897;
  _this setPos [13599.156, 2981.6804, -0.0001411438];
};

_vehicle_233 = objNull;
if (true) then
{
  _this = createVehicle ["Hhedgehog_concreteBig", [13606.326, 2981.9365, -8.0108643e-005], [], 0, "CAN_COLLIDE"];
  _vehicle_233 = _this;
  _this setDir -182.88142;
  _this setPos [13606.326, 2981.9365, -8.0108643e-005];
};
};


