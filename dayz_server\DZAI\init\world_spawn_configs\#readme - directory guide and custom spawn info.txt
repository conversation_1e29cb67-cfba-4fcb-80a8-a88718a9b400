Directory Guide:

(Folders)
---------

Custom Spawns
--------------

custom_spawns: Custom static spawn definitions.

custom_markers:	Custom spawn point definitions, area blacklists for dynamic AI spawns.



Preset Static Spawns
--------------	

spawn_areas: Markers defining static spawn areas (Used for: Chernarus, Trinity Island, Napf, Sauerland)*

spawn_markers: Markers defining static spawn points


* Static spawn areas for every map to be converted to the new format eventually. 
I do all of this by hand, so it will take some time to fully complete.



(Files)
---------

world_(mapname).sqf: Pre-defined static spawn definitions. 
- Spawn areas (for Chernarus, Trinity Island, Napf, Sauerland) are defined in spawn_areas folder.
- Manual spawn points are defined in spawn_markers folder.
	
